'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// Import all major systems
import { ComprehensiveEngagementSystem } from '@/components/engagement/ComprehensiveEngagementSystem';
import { ComprehensiveAnalyticsSystem } from '@/components/analytics/ComprehensiveAnalyticsSystem';
import { ComprehensiveConversionSystem } from '@/components/conversion/ComprehensiveConversionSystem';
import { ComprehensiveContentSystem } from '@/components/content/ComprehensiveContentSystem';
import { ComprehensivePerformanceMonitoring } from '@/components/analytics/ComprehensivePerformanceMonitoring';
import { EnhancedPWAProvider } from '@/components/performance/PWAUtils';

// Integration context types
interface IntegrationConfig {
  // System enablement
  enableEngagement: boolean;
  enableAnalytics: boolean;
  enableConversion: boolean;
  enableContent: boolean;
  enablePerformanceMonitoring: boolean;
  enablePWA: boolean;
  
  // Cross-system features
  enableCrossSystemAnalytics: boolean;
  enableUnifiedUserTracking: boolean;
  enableRealTimeSync: boolean;
  enableErrorBoundaries: boolean;
  
  // Environment settings
  environment: 'development' | 'staging' | 'production';
  debugMode: boolean;
  enableDevTools: boolean;
  
  // Performance settings
  enableLazyLoading: boolean;
  enableCodeSplitting: boolean;
  enableServiceWorker: boolean;
  
  // Analytics configuration
  analyticsConfig: {
    googleAnalyticsId?: string;
    hotjarId?: string;
    sentryDsn?: string;
    enableHeatmaps: boolean;
    enableSessionRecordings: boolean;
    enableUserConsent: boolean;
    privacyCompliant: boolean;
    trackingLevel: 'minimal' | 'standard' | 'enhanced';
  };
  
  // Engagement configuration
  engagementConfig: {
    enableNotifications: boolean;
    enableStatsCounter: boolean;
    enableAIAssistant: boolean;
    enableFAQ: boolean;
    enableExitIntent: boolean;
    enableScrollCTAs: boolean;
    enableUrgencyTimers: boolean;
    enableSocialSharing: boolean;
    enableAchievements: boolean;
    theme: 'light' | 'dark' | 'auto';
    position: 'bottom-right' | 'bottom-left';
    animationLevel: 'minimal' | 'standard' | 'enhanced';
  };
  
  // Conversion configuration
  conversionConfig: {
    enableExitIntent: boolean;
    enableScrollTriggers: boolean;
    enableUrgencyTimers: boolean;
    enableScarcityIndicators: boolean;
    enableSocialProof: boolean;
    enablePersonalization: boolean;
    enableABTesting: boolean;
  };
}

interface UserContext {
  id?: string;
  name?: string;
  email?: string;
  role?: 'student' | 'instructor' | 'admin';
  currentPage: string;
  sessionId: string;
  progress?: {
    currentLesson?: string;
    completedLessons?: number;
    currentCourse?: string;
    totalXP?: number;
    level?: number;
    streak?: number;
  };
  preferences?: {
    reducedMotion?: boolean;
    notifications?: boolean;
    sounds?: boolean;
    theme?: 'light' | 'dark' | 'auto';
  };
  metadata?: {
    signupDate?: Date;
    lastLoginDate?: Date;
    deviceType?: 'mobile' | 'tablet' | 'desktop';
    browser?: string;
    location?: string;
  };
}

interface SystemHealth {
  engagement: 'healthy' | 'warning' | 'error';
  analytics: 'healthy' | 'warning' | 'error';
  conversion: 'healthy' | 'warning' | 'error';
  content: 'healthy' | 'warning' | 'error';
  performance: 'healthy' | 'warning' | 'error';
  overall: 'healthy' | 'warning' | 'error';
  lastChecked: Date;
  errors: string[];
  warnings: string[];
}

interface IntegrationContextType {
  config: IntegrationConfig;
  userContext: UserContext;
  systemHealth: SystemHealth;
  isInitialized: boolean;
  
  // System management
  updateConfig: (updates: Partial<IntegrationConfig>) => void;
  updateUserContext: (updates: Partial<UserContext>) => void;
  checkSystemHealth: () => Promise<SystemHealth>;
  
  // Cross-system communication
  broadcastEvent: (event: string, data: any) => void;
  subscribeToEvents: (callback: (event: string, data: any) => void) => () => void;
  
  // Error handling
  reportError: (error: Error, context?: any) => void;
  clearErrors: () => void;
  
  // Performance monitoring
  trackPerformanceMetric: (metric: string, value: number) => void;
  getPerformanceMetrics: () => any;
}

const IntegrationContext = createContext<IntegrationContextType | null>(null);

// Default configuration
const defaultConfig: IntegrationConfig = {
  enableEngagement: true,
  enableAnalytics: true,
  enableConversion: true,
  enableContent: true,
  enablePerformanceMonitoring: true,
  enablePWA: true,
  enableCrossSystemAnalytics: true,
  enableUnifiedUserTracking: true,
  enableRealTimeSync: true,
  enableErrorBoundaries: true,
  environment: 'production',
  debugMode: false,
  enableDevTools: false,
  enableLazyLoading: true,
  enableCodeSplitting: true,
  enableServiceWorker: true,
  analyticsConfig: {
    enableHeatmaps: true,
    enableSessionRecordings: true,
    enableUserConsent: true,
    privacyCompliant: true,
    trackingLevel: 'standard'
  },
  engagementConfig: {
    enableNotifications: true,
    enableStatsCounter: true,
    enableAIAssistant: true,
    enableFAQ: true,
    enableExitIntent: true,
    enableScrollCTAs: true,
    enableUrgencyTimers: true,
    enableSocialSharing: true,
    enableAchievements: true,
    theme: 'auto',
    position: 'bottom-right',
    animationLevel: 'standard'
  },
  conversionConfig: {
    enableExitIntent: true,
    enableScrollTriggers: true,
    enableUrgencyTimers: true,
    enableScarcityIndicators: true,
    enableSocialProof: true,
    enablePersonalization: true,
    enableABTesting: true
  }
};

interface ComprehensiveIntegrationSystemProps {
  children: React.ReactNode;
  config?: Partial<IntegrationConfig>;
  userContext: Partial<UserContext>;
  onSystemEvent?: (event: string, data: any) => void;
  onError?: (error: Error, context?: any) => void;
}

export function ComprehensiveIntegrationSystem({
  children,
  config: configOverrides = {},
  userContext: userContextOverrides,
  onSystemEvent,
  onError
}: ComprehensiveIntegrationSystemProps) {
  const [config, setConfig] = useState<IntegrationConfig>({
    ...defaultConfig,
    ...configOverrides
  });
  
  const [userContext, setUserContext] = useState<UserContext>({
    currentPage: '/',
    sessionId: generateSessionId(),
    ...userContextOverrides
  });
  
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    engagement: 'healthy',
    analytics: 'healthy',
    conversion: 'healthy',
    content: 'healthy',
    performance: 'healthy',
    overall: 'healthy',
    lastChecked: new Date(),
    errors: [],
    warnings: []
  });
  
  const [isInitialized, setIsInitialized] = useState(false);
  const [eventSubscribers, setEventSubscribers] = useState<((event: string, data: any) => void)[]>([]);

  // Initialize system
  useEffect(() => {
    const initializeSystem = async () => {
      try {
        // Initialize analytics if enabled
        if (config.enableAnalytics && config.analyticsConfig.googleAnalyticsId) {
          await initializeGoogleAnalytics(config.analyticsConfig.googleAnalyticsId);
        }
        
        // Initialize error reporting
        if (config.analyticsConfig.sentryDsn) {
          await initializeSentry(config.analyticsConfig.sentryDsn);
        }
        
        // Initialize service worker
        if (config.enableServiceWorker && 'serviceWorker' in navigator) {
          await navigator.serviceWorker.register('/sw.js');
        }
        
        // Check initial system health
        await checkSystemHealth();
        
        setIsInitialized(true);
        
        // Broadcast initialization complete
        broadcastEvent('system_initialized', {
          timestamp: new Date(),
          config,
          userContext
        });
        
      } catch (error) {
        console.error('System initialization failed:', error);
        reportError(error as Error, { phase: 'initialization' });
      }
    };

    initializeSystem();
  }, []);

  // System health monitoring
  const checkSystemHealth = useCallback(async (): Promise<SystemHealth> => {
    const health: SystemHealth = {
      engagement: 'healthy',
      analytics: 'healthy',
      conversion: 'healthy',
      content: 'healthy',
      performance: 'healthy',
      overall: 'healthy',
      lastChecked: new Date(),
      errors: [],
      warnings: []
    };

    try {
      // Check each system
      if (config.enableEngagement) {
        health.engagement = await checkEngagementHealth();
      }
      
      if (config.enableAnalytics) {
        health.analytics = await checkAnalyticsHealth();
      }
      
      if (config.enableConversion) {
        health.conversion = await checkConversionHealth();
      }
      
      if (config.enableContent) {
        health.content = await checkContentHealth();
      }
      
      if (config.enablePerformanceMonitoring) {
        health.performance = await checkPerformanceHealth();
      }

      // Determine overall health
      const systems = [health.engagement, health.analytics, health.conversion, health.content, health.performance];
      if (systems.some(status => status === 'error')) {
        health.overall = 'error';
      } else if (systems.some(status => status === 'warning')) {
        health.overall = 'warning';
      }

      setSystemHealth(health);
      return health;
      
    } catch (error) {
      health.overall = 'error';
      health.errors.push((error as Error).message);
      setSystemHealth(health);
      return health;
    }
  }, [config]);

  // Cross-system event broadcasting
  const broadcastEvent = useCallback((event: string, data: any) => {
    // Notify external handler
    onSystemEvent?.(event, data);
    
    // Notify internal subscribers
    eventSubscribers.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        console.error('Event subscriber error:', error);
      }
    });
    
    // Log in debug mode
    if (config.debugMode) {
      console.log('System Event:', event, data);
    }
  }, [eventSubscribers, onSystemEvent, config.debugMode]);

  // Event subscription
  const subscribeToEvents = useCallback((callback: (event: string, data: any) => void) => {
    setEventSubscribers(prev => [...prev, callback]);
    
    // Return unsubscribe function
    return () => {
      setEventSubscribers(prev => prev.filter(cb => cb !== callback));
    };
  }, []);

  // Error reporting
  const reportError = useCallback((error: Error, context: any = {}) => {
    const errorReport = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date(),
      userContext,
      systemContext: context,
      url: window.location.href,
      userAgent: navigator.userAgent
    };

    // Report to external handler
    onError?.(error, errorReport);
    
    // Report to Sentry if configured
    if (config.analyticsConfig.sentryDsn && window.Sentry) {
      window.Sentry.captureException(error, {
        extra: errorReport
      });
    }
    
    // Update system health
    setSystemHealth(prev => ({
      ...prev,
      errors: [...prev.errors, error.message],
      overall: 'error'
    }));
    
    // Broadcast error event
    broadcastEvent('system_error', errorReport);
  }, [userContext, onError, config.analyticsConfig.sentryDsn, broadcastEvent]);

  // Performance tracking
  const trackPerformanceMetric = useCallback((metric: string, value: number) => {
    if (config.enablePerformanceMonitoring) {
      // Track with analytics
      if (config.enableAnalytics && window.gtag) {
        window.gtag('event', 'performance_metric', {
          metric_name: metric,
          metric_value: value,
          user_id: userContext.id
        });
      }
      
      // Broadcast performance event
      broadcastEvent('performance_metric', {
        metric,
        value,
        timestamp: new Date(),
        userId: userContext.id
      });
    }
  }, [config.enablePerformanceMonitoring, config.enableAnalytics, userContext.id, broadcastEvent]);

  // Configuration updates
  const updateConfig = useCallback((updates: Partial<IntegrationConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
    broadcastEvent('config_updated', { updates, timestamp: new Date() });
  }, [broadcastEvent]);

  // User context updates
  const updateUserContext = useCallback((updates: Partial<UserContext>) => {
    setUserContext(prev => ({ ...prev, ...updates }));
    broadcastEvent('user_context_updated', { updates, timestamp: new Date() });
  }, [broadcastEvent]);

  // Clear errors
  const clearErrors = useCallback(() => {
    setSystemHealth(prev => ({
      ...prev,
      errors: [],
      warnings: [],
      overall: 'healthy'
    }));
  }, []);

  // Get performance metrics
  const getPerformanceMetrics = useCallback(() => {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const paint = performance.getEntriesByType('paint');
      
      return {
        loadTime: navigation?.loadEventEnd - navigation?.navigationStart,
        domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.navigationStart,
        firstPaint: paint.find(entry => entry.name === 'first-paint')?.startTime,
        firstContentfulPaint: paint.find(entry => entry.name === 'first-contentful-paint')?.startTime,
        timestamp: new Date()
      };
    }
    return null;
  }, []);

  const contextValue: IntegrationContextType = {
    config,
    userContext,
    systemHealth,
    isInitialized,
    updateConfig,
    updateUserContext,
    checkSystemHealth,
    broadcastEvent,
    subscribeToEvents,
    reportError,
    clearErrors,
    trackPerformanceMetric,
    getPerformanceMetrics
  };

  // Render system with error boundaries if enabled
  const SystemContent = () => (
    <IntegrationContext.Provider value={contextValue}>
      {config.enablePWA ? (
        <EnhancedPWAProvider
          enableOfflineSync={true}
          enablePushNotifications={true}
          enableBackgroundSync={true}
        >
          <SystemProviders />
        </EnhancedPWAProvider>
      ) : (
        <SystemProviders />
      )}
    </IntegrationContext.Provider>
  );

  const SystemProviders = () => (
    <>
      {config.enableAnalytics && (
        <ComprehensiveAnalyticsSystem
          config={config.analyticsConfig}
          userId={userContext.id}
          userTraits={userContext.metadata}
        >
          <ContentProviders />
        </ComprehensiveAnalyticsSystem>
      )}
      {!config.enableAnalytics && <ContentProviders />}
    </>
  );

  const ContentProviders = () => (
    <>
      {config.enableContent && (
        <ComprehensiveContentSystem
          enableAnalytics={config.enableAnalytics}
          enablePersonalization={config.conversionConfig.enablePersonalization}
        >
          <ConversionProviders />
        </ComprehensiveContentSystem>
      )}
      {!config.enableContent && <ConversionProviders />}
    </>
  );

  const ConversionProviders = () => (
    <>
      {config.enableConversion && (
        <ComprehensiveConversionSystem
          config={config.conversionConfig}
          userId={userContext.id}
          userTraits={userContext.metadata}
        >
          <EngagementProviders />
        </ComprehensiveConversionSystem>
      )}
      {!config.enableConversion && <EngagementProviders />}
    </>
  );

  const EngagementProviders = () => (
    <>
      {config.enableEngagement && (
        <ComprehensiveEngagementSystem
          config={config.engagementConfig}
          userContext={userContext}
          onEngagementEvent={(event, data) => {
            broadcastEvent(`engagement_${event}`, data);
          }}
        >
          <MainContent />
        </ComprehensiveEngagementSystem>
      )}
      {!config.enableEngagement && <MainContent />}
    </>
  );

  const MainContent = () => (
    <>
      {config.enablePerformanceMonitoring && (
        <ComprehensivePerformanceMonitoring
          enableRealTimeMonitoring={true}
          enableErrorTracking={true}
          sentryDsn={config.analyticsConfig.sentryDsn}
        />
      )}
      {children}
      {config.enableDevTools && config.environment === 'development' && (
        <SystemDevTools />
      )}
    </>
  );

  if (config.enableErrorBoundaries) {
    return (
      <SystemErrorBoundary onError={reportError}>
        <SystemContent />
      </SystemErrorBoundary>
    );
  }

  return <SystemContent />;
}

// System health check functions
async function checkEngagementHealth(): Promise<'healthy' | 'warning' | 'error'> {
  // Check if engagement components are responding
  try {
    // Simulate health check
    await new Promise(resolve => setTimeout(resolve, 100));
    return 'healthy';
  } catch {
    return 'error';
  }
}

async function checkAnalyticsHealth(): Promise<'healthy' | 'warning' | 'error'> {
  // Check analytics connectivity
  try {
    if (window.gtag) {
      return 'healthy';
    }
    return 'warning';
  } catch {
    return 'error';
  }
}

async function checkConversionHealth(): Promise<'healthy' | 'warning' | 'error'> {
  // Check conversion system
  try {
    return 'healthy';
  } catch {
    return 'error';
  }
}

async function checkContentHealth(): Promise<'healthy' | 'warning' | 'error'> {
  // Check content system
  try {
    return 'healthy';
  } catch {
    return 'error';
  }
}

async function checkPerformanceHealth(): Promise<'healthy' | 'warning' | 'error'> {
  // Check performance metrics
  try {
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      const loadTime = navigation?.loadEventEnd - navigation?.navigationStart;
      
      if (loadTime > 5000) return 'warning';
      if (loadTime > 10000) return 'error';
    }
    return 'healthy';
  } catch {
    return 'error';
  }
}

// Utility functions
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

async function initializeGoogleAnalytics(gaId: string) {
  if (typeof window !== 'undefined') {
    const script = document.createElement('script');
    script.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    script.async = true;
    document.head.appendChild(script);

    window.dataLayer = window.dataLayer || [];
    window.gtag = function() {
      window.dataLayer.push(arguments);
    };
    window.gtag('js', new Date());
    window.gtag('config', gaId);
  }
}

async function initializeSentry(dsn: string) {
  if (typeof window !== 'undefined') {
    // Initialize Sentry (would require Sentry SDK)
    console.log('Sentry initialized with DSN:', dsn);
  }
}

// Error boundary component
class SystemErrorBoundary extends React.Component<
  { children: React.ReactNode; onError: (error: Error, context?: any) => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.props.onError(error, { errorInfo, boundary: 'SystemErrorBoundary' });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div className="text-center p-8">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Something went wrong
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              We're sorry, but something unexpected happened. Please try refreshing the page.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Development tools component
function SystemDevTools() {
  const integration = useComprehensiveIntegration();
  const [isOpen, setIsOpen] = useState(false);

  if (!integration) return null;

  return (
    <div className="fixed bottom-4 left-4 z-50">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="px-3 py-2 bg-gray-800 text-white rounded text-xs font-mono"
      >
        DevTools
      </button>
      
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="mt-2 p-4 bg-gray-800 text-white rounded text-xs font-mono max-w-md max-h-96 overflow-auto"
          >
            <h3 className="font-bold mb-2">System Status</h3>
            <div className="space-y-1">
              <div>Health: {integration.systemHealth.overall}</div>
              <div>Initialized: {integration.isInitialized ? 'Yes' : 'No'}</div>
              <div>User ID: {integration.userContext.id || 'Anonymous'}</div>
              <div>Session: {integration.userContext.sessionId}</div>
              <div>Page: {integration.userContext.currentPage}</div>
            </div>
            
            <h4 className="font-bold mt-4 mb-2">Performance</h4>
            <div className="space-y-1">
              {Object.entries(integration.getPerformanceMetrics() || {}).map(([key, value]) => (
                <div key={key}>{key}: {String(value)}</div>
              ))}
            </div>
            
            <button
              onClick={() => integration.checkSystemHealth()}
              className="mt-4 px-2 py-1 bg-blue-600 rounded text-xs"
            >
              Check Health
            </button>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Hook to use integration context
export function useComprehensiveIntegration() {
  const context = useContext(IntegrationContext);
  if (!context) {
    throw new Error('useComprehensiveIntegration must be used within ComprehensiveIntegrationSystem');
  }
  return context;
}

export default ComprehensiveIntegrationSystem;
