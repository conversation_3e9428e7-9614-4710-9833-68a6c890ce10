'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Send, Star, ThumbsUp, ThumbsDown } from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import { useFeedbackWidget } from './ConversionFunnelSystem';

// Main Feedback Widget Component
export function FeedbackWidget() {
  const { activeWidget, isVisible, submitFeedback, dismissWidget } = useFeedbackWidget();
  const [responses, setResponses] = useState<Record<string, any>>({});
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);

  if (!isVisible || !activeWidget) return null;

  const currentQuestion = activeWidget.content.questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === activeWidget.content.questions.length - 1;
  const canProceed = !currentQuestion.required || responses[currentQuestion.id] !== undefined;

  const handleResponse = (questionId: string, value: any) => {
    setResponses(prev => ({ ...prev, [questionId]: value }));
  };

  const handleNext = () => {
    if (isLastQuestion) {
      submitFeedback(responses);
      setResponses({});
      setCurrentQuestionIndex(0);
    } else {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const getPositionClasses = () => {
    switch (activeWidget.styling.position) {
      case 'bottom-right':
        return 'fixed bottom-6 right-6 max-w-sm';
      case 'bottom-left':
        return 'fixed bottom-6 left-6 max-w-sm';
      case 'center':
        return 'fixed inset-0 flex items-center justify-center p-4';
      case 'top-banner':
        return 'fixed top-0 left-0 right-0';
      default:
        return 'fixed bottom-6 right-6 max-w-sm';
    }
  };

  const getSizeClasses = () => {
    switch (activeWidget.styling.size) {
      case 'compact':
        return 'w-80';
      case 'expanded':
        return 'w-96 max-w-lg';
      default:
        return 'w-80';
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        className={cn('z-50', getPositionClasses())}
        initial={{ opacity: 0, scale: 0.8, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: 20 }}
        transition={{ duration: 0.3 }}
      >
        {activeWidget.styling.position === 'center' && (
          <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={dismissWidget} />
        )}
        
        <div className={cn(
          'bg-gray-900 rounded-lg shadow-2xl border border-gray-700 relative',
          getSizeClasses()
        )}>
          {/* Header */}
          <div className="p-4 border-b border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-white font-medium">{activeWidget.content.title}</h3>
                {activeWidget.content.description && (
                  <p className="text-gray-400 text-sm mt-1">{activeWidget.content.description}</p>
                )}
              </div>
              <button
                onClick={dismissWidget}
                className="p-1 text-gray-400 hover:text-white transition-colors"
                aria-label="Close feedback"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            
            {/* Progress indicator */}
            {activeWidget.content.questions.length > 1 && (
              <div className="mt-3">
                <div className="flex items-center justify-between text-xs text-gray-400 mb-1">
                  <span>Question {currentQuestionIndex + 1} of {activeWidget.content.questions.length}</span>
                  <span>{Math.round(((currentQuestionIndex + 1) / activeWidget.content.questions.length) * 100)}%</span>
                </div>
                <div className="w-full bg-gray-700 rounded-full h-1">
                  <div 
                    className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                    style={{ width: `${((currentQuestionIndex + 1) / activeWidget.content.questions.length) * 100}%` }}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Question Content */}
          <div className="p-4">
            <div className="mb-4">
              <h4 className="text-white font-medium mb-3">{currentQuestion.question}</h4>
              
              {/* Question Input */}
              {currentQuestion.type === 'rating' && (
                <div className="flex space-x-2">
                  {[1, 2, 3, 4, 5].map(rating => (
                    <button
                      key={rating}
                      onClick={() => handleResponse(currentQuestion.id, rating)}
                      className={cn(
                        'p-2 rounded-lg transition-colors',
                        responses[currentQuestion.id] === rating
                          ? 'bg-yellow-500 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      )}
                    >
                      <Star className={cn(
                        'w-5 h-5',
                        responses[currentQuestion.id] >= rating ? 'fill-current' : ''
                      )} />
                    </button>
                  ))}
                </div>
              )}

              {currentQuestion.type === 'scale' && (
                <div className="space-y-3">
                  <div className="flex justify-between text-sm text-gray-400">
                    <span>Not likely</span>
                    <span>Very likely</span>
                  </div>
                  <div className="flex space-x-1">
                    {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(score => (
                      <button
                        key={score}
                        onClick={() => handleResponse(currentQuestion.id, score)}
                        className={cn(
                          'flex-1 py-2 text-sm rounded transition-colors',
                          responses[currentQuestion.id] === score
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                        )}
                      >
                        {score}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {currentQuestion.type === 'choice' && currentQuestion.options && (
                <div className="space-y-2">
                  {currentQuestion.options.map(option => (
                    <button
                      key={option}
                      onClick={() => handleResponse(currentQuestion.id, option)}
                      className={cn(
                        'w-full text-left p-3 rounded-lg transition-colors',
                        responses[currentQuestion.id] === option
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      )}
                    >
                      {option}
                    </button>
                  ))}
                </div>
              )}

              {currentQuestion.type === 'text' && (
                <textarea
                  value={responses[currentQuestion.id] || ''}
                  onChange={(e) => handleResponse(currentQuestion.id, e.target.value)}
                  placeholder="Your feedback..."
                  className="w-full p-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                  rows={3}
                />
              )}
            </div>

            {/* Navigation */}
            <div className="flex items-center justify-between">
              <div>
                {currentQuestionIndex > 0 && (
                  <EnhancedButton
                    onClick={handlePrevious}
                    variant="ghost"
                    size="sm"
                    className="text-gray-400 hover:text-white"
                  >
                    Previous
                  </EnhancedButton>
                )}
              </div>
              
              <div className="flex space-x-2">
                <EnhancedButton
                  onClick={dismissWidget}
                  variant="ghost"
                  size="sm"
                  className="text-gray-400 hover:text-white"
                >
                  Skip
                </EnhancedButton>
                <EnhancedButton
                  onClick={handleNext}
                  disabled={!canProceed}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isLastQuestion ? (
                    <>
                      <Send className="w-3 h-3 mr-1" />
                      Submit
                    </>
                  ) : (
                    'Next'
                  )}
                </EnhancedButton>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

// NPS Survey Component
export function NPSSurvey({ onSubmit, onDismiss }: { 
  onSubmit: (score: number, reason?: string) => void; 
  onDismiss: () => void; 
}) {
  const [score, setScore] = useState<number | null>(null);
  const [reason, setReason] = useState('');
  const [showReason, setShowReason] = useState(false);

  const handleScoreSelect = (selectedScore: number) => {
    setScore(selectedScore);
    setShowReason(true);
  };

  const handleSubmit = () => {
    if (score !== null) {
      onSubmit(score, reason || undefined);
    }
  };

  const getScoreColor = (s: number) => {
    if (s <= 6) return 'text-red-400';
    if (s <= 8) return 'text-yellow-400';
    return 'text-green-400';
  };

  const getScoreLabel = () => {
    if (score === null) return '';
    if (score <= 6) return 'Detractor';
    if (score <= 8) return 'Passive';
    return 'Promoter';
  };

  return (
    <motion.div
      className="fixed bottom-6 right-6 max-w-sm z-50"
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="bg-gray-900 rounded-lg shadow-2xl border border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-white font-medium">Quick feedback</h3>
          <button
            onClick={onDismiss}
            className="p-1 text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        </div>

        <div className="space-y-4">
          <div>
            <p className="text-gray-300 text-sm mb-3">
              How likely are you to recommend our platform to a friend or colleague?
            </p>
            
            <div className="flex justify-between text-xs text-gray-400 mb-2">
              <span>Not likely</span>
              <span>Very likely</span>
            </div>
            
            <div className="grid grid-cols-11 gap-1">
              {[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map(s => (
                <button
                  key={s}
                  onClick={() => handleScoreSelect(s)}
                  className={cn(
                    'aspect-square text-sm rounded transition-colors',
                    score === s
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  )}
                >
                  {s}
                </button>
              ))}
            </div>
          </div>

          <AnimatePresence>
            {showReason && score !== null && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="space-y-3">
                  <div className="text-center">
                    <span className={cn('font-medium', getScoreColor(score))}>
                      {getScoreLabel()} ({score}/10)
                    </span>
                  </div>
                  
                  <textarea
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    placeholder="What's the main reason for your score? (optional)"
                    className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                    rows={2}
                  />
                  
                  <div className="flex space-x-2">
                    <EnhancedButton
                      onClick={onDismiss}
                      variant="ghost"
                      size="sm"
                      className="flex-1 text-gray-400 hover:text-white"
                    >
                      Skip
                    </EnhancedButton>
                    <EnhancedButton
                      onClick={handleSubmit}
                      size="sm"
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      Submit
                    </EnhancedButton>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
}

// Quick Feedback Component (Thumbs up/down)
export function QuickFeedback({ 
  onFeedback, 
  onDismiss,
  question = "Was this helpful?"
}: { 
  onFeedback: (positive: boolean, comment?: string) => void; 
  onDismiss: () => void;
  question?: string;
}) {
  const [selectedFeedback, setSelectedFeedback] = useState<boolean | null>(null);
  const [comment, setComment] = useState('');
  const [showComment, setShowComment] = useState(false);

  const handleFeedbackSelect = (positive: boolean) => {
    setSelectedFeedback(positive);
    setShowComment(true);
  };

  const handleSubmit = () => {
    if (selectedFeedback !== null) {
      onFeedback(selectedFeedback, comment || undefined);
    }
  };

  return (
    <motion.div
      className="fixed bottom-6 left-6 max-w-sm z-50"
      initial={{ opacity: 0, scale: 0.8, y: 20 }}
      animate={{ opacity: 1, scale: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="bg-gray-900 rounded-lg shadow-2xl border border-gray-700 p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-white font-medium text-sm">{question}</h4>
          <button
            onClick={onDismiss}
            className="p-1 text-gray-400 hover:text-white transition-colors"
          >
            <X className="w-3 h-3" />
          </button>
        </div>

        <div className="space-y-3">
          <div className="flex space-x-3 justify-center">
            <button
              onClick={() => handleFeedbackSelect(true)}
              className={cn(
                'p-3 rounded-lg transition-colors',
                selectedFeedback === true
                  ? 'bg-green-500 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              )}
            >
              <ThumbsUp className="w-5 h-5" />
            </button>
            <button
              onClick={() => handleFeedbackSelect(false)}
              className={cn(
                'p-3 rounded-lg transition-colors',
                selectedFeedback === false
                  ? 'bg-red-500 text-white'
                  : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
              )}
            >
              <ThumbsDown className="w-5 h-5" />
            </button>
          </div>

          <AnimatePresence>
            {showComment && selectedFeedback !== null && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                <div className="space-y-3">
                  <textarea
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    placeholder="Tell us more... (optional)"
                    className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-white placeholder-gray-400 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                    rows={2}
                  />
                  
                  <div className="flex space-x-2">
                    <EnhancedButton
                      onClick={onDismiss}
                      variant="ghost"
                      size="sm"
                      className="flex-1 text-gray-400 hover:text-white"
                    >
                      Skip
                    </EnhancedButton>
                    <EnhancedButton
                      onClick={handleSubmit}
                      size="sm"
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      Submit
                    </EnhancedButton>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
}
