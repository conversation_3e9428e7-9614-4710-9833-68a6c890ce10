'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Users, 
  TrendingUp, 
  Target, 
  Clock, 
  Activity,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  RefreshCw,
  Download,
  Filter,
  Calendar,
  AlertCircle,
  CheckCircle,
  ArrowUp,
  ArrowDown
} from 'lucide-react';

// Import analytics context
import { useComprehensiveAnalytics } from './ComprehensiveAnalyticsSystem';

interface RealTimeMetric {
  id: string;
  name: string;
  value: number;
  previousValue: number;
  change: number;
  changeType: 'increase' | 'decrease' | 'neutral';
  unit: string;
  icon: React.ReactNode;
  color: string;
  target?: number;
  description: string;
}

interface RealTimeAnalyticsDashboardProps {
  className?: string;
  refreshInterval?: number;
  showFilters?: boolean;
  showExport?: boolean;
  compactMode?: boolean;
}

export function RealTimeAnalyticsDashboard({
  className,
  refreshInterval = 30000, // 30 seconds
  showFilters = true,
  showExport = true,
  compactMode = false
}: RealTimeAnalyticsDashboardProps) {
  const { 
    conversionFunnel, 
    userSegment, 
    trackEvent,
    isLoading,
    error 
  } = useComprehensiveAnalytics();

  const [metrics, setMetrics] = useState<RealTimeMetric[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [selectedTimeRange, setSelectedTimeRange] = useState('1h');
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);
  const intervalRef = useRef<NodeJS.Timeout>();

  // Initialize metrics
  useEffect(() => {
    const initialMetrics: RealTimeMetric[] = [
      {
        id: 'active_users',
        name: 'Active Users',
        value: 247,
        previousValue: 231,
        change: 6.9,
        changeType: 'increase',
        unit: '',
        icon: <Users className="w-5 h-5" />,
        color: 'text-blue-500',
        description: 'Users currently online'
      },
      {
        id: 'conversion_rate',
        name: 'Conversion Rate',
        value: 3.2,
        previousValue: 2.8,
        change: 14.3,
        changeType: 'increase',
        unit: '%',
        icon: <Target className="w-5 h-5" />,
        color: 'text-green-500',
        target: 5.0,
        description: 'Trial signup conversion rate'
      },
      {
        id: 'avg_session_duration',
        name: 'Avg Session Duration',
        value: 8.5,
        previousValue: 7.2,
        change: 18.1,
        changeType: 'increase',
        unit: 'min',
        icon: <Clock className="w-5 h-5" />,
        color: 'text-purple-500',
        description: 'Average time spent per session'
      },
      {
        id: 'bounce_rate',
        name: 'Bounce Rate',
        value: 32.1,
        previousValue: 38.7,
        change: -17.1,
        changeType: 'decrease',
        unit: '%',
        icon: <TrendingUp className="w-5 h-5" />,
        color: 'text-orange-500',
        description: 'Percentage of single-page sessions'
      },
      {
        id: 'page_load_time',
        name: 'Page Load Time',
        value: 1.2,
        previousValue: 1.8,
        change: -33.3,
        changeType: 'decrease',
        unit: 's',
        icon: <Activity className="w-5 h-5" />,
        color: 'text-indigo-500',
        target: 2.0,
        description: 'Average page load time'
      },
      {
        id: 'error_rate',
        name: 'Error Rate',
        value: 0.3,
        previousValue: 0.7,
        change: -57.1,
        changeType: 'decrease',
        unit: '%',
        icon: <AlertCircle className="w-5 h-5" />,
        color: 'text-red-500',
        target: 1.0,
        description: 'Percentage of requests with errors'
      }
    ];

    setMetrics(initialMetrics);
    setSelectedMetrics(initialMetrics.map(m => m.id));
  }, []);

  // Auto-refresh functionality
  useEffect(() => {
    const refreshData = async () => {
      setIsRefreshing(true);
      
      try {
        // Simulate API call to fetch real-time data
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Update metrics with simulated real-time data
        setMetrics(prev => prev.map(metric => ({
          ...metric,
          previousValue: metric.value,
          value: metric.value + (Math.random() - 0.5) * metric.value * 0.1,
          change: (Math.random() - 0.5) * 20
        })));

        setLastUpdated(new Date());
        
        // Track dashboard view
        trackEvent('dashboard_refreshed', {
          category: 'analytics',
          action: 'refresh',
          timeRange: selectedTimeRange
        });
      } catch (error) {
        console.error('Failed to refresh analytics data:', error);
      } finally {
        setIsRefreshing(false);
      }
    };

    // Initial load
    refreshData();

    // Set up interval
    intervalRef.current = setInterval(refreshData, refreshInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [refreshInterval, selectedTimeRange, trackEvent]);

  const handleManualRefresh = () => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    // Restart interval
    const refreshData = async () => {
      setIsRefreshing(true);
      await new Promise(resolve => setTimeout(resolve, 1000));
      setMetrics(prev => prev.map(metric => ({
        ...metric,
        previousValue: metric.value,
        value: metric.value + (Math.random() - 0.5) * metric.value * 0.1,
        change: (Math.random() - 0.5) * 20
      })));
      setLastUpdated(new Date());
      setIsRefreshing(false);
    };

    refreshData();
    intervalRef.current = setInterval(refreshData, refreshInterval);
  };

  const handleExportData = () => {
    const data = {
      metrics,
      conversionFunnel,
      userSegment,
      exportedAt: new Date().toISOString(),
      timeRange: selectedTimeRange
    };

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `analytics-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    trackEvent('dashboard_exported', {
      category: 'analytics',
      action: 'export',
      format: 'json'
    });
  };

  const filteredMetrics = metrics.filter(metric => 
    selectedMetrics.includes(metric.id)
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64 text-red-500">
        <AlertCircle className="w-6 h-6 mr-2" />
        <span>Failed to load analytics data</span>
      </div>
    );
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Real-Time Analytics
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {/* Time Range Selector */}
          {showFilters && (
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-sm"
            >
              <option value="1h">Last Hour</option>
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
            </select>
          )}

          {/* Export Button */}
          {showExport && (
            <button
              onClick={handleExportData}
              className="flex items-center space-x-2 px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </button>
          )}

          {/* Refresh Button */}
          <button
            onClick={handleManualRefresh}
            disabled={isRefreshing}
            className="flex items-center space-x-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors text-sm"
          >
            <RefreshCw className={cn('w-4 h-4', isRefreshing && 'animate-spin')} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className={cn(
        'grid gap-6 mb-8',
        compactMode 
          ? 'grid-cols-2 lg:grid-cols-3' 
          : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
      )}>
        <AnimatePresence>
          {filteredMetrics.map((metric, index) => (
            <motion.div
              key={metric.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
            >
              <MetricCard metric={metric} compact={compactMode} />
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Conversion Funnel Visualization */}
      {!compactMode && conversionFunnel.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Conversion Funnel
          </h3>
          <ConversionFunnelChart funnel={conversionFunnel} />
        </div>
      )}
    </div>
  );
}

interface MetricCardProps {
  metric: RealTimeMetric;
  compact?: boolean;
}

function MetricCard({ metric, compact = false }: MetricCardProps) {
  const isPositiveChange = metric.changeType === 'increase';
  const isNegativeChange = metric.changeType === 'decrease';
  
  // For metrics like bounce rate and error rate, decrease is good
  const isGoodChange = (metric.id === 'bounce_rate' || metric.id === 'error_rate' || metric.id === 'page_load_time') 
    ? isNegativeChange 
    : isPositiveChange;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-shadow">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className={cn('p-2 rounded-lg', `bg-${metric.color.split('-')[1]}-100 dark:bg-${metric.color.split('-')[1]}-900/20`)}>
          <div className={metric.color}>
            {metric.icon}
          </div>
        </div>
        
        {metric.target && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Target: {metric.target}{metric.unit}
          </div>
        )}
      </div>

      {/* Value */}
      <div className="mb-2">
        <div className="text-2xl font-bold text-gray-900 dark:text-white">
          {metric.value.toFixed(metric.unit === '%' || metric.unit === 's' ? 1 : 0)}
          <span className="text-sm font-normal text-gray-500 ml-1">
            {metric.unit}
          </span>
        </div>
        <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
          {metric.name}
        </div>
      </div>

      {/* Change Indicator */}
      <div className="flex items-center justify-between">
        <div className={cn(
          'flex items-center space-x-1 text-sm',
          isGoodChange ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
        )}>
          {isPositiveChange ? (
            <ArrowUp className="w-3 h-3" />
          ) : isNegativeChange ? (
            <ArrowDown className="w-3 h-3" />
          ) : null}
          <span>{Math.abs(metric.change).toFixed(1)}%</span>
        </div>

        {metric.target && (
          <div className="flex items-center space-x-1">
            {metric.value <= metric.target ? (
              <CheckCircle className="w-4 h-4 text-green-500" />
            ) : (
              <AlertCircle className="w-4 h-4 text-red-500" />
            )}
          </div>
        )}
      </div>

      {/* Description */}
      {!compact && (
        <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
          {metric.description}
        </div>
      )}
    </div>
  );
}

interface ConversionFunnelChartProps {
  funnel: any[];
}

function ConversionFunnelChart({ funnel }: ConversionFunnelChartProps) {
  const maxUsers = Math.max(...funnel.map(step => step.users));

  return (
    <div className="space-y-4">
      {funnel.map((step, index) => {
        const width = (step.users / maxUsers) * 100;
        const isLastStep = index === funnel.length - 1;
        
        return (
          <div key={step.step} className="relative">
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm font-medium text-gray-900 dark:text-white">
                {step.name}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">
                {step.users.toLocaleString()} users ({step.conversionRate.toFixed(1)}%)
              </div>
            </div>
            
            <div className="relative h-8 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden">
              <motion.div
                className={cn(
                  'h-full rounded-lg',
                  isLastStep ? 'bg-green-500' : 'bg-blue-500'
                )}
                initial={{ width: 0 }}
                animate={{ width: `${width}%` }}
                transition={{ duration: 1, delay: index * 0.2 }}
              />
              
              <div className="absolute inset-0 flex items-center justify-center text-white text-sm font-medium">
                {step.conversions.toLocaleString()} conversions
              </div>
            </div>
            
            {!isLastStep && (
              <div className="text-center mt-1 text-xs text-red-600 dark:text-red-400">
                {step.dropOffRate.toFixed(1)}% drop-off
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}

export default RealTimeAnalyticsDashboard;
