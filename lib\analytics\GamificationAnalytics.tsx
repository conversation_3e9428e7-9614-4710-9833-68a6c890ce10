'use client';

import { useState, useEffect, useCallback } from 'react';

export interface AnalyticsEvent {
  id: string;
  type: 'xp_gained' | 'achievement_unlocked' | 'level_up' | 'streak_extended' | 'feature_unlocked' | 'social_share';
  userId: string;
  timestamp: Date;
  data: Record<string, any>;
  sessionId: string;
  source: string;
  metadata?: Record<string, any>;
}

export interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  timestamp: Date;
  category: 'response_time' | 'render_time' | 'animation_fps' | 'memory_usage' | 'network';
  threshold?: number;
  status: 'good' | 'warning' | 'critical';
}

export interface UserEngagementMetrics {
  sessionDuration: number;
  actionsPerMinute: number;
  xpPerSession: number;
  achievementsUnlocked: number;
  featuresUsed: string[];
  retentionScore: number;
  engagementLevel: 'low' | 'medium' | 'high' | 'very_high';
}

class GamificationAnalytics {
  private static instance: GamificationAnalytics;
  private events: AnalyticsEvent[] = [];
  private metrics: PerformanceMetric[] = [];
  private sessionId: string;
  private userId: string | null = null;
  private sessionStart: Date;
  private isEnabled: boolean = true;
  private batchSize: number = 10;
  private flushInterval: number = 30000; // 30 seconds
  private performanceObserver: PerformanceObserver | null = null;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.sessionStart = new Date();
    this.initializePerformanceMonitoring();
    this.startBatchFlush();
  }

  static getInstance(): GamificationAnalytics {
    if (!GamificationAnalytics.instance) {
      GamificationAnalytics.instance = new GamificationAnalytics();
    }
    return GamificationAnalytics.instance;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private initializePerformanceMonitoring() {
    if (typeof window === 'undefined') return;

    // Monitor performance entries
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.recordPerformanceMetric({
            name: entry.name,
            value: entry.duration || entry.startTime,
            category: this.categorizePerformanceEntry(entry),
            threshold: this.getThresholdForMetric(entry.name)
          });
        }
      });

      try {
        this.performanceObserver.observe({ entryTypes: ['measure', 'navigation', 'paint'] });
      } catch (error) {
        console.warn('Performance monitoring not fully supported:', error);
      }
    }

    // Monitor FPS
    this.monitorFPS();

    // Monitor memory usage
    this.monitorMemoryUsage();
  }

  private categorizePerformanceEntry(entry: PerformanceEntry): PerformanceMetric['category'] {
    if (entry.name.includes('xp') || entry.name.includes('achievement')) {
      return 'response_time';
    }
    if (entry.entryType === 'paint') {
      return 'render_time';
    }
    if (entry.entryType === 'navigation') {
      return 'network';
    }
    return 'response_time';
  }

  private getThresholdForMetric(name: string): number {
    if (name.includes('xp')) return 200; // 200ms for XP updates
    if (name.includes('achievement')) return 300; // 300ms for achievements
    if (name.includes('level')) return 500; // 500ms for level ups
    return 1000; // Default threshold
  }

  private monitorFPS() {
    let lastTime = performance.now();
    let frameCount = 0;

    const measureFPS = () => {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
        this.recordPerformanceMetric({
          name: 'fps',
          value: fps,
          category: 'animation_fps',
          threshold: 60
        });
        
        frameCount = 0;
        lastTime = currentTime;
      }
      
      requestAnimationFrame(measureFPS);
    };

    requestAnimationFrame(measureFPS);
  }

  private monitorMemoryUsage() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = (performance as any).memory;
        this.recordPerformanceMetric({
          name: 'memory_used',
          value: memory.usedJSHeapSize / 1024 / 1024, // MB
          category: 'memory_usage',
          threshold: 100 // 100MB threshold
        });
      }, 10000); // Every 10 seconds
    }
  }

  private startBatchFlush() {
    setInterval(() => {
      this.flushEvents();
    }, this.flushInterval);
  }

  setUserId(userId: string) {
    this.userId = userId;
  }

  setEnabled(enabled: boolean) {
    this.isEnabled = enabled;
  }

  // Track gamification events
  trackXPGained(amount: number, source: string, metadata?: Record<string, any>) {
    const startTime = performance.now();
    
    this.trackEvent({
      type: 'xp_gained',
      data: {
        amount,
        source,
        responseTime: performance.now() - startTime
      },
      source: 'gamification',
      metadata
    });
  }

  trackAchievementUnlocked(achievementId: string, rarity: string, xpReward: number) {
    this.trackEvent({
      type: 'achievement_unlocked',
      data: {
        achievementId,
        rarity,
        xpReward,
        sessionTime: Date.now() - this.sessionStart.getTime()
      },
      source: 'achievements'
    });
  }

  trackLevelUp(newLevel: number, previousLevel: number, totalXP: number) {
    this.trackEvent({
      type: 'level_up',
      data: {
        newLevel,
        previousLevel,
        totalXP,
        sessionTime: Date.now() - this.sessionStart.getTime()
      },
      source: 'progression'
    });
  }

  trackStreakExtended(newStreak: number, previousStreak: number) {
    this.trackEvent({
      type: 'streak_extended',
      data: {
        newStreak,
        previousStreak,
        streakGrowth: newStreak - previousStreak
      },
      source: 'streaks'
    });
  }

  trackFeatureUnlocked(featureId: string, requiredLevel: number, currentLevel: number) {
    this.trackEvent({
      type: 'feature_unlocked',
      data: {
        featureId,
        requiredLevel,
        currentLevel,
        unlockTime: Date.now() - this.sessionStart.getTime()
      },
      source: 'features'
    });
  }

  trackSocialShare(platform: string, contentType: 'achievement' | 'level' | 'progress', contentId: string) {
    this.trackEvent({
      type: 'social_share',
      data: {
        platform,
        contentType,
        contentId,
        shareTime: Date.now() - this.sessionStart.getTime()
      },
      source: 'social'
    });
  }

  // Performance tracking
  recordPerformanceMetric(metric: Omit<PerformanceMetric, 'id' | 'timestamp' | 'status'>) {
    const status = this.determineMetricStatus(metric.value, metric.threshold);
    
    const performanceMetric: PerformanceMetric = {
      id: `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      status,
      ...metric
    };

    this.metrics.push(performanceMetric);

    // Alert on critical performance issues
    if (status === 'critical') {
      console.warn(`Critical performance issue detected: ${metric.name} = ${metric.value}ms (threshold: ${metric.threshold}ms)`);
    }

    // Keep only recent metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-500);
    }
  }

  private determineMetricStatus(value: number, threshold?: number): PerformanceMetric['status'] {
    if (!threshold) return 'good';
    
    if (value <= threshold) return 'good';
    if (value <= threshold * 1.5) return 'warning';
    return 'critical';
  }

  // Measure specific operations
  measureOperation<T>(name: string, operation: () => T): T {
    const startTime = performance.now();
    const result = operation();
    const duration = performance.now() - startTime;
    
    this.recordPerformanceMetric({
      name,
      value: duration,
      category: 'response_time',
      threshold: this.getThresholdForMetric(name)
    });
    
    return result;
  }

  async measureAsyncOperation<T>(name: string, operation: () => Promise<T>): Promise<T> {
    const startTime = performance.now();
    const result = await operation();
    const duration = performance.now() - startTime;
    
    this.recordPerformanceMetric({
      name,
      value: duration,
      category: 'response_time',
      threshold: this.getThresholdForMetric(name)
    });
    
    return result;
  }

  private trackEvent(event: Omit<AnalyticsEvent, 'id' | 'userId' | 'timestamp' | 'sessionId'>) {
    if (!this.isEnabled) return;

    const analyticsEvent: AnalyticsEvent = {
      id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: this.userId || 'anonymous',
      timestamp: new Date(),
      sessionId: this.sessionId,
      ...event
    };

    this.events.push(analyticsEvent);

    // Auto-flush if batch size reached
    if (this.events.length >= this.batchSize) {
      this.flushEvents();
    }
  }

  private async flushEvents() {
    if (this.events.length === 0) return;

    const eventsToFlush = [...this.events];
    this.events = [];

    try {
      // In a real implementation, this would send to analytics service
      console.log('Flushing analytics events:', eventsToFlush);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.error('Failed to flush analytics events:', error);
      // Re-add events back to queue on failure
      this.events.unshift(...eventsToFlush);
    }
  }

  // Get analytics data
  getSessionMetrics(): UserEngagementMetrics {
    const sessionDuration = Date.now() - this.sessionStart.getTime();
    const xpEvents = this.events.filter(e => e.type === 'xp_gained');
    const achievementEvents = this.events.filter(e => e.type === 'achievement_unlocked');
    
    const totalXP = xpEvents.reduce((sum, event) => sum + (event.data.amount || 0), 0);
    const actionsPerMinute = (this.events.length / (sessionDuration / 60000));
    
    const engagementLevel = this.calculateEngagementLevel(actionsPerMinute, totalXP, achievementEvents.length);
    
    return {
      sessionDuration,
      actionsPerMinute,
      xpPerSession: totalXP,
      achievementsUnlocked: achievementEvents.length,
      featuresUsed: [...new Set(this.events.map(e => e.source))],
      retentionScore: this.calculateRetentionScore(),
      engagementLevel
    };
  }

  private calculateEngagementLevel(actionsPerMinute: number, xp: number, achievements: number): UserEngagementMetrics['engagementLevel'] {
    const score = (actionsPerMinute * 0.4) + (xp / 100 * 0.4) + (achievements * 0.2);
    
    if (score >= 8) return 'very_high';
    if (score >= 5) return 'high';
    if (score >= 2) return 'medium';
    return 'low';
  }

  private calculateRetentionScore(): number {
    const sessionDuration = Date.now() - this.sessionStart.getTime();
    const engagementEvents = this.events.filter(e => 
      ['xp_gained', 'achievement_unlocked', 'level_up'].includes(e.type)
    ).length;
    
    // Simple retention score based on session duration and engagement
    const durationScore = Math.min(sessionDuration / 300000, 1); // Max 5 minutes
    const engagementScore = Math.min(engagementEvents / 10, 1); // Max 10 events
    
    return Math.round((durationScore + engagementScore) * 50);
  }

  getPerformanceReport() {
    const recentMetrics = this.metrics.filter(m => 
      Date.now() - m.timestamp.getTime() < 300000 // Last 5 minutes
    );

    const categories = ['response_time', 'render_time', 'animation_fps', 'memory_usage'] as const;
    
    return categories.map(category => {
      const categoryMetrics = recentMetrics.filter(m => m.category === category);
      if (categoryMetrics.length === 0) return null;

      const values = categoryMetrics.map(m => m.value);
      const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
      const max = Math.max(...values);
      const min = Math.min(...values);
      
      const criticalCount = categoryMetrics.filter(m => m.status === 'critical').length;
      const warningCount = categoryMetrics.filter(m => m.status === 'warning').length;
      
      return {
        category,
        average: Math.round(avg * 100) / 100,
        maximum: Math.round(max * 100) / 100,
        minimum: Math.round(min * 100) / 100,
        criticalIssues: criticalCount,
        warnings: warningCount,
        totalSamples: categoryMetrics.length,
        status: criticalCount > 0 ? 'critical' : warningCount > 0 ? 'warning' : 'good'
      };
    }).filter(Boolean);
  }

  // Cleanup
  destroy() {
    this.flushEvents();
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
  }
}

export default GamificationAnalytics;
