import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import '@testing-library/jest-dom';
import { StreakTracker, useStreakManager, defaultStreakMilestones } from '@/components/gamification/StreakTracker';
import { StreakLeaderboard } from '@/components/gamification/StreakLeaderboard';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock XP notifications
jest.mock('@/components/xp/XPNotification', () => ({
  useXPNotifications: () => ({
    triggerStreakXP: jest.fn(),
  }),
}));

const mockStreakData = {
  currentStreak: 5,
  longestStreak: 12,
  lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
  streakGoal: 30,
  todayCompleted: false,
  streakHistory: [
    new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
    new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    new Date(Date.now() - 0 * 24 * 60 * 60 * 1000)
  ],
  milestones: defaultStreakMilestones.map(m => ({
    ...m,
    unlocked: m.days <= 5
  }))
};

describe('StreakTracker', () => {
  const mockOnStreakUpdate = jest.fn();
  const mockOnMilestoneUnlocked = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock Date.now for consistent testing
    jest.spyOn(Date, 'now').mockImplementation(() => 1640995200000); // Fixed timestamp
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders streak tracker with current streak', () => {
    render(
      <StreakTracker
        streakData={mockStreakData}
        onStreakUpdate={mockOnStreakUpdate}
        onMilestoneUnlocked={mockOnMilestoneUnlocked}
      />
    );

    expect(screen.getByText('5')).toBeInTheDocument();
    expect(screen.getByText('days in a row')).toBeInTheDocument();
  });

  it('displays streak goal progress', () => {
    render(
      <StreakTracker
        streakData={mockStreakData}
        onStreakUpdate={mockOnStreakUpdate}
      />
    );

    expect(screen.getByText('30')).toBeInTheDocument(); // Goal
    expect(screen.getByText('Goal')).toBeInTheDocument();
  });

  it('shows longest streak', () => {
    render(
      <StreakTracker
        streakData={mockStreakData}
        onStreakUpdate={mockOnStreakUpdate}
      />
    );

    expect(screen.getByText('12')).toBeInTheDocument();
    expect(screen.getByText('Longest streak')).toBeInTheDocument();
  });

  it('displays next milestone progress', () => {
    render(
      <StreakTracker
        streakData={mockStreakData}
        onStreakUpdate={mockOnStreakUpdate}
      />
    );

    // Should show progress to next milestone (7 days)
    expect(screen.getByText(/2 days to go/)).toBeInTheDocument();
  });

  it('shows streak at risk warning', () => {
    const atRiskStreakData = {
      ...mockStreakData,
      lastActivity: new Date(Date.now() - 22 * 60 * 60 * 1000), // 22 hours ago
      todayCompleted: false
    };

    render(
      <StreakTracker
        streakData={atRiskStreakData}
        onStreakUpdate={mockOnStreakUpdate}
      />
    );

    expect(screen.getByText('Streak at risk!')).toBeInTheDocument();
  });

  it('handles streak recovery', async () => {
    const atRiskStreakData = {
      ...mockStreakData,
      lastActivity: new Date(Date.now() - 22 * 60 * 60 * 1000),
      todayCompleted: false
    };

    render(
      <StreakTracker
        streakData={atRiskStreakData}
        onStreakUpdate={mockOnStreakUpdate}
      />
    );

    const recoveryButton = screen.getByText('Use Streak Recovery');
    fireEvent.click(recoveryButton);

    const confirmButton = screen.getByText('Use Recovery');
    fireEvent.click(confirmButton);

    await waitFor(() => {
      expect(mockOnStreakUpdate).toHaveBeenCalled();
    });
  });

  it('renders compact version correctly', () => {
    render(
      <StreakTracker
        streakData={mockStreakData}
        onStreakUpdate={mockOnStreakUpdate}
        compact={true}
      />
    );

    expect(screen.getByText('5 days')).toBeInTheDocument();
    expect(screen.getByText('streak')).toBeInTheDocument();
  });

  it('shows today completed status', () => {
    const completedStreakData = {
      ...mockStreakData,
      todayCompleted: true
    };

    render(
      <StreakTracker
        streakData={completedStreakData}
        onStreakUpdate={mockOnStreakUpdate}
      />
    );

    expect(screen.getByText('Today completed!')).toBeInTheDocument();
  });

  it('displays time until reset', () => {
    render(
      <StreakTracker
        streakData={mockStreakData}
        onStreakUpdate={mockOnStreakUpdate}
      />
    );

    // Should show remaining time in the day
    expect(screen.getByText(/\d+h \d+m left today/)).toBeInTheDocument();
  });
});

describe('StreakLeaderboard', () => {
  const mockEntries = [
    {
      id: '1',
      username: 'user1',
      currentStreak: 15,
      longestStreak: 20,
      rank: 1,
      isCurrentUser: false,
      streakStartDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
      totalXP: 5000,
      badges: ['streak-master', 'consistent-learner']
    },
    {
      id: '2',
      username: 'user2',
      currentStreak: 10,
      longestStreak: 15,
      rank: 2,
      isCurrentUser: true,
      streakStartDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
      totalXP: 3500,
      badges: ['week-warrior']
    },
    {
      id: '3',
      username: 'user3',
      currentStreak: 8,
      longestStreak: 12,
      rank: 3,
      isCurrentUser: false,
      streakStartDate: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
      totalXP: 2800,
      badges: []
    }
  ];

  const mockOnTimeframeChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders leaderboard entries', () => {
    render(
      <StreakLeaderboard
        entries={mockEntries}
        timeframe="weekly"
        onTimeframeChange={mockOnTimeframeChange}
      />
    );

    expect(screen.getByText('user1')).toBeInTheDocument();
    expect(screen.getByText('user2')).toBeInTheDocument();
    expect(screen.getByText('user3')).toBeInTheDocument();
  });

  it('highlights current user', () => {
    render(
      <StreakLeaderboard
        entries={mockEntries}
        timeframe="weekly"
        onTimeframeChange={mockOnTimeframeChange}
      />
    );

    expect(screen.getByText('You')).toBeInTheDocument();
  });

  it('shows current user rank', () => {
    render(
      <StreakLeaderboard
        entries={mockEntries}
        currentUserRank={2}
        timeframe="weekly"
        onTimeframeChange={mockOnTimeframeChange}
      />
    );

    expect(screen.getByText('#2')).toBeInTheDocument();
    expect(screen.getByText('Your Rank')).toBeInTheDocument();
  });

  it('handles timeframe changes', () => {
    render(
      <StreakLeaderboard
        entries={mockEntries}
        timeframe="weekly"
        onTimeframeChange={mockOnTimeframeChange}
      />
    );

    const monthlyButton = screen.getByText('This Month');
    fireEvent.click(monthlyButton);

    expect(mockOnTimeframeChange).toHaveBeenCalledWith('monthly');
  });

  it('switches between current and longest streaks', () => {
    render(
      <StreakLeaderboard
        entries={mockEntries}
        timeframe="weekly"
        onTimeframeChange={mockOnTimeframeChange}
      />
    );

    const longestStreaksTab = screen.getByText('Longest Streaks');
    fireEvent.click(longestStreaksTab);

    // Should show longest streak values
    expect(screen.getByText('20')).toBeInTheDocument(); // user1's longest streak
  });

  it('renders compact version', () => {
    render(
      <StreakLeaderboard
        entries={mockEntries}
        timeframe="weekly"
        onTimeframeChange={mockOnTimeframeChange}
        compact={true}
      />
    );

    expect(screen.getByText('Streak Leaders')).toBeInTheDocument();
    // Should show only top 5 entries in compact mode
    expect(screen.getByText('user1')).toBeInTheDocument();
  });

  it('displays stats summary', () => {
    render(
      <StreakLeaderboard
        entries={mockEntries}
        timeframe="weekly"
        onTimeframeChange={mockOnTimeframeChange}
      />
    );

    expect(screen.getByText('Top Streak')).toBeInTheDocument();
    expect(screen.getByText('Average')).toBeInTheDocument();
    expect(screen.getByText('Active Users')).toBeInTheDocument();
  });

  it('shows empty state when no entries', () => {
    render(
      <StreakLeaderboard
        entries={[]}
        timeframe="weekly"
        onTimeframeChange={mockOnTimeframeChange}
      />
    );

    expect(screen.getByText('No streaks yet')).toBeInTheDocument();
    expect(screen.getByText('Be the first to start a learning streak!')).toBeInTheDocument();
  });
});

describe('useStreakManager hook', () => {
  const TestComponent = () => {
    const {
      streakData,
      loadStreakData,
      updateStreak,
      breakStreak,
      extendStreak
    } = useStreakManager();

    return (
      <div>
        <div data-testid="current-streak">
          {streakData?.currentStreak || 0}
        </div>
        <button onClick={() => loadStreakData('test-user')}>
          Load Data
        </button>
        <button onClick={() => updateStreak(10)}>
          Update Streak
        </button>
        <button onClick={breakStreak}>
          Break Streak
        </button>
        <button onClick={extendStreak}>
          Extend Streak
        </button>
      </div>
    );
  };

  it('loads streak data', async () => {
    render(<TestComponent />);

    const loadButton = screen.getByText('Load Data');
    fireEvent.click(loadButton);

    await waitFor(() => {
      const streakDisplay = screen.getByTestId('current-streak');
      expect(streakDisplay.textContent).not.toBe('0');
    });
  });

  it('updates streak', async () => {
    render(<TestComponent />);

    // Load data first
    fireEvent.click(screen.getByText('Load Data'));

    await waitFor(() => {
      const updateButton = screen.getByText('Update Streak');
      fireEvent.click(updateButton);
    });

    await waitFor(() => {
      const streakDisplay = screen.getByTestId('current-streak');
      expect(streakDisplay.textContent).toBe('10');
    });
  });

  it('breaks streak', async () => {
    render(<TestComponent />);

    // Load data first
    fireEvent.click(screen.getByText('Load Data'));

    await waitFor(() => {
      const breakButton = screen.getByText('Break Streak');
      fireEvent.click(breakButton);
    });

    await waitFor(() => {
      const streakDisplay = screen.getByTestId('current-streak');
      expect(streakDisplay.textContent).toBe('0');
    });
  });

  it('extends streak', async () => {
    render(<TestComponent />);

    // Load data first
    fireEvent.click(screen.getByText('Load Data'));

    await waitFor(() => {
      const extendButton = screen.getByText('Extend Streak');
      fireEvent.click(extendButton);
    });

    await waitFor(() => {
      const streakDisplay = screen.getByTestId('current-streak');
      expect(parseInt(streakDisplay.textContent || '0')).toBeGreaterThan(0);
    });
  });
});

describe('Performance Tests', () => {
  it('streak tracker renders within performance threshold', () => {
    const startTime = performance.now();

    render(
      <StreakTracker
        streakData={mockStreakData}
        onStreakUpdate={() => {}}
      />
    );

    const renderTime = performance.now() - startTime;

    // Should render within 50ms
    expect(renderTime).toBeLessThan(50);
  });

  it('leaderboard handles large datasets efficiently', () => {
    const largeEntries = Array.from({ length: 1000 }, (_, i) => ({
      id: `user-${i}`,
      username: `user${i}`,
      currentStreak: Math.floor(Math.random() * 100),
      longestStreak: Math.floor(Math.random() * 200),
      rank: i + 1,
      isCurrentUser: i === 0,
      streakStartDate: new Date(),
      totalXP: Math.floor(Math.random() * 10000),
      badges: []
    }));

    const startTime = performance.now();

    render(
      <StreakLeaderboard
        entries={largeEntries}
        timeframe="weekly"
      />
    );

    const renderTime = performance.now() - startTime;

    // Should handle large datasets within 200ms
    expect(renderTime).toBeLessThan(200);
  });
});

describe('Accessibility Tests', () => {
  it('streak tracker has proper ARIA labels', () => {
    render(
      <StreakTracker
        streakData={mockStreakData}
        onStreakUpdate={() => {}}
      />
    );

    // Check for accessible content
    expect(screen.getByText('Learning Streak')).toBeInTheDocument();
    expect(screen.getByText('Keep the momentum going!')).toBeInTheDocument();
  });

  it('leaderboard supports keyboard navigation', () => {
    render(
      <StreakLeaderboard
        entries={mockEntries}
        timeframe="weekly"
      />
    );

    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button.getAttribute('tabIndex')).not.toBe('-1');
    });
  });

  it('provides screen reader friendly content', () => {
    render(
      <StreakTracker
        streakData={mockStreakData}
        onStreakUpdate={() => {}}
      />
    );

    // Should have descriptive text for screen readers
    expect(screen.getByText('days in a row')).toBeInTheDocument();
    expect(screen.getByText('Longest streak')).toBeInTheDocument();
  });
});
