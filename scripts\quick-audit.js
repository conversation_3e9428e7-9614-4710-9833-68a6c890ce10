#!/usr/bin/env node

/**
 * Quick Performance and Accessibility Audit
 * Analyzes existing build and provides immediate insights
 */

const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Analyze bundle sizes from existing build
function analyzeBundleSizes() {
  logHeader('Bundle Size Analysis');
  
  const results = {
    javascript: 0,
    css: 0,
    chunks: [],
    violations: [],
    recommendations: []
  };

  try {
    const staticDir = path.join(process.cwd(), '.next', 'static');
    
    if (!fs.existsSync(staticDir)) {
      logError('No static build directory found');
      return results;
    }

    // Analyze JavaScript chunks
    const chunksDir = path.join(staticDir, 'chunks');
    if (fs.existsSync(chunksDir)) {
      const files = fs.readdirSync(chunksDir);
      
      files.forEach(file => {
        if (file.endsWith('.js')) {
          const filePath = path.join(chunksDir, file);
          const stats = fs.statSync(filePath);
          const sizeKB = Math.round(stats.size / 1024);
          
          results.javascript += sizeKB;
          results.chunks.push({
            name: file,
            size: sizeKB,
            type: 'javascript'
          });
        }
      });
    }

    // Check for CSS files
    const cssDir = path.join(staticDir, 'css');
    if (fs.existsSync(cssDir)) {
      const files = fs.readdirSync(cssDir);
      
      files.forEach(file => {
        if (file.endsWith('.css')) {
          const filePath = path.join(cssDir, file);
          const stats = fs.statSync(filePath);
          const sizeKB = Math.round(stats.size / 1024);
          
          results.css += sizeKB;
          results.chunks.push({
            name: file,
            size: sizeKB,
            type: 'css'
          });
        }
      });
    }

    // Display results
    log(`Total JavaScript: ${results.javascript} KB`, results.javascript > 400 ? 'red' : 'green');
    log(`Total CSS: ${results.css} KB`, results.css > 100 ? 'yellow' : 'green');
    
    // Check against budgets
    if (results.javascript > 400) {
      results.violations.push({
        type: 'javascript',
        actual: results.javascript,
        budget: 400,
        severity: 'high'
      });
      results.recommendations.push('Implement code splitting and remove unused JavaScript');
    }

    if (results.css > 100) {
      results.violations.push({
        type: 'css',
        actual: results.css,
        budget: 100,
        severity: 'medium'
      });
      results.recommendations.push('Optimize CSS and remove unused styles');
    }

    // Show largest chunks
    const largestChunks = results.chunks
      .sort((a, b) => b.size - a.size)
      .slice(0, 5);

    if (largestChunks.length > 0) {
      log('\nLargest Chunks:', 'bright');
      largestChunks.forEach(chunk => {
        log(`  ${chunk.name}: ${chunk.size} KB (${chunk.type})`, 'blue');
      });
    }

  } catch (error) {
    logError(`Bundle analysis failed: ${error.message}`);
  }

  return results;
}

// Analyze dependencies
function analyzeDependencies() {
  logHeader('Dependency Analysis');
  
  const results = {
    production: 0,
    development: 0,
    heavy: [],
    security: [],
    recommendations: []
  };

  try {
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const deps = packageJson.dependencies || {};
    const devDeps = packageJson.devDependencies || {};
    
    results.production = Object.keys(deps).length;
    results.development = Object.keys(devDeps).length;
    
    log(`Production Dependencies: ${results.production}`, 'blue');
    log(`Development Dependencies: ${results.development}`, 'blue');

    // Check for heavy dependencies
    const heavyPackages = {
      'monaco-editor': 'Code editor (heavy)',
      'three': '3D graphics library (heavy)',
      '@google/genai': 'AI integration (heavy)',
      'framer-motion': 'Animation library (heavy)',
      'react-syntax-highlighter': 'Syntax highlighting (heavy)',
      'chart.js': 'Charting library (heavy)',
      'lodash': 'Utility library (consider tree-shaking)'
    };
    
    Object.entries(heavyPackages).forEach(([pkg, description]) => {
      if (deps[pkg]) {
        results.heavy.push({ package: pkg, description });
        logWarning(`Heavy dependency: ${pkg} - ${description}`);
      }
    });

    // Security recommendations
    const securityPackages = {
      'lodash': 'Consider using native methods or lodash-es',
      'moment': 'Consider using date-fns or dayjs',
      'request': 'Deprecated, use axios or fetch'
    };

    Object.entries(securityPackages).forEach(([pkg, recommendation]) => {
      if (deps[pkg]) {
        results.security.push({ package: pkg, recommendation });
        results.recommendations.push(`${pkg}: ${recommendation}`);
      }
    });

    if (results.production > 50) {
      results.recommendations.push('Consider reducing the number of dependencies');
    }

    if (results.heavy.length > 3) {
      results.recommendations.push('Multiple heavy dependencies detected - consider lazy loading');
    }

  } catch (error) {
    logError(`Dependency analysis failed: ${error.message}`);
  }

  return results;
}

// Check accessibility setup
function checkAccessibilitySetup() {
  logHeader('Accessibility Setup Analysis');
  
  const results = {
    toolsInstalled: false,
    ariaUsage: 0,
    altTextUsage: 0,
    semanticElements: 0,
    recommendations: []
  };

  try {
    // Check for accessibility tools
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const allDeps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    if (allDeps['@axe-core/react'] || allDeps['axe-core']) {
      results.toolsInstalled = true;
      logSuccess('Accessibility testing tools found');
    } else {
      logWarning('No accessibility testing tools found');
      results.recommendations.push('Install @axe-core/react for automated accessibility testing');
    }

    // Analyze code for accessibility patterns
    const codeFiles = [];
    const searchDirs = ['app', 'components', 'lib'];
    
    searchDirs.forEach(dir => {
      const dirPath = path.join(process.cwd(), dir);
      if (fs.existsSync(dirPath)) {
        const files = getFilesRecursively(dirPath, ['.tsx', '.jsx']);
        codeFiles.push(...files);
      }
    });

    let totalAriaUsage = 0;
    let totalAltUsage = 0;
    let totalSemanticElements = 0;

    codeFiles.forEach(file => {
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // Count ARIA attributes
        const ariaMatches = content.match(/aria-\w+/g);
        if (ariaMatches) totalAriaUsage += ariaMatches.length;
        
        // Count alt attributes
        const altMatches = content.match(/alt\s*=/g);
        if (altMatches) totalAltUsage += altMatches.length;
        
        // Count semantic HTML elements
        const semanticMatches = content.match(/<(main|nav|header|footer|section|article|aside)\b/g);
        if (semanticMatches) totalSemanticElements += semanticMatches.length;
        
      } catch (error) {
        // Skip files that can't be read
      }
    });

    results.ariaUsage = totalAriaUsage;
    results.altTextUsage = totalAltUsage;
    results.semanticElements = totalSemanticElements;

    log(`ARIA attributes found: ${totalAriaUsage}`, totalAriaUsage > 10 ? 'green' : 'yellow');
    log(`Alt text attributes: ${totalAltUsage}`, totalAltUsage > 5 ? 'green' : 'yellow');
    log(`Semantic HTML elements: ${totalSemanticElements}`, totalSemanticElements > 10 ? 'green' : 'yellow');

    // Generate recommendations
    if (totalAriaUsage < 10) {
      results.recommendations.push('Add more ARIA labels and roles for better screen reader support');
    }

    if (totalAltUsage < 5) {
      results.recommendations.push('Add alt text to all images for accessibility');
    }

    if (totalSemanticElements < 10) {
      results.recommendations.push('Use more semantic HTML elements (main, nav, header, footer, etc.)');
    }

  } catch (error) {
    logError(`Accessibility analysis failed: ${error.message}`);
  }

  return results;
}

// Helper function to get files recursively
function getFilesRecursively(dir, extensions) {
  const files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        files.push(...getFilesRecursively(fullPath, extensions));
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  } catch (error) {
    // Skip directories that can't be read
  }
  
  return files;
}

// Generate final report
function generateReport(bundleResults, depResults, a11yResults) {
  logHeader('Quick Audit Summary');
  
  const allRecommendations = [
    ...bundleResults.recommendations,
    ...depResults.recommendations,
    ...a11yResults.recommendations
  ];

  let score = 100;
  
  // Deduct points for violations
  bundleResults.violations.forEach(violation => {
    if (violation.severity === 'high') score -= 20;
    else if (violation.severity === 'medium') score -= 10;
    else score -= 5;
  });

  // Deduct points for missing accessibility tools
  if (!a11yResults.toolsInstalled) score -= 15;
  
  // Deduct points for heavy dependencies
  if (depResults.heavy.length > 3) score -= 10;

  score = Math.max(0, score);

  log(`\nOverall Score: ${score}/100`, score >= 80 ? 'green' : score >= 60 ? 'yellow' : 'red');
  log(`Total Issues Found: ${allRecommendations.length}`, allRecommendations.length === 0 ? 'green' : 'yellow');

  if (allRecommendations.length > 0) {
    log('\n🔧 Key Recommendations:', 'bright');
    allRecommendations.slice(0, 5).forEach((rec, index) => {
      log(`${index + 1}. ${rec}`, 'cyan');
    });
  }

  // Save report
  const report = {
    timestamp: new Date().toISOString(),
    score,
    bundleAnalysis: bundleResults,
    dependencyAnalysis: depResults,
    accessibilityAnalysis: a11yResults,
    recommendations: allRecommendations
  };

  try {
    const reportDir = path.join(process.cwd(), 'reports');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(reportDir, `quick-audit-${timestamp}.json`);
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    logInfo(`Report saved to: ${reportPath}`);
  } catch (error) {
    logError(`Failed to save report: ${error.message}`);
  }

  return { score, recommendations: allRecommendations };
}

// Main execution
function main() {
  logHeader('Solidity Learning Platform - Quick Audit');
  
  try {
    const bundleResults = analyzeBundleSizes();
    const depResults = analyzeDependencies();
    const a11yResults = checkAccessibilitySetup();
    
    const summary = generateReport(bundleResults, depResults, a11yResults);
    
    const success = summary.score >= 70;
    
    log('\n' + '='.repeat(60), 'cyan');
    if (success) {
      logSuccess('🎉 Quick audit completed successfully!');
    } else {
      logWarning('⚠️  Issues found. See recommendations above.');
    }
    log('='.repeat(60), 'cyan');
    
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    logError(`Quick audit failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { analyzeBundleSizes, analyzeDependencies, checkAccessibilitySetup };
