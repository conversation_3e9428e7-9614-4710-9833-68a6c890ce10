'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// Import all engagement components
import { FloatingNotifications } from './FloatingNotifications';
import { AnimatedActivityIndicator, CompactActivityIndicator } from './AnimatedActivityIndicator';
import { AIAssistantWidget } from './AIAssistantWidget';
import { EnhancedCounterAnimations } from './EnhancedCounterAnimations';
import { UrgencyElements, CountdownTimer, SpotsRemainingIndicator } from './UrgencyElements';
import { InteractiveFeaturesGrid } from './InteractiveElements';

interface HeroEngagementSuiteProps {
  className?: string;
  enableAllFeatures?: boolean;
  enableSocialProof?: boolean;
  enableAIAssistant?: boolean;
  enableCounters?: boolean;
  enableUrgency?: boolean;
  enableInteractiveCards?: boolean;
  enableGamification?: boolean;
  enableAccessibility?: boolean;
  variant?: 'full' | 'minimal' | 'focused';
}

export function HeroEngagementSuite({
  className,
  enableAllFeatures = true,
  enableSocialProof = true,
  enableAIAssistant = true,
  enableCounters = true,
  enableUrgency = true,
  enableInteractiveCards = true,
  enableGamification = true,
  enableAccessibility = true,
  variant = 'full'
}: HeroEngagementSuiteProps) {
  const [userEngagement, setUserEngagement] = useState({
    timeOnPage: 0,
    interactionCount: 0,
    scrollDepth: 0,
    hasInteracted: false
  });

  // Track user engagement metrics
  useEffect(() => {
    const startTime = Date.now();
    let interactionCount = 0;

    const trackInteraction = () => {
      interactionCount++;
      setUserEngagement(prev => ({
        ...prev,
        interactionCount,
        hasInteracted: true
      }));
    };

    const trackScroll = () => {
      const scrollDepth = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );
      setUserEngagement(prev => ({
        ...prev,
        scrollDepth: Math.max(prev.scrollDepth, scrollDepth)
      }));
    };

    const trackTime = () => {
      const timeOnPage = Math.round((Date.now() - startTime) / 1000);
      setUserEngagement(prev => ({
        ...prev,
        timeOnPage
      }));
    };

    // Event listeners
    document.addEventListener('click', trackInteraction);
    document.addEventListener('keydown', trackInteraction);
    document.addEventListener('scroll', trackScroll);
    
    // Time tracking interval
    const timeInterval = setInterval(trackTime, 1000);

    return () => {
      document.removeEventListener('click', trackInteraction);
      document.removeEventListener('keydown', trackInteraction);
      document.removeEventListener('scroll', trackScroll);
      clearInterval(timeInterval);
    };
  }, []);

  // Analytics tracking for success metrics
  useEffect(() => {
    // Track engagement milestones
    if (userEngagement.timeOnPage >= 5 && userEngagement.interactionCount >= 1) {
      // Success metric: User engaged for 5+ seconds with interaction
      console.log('Engagement milestone: 5s + interaction achieved');
    }
    
    if (userEngagement.scrollDepth >= 25) {
      // Success metric: User scrolled 25% of page
      console.log('Engagement milestone: 25% scroll depth achieved');
    }
  }, [userEngagement]);

  const getVariantConfig = () => {
    switch (variant) {
      case 'minimal':
        return {
          showFloatingNotifications: false,
          showActivityIndicator: true,
          showCounters: false,
          showUrgency: false,
          showInteractiveCards: true,
          compactLayout: true
        };
      case 'focused':
        return {
          showFloatingNotifications: true,
          showActivityIndicator: false,
          showCounters: true,
          showUrgency: true,
          showInteractiveCards: false,
          compactLayout: false
        };
      default: // full
        return {
          showFloatingNotifications: true,
          showActivityIndicator: true,
          showCounters: true,
          showUrgency: true,
          showInteractiveCards: true,
          compactLayout: false
        };
    }
  };

  const config = getVariantConfig();

  return (
    <div className={cn('relative', className)}>
      {/* Social Proof & Activity Notifications */}
      {enableAllFeatures && enableSocialProof && config.showFloatingNotifications && (
        <FloatingNotifications
          className="fixed top-4 right-4 z-40"
          maxVisible={3}
          enableSound={false}
          respectReducedMotion={enableAccessibility}
        />
      )}

      {/* Compact Activity Indicator for minimal layouts */}
      {enableAllFeatures && enableSocialProof && config.showActivityIndicator && (
        <div className="fixed top-4 left-4 z-40">
          <CompactActivityIndicator />
        </div>
      )}

      {/* AI Assistant Widget */}
      {enableAllFeatures && enableAIAssistant && (
        <AIAssistantWidget
          position="bottom-right"
          enableContextualHelp={true}
          enableGamification={enableGamification}
          enableSound={false}
          respectReducedMotion={enableAccessibility}
        />
      )}

      {/* Animated Statistics & Counters */}
      {enableAllFeatures && enableCounters && config.showCounters && (
        <motion.section
          className="py-16"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
        >
          <EnhancedCounterAnimations
            variant={config.compactLayout ? 'compact' : 'default'}
            enableScrollTrigger={true}
            showProgressBars={true}
            enableAccessibility={enableAccessibility}
            enableGamification={enableGamification}
          />
        </motion.section>
      )}

      {/* Urgency & Scarcity Elements */}
      {enableAllFeatures && enableUrgency && config.showUrgency && (
        <div className="relative">
          <UrgencyElements
            variant="banner"
            position="top"
            enableEthicalMarketing={true}
            autoRotate={true}
            rotationInterval={30000}
          />
        </div>
      )}

      {/* Interactive Feature Cards */}
      {enableAllFeatures && enableInteractiveCards && config.showInteractiveCards && (
        <motion.section
          className="py-16"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 0.8 }}
        >
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
              Interactive Learning Experience
            </h2>
            <p className="text-gray-300 text-lg max-w-2xl mx-auto">
              Explore our comprehensive platform features designed to accelerate your Solidity learning journey
            </p>
          </div>
          
          <InteractiveFeaturesGrid className="max-w-7xl mx-auto px-4" />
        </motion.section>
      )}

      {/* Engagement Analytics Overlay (Development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-50 bg-black/80 text-white p-4 rounded-lg text-xs font-mono">
          <div>Time: {userEngagement.timeOnPage}s</div>
          <div>Interactions: {userEngagement.interactionCount}</div>
          <div>Scroll: {userEngagement.scrollDepth}%</div>
          <div>Engaged: {userEngagement.hasInteracted ? 'Yes' : 'No'}</div>
        </div>
      )}

      {/* Success Metrics Tracking */}
      <script
        dangerouslySetInnerHTML={{
          __html: `
            // Track success metrics
            window.heroEngagementMetrics = {
              timeOnPage: ${userEngagement.timeOnPage},
              interactionCount: ${userEngagement.interactionCount},
              scrollDepth: ${userEngagement.scrollDepth},
              hasInteracted: ${userEngagement.hasInteracted}
            };
          `
        }}
      />
    </div>
  );
}

// Hook for accessing engagement metrics
export function useHeroEngagementMetrics() {
  const [metrics, setMetrics] = useState({
    timeOnPage: 0,
    interactionCount: 0,
    scrollDepth: 0,
    hasInteracted: false,
    conversionGoals: {
      fiveSecondEngagement: false,
      demoCompletion: false,
      bounceReduction: false,
      featureInteraction: false
    }
  });

  useEffect(() => {
    const checkMetrics = () => {
      if (typeof window !== 'undefined' && window.heroEngagementMetrics) {
        const current = window.heroEngagementMetrics;
        setMetrics(prev => ({
          ...current,
          conversionGoals: {
            fiveSecondEngagement: current.timeOnPage >= 5 && current.hasInteracted,
            demoCompletion: current.interactionCount >= 3,
            bounceReduction: current.scrollDepth >= 25,
            featureInteraction: current.interactionCount >= 1
          }
        }));
      }
    };

    const interval = setInterval(checkMetrics, 1000);
    return () => clearInterval(interval);
  }, []);

  return metrics;
}

// Export individual components for granular usage
export {
  FloatingNotifications,
  AnimatedActivityIndicator,
  AIAssistantWidget,
  EnhancedCounterAnimations,
  UrgencyElements,
  CountdownTimer,
  SpotsRemainingIndicator,
  InteractiveFeaturesGrid
};
