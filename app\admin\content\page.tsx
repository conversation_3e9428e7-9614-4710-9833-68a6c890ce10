'use client';

import React, { Suspense, lazy } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { AdminGuard } from '@/components/admin/AdminGuard';
import { ADMIN_PERMISSIONS } from '@/lib/admin/auth';

// Dynamically import ContentManagement for better code splitting
const ContentManagement = lazy(() => import('@/components/admin/ContentManagement').then(mod => ({ default: mod.ContentManagement })));

export default function AdminContentPage() {
  return (
    <AdminGuard requiredPermission={ADMIN_PERMISSIONS.CONTENT_READ}>
      <AdminLayout currentPage="content">
        <Suspense fallback={
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-400">Loading Content Management...</p>
            </div>
          </div>
        }>
          <ContentManagement />
        </Suspense>
      </AdminLayout>
    </AdminGuard>
  );
}
