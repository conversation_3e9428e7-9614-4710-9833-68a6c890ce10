#!/usr/bin/env node

/**
 * Comprehensive Codebase Cleanup Script
 * Removes debug statements, commented code, and optimizes imports
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  // Directories to process
  directories: [
    'components',
    'app',
    'lib',
    'hooks',
    'types',
    'utils',
    'pages'
  ],
  
  // File extensions to process
  extensions: ['.ts', '.tsx', '.js', '.jsx'],
  
  // Patterns to remove
  removePatterns: {
    // Debug statements
    debugStatements: [
      /console\.(log|debug|info|warn|error)\([^)]*\);?\s*\n?/g,
      /debugger;?\s*\n?/g,
      /\/\/ DEBUG:.*\n?/g,
      /\/\* DEBUG:.*?\*\/\s*\n?/gs
    ],
    
    // Commented code blocks
    commentedCode: [
      /\/\*[\s\S]*?TODO:[\s\S]*?\*\/\s*\n?/g,
      /\/\*[\s\S]*?FIXME:[\s\S]*?\*\/\s*\n?/g,
      /\/\*[\s\S]*?HACK:[\s\S]*?\*\/\s*\n?/g,
      /\/\/ TODO:.*\n?/g,
      /\/\/ FIXME:.*\n?/g,
      /\/\/ HACK:.*\n?/g,
      /\/\/ OLD:.*\n?/g,
      /\/\/ DEPRECATED:.*\n?/g
    ],
    
    // Unreachable code (simplified detection)
    unreachableCode: [
      /return[^;]*;[\s]*[^}]/g,
      /throw[^;]*;[\s]*[^}]/g
    ],
    
    // Unused imports (basic detection)
    unusedImports: [
      /import\s+{\s*[^}]*\s*}\s+from\s+['"][^'"]*['"];\s*\n?/g
    ],
    
    // Empty lines (more than 2 consecutive)
    excessiveEmptyLines: /\n\s*\n\s*\n+/g,
    
    // Trailing whitespace
    trailingWhitespace: /[ \t]+$/gm
  },
  
  // Files to skip
  skipFiles: [
    'node_modules',
    '.git',
    '.next',
    'dist',
    'build',
    '.env',
    'package-lock.json',
    'yarn.lock'
  ],
  
  // Backup directory
  backupDir: 'cleanup-backup',
  
  // Dry run mode
  dryRun: process.argv.includes('--dry-run'),
  
  // Verbose output
  verbose: process.argv.includes('--verbose')
};

class CodebaseCleanup {
  constructor() {
    this.stats = {
      filesProcessed: 0,
      debugStatementsRemoved: 0,
      commentedCodeRemoved: 0,
      unreachableCodeRemoved: 0,
      unusedImportsRemoved: 0,
      bytesReduced: 0,
      errors: []
    };
  }

  log(message, level = 'info') {
    if (CONFIG.verbose || level === 'error') {
      const timestamp = new Date().toISOString();
      console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
    }
  }

  createBackup() {
    if (CONFIG.dryRun) {
      this.log('Dry run mode - skipping backup creation');
      return;
    }

    this.log('Creating backup...');
    
    if (!fs.existsSync(CONFIG.backupDir)) {
      fs.mkdirSync(CONFIG.backupDir, { recursive: true });
    }

    try {
      execSync(`cp -r ${CONFIG.directories.join(' ')} ${CONFIG.backupDir}/`, { stdio: 'inherit' });
      this.log('Backup created successfully');
    } catch (error) {
      this.log(`Backup creation failed: ${error.message}`, 'error');
      throw error;
    }
  }

  shouldProcessFile(filePath) {
    // Check if file should be skipped
    if (CONFIG.skipFiles.some(skip => filePath.includes(skip))) {
      return false;
    }

    // Check file extension
    const ext = path.extname(filePath);
    return CONFIG.extensions.includes(ext);
  }

  processFile(filePath) {
    try {
      const originalContent = fs.readFileSync(filePath, 'utf8');
      let content = originalContent;
      const originalSize = Buffer.byteLength(content, 'utf8');

      // Track changes for this file
      let changes = {
        debugStatements: 0,
        commentedCode: 0,
        unreachableCode: 0,
        unusedImports: 0
      };

      // Remove debug statements
      CONFIG.removePatterns.debugStatements.forEach(pattern => {
        const matches = content.match(pattern) || [];
        changes.debugStatements += matches.length;
        content = content.replace(pattern, '');
      });

      // Remove commented code
      CONFIG.removePatterns.commentedCode.forEach(pattern => {
        const matches = content.match(pattern) || [];
        changes.commentedCode += matches.length;
        content = content.replace(pattern, '');
      });

      // Remove unreachable code (basic detection)
      CONFIG.removePatterns.unreachableCode.forEach(pattern => {
        const matches = content.match(pattern) || [];
        changes.unreachableCode += matches.length;
        // More conservative replacement for unreachable code
        content = content.replace(pattern, (match) => {
          const returnPart = match.split(';')[0] + ';';
          return returnPart;
        });
      });

      // Clean up excessive empty lines
      content = content.replace(CONFIG.removePatterns.excessiveEmptyLines, '\n\n');

      // Remove trailing whitespace
      content = content.replace(CONFIG.removePatterns.trailingWhitespace, '');

      // Ensure file ends with single newline
      content = content.replace(/\n*$/, '\n');

      // Calculate size reduction
      const newSize = Buffer.byteLength(content, 'utf8');
      const sizeReduction = originalSize - newSize;

      // Update stats
      this.stats.debugStatementsRemoved += changes.debugStatements;
      this.stats.commentedCodeRemoved += changes.commentedCode;
      this.stats.unreachableCodeRemoved += changes.unreachableCode;
      this.stats.unusedImportsRemoved += changes.unusedImports;
      this.stats.bytesReduced += sizeReduction;

      // Write file if changes were made and not in dry run mode
      if (content !== originalContent) {
        if (!CONFIG.dryRun) {
          fs.writeFileSync(filePath, content, 'utf8');
        }

        this.log(`Processed ${filePath}: ${sizeReduction} bytes reduced`, 'info');
        
        if (CONFIG.verbose) {
          this.log(`  - Debug statements: ${changes.debugStatements}`);
          this.log(`  - Commented code: ${changes.commentedCode}`);
          this.log(`  - Unreachable code: ${changes.unreachableCode}`);
        }
      }

      this.stats.filesProcessed++;

    } catch (error) {
      this.stats.errors.push({ file: filePath, error: error.message });
      this.log(`Error processing ${filePath}: ${error.message}`, 'error');
    }
  }

  processDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      this.log(`Directory ${dirPath} does not exist, skipping`);
      return;
    }

    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        this.processDirectory(itemPath);
      } else if (stat.isFile() && this.shouldProcessFile(itemPath)) {
        this.processFile(itemPath);
      }
    }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      mode: CONFIG.dryRun ? 'dry-run' : 'actual',
      summary: {
        filesProcessed: this.stats.filesProcessed,
        totalBytesReduced: this.stats.bytesReduced,
        totalKBReduced: Math.round(this.stats.bytesReduced / 1024 * 100) / 100
      },
      details: {
        debugStatementsRemoved: this.stats.debugStatementsRemoved,
        commentedCodeRemoved: this.stats.commentedCodeRemoved,
        unreachableCodeRemoved: this.stats.unreachableCodeRemoved,
        unusedImportsRemoved: this.stats.unusedImportsRemoved
      },
      errors: this.stats.errors
    };

    // Write report to file
    const reportPath = `cleanup-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    return report;
  }

  run() {
    this.log('Starting codebase cleanup...');
    this.log(`Mode: ${CONFIG.dryRun ? 'DRY RUN' : 'ACTUAL CLEANUP'}`);

    // Create backup if not in dry run mode
    if (!CONFIG.dryRun) {
      this.createBackup();
    }

    // Process each directory
    for (const dir of CONFIG.directories) {
      this.log(`Processing directory: ${dir}`);
      this.processDirectory(dir);
    }

    // Generate and display report
    const report = this.generateReport();
    
    console.log('\n=== CLEANUP REPORT ===');
    console.log(`Files processed: ${report.summary.filesProcessed}`);
    console.log(`Total size reduction: ${report.summary.totalKBReduced} KB`);
    console.log(`Debug statements removed: ${report.details.debugStatementsRemoved}`);
    console.log(`Commented code blocks removed: ${report.details.commentedCodeRemoved}`);
    console.log(`Unreachable code instances removed: ${report.details.unreachableCodeRemoved}`);
    
    if (report.errors.length > 0) {
      console.log(`\nErrors encountered: ${report.errors.length}`);
      report.errors.forEach(error => {
        console.log(`  - ${error.file}: ${error.error}`);
      });
    }

    if (CONFIG.dryRun) {
      console.log('\nThis was a dry run. No files were modified.');
      console.log('Run without --dry-run to apply changes.');
    } else {
      console.log(`\nBackup created in: ${CONFIG.backupDir}`);
      console.log('Cleanup completed successfully!');
    }

    this.log('Cleanup process finished');
  }
}

// Run the cleanup
if (require.main === module) {
  const cleanup = new CodebaseCleanup();
  cleanup.run();
}

module.exports = CodebaseCleanup;
