/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EnhancedCTAButtons } from '../EnhancedCTAButtons';

// Mock the notification system
const mockShowSuccess = jest.fn();
const mockShowInfo = jest.fn();

jest.mock('@/components/ui/NotificationSystem', () => ({
  useNotifications: () => ({
    showSuccess: mockShowSuccess,
    showInfo: mockShowInfo,
  }),
}));

// Mock Next.js Link
jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: any) {
    return (
      <a href={href} {...props}>
        {children}
      </a>
    );
  };
});

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

describe('EnhancedCTAButtons', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Horizontal Variant (Default)', () => {
    it('renders primary and secondary CTA buttons', () => {
      render(<EnhancedCTAButtons />);
      
      expect(screen.getByText('Get Started in 30 Seconds')).toBeInTheDocument();
      expect(screen.getByText('Try Interactive Editor')).toBeInTheDocument();
    });

    it('shows urgency messaging when enabled', () => {
      render(<EnhancedCTAButtons showUrgency={true} />);
      
      expect(screen.getByText('Free access • No credit card required')).toBeInTheDocument();
    });

    it('hides urgency messaging when disabled', () => {
      render(<EnhancedCTAButtons showUrgency={false} />);
      
      expect(screen.queryByText('Free access • No credit card required')).not.toBeInTheDocument();
    });

    it('has proper accessibility attributes', () => {
      render(<EnhancedCTAButtons />);
      
      const group = screen.getByRole('group', { name: 'Primary actions' });
      expect(group).toBeInTheDocument();
      
      const primaryButton = screen.getByLabelText(/Start learning Solidity for free/);
      expect(primaryButton).toHaveAttribute('aria-label');
      expect(primaryButton).toHaveAttribute('min-h-[48px]');
    });
  });

  describe('Stacked Variant', () => {
    it('renders buttons in stacked layout', () => {
      render(<EnhancedCTAButtons variant="stacked" />);
      
      const buttons = screen.getAllByRole('link');
      expect(buttons).toHaveLength(2);
      
      // Check for stacked-specific urgency message
      expect(screen.getByText('Get started in 30 seconds')).toBeInTheDocument();
    });

    it('applies full width styling to buttons', () => {
      render(<EnhancedCTAButtons variant="stacked" />);
      
      const primaryButton = screen.getByText('Get Started in 30 Seconds').closest('a');
      expect(primaryButton).toHaveClass('w-full', 'justify-center');
    });
  });

  describe('Minimal Variant', () => {
    it('renders compact button layout', () => {
      render(<EnhancedCTAButtons variant="minimal" />);
      
      expect(screen.getByText('Start Learning')).toBeInTheDocument();
      expect(screen.getByText('Demo')).toBeInTheDocument();
    });

    it('uses smaller button sizes', () => {
      render(<EnhancedCTAButtons variant="minimal" />);
      
      const buttons = screen.getAllByRole('link');
      buttons.forEach(button => {
        expect(button).toHaveClass('min-h-[44px]');
      });
    });
  });

  describe('Button Interactions', () => {
    it('handles primary button click with loading state', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      
      render(<EnhancedCTAButtons />);
      
      const primaryButton = screen.getByText('Get Started in 30 Seconds');
      
      await user.click(primaryButton);
      
      // Should show loading state
      expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument();
      
      // Fast-forward through the async operation
      jest.advanceTimersByTime(1500);
      
      await waitFor(() => {
        expect(mockShowSuccess).toHaveBeenCalledWith({
          title: 'Welcome to SolanaLearn!',
          description: 'Your learning journey begins now. Redirecting to your dashboard...',
          duration: 3000
        });
      });
    });

    it('handles secondary button click with loading state', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      
      render(<EnhancedCTAButtons />);
      
      const secondaryButton = screen.getByText('Try Interactive Editor');
      
      await user.click(secondaryButton);
      
      // Should show loading state
      expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument();
      
      jest.advanceTimersByTime(800);
      
      await waitFor(() => {
        expect(mockShowInfo).toHaveBeenCalledWith({
          title: 'Demo Loading',
          description: 'Preparing interactive demo experience...',
          duration: 2000
        });
      });
    });

    it('prevents multiple clicks during loading', async () => {
      const user = userEvent.setup();
      
      render(<EnhancedCTAButtons />);
      
      const primaryButton = screen.getByText('Get Started in 30 Seconds');
      
      // Click multiple times rapidly
      await user.click(primaryButton);
      await user.click(primaryButton);
      await user.click(primaryButton);
      
      // Should only trigger once
      expect(mockShowSuccess).toHaveBeenCalledTimes(0); // Not called yet due to delay
    });

    it('shows hover effects on button interaction', async () => {
      const user = userEvent.setup();
      
      render(<EnhancedCTAButtons />);
      
      const primaryButton = screen.getByText('Get Started in 30 Seconds');
      
      await user.hover(primaryButton);
      
      // Button should have hover styling applied
      expect(primaryButton.closest('a')).toHaveClass('hover:from-blue-700');
    });
  });

  describe('Accessibility Compliance', () => {
    it('meets minimum touch target requirements', () => {
      render(<EnhancedCTAButtons />);
      
      const buttons = screen.getAllByRole('link');
      buttons.forEach(button => {
        // Check for minimum 44px height (WCAG AA compliance)
        expect(button).toHaveClass('min-h-[48px]');
      });
    });

    it('provides proper ARIA labels', () => {
      render(<EnhancedCTAButtons />);
      
      const primaryButton = screen.getByLabelText(/Start learning Solidity for free/);
      const secondaryButton = screen.getByLabelText(/Watch platform demo/);
      
      expect(primaryButton).toBeInTheDocument();
      expect(secondaryButton).toBeInTheDocument();
    });

    it('handles keyboard navigation correctly', async () => {
      const user = userEvent.setup();
      
      render(<EnhancedCTAButtons />);
      
      // Tab through buttons
      await user.tab();
      expect(screen.getByText('Get Started in 30 Seconds').closest('a')).toHaveFocus();
      
      await user.tab();
      expect(screen.getByText('Try Interactive Editor').closest('a')).toHaveFocus();
    });

    it('supports Enter key activation', async () => {
      const user = userEvent.setup();
      
      render(<EnhancedCTAButtons />);
      
      const primaryButton = screen.getByText('Get Started in 30 Seconds').closest('a');
      
      if (primaryButton) {
        primaryButton.focus();
        await user.keyboard('{Enter}');
        
        // Should trigger the click handler
        expect(screen.getByRole('status', { hidden: true })).toBeInTheDocument();
      }
    });

    it('provides loading state announcements', async () => {
      const user = userEvent.setup();
      
      render(<EnhancedCTAButtons />);
      
      const primaryButton = screen.getByText('Get Started in 30 Seconds');
      
      await user.click(primaryButton);
      
      // Should have aria-disabled during loading
      expect(primaryButton.closest('a')).toHaveAttribute('aria-disabled', 'true');
    });
  });

  describe('Performance and Error Handling', () => {
    it('handles async errors gracefully', async () => {
      // Mock console.error to avoid test output noise
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Mock a failing async operation
      const originalPromise = global.Promise;
      global.Promise = class extends originalPromise {
        constructor(executor: any) {
          super((resolve, reject) => {
            executor(reject, reject); // Force rejection
          });
        }
      } as any;
      
      const user = userEvent.setup();
      render(<EnhancedCTAButtons />);
      
      const primaryButton = screen.getByText('Get Started in 30 Seconds');
      await user.click(primaryButton);
      
      // Should handle error without crashing
      expect(primaryButton).toBeInTheDocument();
      
      // Restore
      global.Promise = originalPromise;
      consoleSpy.mockRestore();
    });

    it('cleans up timers on unmount', () => {
      const { unmount } = render(<EnhancedCTAButtons />);
      
      // Start an async operation
      const primaryButton = screen.getByText('Get Started in 30 Seconds');
      fireEvent.click(primaryButton);
      
      // Unmount component
      unmount();
      
      // Should not cause memory leaks or errors
      jest.advanceTimersByTime(5000);
    });
  });

  describe('Custom Styling', () => {
    it('applies custom className', () => {
      const { container } = render(<EnhancedCTAButtons className="custom-cta-class" />);
      
      expect(container.firstChild).toHaveClass('custom-cta-class');
    });

    it('maintains glassmorphism styling', () => {
      render(<EnhancedCTAButtons />);
      
      const secondaryButton = screen.getByText('Try Interactive Editor').closest('a');
      expect(secondaryButton).toHaveClass('glass', 'backdrop-blur-md');
    });

    it('applies proper gradient styling to primary button', () => {
      render(<EnhancedCTAButtons />);
      
      const primaryButton = screen.getByText('Get Started in 30 Seconds').closest('a');
      expect(primaryButton).toHaveClass('bg-gradient-to-r', 'from-blue-600', 'to-purple-600');
    });
  });
});
