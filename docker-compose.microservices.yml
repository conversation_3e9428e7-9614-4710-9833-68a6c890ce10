version: '3.8'

services:
  # =============================================================================
  # INFRASTRUCTURE SERVICES
  # =============================================================================
  
  # Primary Database
  postgres:
    image: postgres:15-alpine
    container_name: solidity-postgres
    environment:
      POSTGRES_DB: solidity_learning
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-dev_password_123}
      POSTGRES_MULTIPLE_DATABASES: auth_db,user_db,content_db,learning_db,gamification_db,analytics_db,notification_db,blockchain_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./infrastructure/postgres/init-multiple-databases.sh:/docker-entrypoint-initdb.d/init-multiple-databases.sh
    ports:
      - "5432:5432"
    networks:
      - solidity-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache & Session Store
  redis:
    image: redis:7-alpine
    container_name: solidity-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-dev_redis_123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - solidity-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB for Content Storage
  mongodb:
    image: mongo:6-jammy
    container_name: solidity-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: ${MONGO_USERNAME:-admin}
      MONGO_INITDB_ROOT_PASSWORD: ${MONGO_PASSWORD:-dev_mongo_123}
      MONGO_INITDB_DATABASE: content_db
    volumes:
      - mongodb_data:/data/db
    ports:
      - "27017:27017"
    networks:
      - solidity-network

  # Apache Kafka for Event Streaming
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: solidity-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - solidity-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: solidity-kafka
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    ports:
      - "29092:29092"
    networks:
      - solidity-network

  # ClickHouse for Analytics
  clickhouse:
    image: clickhouse/clickhouse-server:23.8-alpine
    container_name: solidity-clickhouse
    environment:
      CLICKHOUSE_DB: analytics_db
      CLICKHOUSE_USER: ${CLICKHOUSE_USER:-analytics}
      CLICKHOUSE_PASSWORD: ${CLICKHOUSE_PASSWORD:-dev_clickhouse_123}
    volumes:
      - clickhouse_data:/var/lib/clickhouse
    ports:
      - "8123:8123"
      - "9000:9000"
    networks:
      - solidity-network

  # Elasticsearch for Search
  elasticsearch:
    image: elasticsearch:8.8.0
    container_name: solidity-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - solidity-network

  # =============================================================================
  # CORE MICROSERVICES
  # =============================================================================

  # API Gateway
  api-gateway:
    build:
      context: ./services/api-gateway
      dockerfile: Dockerfile
    container_name: solidity-api-gateway
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=8080
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379
      - JWT_SECRET=${JWT_SECRET:-dev_jwt_secret_key_123}
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
    ports:
      - "8080:8080"
    depends_on:
      - redis
    networks:
      - solidity-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Authentication Service
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    container_name: solidity-auth-service
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3001
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-dev_password_123}@postgres:5432/auth_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379
      - JWT_SECRET=${JWT_SECRET:-dev_jwt_secret_key_123}
      - JWT_EXPIRES_IN=24h
      - BCRYPT_ROUNDS=12
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
    depends_on:
      - postgres
      - redis
    networks:
      - solidity-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # User Management Service
  user-service:
    build:
      context: ./services/user-service
      dockerfile: Dockerfile
    container_name: solidity-user-service
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3002
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-dev_password_123}@postgres:5432/user_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379
      - AUTH_SERVICE_URL=http://auth-service:3001
    depends_on:
      - postgres
      - redis
      - auth-service
    networks:
      - solidity-network

  # Content Management Service
  content-service:
    build:
      context: ./services/content-service
      dockerfile: Dockerfile
    container_name: solidity-content-service
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3003
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-dev_password_123}@postgres:5432/content_db
      - MONGODB_URL=mongodb://${MONGO_USERNAME:-admin}:${MONGO_PASSWORD:-dev_mongo_123}@mongodb:27017/content_db?authSource=admin
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - S3_BUCKET=${S3_BUCKET:-solidity-learning-content}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION:-us-east-1}
    depends_on:
      - postgres
      - mongodb
      - elasticsearch
    networks:
      - solidity-network

  # Learning Engine Service
  learning-service:
    build:
      context: ./services/learning-service
      dockerfile: Dockerfile
    container_name: solidity-learning-service
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3004
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-dev_password_123}@postgres:5432/learning_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379
      - KAFKA_BROKERS=kafka:9092
      - USER_SERVICE_URL=http://user-service:3002
      - CONTENT_SERVICE_URL=http://content-service:3003
    depends_on:
      - postgres
      - redis
      - kafka
      - user-service
      - content-service
    networks:
      - solidity-network

  # Analytics Service
  analytics-service:
    build:
      context: ./services/analytics-service
      dockerfile: Dockerfile
    container_name: solidity-analytics-service
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3005
      - CLICKHOUSE_URL=http://clickhouse:8123
      - CLICKHOUSE_USER=${CLICKHOUSE_USER:-analytics}
      - CLICKHOUSE_PASSWORD=${CLICKHOUSE_PASSWORD:-dev_clickhouse_123}
      - KAFKA_BROKERS=kafka:9092
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379
    depends_on:
      - clickhouse
      - kafka
      - redis
    networks:
      - solidity-network

  # Gamification Service
  gamification-service:
    build:
      context: ./services/gamification-service
      dockerfile: Dockerfile
    container_name: solidity-gamification-service
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3006
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-dev_password_123}@postgres:5432/gamification_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379
      - KAFKA_BROKERS=kafka:9092
      - USER_SERVICE_URL=http://user-service:3002
    depends_on:
      - postgres
      - redis
      - kafka
      - user-service
    networks:
      - solidity-network

  # Real-time Collaboration Service
  collaboration-service:
    build:
      context: ./services/collaboration-service
      dockerfile: Dockerfile
    container_name: solidity-collaboration-service
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3007
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379
      - AUTH_SERVICE_URL=http://auth-service:3001
      - CORS_ORIGIN=${CORS_ORIGIN:-http://localhost:3000}
    ports:
      - "3007:3007"
    depends_on:
      - redis
      - auth-service
    networks:
      - solidity-network

  # AI/ML Service
  ai-service:
    build:
      context: ./services/ai-service
      dockerfile: Dockerfile
    container_name: solidity-ai-service
    environment:
      - PYTHON_ENV=${NODE_ENV:-development}
      - PORT=3008
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-dev_password_123}@postgres:5432/ai_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      - HUGGINGFACE_API_KEY=${HUGGINGFACE_API_KEY}
    depends_on:
      - postgres
      - redis
    networks:
      - solidity-network

  # Notification Service
  notification-service:
    build:
      context: ./services/notification-service
      dockerfile: Dockerfile
    container_name: solidity-notification-service
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3009
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-dev_password_123}@postgres:5432/notification_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-dev_redis_123}@redis:6379
      - KAFKA_BROKERS=kafka:9092
      - SENDGRID_API_KEY=${SENDGRID_API_KEY}
      - FIREBASE_PROJECT_ID=${FIREBASE_PROJECT_ID}
      - FIREBASE_PRIVATE_KEY=${FIREBASE_PRIVATE_KEY}
      - FIREBASE_CLIENT_EMAIL=${FIREBASE_CLIENT_EMAIL}
    depends_on:
      - postgres
      - redis
      - kafka
    networks:
      - solidity-network

  # Blockchain Service
  blockchain-service:
    build:
      context: ./services/blockchain-service
      dockerfile: Dockerfile
    container_name: solidity-blockchain-service
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3010
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-dev_password_123}@postgres:5432/blockchain_db
      - INFURA_PROJECT_ID=${INFURA_PROJECT_ID}
      - ALCHEMY_API_KEY=${ALCHEMY_API_KEY}
      - PRIVATE_KEY=${BLOCKCHAIN_PRIVATE_KEY}
      - NETWORK=${BLOCKCHAIN_NETWORK:-sepolia}
    depends_on:
      - postgres
    networks:
      - solidity-network

  # Frontend Service
  frontend-service:
    build:
      context: ./services/frontend-service
      dockerfile: Dockerfile
    container_name: solidity-frontend-service
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3000
      - NEXT_PUBLIC_API_GATEWAY_URL=http://localhost:8080
      - NEXT_PUBLIC_WS_URL=ws://localhost:3007
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET:-dev_nextauth_secret_123}
      - NEXT_PUBLIC_GOOGLE_ANALYTICS_ID=${GOOGLE_ANALYTICS_ID}
    ports:
      - "3000:3000"
    depends_on:
      - api-gateway
      - collaboration-service
    networks:
      - solidity-network

# =============================================================================
# VOLUMES
# =============================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  mongodb_data:
    driver: local
  clickhouse_data:
    driver: local
  elasticsearch_data:
    driver: local

# =============================================================================
# NETWORKS
# =============================================================================
networks:
  solidity-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
