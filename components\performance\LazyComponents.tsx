'use client';

import React, { Suspense, lazy, ComponentType, LazyExoticComponent } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Skeleton, 
  ContentSkeleton, 
  LoadingWrapper,
  useLoadingState 
} from './LoadingStates';

// Enhanced lazy loading with error boundaries and loading states
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ReactNode,
  errorFallback?: React.ReactNode
): LazyExoticComponent<T> {
  const LazyComponent = lazy(importFn);
  
  return React.forwardRef<any, React.ComponentProps<T>>((props, ref) => (
    <ErrorBoundary fallback={errorFallback}>
      <Suspense fallback={fallback || <ContentSkeleton lines={5} showButton />}>
        <LazyComponent {...props} ref={ref} />
      </Suspense>
    </ErrorBoundary>
  )) as LazyExoticComponent<T>;
}

// Error boundary component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; fallback?: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component error:', error, errorInfo);
    
    // Track error with analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: `Lazy component error: ${error.message}`,
        fatal: false
      });
    }
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-6 text-center">
          <h3 className="text-red-400 font-medium mb-2">Component Failed to Load</h3>
          <p className="text-gray-300 text-sm mb-4">
            There was an error loading this component. Please try refreshing the page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm transition-colors"
          >
            Refresh Page
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Lazy-loaded heavy components with optimized loading states

// Interactive Code Editor (heavy component)
export const LazyCodeEditor = createLazyComponent(
  () => import('@/components/editor/InteractiveCodeEditor'),
  <div className="bg-gray-900 rounded-lg border border-gray-700 p-6 space-y-4">
    <div className="flex items-center justify-between">
      <Skeleton variant="text" width="150px" />
      <div className="flex space-x-2">
        <Skeleton variant="rounded" width="80px" height="32px" />
        <Skeleton variant="rounded" width="80px" height="32px" />
      </div>
    </div>
    <div className="space-y-2">
      {Array.from({ length: 15 }).map((_, index) => (
        <div key={index} className="flex items-center space-x-4">
          <Skeleton variant="text" width="30px" />
          <Skeleton 
            variant="text" 
            width={`${Math.random() * 40 + 60}%`} 
          />
        </div>
      ))}
    </div>
    <div className="flex justify-between items-center pt-4 border-t border-gray-700">
      <Skeleton variant="text" width="100px" />
      <Skeleton variant="rounded" width="120px" height="36px" />
    </div>
  </div>
);

// 3D Visualizations (heavy component)
export const Lazy3DVisualization = createLazyComponent(
  () => import('@/components/3d/BlockchainVisualization'),
  <div className="bg-gray-900 rounded-lg border border-gray-700 p-8 text-center space-y-4">
    <Skeleton variant="circular" width="80px" height="80px" className="mx-auto" />
    <Skeleton variant="text" width="200px" className="mx-auto" />
    <Skeleton variant="text" lines={2} className="max-w-md mx-auto" />
    <div className="grid grid-cols-3 gap-4 max-w-xs mx-auto">
      {Array.from({ length: 3 }).map((_, index) => (
        <Skeleton key={index} variant="rounded" height="60px" />
      ))}
    </div>
  </div>
);

// Advanced Analytics Dashboard (heavy component)
export const LazyAnalyticsDashboard = createLazyComponent(
  () => import('@/components/analytics/AdvancedDashboard'),
  <div className="space-y-6">
    {/* Header */}
    <div className="flex items-center justify-between">
      <Skeleton variant="text" width="250px" height="2rem" />
      <div className="flex space-x-2">
        <Skeleton variant="rounded" width="100px" height="36px" />
        <Skeleton variant="rounded" width="120px" height="36px" />
      </div>
    </div>
    
    {/* Metrics cards */}
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {Array.from({ length: 4 }).map((_, index) => (
        <div key={index} className="bg-white/5 rounded-lg p-6 space-y-3">
          <div className="flex items-center justify-between">
            <Skeleton variant="circular" width="40px" height="40px" />
            <Skeleton variant="text" width="60px" />
          </div>
          <Skeleton variant="text" width="80%" />
          <Skeleton variant="text" width="50%" />
        </div>
      ))}
    </div>
    
    {/* Charts */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {Array.from({ length: 2 }).map((_, index) => (
        <div key={index} className="bg-white/5 rounded-lg p-6 space-y-4">
          <Skeleton variant="text" width="150px" />
          <Skeleton variant="rounded" height="200px" />
        </div>
      ))}
    </div>
  </div>
);

// Video Player (heavy component)
export const LazyVideoPlayer = createLazyComponent(
  () => import('@/components/media/VideoPlayer'),
  <div className="bg-gray-900 rounded-lg border border-gray-700 overflow-hidden">
    <div className="aspect-video bg-gray-800 flex items-center justify-center">
      <div className="text-center space-y-4">
        <Skeleton variant="circular" width="60px" height="60px" className="mx-auto" />
        <Skeleton variant="text" width="150px" className="mx-auto" />
      </div>
    </div>
    <div className="p-4 space-y-3">
      <Skeleton variant="text" width="80%" />
      <Skeleton variant="text" width="60%" />
      <div className="flex items-center space-x-4">
        <Skeleton variant="circular" width="32px" height="32px" />
        <Skeleton variant="text" width="100px" />
        <Skeleton variant="text" width="80px" />
      </div>
    </div>
  </div>
);

// Chat Widget (heavy component)
export const LazyChatWidget = createLazyComponent(
  () => import('@/components/chat/ChatWidget'),
  <div className="bg-white/5 rounded-lg border border-white/10 p-4 space-y-4">
    <div className="flex items-center space-x-3">
      <Skeleton variant="circular" width="40px" height="40px" />
      <div className="flex-1">
        <Skeleton variant="text" width="120px" />
        <Skeleton variant="text" width="80px" />
      </div>
    </div>
    <div className="space-y-3">
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className={cn(
          'flex',
          index % 2 === 0 ? 'justify-start' : 'justify-end'
        )}>
          <div className={cn(
            'max-w-xs space-y-2',
            index % 2 === 0 ? 'bg-gray-700' : 'bg-blue-600',
            'rounded-lg p-3'
          )}>
            <Skeleton variant="text" width="90%" />
            <Skeleton variant="text" width="60%" />
          </div>
        </div>
      ))}
    </div>
    <div className="flex space-x-2">
      <Skeleton variant="rounded" className="flex-1" height="40px" />
      <Skeleton variant="rounded" width="40px" height="40px" />
    </div>
  </div>
);

// Progressive component loader with intersection observer
export function ProgressiveLoader({
  children,
  fallback,
  threshold = 0.1,
  rootMargin = '50px',
  className
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  threshold?: number;
  rootMargin?: string;
  className?: string;
}) {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold, rootMargin }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [threshold, rootMargin]);

  return (
    <div ref={ref} className={className}>
      {isVisible ? children : (fallback || <ContentSkeleton lines={3} />)}
    </div>
  );
}

// Hook for dynamic imports with loading states
export function useDynamicImport<T>(
  importFn: () => Promise<T>,
  deps: React.DependencyList = []
) {
  const [state, setState] = React.useState<{
    loading: boolean;
    data: T | null;
    error: Error | null;
  }>({
    loading: false,
    data: null,
    error: null
  });

  const load = React.useCallback(async () => {
    setState({ loading: true, data: null, error: null });
    
    try {
      const data = await importFn();
      setState({ loading: false, data, error: null });
    } catch (error) {
      setState({ loading: false, data: null, error: error as Error });
    }
  }, deps);

  return { ...state, load };
}

// Preload component utility
export function preloadComponent(importFn: () => Promise<any>) {
  return importFn();
}

// Bundle of commonly used lazy components
export const LazyComponents = {
  CodeEditor: LazyCodeEditor,
  Visualization3D: Lazy3DVisualization,
  AnalyticsDashboard: LazyAnalyticsDashboard,
  VideoPlayer: LazyVideoPlayer,
  ChatWidget: LazyChatWidget
};

// Component preloader for critical path optimization
export function preloadCriticalComponents() {
  // Preload components that are likely to be needed soon
  const criticalComponents = [
    () => import('@/components/editor/InteractiveCodeEditor'),
    () => import('@/components/chat/ChatWidget')
  ];

  // Use requestIdleCallback for non-blocking preloading
  if (typeof window !== 'undefined' && 'requestIdleCallback' in window) {
    window.requestIdleCallback(() => {
      criticalComponents.forEach(preloadComponent);
    });
  } else {
    // Fallback for browsers without requestIdleCallback
    setTimeout(() => {
      criticalComponents.forEach(preloadComponent);
    }, 1000);
  }
}
