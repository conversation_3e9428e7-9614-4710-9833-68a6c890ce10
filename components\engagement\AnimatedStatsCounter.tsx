'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion, useInView, useMotionValue, useSpring } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Users, BookOpen, Trophy, Star, TrendingUp, Globe } from 'lucide-react';

interface StatItem {
  id: string;
  label: string;
  value: number;
  suffix?: string;
  prefix?: string;
  icon: React.ReactNode;
  color: string;
  description?: string;
}

interface AnimatedStatsCounterProps {
  className?: string;
  layout?: 'grid' | 'horizontal' | 'vertical';
  animationDuration?: number;
  enableParticles?: boolean;
  showDescriptions?: boolean;
}

const defaultStats: StatItem[] = [
  {
    id: 'users',
    label: 'Active Learners',
    value: 15420,
    suffix: '+',
    icon: <Users className="w-6 h-6" />,
    color: 'text-blue-500',
    description: 'Students actively learning Solidity'
  },
  {
    id: 'lessons',
    label: 'Lessons Completed',
    value: 89340,
    suffix: '+',
    icon: <BookOpen className="w-6 h-6" />,
    color: 'text-green-500',
    description: 'Total lessons completed by all users'
  },
  {
    id: 'achievements',
    label: 'Achievements Earned',
    value: 34567,
    suffix: '+',
    icon: <Trophy className="w-6 h-6" />,
    color: 'text-yellow-500',
    description: 'Badges and achievements unlocked'
  },
  {
    id: 'rating',
    label: 'Average Rating',
    value: 4.9,
    suffix: '/5',
    icon: <Star className="w-6 h-6" />,
    color: 'text-purple-500',
    description: 'Based on 2,500+ student reviews'
  },
  {
    id: 'success',
    label: 'Success Rate',
    value: 94,
    suffix: '%',
    icon: <TrendingUp className="w-6 h-6" />,
    color: 'text-emerald-500',
    description: 'Students who complete their first course'
  },
  {
    id: 'countries',
    label: 'Countries',
    value: 127,
    suffix: '+',
    icon: <Globe className="w-6 h-6" />,
    color: 'text-indigo-500',
    description: 'Countries with active learners'
  }
];

export function AnimatedStatsCounter({
  className,
  layout = 'grid',
  animationDuration = 2000,
  enableParticles = true,
  showDescriptions = true
}: AnimatedStatsCounterProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { once: true, margin: '-100px' });
  const [hasAnimated, setHasAnimated] = useState(false);

  useEffect(() => {
    if (isInView && !hasAnimated) {
      setHasAnimated(true);
    }
  }, [isInView, hasAnimated]);

  const layoutClasses = {
    grid: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6',
    horizontal: 'flex flex-wrap justify-center gap-8',
    vertical: 'flex flex-col space-y-6'
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        'relative',
        layoutClasses[layout],
        className
      )}
      role="region"
      aria-label="Platform statistics"
    >
      {defaultStats.map((stat, index) => (
        <AnimatedStatCard
          key={stat.id}
          stat={stat}
          isVisible={hasAnimated}
          delay={index * 200}
          animationDuration={animationDuration}
          enableParticles={enableParticles}
          showDescription={showDescriptions}
        />
      ))}
    </div>
  );
}

interface AnimatedStatCardProps {
  stat: StatItem;
  isVisible: boolean;
  delay: number;
  animationDuration: number;
  enableParticles: boolean;
  showDescription: boolean;
}

function AnimatedStatCard({
  stat,
  isVisible,
  delay,
  animationDuration,
  enableParticles,
  showDescription
}: AnimatedStatCardProps) {
  const [isHovered, setIsHovered] = useState(false);
  const countRef = useRef<HTMLSpanElement>(null);

  // Animated counter using Framer Motion
  const motionValue = useMotionValue(0);
  const springValue = useSpring(motionValue, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  useEffect(() => {
    if (isVisible) {
      const timer = setTimeout(() => {
        motionValue.set(stat.value);
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [isVisible, delay, motionValue, stat.value]);

  useEffect(() => {
    const unsubscribe = springValue.on('change', (latest) => {
      if (countRef.current) {
        // Format number based on value
        let formattedValue: string;
        
        if (stat.id === 'rating') {
          formattedValue = latest.toFixed(1);
        } else if (latest >= 1000000) {
          formattedValue = (latest / 1000000).toFixed(1) + 'M';
        } else if (latest >= 1000) {
          formattedValue = (latest / 1000).toFixed(latest >= 10000 ? 0 : 1) + 'K';
        } else {
          formattedValue = Math.round(latest).toString();
        }

        countRef.current.textContent = formattedValue;
      }
    });

    return unsubscribe;
  }, [springValue, stat.id]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.9 }}
      animate={isVisible ? { opacity: 1, y: 0, scale: 1 } : {}}
      transition={{
        duration: 0.6,
        delay: delay / 1000,
        type: 'spring',
        stiffness: 100,
        damping: 15
      }}
      whileHover={{ 
        scale: 1.05,
        transition: { duration: 0.2 }
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={cn(
        'relative group',
        'bg-white dark:bg-gray-800',
        'rounded-xl p-6',
        'border border-gray-200 dark:border-gray-700',
        'shadow-lg hover:shadow-xl',
        'transition-all duration-300',
        'overflow-hidden',
        'cursor-pointer'
      )}
      role="article"
      aria-label={`${stat.label}: ${stat.value}${stat.suffix || ''}`}
    >
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-gray-50/50 dark:to-gray-900/50" />
      
      {/* Particles effect */}
      {enableParticles && isHovered && (
        <ParticleEffect color={stat.color} />
      )}

      {/* Icon */}
      <motion.div
        className={cn(
          'inline-flex items-center justify-center',
          'w-12 h-12 rounded-lg mb-4',
          'bg-gradient-to-br from-gray-100 to-gray-200',
          'dark:from-gray-700 dark:to-gray-800',
          stat.color
        )}
        whileHover={{ rotate: 360 }}
        transition={{ duration: 0.6 }}
      >
        {stat.icon}
      </motion.div>

      {/* Value */}
      <div className="mb-2">
        <div className="flex items-baseline space-x-1">
          {stat.prefix && (
            <span className="text-lg font-semibold text-gray-600 dark:text-gray-400">
              {stat.prefix}
            </span>
          )}
          <span
            ref={countRef}
            className={cn(
              'text-3xl font-bold',
              'bg-gradient-to-r from-gray-900 to-gray-700',
              'dark:from-white dark:to-gray-300',
              'bg-clip-text text-transparent'
            )}
            aria-live="polite"
          >
            0
          </span>
          {stat.suffix && (
            <span className="text-lg font-semibold text-gray-600 dark:text-gray-400">
              {stat.suffix}
            </span>
          )}
        </div>
      </div>

      {/* Label */}
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
        {stat.label}
      </h3>

      {/* Description */}
      {showDescription && stat.description && (
        <motion.p
          initial={{ opacity: 0, height: 0 }}
          animate={isHovered ? { opacity: 1, height: 'auto' } : { opacity: 0.7, height: 'auto' }}
          transition={{ duration: 0.3 }}
          className="text-sm text-gray-600 dark:text-gray-400"
        >
          {stat.description}
        </motion.p>
      )}

      {/* Progress indicator */}
      <motion.div
        className={cn(
          'absolute bottom-0 left-0 h-1',
          'bg-gradient-to-r',
          stat.color.replace('text-', 'from-').replace('-500', '-400'),
          stat.color.replace('text-', 'to-').replace('-500', '-600')
        )}
        initial={{ width: 0 }}
        animate={isVisible ? { width: '100%' } : {}}
        transition={{
          duration: animationDuration / 1000,
          delay: delay / 1000,
          ease: 'easeOut'
        }}
      />

      {/* Hover overlay */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent"
        initial={{ x: '-100%' }}
        animate={isHovered ? { x: '100%' } : {}}
        transition={{ duration: 0.6 }}
      />
    </motion.div>
  );
}

interface ParticleEffectProps {
  color: string;
}

function ParticleEffect({ color }: ParticleEffectProps) {
  const particles = Array.from({ length: 12 }, (_, i) => i);

  return (
    <div className="absolute inset-0 pointer-events-none overflow-hidden">
      {particles.map((particle) => (
        <motion.div
          key={particle}
          className={cn(
            'absolute w-1 h-1 rounded-full',
            color.replace('text-', 'bg-')
          )}
          initial={{
            x: '50%',
            y: '50%',
            scale: 0,
            opacity: 0
          }}
          animate={{
            x: `${50 + (Math.random() - 0.5) * 200}%`,
            y: `${50 + (Math.random() - 0.5) * 200}%`,
            scale: [0, 1, 0],
            opacity: [0, 1, 0]
          }}
          transition={{
            duration: 1.5,
            delay: particle * 0.1,
            ease: 'easeOut'
          }}
        />
      ))}
    </div>
  );
}

// Hook for custom stats
export function useAnimatedStats(customStats?: StatItem[]) {
  const [stats, setStats] = useState(customStats || defaultStats);

  const updateStat = (id: string, newValue: number) => {
    setStats(prev => prev.map(stat => 
      stat.id === id ? { ...stat, value: newValue } : stat
    ));
  };

  const addStat = (newStat: StatItem) => {
    setStats(prev => [...prev, newStat]);
  };

  const removeStat = (id: string) => {
    setStats(prev => prev.filter(stat => stat.id !== id));
  };

  return {
    stats,
    updateStat,
    addStat,
    removeStat
  };
}

export default AnimatedStatsCounter;
