<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Debug React App</title>
  <style>
    body { 
      margin: 0; 
      padding: 20px; 
      font-family: Arial, sans-serif; 
      background: #1a202c; 
      color: white; 
    }
    .debug-info {
      background: #2d3748;
      padding: 15px;
      border-radius: 8px;
      margin: 10px 0;
    }
  </style>
</head>
<body>
  <div class="debug-info">
    <h2>Debug Information</h2>
    <p>If React loads, this text will be replaced.</p>
    <p>Current time: <span id="timestamp"></span></p>
  </div>
  
  <div id="root">
    <div class="debug-info">
      <p>🔄 Loading React app...</p>
      <p>If you see this message, React has not mounted yet.</p>
    </div>
  </div>

  <script>
    // Basic JavaScript test
    document.getElementById('timestamp').textContent = new Date().toISOString();
    console.log('Debug HTML loaded successfully');
    
    // Check if important globals exist
    console.log('window.React:', typeof window.React);
    console.log('window.ReactDOM:', typeof window.ReactDOM);
    
    // Listen for any errors
    window.addEventListener('error', function(e) {
      console.error('Global error:', e.error);
      document.body.innerHTML += '<div class="debug-info" style="background: #e53e3e;"><h3>ERROR:</h3><p>' + e.message + '</p></div>';
    });
    
    window.addEventListener('unhandledrejection', function(e) {
      console.error('Unhandled promise rejection:', e.reason);
      document.body.innerHTML += '<div class="debug-info" style="background: #e53e3e;"><h3>PROMISE ERROR:</h3><p>' + e.reason + '</p></div>';
    });
  </script>
  
  <!-- Try loading the React bundle -->
  <script type="module" crossorigin src="/learning_sol/assets/index-BlrtLb1j.js"></script>
</body>
</html>
