// Components Barrel Export File
// Auto-generated for cleaner imports

// UI Components
export * from './ui';

// Layout Components
export { default as Sidebar } from './Sidebar';
export { default as MobileNavigation } from './MobileNavigation';
export { default as LandingPage } from './LandingPage';

// Feature Components
export { default as ModuleContent } from './ModuleContent';
export { default as QuizComponent } from './QuizComponent';
export { default as GeminiChat } from './GeminiChat';

// Utility Components
export { default as ConfirmationModal } from './ConfirmationModal';
export { default as CopyButton } from './CopyButton';

// Achievement Components
export * from './achievements/AchievementCard';
export * from './achievements/AchievementGrid';
export * from './achievements/AchievementNotification';
export { default as AchievementsPage } from './achievements/AchievementsPage';

// Admin Components
export { default as AdminDashboard } from './admin/AdminDashboard';
export { default as AdminGuard } from './admin/AdminGuard';
export { default as AdminLayout } from './admin/AdminLayout';
export { default as UserManagement } from './admin/UserManagement';
export { default as ContentManagement } from './admin/ContentManagement';
export { default as SecurityManagement } from './admin/SecurityManagement';

// Auth Components
export { default as AuthModal } from './auth/AuthModal';
export { default as EnhancedAuthProvider } from './auth/EnhancedAuthProvider';
export { default as EnhancedLoginModal } from './auth/EnhancedLoginModal';
export { default as PasswordResetModal } from './auth/PasswordResetModal';
export { default as PasswordStrengthIndicator } from './auth/PasswordStrengthIndicator';
export { default as ProtectedRoute } from './auth/ProtectedRoute';

// AI Components
export { default as AILearningPath } from './ai/AILearningPath';
export { default as EnhancedAIAssistant } from './ai/EnhancedAIAssistant';

// Blockchain Components
export { default as BlockchainIntegration } from './blockchain/BlockchainIntegration';
export { default as ContractDeployer } from './blockchain/ContractDeployer';
export { default as WalletConnect } from './blockchain/WalletConnect';

// Code Components
export { default as CodeLab } from './code/CodeLab';

// Collaboration Components
export { default as CollaborationHub } from './collaboration/CollaborationHub';
export { default as CollaborativeEditor } from './collaboration/CollaborativeEditor';
export { default as RealTimeCodeEditor } from './collaboration/RealTimeCodeEditor';
export { default as UserPresenceIndicator } from './collaboration/UserPresenceIndicator';

// Community Components
export { default as CommunityHub } from './community/CommunityHub';
export { default as CommunityStats } from './community/CommunityStats';
export { default as Leaderboards } from './community/Leaderboards';

// Curriculum Components
export { default as CurriculumDashboard } from './curriculum/CurriculumDashboard';
export { default as LearningAnalytics } from './curriculum/LearningAnalytics';
export { default as LessonCard } from './curriculum/LessonCard';
export { default as ModuleCard } from './curriculum/ModuleCard';

// Error Components
export { default as ErrorBoundary } from './error-handling/ErrorBoundary';
export { default as AsyncErrorBoundary } from './error-handling/AsyncErrorBoundary';
export { default as NotFoundPage } from './error-handling/NotFoundPage';

// Form Components
export { default as ContactForm } from './forms/ContactForm';

// Help Components
export { default as HelpSystem } from './help/HelpSystem';
export { default as KeyboardShortcuts } from './help/KeyboardShortcuts';

// Icon Components
export { default as CheckIcon } from './icons/CheckIcon';
export { default as MenuIcon } from './icons/MenuIcon';
export { default as UserIcon } from './icons/UserIcon';
export { default as BotIcon } from './icons/BotIcon';
export { default as SendIcon } from './icons/SendIcon';
export { default as SpinnerIcon } from './icons/SpinnerIcon';

// Layout Components
export { default as Footer } from './layout/Footer';
export { default as Navigation } from './layout/Navigation';

// Learning Components
export { default as LearningDashboard } from './learning/LearningDashboard';
export { default as InteractiveCodeEditor } from './learning/InteractiveCodeEditor';
export { default as GamificationSystem } from './learning/GamificationSystem';

// Navigation Components
export { default as AuthenticatedNavbar } from './navigation/AuthenticatedNavbar';
export { default as SmartNavigation } from './navigation/SmartNavigation';

// Profile Components
export { default as UserProfile } from './profile/UserProfile';

// Progress Components
export { default as ProgressDashboard } from './progress/ProgressDashboard';

// Provider Components
export { default as SessionProvider } from './providers/SessionProvider';
export { default as FallbackProvider } from './providers/FallbackProvider';

// Section Components
export { default as HeroSection } from './sections/HeroSection';
export { default as FeaturesSection } from './sections/FeaturesSection';
export { default as CTASection } from './sections/CTASection';

// Settings Components
export { default as SettingsPage } from './settings/SettingsPage';
export { default as ProfileSection } from './settings/ProfileSection';
export { default as SecuritySection } from './settings/SecuritySection';
export { default as NotificationSection } from './settings/NotificationSection';

// XP Components
export { default as XPCounter } from './xp/XPCounter';
export { default as ProgressBar } from './xp/ProgressBar';
export { default as LevelUpCelebration } from './xp/LevelUpCelebration';
export { default as XPNotification } from './xp/XPNotification';

// Analytics Components
export * from './analytics/PerformanceAnalyticsIntegration';
export * from './analytics/RealTimeDashboard';
export * from './analytics/ABTestingFramework';
export * from './analytics/ConversionFunnelSystem';
export * from './analytics/FeedbackWidgets';
export * from './analytics/PerformanceMonitoring';

// Performance Components
export * from './performance/LoadingStates';
export * from './performance/LazyComponents';
export * from './performance/PWAUtils';
export * from './performance/ProgressiveImage';

// Conversion Optimization Components
export * from './conversion/ConversionOptimizationIntegration';
export * from './conversion/ExitIntentSystem';
export * from './conversion/UrgencyScarcitySystem';
export * from './conversion/TrialOnboardingSystem';

// Content Enhancement Components
export * from './content/ContentEnhancementIntegration';
export * from './content/InteractiveFAQ';
export * from './content/LearningPathPreview';
export * from './content/CompetitiveComparison';

// Social & Gamification Components
export * from './social/SocialGamificationIntegration';
export * from './social/SocialProofSystem';
export * from './gamification/AchievementCelebration';
export * from './gamification/StreakTracker';
export * from './gamification/SocialSharing';

// Hero Enhancement Components
export * from './hero/HeroEngagementSuite';
export * from './hero/EnhancedCTAButtons';
export * from './hero/InteractiveMiniDemo';
export * from './hero/GamificationSystem';

// Onboarding Components
export * from './onboarding/GuidedTourSystem';
export * from './onboarding/InteractiveTutorial';
export * from './onboarding/OnboardingFlow';

// Discovery Components
export * from './discovery/FeatureSpotlight';
export * from './discovery/SmartTooltip';

// Testing Components (development only)
export * from './testing/UATDashboard';
export * from './testing/FeedbackCollectionSystem';

// Development Components (development only)
export * from './dev/AccessibilityTester';

// Feature flags
export const FEATURES = {
  ANALYTICS: true,
  GAMIFICATION: true,
  COLLABORATION: true,
  AI_ASSISTANT: true,
  BLOCKCHAIN: true,
  PWA: true,
  ADMIN_PANEL: true,
  SOCIAL_FEATURES: true,
  PERFORMANCE_MONITORING: true,
  A_B_TESTING: true,
  CONVERSION_OPTIMIZATION: true,
  CONTENT_ENHANCEMENT: true
} as const;

// Version info
export const COMPONENT_VERSION = '2.0.0';
export const LAST_UPDATED = '2024-12-26';
