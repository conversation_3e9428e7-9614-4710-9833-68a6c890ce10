<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100" width="100" height="100">
  <defs>
    <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
      <path d="M20 0L0 0 0 20" fill="none" stroke="rgba(255,255,255,.1)" stroke-width=".5"/>
    </pattern>
    <pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse">
      <circle cx="10" cy="10" r=".8" fill="rgba(139,92,246,.3)"/>
    </pattern>
    <linearGradient id="gridGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0" stop-color="rgba(139,92,246,.1)"/>
      <stop offset=".5" stop-color="rgba(59,130,246,.05)"/>
      <stop offset="1" stop-color="rgba(139,92,246,.1)"/>
    </linearGradient>
  </defs>
  <rect width="100" height="100" fill="url(#grid)"/>
  <rect width="100" height="100" fill="url(#dots)"/>
  <rect width="100" height="100" fill="url(#gridGradient)" opacity=".3"/>
</svg>
