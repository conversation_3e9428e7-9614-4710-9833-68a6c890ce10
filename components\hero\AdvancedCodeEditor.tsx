'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Copy, 
  Check, 
  RotateCcw, 
  Play, 
  AlertCircle, 
  Info, 
  Lightbulb,
  Code,
  Wand2
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CodeAnnotation {
  line: number;
  column?: number;
  type: 'info' | 'warning' | 'error' | 'tip';
  title: string;
  content: string;
  link?: string;
}

interface AdvancedCodeEditorProps {
  initialCode: string;
  language: 'solidity' | 'javascript' | 'typescript';
  filename: string;
  editable?: boolean;
  copyable?: boolean;
  showLineNumbers?: boolean;
  annotations?: CodeAnnotation[];
  onCodeChange?: (code: string) => void;
  onCompile?: (code: string) => void;
  className?: string;
}

/**
 * Advanced Code Editor with syntax highlighting, tooltips, and interactive features
 */
export function AdvancedCodeEditor({
  initialCode,
  language,
  filename,
  editable = true,
  copyable = true,
  showLineNumbers = true,
  annotations = [],
  onCodeChange,
  onCompile,
  className = ''
}: AdvancedCodeEditorProps) {
  const [code, setCode] = useState(initialCode);
  const [copied, setCopied] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [hoveredLine, setHoveredLine] = useState<number | null>(null);
  const [activeTooltip, setActiveTooltip] = useState<CodeAnnotation | null>(null);
  
  const editorRef = useRef<HTMLTextAreaElement>(null);
  const codeDisplayRef = useRef<HTMLPreElement>(null);

  // Syntax highlighting patterns for Solidity
  const solidityKeywords = [
    'pragma', 'solidity', 'contract', 'function', 'modifier', 'event', 'struct', 'enum',
    'mapping', 'address', 'uint', 'uint256', 'int', 'bool', 'string', 'bytes', 'bytes32',
    'public', 'private', 'internal', 'external', 'pure', 'view', 'payable', 'constant',
    'memory', 'storage', 'calldata', 'constructor', 'fallback', 'receive', 'virtual', 'override',
    'if', 'else', 'for', 'while', 'do', 'break', 'continue', 'return', 'try', 'catch',
    'require', 'assert', 'revert', 'emit', 'new', 'delete', 'this', 'super', 'msg', 'tx', 'block'
  ];

  const highlightSyntax = (code: string) => {
    let highlighted = code;
    
    // Comments
    highlighted = highlighted.replace(
      /(\/\/.*$|\/\*[\s\S]*?\*\/)/gm,
      '<span class="text-gray-500 italic">$1</span>'
    );
    
    // Strings
    highlighted = highlighted.replace(
      /(["'])((?:\\.|(?!\1)[^\\])*?)\1/g,
      '<span class="text-green-400">$1$2$1</span>'
    );
    
    // Numbers
    highlighted = highlighted.replace(
      /\b(\d+)\b/g,
      '<span class="text-blue-400">$1</span>'
    );
    
    // Keywords
    solidityKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      highlighted = highlighted.replace(
        regex,
        `<span class="text-purple-400 font-medium">${keyword}</span>`
      );
    });
    
    // Function names
    highlighted = highlighted.replace(
      /\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g,
      '<span class="text-yellow-400">$1</span>('
    );
    
    return highlighted;
  };

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
    }
  };

  const handleReset = () => {
    setCode(initialCode);
    onCodeChange?.(initialCode);
  };

  const handleCodeChange = (newCode: string) => {
    setCode(newCode);
    onCodeChange?.(newCode);
  };

  const handleFormat = () => {
    // Simple formatting for demo purposes
    const formatted = code
      .split('\n')
      .map(line => {
        const trimmed = line.trim();
        if (trimmed.startsWith('}')) return trimmed;
        if (trimmed.includes('{')) return trimmed;
        if (trimmed.startsWith('//')) return '    ' + trimmed;
        return '    ' + trimmed;
      })
      .join('\n');
    
    setCode(formatted);
    onCodeChange?.(formatted);
  };

  const getLineAnnotation = (lineNumber: number) => {
    return annotations.find(annotation => annotation.line === lineNumber);
  };

  const getAnnotationIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-400" />;
      case 'tip':
        return <Lightbulb className="w-4 h-4 text-blue-400" />;
      default:
        return <Info className="w-4 h-4 text-gray-400" />;
    }
  };

  const codeLines = code.split('\n');

  return (
    <div className={cn('relative', className)}>
      {/* Editor Header */}
      <div className="flex items-center justify-between p-3 bg-gray-900 border-b border-gray-700 rounded-t-lg">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-gray-400 ml-2 text-sm">{filename}</span>
        </div>
        
        <div className="flex items-center space-x-2">
          {copyable && (
            <motion.button
              onClick={handleCopy}
              className="flex items-center space-x-1 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 rounded transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {copied ? <Check className="w-3 h-3" /> : <Copy className="w-3 h-3" />}
              <span>{copied ? 'Copied!' : 'Copy'}</span>
            </motion.button>
          )}
          
          {editable && (
            <>
              <button
                onClick={handleFormat}
                className="flex items-center space-x-1 px-2 py-1 text-xs bg-blue-600 hover:bg-blue-700 rounded transition-colors"
                title="Format code"
              >
                <Wand2 className="w-3 h-3" />
                <span>Format</span>
              </button>
              
              <button
                onClick={handleReset}
                className="flex items-center space-x-1 px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 rounded transition-colors"
                title="Reset to original"
              >
                <RotateCcw className="w-3 h-3" />
                <span>Reset</span>
              </button>
            </>
          )}
          
          {onCompile && (
            <button
              onClick={() => onCompile(code)}
              className="flex items-center space-x-1 px-2 py-1 text-xs bg-green-600 hover:bg-green-700 rounded transition-colors"
            >
              <Play className="w-3 h-3" />
              <span>Compile</span>
            </button>
          )}
        </div>
      </div>

      {/* Code Editor */}
      <div className="relative bg-gray-900 rounded-b-lg overflow-hidden">
        {editable && isEditing ? (
          <textarea
            ref={editorRef}
            value={code}
            onChange={(e) => handleCodeChange(e.target.value)}
            onBlur={() => setIsEditing(false)}
            className="w-full h-64 p-4 bg-transparent text-gray-300 font-mono text-sm resize-none focus:outline-none"
            style={{ lineHeight: '1.5' }}
            autoFocus
          />
        ) : (
          <div className="relative">
            <pre
              ref={codeDisplayRef}
              className={cn(
                'p-4 text-gray-300 font-mono text-sm overflow-x-auto',
                editable && 'cursor-text hover:bg-gray-800/50 transition-colors'
              )}
              onClick={() => editable && setIsEditing(true)}
              style={{ lineHeight: '1.5' }}
            >
              <code>
                {codeLines.map((line, index) => {
                  const lineNumber = index + 1;
                  const annotation = getLineAnnotation(lineNumber);
                  
                  return (
                    <div
                      key={index}
                      className={cn(
                        'flex items-start relative',
                        annotation && 'bg-red-500/10 border-l-2 border-red-500/50'
                      )}
                      onMouseEnter={() => {
                        setHoveredLine(lineNumber);
                        if (annotation) setActiveTooltip(annotation);
                      }}
                      onMouseLeave={() => {
                        setHoveredLine(null);
                        setActiveTooltip(null);
                      }}
                    >
                      {showLineNumbers && (
                        <span className="inline-block w-8 text-gray-500 text-right mr-4 select-none">
                          {lineNumber}
                        </span>
                      )}
                      
                      <span
                        className="flex-1"
                        dangerouslySetInnerHTML={{ __html: highlightSyntax(line) || '&nbsp;' }}
                      />
                      
                      {annotation && (
                        <div className="ml-2 flex items-center">
                          {getAnnotationIcon(annotation.type)}
                        </div>
                      )}
                    </div>
                  );
                })}
              </code>
            </pre>
            
            {/* Tooltip */}
            {activeTooltip && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 10 }}
                className="absolute z-10 bg-gray-800 border border-gray-600 rounded-lg p-3 shadow-lg max-w-xs"
                style={{
                  top: `${(activeTooltip.line - 1) * 24 + 60}px`,
                  right: '20px'
                }}
              >
                <div className="flex items-start space-x-2">
                  {getAnnotationIcon(activeTooltip.type)}
                  <div>
                    <h4 className="text-sm font-medium text-white mb-1">
                      {activeTooltip.title}
                    </h4>
                    <p className="text-xs text-gray-300">
                      {activeTooltip.content}
                    </p>
                    {activeTooltip.link && (
                      <a
                        href={activeTooltip.link}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-xs text-blue-400 hover:text-blue-300 mt-1 inline-block"
                      >
                        Learn more →
                      </a>
                    )}
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
