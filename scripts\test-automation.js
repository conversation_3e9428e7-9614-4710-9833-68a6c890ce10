#!/usr/bin/env node

/**
 * Comprehensive Test Automation Script
 * Runs all tests with coverage reporting and quality gates
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class TestAutomation {
  constructor() {
    this.testResults = {
      unit: { passed: 0, failed: 0, coverage: 0 },
      integration: { passed: 0, failed: 0, coverage: 0 },
      e2e: { passed: 0, failed: 0, coverage: 0 },
      accessibility: { passed: 0, failed: 0, coverage: 0 },
      performance: { passed: 0, failed: 0, coverage: 0 },
      analytics: { passed: 0, failed: 0, coverage: 0 },
      conversion: { passed: 0, failed: 0, coverage: 0 },
      content: { passed: 0, failed: 0, coverage: 0 },
      social: { passed: 0, failed: 0, coverage: 0 }
    };
    
    this.coverageThresholds = {
      global: { statements: 80, branches: 80, functions: 80, lines: 80 },
      auth: { statements: 90, branches: 90, functions: 90, lines: 90 },
      analytics: { statements: 90, branches: 90, functions: 90, lines: 90 },
      performance: { statements: 85, branches: 85, functions: 85, lines: 85 },
      conversion: { statements: 80, branches: 80, functions: 80, lines: 80 },
      content: { statements: 75, branches: 75, functions: 75, lines: 75 },
      social: { statements: 75, branches: 75, functions: 75, lines: 75 }
    };

    this.qualityGates = {
      minCoverage: 80,
      maxFailedTests: 0,
      maxTestDuration: 300, // 5 minutes
      requiredTestTypes: ['unit', 'integration', 'accessibility']
    };
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    const colors = {
      info: '\x1b[36m',
      success: '\x1b[32m',
      warn: '\x1b[33m',
      error: '\x1b[31m',
      reset: '\x1b[0m'
    };
    
    console.log(`${colors[level]}[${timestamp}] [${level.toUpperCase()}] ${message}${colors.reset}`);
  }

  async runCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
      this.log(`Running: ${command}`);
      
      const child = spawn('npm', ['run', command], {
        stdio: 'pipe',
        shell: true,
        ...options
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
        if (options.verbose) {
          process.stdout.write(data);
        }
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
        if (options.verbose) {
          process.stderr.write(data);
        }
      });

      child.on('close', (code) => {
        if (code === 0) {
          resolve({ stdout, stderr, code });
        } else {
          reject(new Error(`Command failed with code ${code}: ${stderr}`));
        }
      });

      child.on('error', (error) => {
        reject(error);
      });
    });
  }

  async setupTestEnvironment() {
    this.log('Setting up test environment...');
    
    // Ensure test directories exist
    const testDirs = [
      '__tests__/unit',
      '__tests__/integration',
      '__tests__/e2e',
      '__tests__/accessibility',
      '__tests__/analytics',
      '__tests__/conversion',
      '__tests__/content',
      '__tests__/social',
      'coverage'
    ];

    testDirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        this.log(`Created directory: ${dir}`);
      }
    });

    // Create test environment file if it doesn't exist
    const testEnvPath = '.env.test';
    if (!fs.existsSync(testEnvPath)) {
      const testEnvContent = `# Test Environment Variables
NODE_ENV=test
NEXT_PUBLIC_GA_ID=GA-TEST-123
NEXT_PUBLIC_HOTJAR_ID=HJ-TEST-456
DATABASE_URL=sqlite://test.db
NEXTAUTH_SECRET=test-secret
NEXTAUTH_URL=http://localhost:3000
`;
      fs.writeFileSync(testEnvPath, testEnvContent);
      this.log('Created .env.test file');
    }
  }

  async runUnitTests() {
    this.log('Running unit tests...');
    
    try {
      const result = await this.runCommand('test:unit', { verbose: true });
      this.parseTestResults('unit', result.stdout);
      this.log('Unit tests completed successfully', 'success');
    } catch (error) {
      this.log(`Unit tests failed: ${error.message}`, 'error');
      this.testResults.unit.failed = 1;
    }
  }

  async runIntegrationTests() {
    this.log('Running integration tests...');
    
    try {
      const result = await this.runCommand('test:integration', { verbose: true });
      this.parseTestResults('integration', result.stdout);
      this.log('Integration tests completed successfully', 'success');
    } catch (error) {
      this.log(`Integration tests failed: ${error.message}`, 'error');
      this.testResults.integration.failed = 1;
    }
  }

  async runE2ETests() {
    this.log('Running E2E tests...');
    
    try {
      // Start the development server
      this.log('Starting development server for E2E tests...');
      const serverProcess = spawn('npm', ['run', 'dev'], {
        stdio: 'pipe',
        detached: true
      });

      // Wait for server to start
      await new Promise(resolve => setTimeout(resolve, 10000));

      const result = await this.runCommand('test:e2e', { verbose: true });
      this.parseTestResults('e2e', result.stdout);
      
      // Kill the server
      process.kill(-serverProcess.pid);
      
      this.log('E2E tests completed successfully', 'success');
    } catch (error) {
      this.log(`E2E tests failed: ${error.message}`, 'error');
      this.testResults.e2e.failed = 1;
    }
  }

  async runAccessibilityTests() {
    this.log('Running accessibility tests...');
    
    try {
      const result = await this.runCommand('test:accessibility', { verbose: true });
      this.parseTestResults('accessibility', result.stdout);
      this.log('Accessibility tests completed successfully', 'success');
    } catch (error) {
      this.log(`Accessibility tests failed: ${error.message}`, 'error');
      this.testResults.accessibility.failed = 1;
    }
  }

  async runAnalyticsTests() {
    this.log('Running analytics tests...');
    
    try {
      const result = await this.runCommand('test:analytics', { verbose: true });
      this.parseTestResults('analytics', result.stdout);
      this.log('Analytics tests completed successfully', 'success');
    } catch (error) {
      this.log(`Analytics tests failed: ${error.message}`, 'error');
      this.testResults.analytics.failed = 1;
    }
  }

  async runConversionTests() {
    this.log('Running conversion tests...');
    
    try {
      const result = await this.runCommand('test:conversion', { verbose: true });
      this.parseTestResults('conversion', result.stdout);
      this.log('Conversion tests completed successfully', 'success');
    } catch (error) {
      this.log(`Conversion tests failed: ${error.message}`, 'error');
      this.testResults.conversion.failed = 1;
    }
  }

  parseTestResults(testType, output) {
    // Parse Jest output to extract test results
    const passedMatch = output.match(/(\d+) passed/);
    const failedMatch = output.match(/(\d+) failed/);
    const coverageMatch = output.match(/All files[^|]*\|[^|]*\|[^|]*\|[^|]*\|[^|]*(\d+\.?\d*)/);

    if (passedMatch) {
      this.testResults[testType].passed = parseInt(passedMatch[1]);
    }

    if (failedMatch) {
      this.testResults[testType].failed = parseInt(failedMatch[1]);
    }

    if (coverageMatch) {
      this.testResults[testType].coverage = parseFloat(coverageMatch[1]);
    }
  }

  async generateCoverageReport() {
    this.log('Generating comprehensive coverage report...');
    
    try {
      await this.runCommand('test:coverage');
      
      // Parse coverage data
      const coveragePath = 'coverage/coverage-summary.json';
      if (fs.existsSync(coveragePath)) {
        const coverageData = JSON.parse(fs.readFileSync(coveragePath, 'utf8'));
        this.processCoverageData(coverageData);
      }
      
      this.log('Coverage report generated successfully', 'success');
    } catch (error) {
      this.log(`Coverage report generation failed: ${error.message}`, 'error');
    }
  }

  processCoverageData(coverageData) {
    // Process coverage data and check against thresholds
    const totalCoverage = coverageData.total;
    
    Object.entries(this.coverageThresholds).forEach(([component, thresholds]) => {
      if (component === 'global') {
        this.checkCoverageThreshold('Global', totalCoverage, thresholds);
      } else {
        // Check component-specific coverage
        const componentPattern = new RegExp(`components/${component}/`);
        const componentFiles = Object.keys(coverageData).filter(file => 
          componentPattern.test(file)
        );
        
        if (componentFiles.length > 0) {
          const componentCoverage = this.calculateComponentCoverage(
            coverageData, 
            componentFiles
          );
          this.checkCoverageThreshold(component, componentCoverage, thresholds);
        }
      }
    });
  }

  calculateComponentCoverage(coverageData, files) {
    let totalStatements = 0;
    let coveredStatements = 0;
    let totalBranches = 0;
    let coveredBranches = 0;
    let totalFunctions = 0;
    let coveredFunctions = 0;
    let totalLines = 0;
    let coveredLines = 0;

    files.forEach(file => {
      const fileCoverage = coverageData[file];
      totalStatements += fileCoverage.statements.total;
      coveredStatements += fileCoverage.statements.covered;
      totalBranches += fileCoverage.branches.total;
      coveredBranches += fileCoverage.branches.covered;
      totalFunctions += fileCoverage.functions.total;
      coveredFunctions += fileCoverage.functions.covered;
      totalLines += fileCoverage.lines.total;
      coveredLines += fileCoverage.lines.covered;
    });

    return {
      statements: { pct: (coveredStatements / totalStatements) * 100 },
      branches: { pct: (coveredBranches / totalBranches) * 100 },
      functions: { pct: (coveredFunctions / totalFunctions) * 100 },
      lines: { pct: (coveredLines / totalLines) * 100 }
    };
  }

  checkCoverageThreshold(component, coverage, thresholds) {
    const checks = ['statements', 'branches', 'functions', 'lines'];
    
    checks.forEach(check => {
      const actual = coverage[check].pct;
      const required = thresholds[check];
      
      if (actual < required) {
        this.log(
          `Coverage threshold not met for ${component} ${check}: ${actual.toFixed(2)}% < ${required}%`,
          'warn'
        );
      } else {
        this.log(
          `Coverage threshold met for ${component} ${check}: ${actual.toFixed(2)}% >= ${required}%`,
          'success'
        );
      }
    });
  }

  checkQualityGates() {
    this.log('Checking quality gates...');
    
    let passed = true;
    const issues = [];

    // Check minimum coverage
    const totalCoverage = this.calculateOverallCoverage();
    if (totalCoverage < this.qualityGates.minCoverage) {
      issues.push(`Overall coverage ${totalCoverage.toFixed(2)}% below minimum ${this.qualityGates.minCoverage}%`);
      passed = false;
    }

    // Check for failed tests
    const totalFailed = Object.values(this.testResults).reduce((sum, result) => sum + result.failed, 0);
    if (totalFailed > this.qualityGates.maxFailedTests) {
      issues.push(`${totalFailed} tests failed, maximum allowed: ${this.qualityGates.maxFailedTests}`);
      passed = false;
    }

    // Check required test types
    this.qualityGates.requiredTestTypes.forEach(testType => {
      if (this.testResults[testType].passed === 0) {
        issues.push(`Required test type '${testType}' has no passing tests`);
        passed = false;
      }
    });

    if (passed) {
      this.log('All quality gates passed!', 'success');
    } else {
      this.log('Quality gates failed:', 'error');
      issues.forEach(issue => this.log(`  - ${issue}`, 'error'));
    }

    return passed;
  }

  calculateOverallCoverage() {
    const coverages = Object.values(this.testResults)
      .map(result => result.coverage)
      .filter(coverage => coverage > 0);
    
    return coverages.length > 0 
      ? coverages.reduce((sum, coverage) => sum + coverage, 0) / coverages.length
      : 0;
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: Object.values(this.testResults).reduce((sum, result) => sum + result.passed + result.failed, 0),
        passedTests: Object.values(this.testResults).reduce((sum, result) => sum + result.passed, 0),
        failedTests: Object.values(this.testResults).reduce((sum, result) => sum + result.failed, 0),
        overallCoverage: this.calculateOverallCoverage(),
        qualityGatesPassed: this.checkQualityGates()
      },
      testResults: this.testResults,
      coverageThresholds: this.coverageThresholds,
      qualityGates: this.qualityGates
    };

    const reportPath = `test-automation-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    return { report, reportPath };
  }

  displaySummary(report) {
    console.log('\n=== TEST AUTOMATION SUMMARY ===');
    console.log(`Total tests: ${report.summary.totalTests}`);
    console.log(`Passed: ${report.summary.passedTests}`);
    console.log(`Failed: ${report.summary.failedTests}`);
    console.log(`Overall coverage: ${report.summary.overallCoverage.toFixed(2)}%`);
    console.log(`Quality gates: ${report.summary.qualityGatesPassed ? 'PASSED' : 'FAILED'}`);
    
    console.log('\nTest Results by Category:');
    Object.entries(this.testResults).forEach(([category, results]) => {
      if (results.passed > 0 || results.failed > 0) {
        console.log(`  ${category}: ${results.passed} passed, ${results.failed} failed, ${results.coverage.toFixed(2)}% coverage`);
      }
    });
  }

  async run() {
    const startTime = Date.now();
    this.log('Starting comprehensive test automation...');
    
    try {
      // Setup
      await this.setupTestEnvironment();
      
      // Run all test suites
      await this.runUnitTests();
      await this.runIntegrationTests();
      await this.runAccessibilityTests();
      await this.runAnalyticsTests();
      await this.runConversionTests();
      
      // Generate coverage report
      await this.generateCoverageReport();
      
      // Generate and display final report
      const { report, reportPath } = this.generateReport();
      this.displaySummary(report);
      
      const duration = (Date.now() - startTime) / 1000;
      this.log(`Test automation completed in ${duration.toFixed(2)} seconds`);
      this.log(`Detailed report saved to: ${reportPath}`);
      
      // Exit with appropriate code
      process.exit(report.summary.qualityGatesPassed ? 0 : 1);
      
    } catch (error) {
      this.log(`Test automation failed: ${error.message}`, 'error');
      process.exit(1);
    }
  }
}

// Run the test automation
if (require.main === module) {
  const automation = new TestAutomation();
  automation.run();
}

module.exports = TestAutomation;
