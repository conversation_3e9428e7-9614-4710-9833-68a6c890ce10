{"timestamp": "2025-06-26T00:25:51.005Z", "summary": {"indexFiles": 1, "missingIndexDirs": 8, "barrelExportIssues": 0, "totalFiles": 432, "inconsistentImportPatterns": 69, "definedAliases": 0, "usedAliases": 0, "unusedAliases": 0}, "findings": {"barrelExports": {"indexFiles": [{"directory": "components/ui", "path": "components\\ui\\index.ts", "hasBarrelExports": true}], "missingIndexDirs": ["app", "components", "lib", "hooks", "utils", "types", "services", "stores"], "barrelExportIssues": []}, "importConsistency": {"totalFiles": 432, "relativeImports": 249, "absoluteImports": 139, "aliasedImports": 820, "externalImports": 734, "inconsistentPatterns": [{"file": "app/layout.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 12}, {"file": "components/achievements/AchievementGrid.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 5}, {"file": "components/admin/AdminDashboard.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 2}, {"file": "components/admin/AdminGuard.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 2}, {"file": "components/admin/AdminLayout.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 3}, {"file": "components/admin/AuditLogViewer.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 3}, {"file": "components/admin/CommunityControls.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 3}, {"file": "components/admin/CommunityManagement.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 3}, {"file": "components/admin/ContentManagement.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 3}, {"file": "components/admin/ContentVersionControl.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 2}, {"file": "components/admin/SafetyConfirmation.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 3}, {"file": "components/admin/SecurityManagement.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 3}, {"file": "components/admin/UserAnalytics.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 1}, {"file": "components/admin/UserManagement.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 3}, {"file": "components/auth/AuthTesting.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 11}, {"file": "components/auth/ProtectedRoute.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 10}, {"file": "components/collaboration/CollaborationChat.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 1}, {"file": "components/collaboration/CollaborationHub.tsx", "issue": "Mixes relative and aliased imports", "relative": 3, "aliased": 9}, {"file": "components/collaboration/ComprehensiveCollaborationDashboard.tsx", "issue": "Mixes relative and aliased imports", "relative": 3, "aliased": 6}, {"file": "components/collaboration/ConnectionStatusIndicator.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 2}, {"file": "components/collaboration/FileSharing.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 1}, {"file": "components/collaboration/SessionRecovery.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 1}, {"file": "components/collaboration/UserPresencePanel.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 2}, {"file": "components/community/CommunityHub.tsx", "issue": "Mixes relative and aliased imports", "relative": 4, "aliased": 3}, {"file": "components/community/CommunityStats.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 3}, {"file": "components/community/Leaderboards.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 4}, {"file": "components/curriculum/CurriculumDashboard.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 5}, {"file": "components/editor/AdvancedIDEInterface.tsx", "issue": "Mixes relative and aliased imports", "relative": 3, "aliased": 2}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 2}, {"file": "components/learning/ComprehensiveLearningPlatform.tsx", "issue": "Mixes relative and aliased imports", "relative": 10, "aliased": 2}, {"file": "components/learning/GamificationSystem.tsx", "issue": "Mixes relative and aliased imports", "relative": 3, "aliased": 2}, {"file": "components/learning/InteractiveCodeEditor.tsx", "issue": "Mixes relative and aliased imports", "relative": 6, "aliased": 19}, {"file": "components/learning/LessonProgressTracker.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 2}, {"file": "components/onboarding/InteractiveTutorial.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "components/settings/SettingsPage.tsx", "issue": "Mixes relative and aliased imports", "relative": 6, "aliased": 3}, {"file": "components/settings/__tests__/ProfileSection.test.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "components/settings/__tests__/SecuritySection.test.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "components/settings/__tests__/SettingsPage.test.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "components/ui/ButtonTesting.tsx", "issue": "Mixes relative and aliased imports", "relative": 5, "aliased": 1}, {"file": "components/ui/ErrorMessage.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 2}, {"file": "components/ui/ErrorTesting.tsx", "issue": "Mixes relative and aliased imports", "relative": 3, "aliased": 4}, {"file": "components/ui/FeedbackIndicators.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 2}, {"file": "components/ui/GlassmorphismButtons.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "components/ui/LazyLoadingComponents.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 3}, {"file": "components/ui/NotificationCenter.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 2}, {"file": "components/ui/NotificationHistory.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 1}, {"file": "components/ui/NotificationPreferences.tsx", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 1}, {"file": "components/ui/NotificationSystem.tsx", "issue": "Mixes relative and aliased imports", "relative": 3, "aliased": 4}, {"file": "components/ui/SessionStatusIndicator.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 3}, {"file": "components/ui/UserAvatar.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 7}, {"file": "components/xp/LevelUpCelebration.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 3}, {"file": "lib/achievements/manager.ts", "issue": "Mixes relative and aliased imports", "relative": 2, "aliased": 1}, {"file": "lib/api/integration.ts", "issue": "Mixes relative and aliased imports", "relative": 3, "aliased": 4}, {"file": "lib/api/logging.ts", "issue": "Mixes relative and aliased imports", "relative": 3, "aliased": 1}, {"file": "lib/database/data-removal.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 2}, {"file": "lib/database/maintenance.ts", "issue": "Mixes relative and aliased imports", "relative": 4, "aliased": 1}, {"file": "lib/database/migrations.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "lib/database/orphaned-data.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "lib/errors/ErrorContext.tsx", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "lib/hooks/useAchievements.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 3}, {"file": "lib/hooks/useAdvancedCollaborativeEditor.ts", "issue": "Mixes relative and aliased imports", "relative": 3, "aliased": 1}, {"file": "lib/hooks/useRealTimeXP.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 4}, {"file": "lib/hooks/useSessionStatus.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 3}, {"file": "lib/hooks/useSolidityAnalyzer.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "lib/hooks/useSolidityDebugger.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "lib/hooks/useSolidityVersionControl.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "lib/monitoring/analytics.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "lib/monitoring/errorTracking.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 1}, {"file": "lib/security/middleware.ts", "issue": "Mixes relative and aliased imports", "relative": 1, "aliased": 2}]}, "pathAliases": {"definedAliases": [], "usedAliases": {}, "unusedAliases": [], "invalidAliases": [{"file": "app/achievements/page.tsx", "alias": "@/components/auth/ProtectedRoute"}, {"file": "app/achievements/page.tsx", "alias": "@/components/achievements/AchievementGrid"}, {"file": "app/achievements/page.tsx", "alias": "@/components/achievements/AchievementNotification"}, {"file": "app/achievements/page.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "app/achievements/page.tsx", "alias": "@/lib/hooks/useAchievements"}, {"file": "app/achievements/page.tsx", "alias": "@/lib/achievements/types"}, {"file": "app/admin/audit/page.tsx", "alias": "@/components/admin/AdminLayout"}, {"file": "app/admin/audit/page.tsx", "alias": "@/components/admin/AdminGuard"}, {"file": "app/admin/audit/page.tsx", "alias": "@/components/admin/AuditLogViewer"}, {"file": "app/admin/audit/page.tsx", "alias": "@/lib/admin/auth"}, {"file": "app/admin/community/page.tsx", "alias": "@/components/admin/AdminLayout"}, {"file": "app/admin/community/page.tsx", "alias": "@/components/admin/AdminGuard"}, {"file": "app/admin/community/page.tsx", "alias": "@/components/admin/CommunityManagement"}, {"file": "app/admin/community/page.tsx", "alias": "@/lib/admin/auth"}, {"file": "app/admin/content/page.tsx", "alias": "@/components/admin/AdminLayout"}, {"file": "app/admin/content/page.tsx", "alias": "@/components/admin/AdminGuard"}, {"file": "app/admin/content/page.tsx", "alias": "@/components/admin/ContentManagement"}, {"file": "app/admin/content/page.tsx", "alias": "@/lib/admin/auth"}, {"file": "app/admin/page.tsx", "alias": "@/components/admin/AdminLayout"}, {"file": "app/admin/page.tsx", "alias": "@/components/admin/AdminGuard"}, {"file": "app/admin/page.tsx", "alias": "@/components/admin/AdminDashboard"}, {"file": "app/admin/page.tsx", "alias": "@/lib/admin/auth"}, {"file": "app/admin/security/page.tsx", "alias": "@/components/admin/AdminLayout"}, {"file": "app/admin/security/page.tsx", "alias": "@/components/admin/AdminGuard"}, {"file": "app/admin/security/page.tsx", "alias": "@/components/admin/SecurityManagement"}, {"file": "app/admin/security/page.tsx", "alias": "@/lib/admin/auth"}, {"file": "app/admin/users/page.tsx", "alias": "@/components/admin/AdminLayout"}, {"file": "app/admin/users/page.tsx", "alias": "@/components/admin/AdminGuard"}, {"file": "app/admin/users/page.tsx", "alias": "@/components/admin/UserManagement"}, {"file": "app/admin/users/page.tsx", "alias": "@/lib/admin/auth"}, {"file": "app/api/achievements/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/achievements/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/ai/assistant/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/ai/assistant/route.ts", "alias": "@/lib/ai/LearningAssistant"}, {"file": "app/api/ai/assistant/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/auth/login/route.ts", "alias": "@/lib/api/utils"}, {"file": "app/api/auth/login/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/auth/register/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/auth/register/route.ts", "alias": "@/lib/auth/password"}, {"file": "app/api/auth/register/route.ts", "alias": "@/lib/monitoring/logger"}, {"file": "app/api/auth/register/route.ts", "alias": "@/lib/security/rateLimiting"}, {"file": "app/api/auth/register/route.ts", "alias": "@/lib/api/utils"}, {"file": "app/api/auth/register/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/chat/delete/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/chat/delete/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/chat/pin/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/chat/pin/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/chat/reactions/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/chat/reactions/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/collaboration/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/collaboration/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/community/enhanced-stats/route.ts", "alias": "@/lib/community/types"}, {"file": "app/api/community/leaderboard/categories/route.ts", "alias": "@/lib/community/types"}, {"file": "app/api/community/leaderboard/route.ts", "alias": "@/lib/community/types"}, {"file": "app/api/community/milestones/route.ts", "alias": "@/lib/community/types"}, {"file": "app/api/community/stats/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/community/stats/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/community/trending/route.ts", "alias": "@/lib/community/types"}, {"file": "app/api/compile/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/compile/route.ts", "alias": "@/lib/compiler/SolidityCompiler"}, {"file": "app/api/compile/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/contact/submit/route.ts", "alias": "@/lib/monitoring/logger"}, {"file": "app/api/contact/submit/route.ts", "alias": "@/lib/monitoring/analytics"}, {"file": "app/api/contact/submit/route.ts", "alias": "@/lib/security/validation"}, {"file": "app/api/courses/route.ts", "alias": "@/lib/api/utils"}, {"file": "app/api/courses/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/courses/[id]/route.ts", "alias": "@/lib/api/utils"}, {"file": "app/api/courses/[id]/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/deployments/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/errors/route.ts", "alias": "@/lib/api/utils"}, {"file": "app/api/errors/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/errors/route.ts", "alias": "@/lib/monitoring/error-tracking"}, {"file": "app/api/errors/route.ts", "alias": "@/lib/api/logging"}, {"file": "app/api/feedback/submit/route.ts", "alias": "@/lib/monitoring/logger"}, {"file": "app/api/feedback/submit/route.ts", "alias": "@/lib/monitoring/analytics"}, {"file": "app/api/feedback/submit/route.ts", "alias": "@/lib/security/validation"}, {"file": "app/api/feedback/submit/route.ts", "alias": "@/lib/prisma-client-wrapper"}, {"file": "app/api/health/route.ts", "alias": "@/lib/config/environment"}, {"file": "app/api/health/route.ts", "alias": "@/lib/monitoring/apiPerformance"}, {"file": "app/api/health/route.ts", "alias": "@/lib/monitoring/logger"}, {"file": "app/api/health/route.ts", "alias": "@/lib/monitoring/errorTracking"}, {"file": "app/api/health/route.ts", "alias": "@/lib/monitoring/analytics"}, {"file": "app/api/health/route.ts", "alias": "@/lib/security/rateLimiting"}, {"file": "app/api/health/route.ts", "alias": "@/lib/security/session"}, {"file": "app/api/leaderboard/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/leaderboard/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/learning-paths/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/learning-paths/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/metrics/route.ts", "alias": "@/lib/api/utils"}, {"file": "app/api/metrics/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/metrics/route.ts", "alias": "@/lib/monitoring/error-tracking"}, {"file": "app/api/metrics/route.ts", "alias": "@/lib/api/logging"}, {"file": "app/api/monitoring/performance/route.ts", "alias": "@/lib/monitoring/apiPerformance"}, {"file": "app/api/projects/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/projects/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/settings/route.ts", "alias": "@/lib/api/utils"}, {"file": "app/api/settings/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/uat/dashboard/route.ts", "alias": "@/lib/monitoring/logger"}, {"file": "app/api/uat/dashboard/route.ts", "alias": "@/lib/monitoring/analytics"}, {"file": "app/api/uat/dashboard/route.ts", "alias": "@/lib/prisma-client-wrapper"}, {"file": "app/api/user/activity-feed/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/user/activity-feed/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/user/code-stats/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/user/code-stats/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/user/community-stats/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/user/community-stats/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/user/profile/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/user/profile/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/user/progress/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/user/progress/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/user/progress-stats/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/user/progress-stats/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/user/study-schedule/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/user/study-schedule/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/user/xp/route.ts", "alias": "@/lib/auth/config"}, {"file": "app/api/user/xp/route.ts", "alias": "@/lib/prisma"}, {"file": "app/api/v1/admin/maintenance/route.ts", "alias": "@/lib/api/middleware"}, {"file": "app/api/v1/admin/maintenance/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/admin/maintenance/route.ts", "alias": "@/lib/api/validation"}, {"file": "app/api/v1/admin/maintenance/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/v1/admin/maintenance/route.ts", "alias": "@/lib/database/maintenance"}, {"file": "app/api/v1/admin/maintenance/route.ts", "alias": "@/lib/database/cleanup"}, {"file": "app/api/v1/admin/maintenance/route.ts", "alias": "@/lib/database/migrations"}, {"file": "app/api/v1/admin/maintenance/schedules/route.ts", "alias": "@/lib/api/middleware"}, {"file": "app/api/v1/admin/maintenance/schedules/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/admin/maintenance/schedules/route.ts", "alias": "@/lib/api/validation"}, {"file": "app/api/v1/admin/maintenance/schedules/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/v1/admin/maintenance/schedules/route.ts", "alias": "@/lib/database/maintenance"}, {"file": "app/api/v1/admin/maintenance/schedules/[id]/route.ts", "alias": "@/lib/api/middleware"}, {"file": "app/api/v1/admin/maintenance/schedules/[id]/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/admin/maintenance/schedules/[id]/route.ts", "alias": "@/lib/api/validation"}, {"file": "app/api/v1/admin/maintenance/schedules/[id]/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/v1/admin/maintenance/schedules/[id]/route.ts", "alias": "@/lib/database/maintenance"}, {"file": "app/api/v1/auth/login/route.ts", "alias": "@/lib/api/middleware"}, {"file": "app/api/v1/auth/login/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/auth/login/route.ts", "alias": "@/lib/api/validation"}, {"file": "app/api/v1/auth/login/route.ts", "alias": "@/lib/api/auth"}, {"file": "app/api/v1/auth/login/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/v1/auth/refresh/route.ts", "alias": "@/lib/api/middleware"}, {"file": "app/api/v1/auth/refresh/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/auth/refresh/route.ts", "alias": "@/lib/api/validation"}, {"file": "app/api/v1/auth/refresh/route.ts", "alias": "@/lib/api/auth"}, {"file": "app/api/v1/auth/refresh/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/v1/auth/register/route.ts", "alias": "@/lib/api/middleware"}, {"file": "app/api/v1/auth/register/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/auth/register/route.ts", "alias": "@/lib/api/validation"}, {"file": "app/api/v1/auth/register/route.ts", "alias": "@/lib/api/auth"}, {"file": "app/api/v1/auth/register/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/v1/health/route.ts", "alias": "@/lib/api/middleware"}, {"file": "app/api/v1/health/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/health/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/v1/lessons/route.ts", "alias": "@/lib/api/middleware"}, {"file": "app/api/v1/lessons/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/lessons/route.ts", "alias": "@/lib/api/validation"}, {"file": "app/api/v1/lessons/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/v1/lessons/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/users/route.ts", "alias": "@/lib/api/middleware"}, {"file": "app/api/v1/users/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/users/route.ts", "alias": "@/lib/api/validation"}, {"file": "app/api/v1/users/route.ts", "alias": "@/lib/api/auth"}, {"file": "app/api/v1/users/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/v1/users/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/users/[id]/route.ts", "alias": "@/lib/api/middleware"}, {"file": "app/api/v1/users/[id]/route.ts", "alias": "@/lib/api/response"}, {"file": "app/api/v1/users/[id]/route.ts", "alias": "@/lib/api/validation"}, {"file": "app/api/v1/users/[id]/route.ts", "alias": "@/lib/api/types"}, {"file": "app/api/v1/users/[id]/route.ts", "alias": "@/lib/api/response"}, {"file": "app/auth/demo/page.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "app/auth/local-test/page.tsx", "alias": "@/lib/auth/mock-auth"}, {"file": "app/auth/local-test/page.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "app/auth/test/page.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "app/auth/test/page.tsx", "alias": "@/components/auth/AuthModal"}, {"file": "app/auth/test/page.tsx", "alias": "@/components/auth/ProtectedRoute"}, {"file": "app/auth/test/page.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "app/auth-testing/page.tsx", "alias": "@/lib/errors/ErrorContext"}, {"file": "app/auth-testing/page.tsx", "alias": "@/components/auth/AuthTesting"}, {"file": "app/button-testing/page.tsx", "alias": "@/components/ui/ButtonTesting"}, {"file": "app/button-testing/page.tsx", "alias": "@/components/ui/GlassmorphismButtons"}, {"file": "app/code/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/collaborate/page.tsx", "alias": "@/components/collaboration/CollaborationHub"}, {"file": "app/collaborate/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/community/page.tsx", "alias": "@/components/community/CommunityHub"}, {"file": "app/community/page.tsx", "alias": "@/components/auth/ProtectedRoute"}, {"file": "app/cookies/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/dashboard/page.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "app/dashboard/page.tsx", "alias": "@/lib/context/LearningContext"}, {"file": "app/dashboard/page.tsx", "alias": "@/lib/hooks/useRealTimeXP"}, {"file": "app/dashboard/page.tsx", "alias": "@/lib/curriculum/manager"}, {"file": "app/dashboard/page.tsx", "alias": "@/lib/curriculum/data"}, {"file": "app/dashboard/page.tsx", "alias": "@/lib/curriculum/types"}, {"file": "app/dashboard/page.tsx", "alias": "@/components/curriculum/CurriculumDashboard"}, {"file": "app/dashboard/page.tsx", "alias": "@/components/curriculum/LearningAnalytics"}, {"file": "app/dashboard/page.tsx", "alias": "@/components/curriculum/LearningPathVisualization"}, {"file": "app/dashboard/page.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "app/dashboard/page.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "app/dashboard/page.tsx", "alias": "@/components/auth/ProtectedRoute"}, {"file": "app/dashboard/page.tsx", "alias": "@/lib/utils"}, {"file": "app/documentation/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/error-testing/page.tsx", "alias": "@/lib/errors/ErrorContext"}, {"file": "app/error-testing/page.tsx", "alias": "@/components/ui/ErrorTesting"}, {"file": "app/examples/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/instructor/page.tsx", "alias": "@/components/auth/ProtectedRoute"}, {"file": "app/instructor/page.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "app/layout.tsx", "alias": "@/components/ui/toaster"}, {"file": "app/layout.tsx", "alias": "@/components/layout/Navigation"}, {"file": "app/layout.tsx", "alias": "@/components/layout/Footer"}, {"file": "app/layout.tsx", "alias": "@/lib/errors/ErrorContext"}, {"file": "app/layout.tsx", "alias": "@/components/errors/ErrorBoundary"}, {"file": "app/layout.tsx", "alias": "@/components/ui/Accessibility"}, {"file": "app/layout.tsx", "alias": "@/components/dev/AccessibilityTester"}, {"file": "app/layout.tsx", "alias": "@/components/performance/PerformanceOptimizer"}, {"file": "app/layout.tsx", "alias": "@/components/performance/ServiceWorkerManager"}, {"file": "app/layout.tsx", "alias": "@/components/monitoring/PerformanceMonitor"}, {"file": "app/layout.tsx", "alias": "@/components/help/HelpProvider"}, {"file": "app/layout.tsx", "alias": "@/components/discovery/DiscoveryProvider"}, {"file": "app/learn/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/mentor/page.tsx", "alias": "@/components/auth/ProtectedRoute"}, {"file": "app/mentor/page.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "app/not-found.tsx", "alias": "@/components/error-handling/NotFoundPage"}, {"file": "app/page.tsx", "alias": "@/components/sections/HeroSection"}, {"file": "app/page.tsx", "alias": "@/components/sections/FeaturesSection"}, {"file": "app/page.tsx", "alias": "@/components/sections/CompetitiveAnalysisSection"}, {"file": "app/page.tsx", "alias": "@/components/sections/TestimonialsSection"}, {"file": "app/page.tsx", "alias": "@/components/sections/CTASection"}, {"file": "app/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/page.tsx", "alias": "@/components/sections/EnhancedFeaturesShowcase"}, {"file": "app/page.tsx", "alias": "@/components/sections/GamificationPreview"}, {"file": "app/page.tsx", "alias": "@/components/sections/InteractiveDemoSection"}, {"file": "app/privacy/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/profile/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/profile/page.tsx", "alias": "@/components/auth/ProtectedRoute"}, {"file": "app/providers.tsx", "alias": "@/lib/socket/SocketProvider"}, {"file": "app/providers.tsx", "alias": "@/lib/context/LearningContext"}, {"file": "app/providers.tsx", "alias": "@/lib/context/CollaborationContext"}, {"file": "app/providers.tsx", "alias": "@/lib/blockchain/Web3Provider"}, {"file": "app/session-expired/page.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "app/session-expired/page.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "app/session-expired/page.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "app/session-expired/page.tsx", "alias": "@/lib/auth/sessionManager"}, {"file": "app/session-expired/page.tsx", "alias": "@/components/errors/SpecializedErrorBoundaries"}, {"file": "app/session-expired/page.tsx", "alias": "@/components/auth/AuthModal"}, {"file": "app/settings/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/terms/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/tutorials/page.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "app/unauthorized/page.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "app/unauthorized/page.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "app/unauthorized/page.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "app/unauthorized/page.tsx", "alias": "@/components/errors/SpecializedErrorBoundaries"}, {"file": "app/unauthorized/page.tsx", "alias": "@/lib/utils"}, {"file": "components/achievements/AchievementCard.tsx", "alias": "@/lib/achievements/types"}, {"file": "components/achievements/AchievementCard.tsx", "alias": "@/lib/achievements/data"}, {"file": "components/achievements/AchievementCard.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/achievements/AchievementCard.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/achievements/AchievementCard.tsx", "alias": "@/lib/utils"}, {"file": "components/achievements/AchievementGrid.tsx", "alias": "@/lib/achievements/types"}, {"file": "components/achievements/AchievementGrid.tsx", "alias": "@/lib/achievements/data"}, {"file": "components/achievements/AchievementGrid.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/achievements/AchievementGrid.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/achievements/AchievementGrid.tsx", "alias": "@/lib/utils"}, {"file": "components/achievements/AchievementNotification.tsx", "alias": "@/lib/achievements/types"}, {"file": "components/achievements/AchievementNotification.tsx", "alias": "@/lib/achievements/data"}, {"file": "components/achievements/AchievementNotification.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/achievements/AchievementNotification.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/achievements/AchievementNotification.tsx", "alias": "@/lib/utils"}, {"file": "components/achievements/AchievementNotificationSystem.tsx", "alias": "@/components/auth/EnhancedAuthProvider"}, {"file": "components/achievements/AchievementNotificationSystem.tsx", "alias": "@/components/ui/NotificationSystem"}, {"file": "components/achievements/AchievementNotificationSystem.tsx", "alias": "@/lib/animations/micro-interactions"}, {"file": "components/achievements/AchievementNotificationSystem.tsx", "alias": "@/lib/utils"}, {"file": "components/achievements/AchievementsPage.tsx", "alias": "@/components/ui/button"}, {"file": "components/achievements/AchievementsPage.tsx", "alias": "@/components/ui/progress"}, {"file": "components/achievements/AchievementsPage.tsx", "alias": "@/components/ui/badge"}, {"file": "components/achievements/AchievementsPage.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/admin/AdminDashboard.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/AdminDashboard.tsx", "alias": "@/lib/admin/types"}, {"file": "components/admin/AdminGuard.tsx", "alias": "@/lib/admin/auth"}, {"file": "components/admin/AdminGuard.tsx", "alias": "@/lib/admin/types"}, {"file": "components/admin/AdminLayout.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/AdminLayout.tsx", "alias": "@/lib/admin/auth"}, {"file": "components/admin/AdminLayout.tsx", "alias": "@/lib/admin/types"}, {"file": "components/admin/AuditLogViewer.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/AuditLogViewer.tsx", "alias": "@/lib/admin/types"}, {"file": "components/admin/AuditLogViewer.tsx", "alias": "@/lib/admin/auditLogger"}, {"file": "components/admin/CommunityControls.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/CommunityControls.tsx", "alias": "@/lib/community/types"}, {"file": "components/admin/CommunityControls.tsx", "alias": "@/lib/admin/auth"}, {"file": "components/admin/CommunityManagement.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/CommunityManagement.tsx", "alias": "@/lib/admin/types"}, {"file": "components/admin/CommunityManagement.tsx", "alias": "@/lib/admin/auth"}, {"file": "components/admin/ContentManagement.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/ContentManagement.tsx", "alias": "@/lib/admin/types"}, {"file": "components/admin/ContentManagement.tsx", "alias": "@/lib/admin/auth"}, {"file": "components/admin/ContentVersionControl.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/ContentVersionControl.tsx", "alias": "@/lib/admin/types"}, {"file": "components/admin/PerformanceDashboard.tsx", "alias": "@/lib/monitoring/apiPerformance"}, {"file": "components/admin/SafetyConfirmation.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/SafetyConfirmation.tsx", "alias": "@/lib/admin/types"}, {"file": "components/admin/SafetyConfirmation.tsx", "alias": "@/lib/admin/auth"}, {"file": "components/admin/SecurityManagement.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/SecurityManagement.tsx", "alias": "@/lib/admin/types"}, {"file": "components/admin/SecurityManagement.tsx", "alias": "@/lib/admin/auth"}, {"file": "components/admin/UserAnalytics.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/UserManagement.tsx", "alias": "@/lib/utils"}, {"file": "components/admin/UserManagement.tsx", "alias": "@/lib/admin/types"}, {"file": "components/admin/UserManagement.tsx", "alias": "@/lib/admin/auth"}, {"file": "components/auth/AuthModal.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/auth/AuthModal.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/auth/AuthModal.tsx", "alias": "@/components/ui/ErrorMessage"}, {"file": "components/auth/AuthModal.tsx", "alias": "@/components/errors/SpecializedErrorBoundaries"}, {"file": "components/auth/AuthModal.tsx", "alias": "@/lib/errors/ErrorContext"}, {"file": "components/auth/AuthModal.tsx", "alias": "@/lib/hooks/useErrorRecovery"}, {"file": "components/auth/AuthModal.tsx", "alias": "@/lib/errors/types"}, {"file": "components/auth/AuthModal.tsx", "alias": "@/lib/auth/password"}, {"file": "components/auth/AuthModal.tsx", "alias": "@/components/ui/AccessibleForm"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/components/ui/card"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/lib/errors/types"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/lib/errors/ErrorContext"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/lib/hooks/useErrorRecovery"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/lib/hooks/useSessionStatus"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/lib/hooks/useRealTimeXP"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/lib/curriculum/manager"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/lib/curriculum/data"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/lib/auth/sessionManager"}, {"file": "components/auth/AuthTesting.tsx", "alias": "@/lib/utils"}, {"file": "components/auth/PasswordResetModal.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/auth/PasswordResetModal.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/auth/PasswordResetModal.tsx", "alias": "@/components/ui/ErrorMessage"}, {"file": "components/auth/PasswordResetModal.tsx", "alias": "@/components/errors/SpecializedErrorBoundaries"}, {"file": "components/auth/PasswordResetModal.tsx", "alias": "@/lib/errors/ErrorContext"}, {"file": "components/auth/PasswordResetModal.tsx", "alias": "@/lib/hooks/useErrorRecovery"}, {"file": "components/auth/PasswordResetModal.tsx", "alias": "@/lib/errors/types"}, {"file": "components/auth/PasswordStrengthIndicator.tsx", "alias": "@/lib/utils"}, {"file": "components/auth/ProtectedRoute.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "components/auth/ProtectedRoute.tsx", "alias": "@/lib/auth/sessionManager"}, {"file": "components/auth/ProtectedRoute.tsx", "alias": "@/components/errors/SpecializedErrorBoundaries"}, {"file": "components/auth/ProtectedRoute.tsx", "alias": "@/components/ui/ErrorMessage"}, {"file": "components/auth/ProtectedRoute.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/auth/ProtectedRoute.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/auth/ProtectedRoute.tsx", "alias": "@/lib/errors/types"}, {"file": "components/auth/ProtectedRoute.tsx", "alias": "@/lib/errors/ErrorContext"}, {"file": "components/auth/ProtectedRoute.tsx", "alias": "@/components/ui/LoadingSpinner"}, {"file": "components/auth/ProtectedRoute.tsx", "alias": "@/lib/utils"}, {"file": "components/blockchain/ContractDeployer.tsx", "alias": "@/components/ui/button"}, {"file": "components/blockchain/ContractDeployer.tsx", "alias": "@/components/ui/card"}, {"file": "components/blockchain/ContractDeployer.tsx", "alias": "@/components/ui/input"}, {"file": "components/blockchain/ContractDeployer.tsx", "alias": "@/components/ui/label"}, {"file": "components/blockchain/ContractDeployer.tsx", "alias": "@/components/ui/textarea"}, {"file": "components/blockchain/ContractDeployer.tsx", "alias": "@/components/ui/tabs"}, {"file": "components/blockchain/ContractDeployer.tsx", "alias": "@/lib/blockchain/Web3Provider"}, {"file": "components/blockchain/ContractDeployer.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/blockchain/WalletConnect.tsx", "alias": "@/components/ui/button"}, {"file": "components/blockchain/WalletConnect.tsx", "alias": "@/components/ui/card"}, {"file": "components/blockchain/WalletConnect.tsx", "alias": "@/components/ui/badge"}, {"file": "components/blockchain/WalletConnect.tsx", "alias": "@/lib/blockchain/Web3Provider"}, {"file": "components/blockchain/WalletConnect.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/code/CodeLab.tsx", "alias": "@/components/ui/button"}, {"file": "components/code/CodeLab.tsx", "alias": "@/components/ui/card"}, {"file": "components/code/CodeLab.tsx", "alias": "@/components/ui/tabs"}, {"file": "components/code/CodeLab.tsx", "alias": "@/components/blockchain/WalletConnect"}, {"file": "components/code/CodeLab.tsx", "alias": "@/components/blockchain/ContractDeployer"}, {"file": "components/code/CodeLab.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/collaboration/AdvancedUserPresence.tsx", "alias": "@/components/ui/card"}, {"file": "components/collaboration/AdvancedUserPresence.tsx", "alias": "@/components/ui/badge"}, {"file": "components/collaboration/AdvancedUserPresence.tsx", "alias": "@/components/ui/button"}, {"file": "components/collaboration/AdvancedUserPresence.tsx", "alias": "@/components/ui/progress"}, {"file": "components/collaboration/AdvancedUserPresence.tsx", "alias": "@/lib/socket/client"}, {"file": "components/collaboration/AdvancedUserPresence.tsx", "alias": "@/components/auth/EnhancedAuthProvider"}, {"file": "components/collaboration/CollaborationChat.tsx", "alias": "@/lib/utils"}, {"file": "components/collaboration/CollaborationHub.tsx", "alias": "@/lib/socket/client"}, {"file": "components/collaboration/CollaborationHub.tsx", "alias": "@/components/ui/button"}, {"file": "components/collaboration/CollaborationHub.tsx", "alias": "@/components/ui/card"}, {"file": "components/collaboration/CollaborationHub.tsx", "alias": "@/components/ui/input"}, {"file": "components/collaboration/CollaborationHub.tsx", "alias": "@/components/ui/badge"}, {"file": "components/collaboration/CollaborationHub.tsx", "alias": "@/components/ui/dialog"}, {"file": "components/collaboration/CollaborationHub.tsx", "alias": "@/components/ui/label"}, {"file": "components/collaboration/CollaborationHub.tsx", "alias": "@/components/ui/select"}, {"file": "components/collaboration/CollaborationHub.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/collaboration/CollaborativeEditor.tsx", "alias": "@/components/ui/button"}, {"file": "components/collaboration/CollaborativeEditor.tsx", "alias": "@/components/ui/card"}, {"file": "components/collaboration/CollaborativeEditor.tsx", "alias": "@/components/ui/badge"}, {"file": "components/collaboration/CollaborativeEditor.tsx", "alias": "@/components/ui/input"}, {"file": "components/collaboration/CollaborativeEditor.tsx", "alias": "@/components/ui/scroll-area"}, {"file": "components/collaboration/CollaborativeEditor.tsx", "alias": "@/lib/context/CollaborationContext"}, {"file": "components/collaboration/CollaborativeEditor.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/collaboration/ComprehensiveCollaborationDashboard.tsx", "alias": "@/components/ui/button"}, {"file": "components/collaboration/ComprehensiveCollaborationDashboard.tsx", "alias": "@/components/ui/card"}, {"file": "components/collaboration/ComprehensiveCollaborationDashboard.tsx", "alias": "@/components/ui/tabs"}, {"file": "components/collaboration/ComprehensiveCollaborationDashboard.tsx", "alias": "@/lib/socket/client"}, {"file": "components/collaboration/ComprehensiveCollaborationDashboard.tsx", "alias": "@/components/auth/EnhancedAuthProvider"}, {"file": "components/collaboration/ComprehensiveCollaborationDashboard.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/collaboration/ConnectionStatusIndicator.tsx", "alias": "@/lib/collaboration/CollaborationClient"}, {"file": "components/collaboration/ConnectionStatusIndicator.tsx", "alias": "@/lib/utils"}, {"file": "components/collaboration/FileSharing.tsx", "alias": "@/lib/utils"}, {"file": "components/collaboration/LiveChatSystem.tsx", "alias": "@/components/ui/button"}, {"file": "components/collaboration/LiveChatSystem.tsx", "alias": "@/components/ui/input"}, {"file": "components/collaboration/LiveChatSystem.tsx", "alias": "@/components/ui/card"}, {"file": "components/collaboration/LiveChatSystem.tsx", "alias": "@/components/ui/badge"}, {"file": "components/collaboration/LiveChatSystem.tsx", "alias": "@/lib/socket/client"}, {"file": "components/collaboration/LiveChatSystem.tsx", "alias": "@/components/auth/EnhancedAuthProvider"}, {"file": "components/collaboration/LiveChatSystem.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/collaboration/MonacoCollaborativeEditor.tsx", "alias": "@/components/ui/button"}, {"file": "components/collaboration/MonacoCollaborativeEditor.tsx", "alias": "@/components/ui/badge"}, {"file": "components/collaboration/MonacoCollaborativeEditor.tsx", "alias": "@/lib/socket/client"}, {"file": "components/collaboration/MonacoCollaborativeEditor.tsx", "alias": "@/components/auth/EnhancedAuthProvider"}, {"file": "components/collaboration/MonacoCollaborativeEditor.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/collaboration/MonacoCollaborativeEditor.tsx", "alias": "@/lib/utils/accessibility"}, {"file": "components/collaboration/RealTimeCodeEditor.tsx", "alias": "@/components/ui/card"}, {"file": "components/collaboration/RealTimeCodeEditor.tsx", "alias": "@/components/ui/button"}, {"file": "components/collaboration/RealTimeCodeEditor.tsx", "alias": "@/components/ui/input"}, {"file": "components/collaboration/RealTimeCodeEditor.tsx", "alias": "@/components/ui/badge"}, {"file": "components/collaboration/RealTimeCodeEditor.tsx", "alias": "@/lib/socket/client"}, {"file": "components/collaboration/RealTimeCodeEditor.tsx", "alias": "@/components/auth/EnhancedAuthProvider"}, {"file": "components/collaboration/RealTimeCodeEditor.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/collaboration/SessionRecovery.tsx", "alias": "@/lib/utils"}, {"file": "components/collaboration/UserPresenceIndicator.tsx", "alias": "@/components/ui/card"}, {"file": "components/collaboration/UserPresenceIndicator.tsx", "alias": "@/components/ui/badge"}, {"file": "components/collaboration/UserPresenceIndicator.tsx", "alias": "@/components/ui/button"}, {"file": "components/collaboration/UserPresenceIndicator.tsx", "alias": "@/lib/socket/client"}, {"file": "components/collaboration/UserPresenceIndicator.tsx", "alias": "@/components/auth/EnhancedAuthProvider"}, {"file": "components/collaboration/UserPresencePanel.tsx", "alias": "@/lib/collaboration/CollaborationClient"}, {"file": "components/collaboration/UserPresencePanel.tsx", "alias": "@/lib/utils"}, {"file": "components/community/CommunityHub.tsx", "alias": "@/lib/utils"}, {"file": "components/community/CommunityHub.tsx", "alias": "@/lib/community/websocket"}, {"file": "components/community/CommunityHub.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "components/community/CommunityStats.tsx", "alias": "@/lib/utils"}, {"file": "components/community/CommunityStats.tsx", "alias": "@/lib/community/types"}, {"file": "components/community/CommunityStats.tsx", "alias": "@/lib/community/statistics"}, {"file": "components/community/Leaderboards.tsx", "alias": "@/lib/utils"}, {"file": "components/community/Leaderboards.tsx", "alias": "@/lib/community/types"}, {"file": "components/community/Leaderboards.tsx", "alias": "@/lib/community/leaderboard"}, {"file": "components/community/Leaderboards.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "components/curriculum/CurriculumDashboard.tsx", "alias": "@/lib/curriculum/types"}, {"file": "components/curriculum/CurriculumDashboard.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/curriculum/CurriculumDashboard.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/curriculum/CurriculumDashboard.tsx", "alias": "@/components/xp/ProgressBar"}, {"file": "components/curriculum/CurriculumDashboard.tsx", "alias": "@/lib/utils"}, {"file": "components/curriculum/LearningAnalytics.tsx", "alias": "@/lib/curriculum/types"}, {"file": "components/curriculum/LearningAnalytics.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/curriculum/LearningAnalytics.tsx", "alias": "@/components/xp/ProgressBar"}, {"file": "components/curriculum/LearningAnalytics.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/curriculum/LearningAnalytics.tsx", "alias": "@/lib/utils"}, {"file": "components/curriculum/LearningPathVisualization.tsx", "alias": "@/lib/curriculum/types"}, {"file": "components/curriculum/LearningPathVisualization.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/curriculum/LearningPathVisualization.tsx", "alias": "@/components/xp/ProgressBar"}, {"file": "components/curriculum/LearningPathVisualization.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/curriculum/LearningPathVisualization.tsx", "alias": "@/lib/utils"}, {"file": "components/curriculum/LessonCard.tsx", "alias": "@/lib/curriculum/types"}, {"file": "components/curriculum/LessonCard.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/curriculum/LessonCard.tsx", "alias": "@/components/xp/ProgressBar"}, {"file": "components/curriculum/LessonCard.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/curriculum/LessonCard.tsx", "alias": "@/lib/utils"}, {"file": "components/curriculum/ModuleCard.tsx", "alias": "@/lib/curriculum/types"}, {"file": "components/curriculum/ModuleCard.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/curriculum/ModuleCard.tsx", "alias": "@/components/xp/ProgressBar"}, {"file": "components/curriculum/ModuleCard.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/curriculum/ModuleCard.tsx", "alias": "@/lib/utils"}, {"file": "components/curriculum/PrerequisiteDisplay.tsx", "alias": "@/lib/curriculum/types"}, {"file": "components/curriculum/PrerequisiteDisplay.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/curriculum/PrerequisiteDisplay.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/curriculum/PrerequisiteDisplay.tsx", "alias": "@/components/xp/ProgressBar"}, {"file": "components/curriculum/PrerequisiteDisplay.tsx", "alias": "@/lib/utils"}, {"file": "components/debugging/SolidityDebuggerInterface.tsx", "alias": "@/lib/hooks/useSolidityDebugger"}, {"file": "components/debugging/SolidityDebuggerInterface.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/debugging/SolidityDebuggerInterface.tsx", "alias": "@/lib/utils"}, {"file": "components/dev/AccessibilityTester.tsx", "alias": "@/components/ui/button"}, {"file": "components/dev/AccessibilityTester.tsx", "alias": "@/lib/accessibility/AccessibilityTester"}, {"file": "components/discovery/FeatureSpotlight.tsx", "alias": "@/lib/utils"}, {"file": "components/discovery/SmartTooltip.tsx", "alias": "@/lib/utils"}, {"file": "components/editor/AdvancedCollaborativeMonacoEditor.tsx", "alias": "@/lib/editor/MonacoSoliditySetup"}, {"file": "components/editor/AdvancedCollaborativeMonacoEditor.tsx", "alias": "@/lib/hooks/useAdvancedCollaborativeEditor"}, {"file": "components/editor/AdvancedCollaborativeMonacoEditor.tsx", "alias": "@/lib/collaboration/OperationalTransform"}, {"file": "components/editor/AdvancedCollaborativeMonacoEditor.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/editor/AdvancedCollaborativeMonacoEditor.tsx", "alias": "@/lib/utils"}, {"file": "components/editor/AdvancedIDEInterface.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/editor/AdvancedIDEInterface.tsx", "alias": "@/lib/utils"}, {"file": "components/error-handling/AsyncErrorBoundary.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/error-handling/AsyncErrorBoundary.tsx", "alias": "@/lib/monitoring/error-tracking"}, {"file": "components/error-handling/AsyncErrorBoundary.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/error-handling/ErrorBoundary.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/error-handling/ErrorBoundary.tsx", "alias": "@/lib/monitoring/error-tracking"}, {"file": "components/error-handling/ErrorBoundary.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/error-handling/NotFoundPage.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/error-handling/NotFoundPage.tsx", "alias": "@/components/ui/SmartSearch"}, {"file": "components/error-handling/NotFoundPage.tsx", "alias": "@/lib/utils/redirects"}, {"file": "components/error-handling/NotFoundPage.tsx", "alias": "@/lib/utils"}, {"file": "components/errors/ErrorBoundary.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/errors/ErrorBoundary.tsx", "alias": "@/components/ui/card"}, {"file": "components/errors/ErrorBoundary.tsx", "alias": "@/lib/errors/types"}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "alias": "@/components/ui/card"}, {"file": "components/forms/ContactForm.tsx", "alias": "@/components/ui/card"}, {"file": "components/forms/ContactForm.tsx", "alias": "@/components/ui/button"}, {"file": "components/forms/ContactForm.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/forms/ContactForm.tsx", "alias": "@/components/ui/input"}, {"file": "components/forms/ContactForm.tsx", "alias": "@/components/ui/textarea"}, {"file": "components/forms/ContactForm.tsx", "alias": "@/components/ui/label"}, {"file": "components/forms/ContactForm.tsx", "alias": "@/components/ui/alert"}, {"file": "components/forms/ContactForm.tsx", "alias": "@/lib/forms/form-handler"}, {"file": "components/forms/ContactForm.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/help/ContextualTooltip.tsx", "alias": "@/lib/utils"}, {"file": "components/help/HelpSystem.tsx", "alias": "@/lib/utils"}, {"file": "components/help/KeyboardShortcuts.tsx", "alias": "@/lib/utils"}, {"file": "components/layout/Navigation.tsx", "alias": "@/components/auth/AuthModal"}, {"file": "components/layout/Navigation.tsx", "alias": "@/components/ui/UserAvatar"}, {"file": "components/layout/Navigation.tsx", "alias": "@/components/ui/SessionStatusIndicator"}, {"file": "components/layout/Navigation.tsx", "alias": "@/components/xp/XPCounter"}, {"file": "components/layout/Navigation.tsx", "alias": "@/components/xp/XPNotification"}, {"file": "components/layout/Navigation.tsx", "alias": "@/components/xp/LevelUpCelebration"}, {"file": "components/layout/Navigation.tsx", "alias": "@/components/ui/button"}, {"file": "components/layout/Navigation.tsx", "alias": "@/components/ui/avatar"}, {"file": "components/layout/Navigation.tsx", "alias": "@/components/ui/dropdown-menu"}, {"file": "components/layout/Navigation.tsx", "alias": "@/lib/context/LearningContext"}, {"file": "components/layout/Navigation.tsx", "alias": "@/lib/hooks/useSwipeGesture"}, {"file": "components/layout/Navigation.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "components/lazy/LazyComponents.tsx", "alias": "@/lib/hooks/useLazyLoading"}, {"file": "components/lazy/LazyComponents.tsx", "alias": "@/components/ui/LoadingSkeletons"}, {"file": "components/lazy/LazyMonacoEditor.tsx", "alias": "@/components/ui/LoadingSkeletons"}, {"file": "components/lazy/LazyMonacoEditor.tsx", "alias": "@/lib/hooks/useLazyLoading"}, {"file": "components/learning/ComprehensiveLearningPlatform.tsx", "alias": "@/lib/hooks/useApiData"}, {"file": "components/learning/ComprehensiveLearningPlatform.tsx", "alias": "@/components/auth/EnhancedAuthProvider"}, {"file": "components/learning/GamificationSystem.tsx", "alias": "@/components/auth/EnhancedAuthProvider"}, {"file": "components/learning/GamificationSystem.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/hooks/useAutoSave"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/errors/ErrorContext"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/context/LearningContext"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/utils"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/editor/ErrorHighlighting"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/editor/RealTimeSyntaxChecker"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/hooks/useGitIntegration"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/hooks/useLessonProgress"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/editor/AdvancedEditorConfig"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/accessibility/EditorAccessibility"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/git/CommitManager"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/performance/PerformanceMonitor"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/collaboration/CollaborativeEditor"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/lib/context/CollaborationContext"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/components/collaboration/UserPresencePanel"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/components/collaboration/CollaborationChat"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/components/collaboration/ConnectionStatusIndicator"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/components/collaboration/SessionRecovery"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "alias": "@/components/collaboration/FileSharing"}, {"file": "components/learning/LearningDashboard.tsx", "alias": "@/components/ui/card"}, {"file": "components/learning/LearningDashboard.tsx", "alias": "@/components/ui/button"}, {"file": "components/learning/LearningDashboard.tsx", "alias": "@/components/ui/progress"}, {"file": "components/learning/LearningDashboard.tsx", "alias": "@/components/ui/badge"}, {"file": "components/learning/LearningDashboard.tsx", "alias": "@/components/ui/tabs"}, {"file": "components/learning/LearningDashboard.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/learning/LearningDashboard.tsx", "alias": "@/lib/context/LearningContext"}, {"file": "components/learning/LessonProgressTracker.tsx", "alias": "@/lib/context/LearningContext"}, {"file": "components/learning/LessonProgressTracker.tsx", "alias": "@/lib/utils"}, {"file": "components/monitoring/PerformanceMonitor.tsx", "alias": "@/lib/monitoring/apiPerformance"}, {"file": "components/navigation/AuthenticatedNavbar.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "components/navigation/AuthenticatedNavbar.tsx", "alias": "@/components/auth/AuthModal"}, {"file": "components/navigation/GuidedOnboarding.tsx", "alias": "@/lib/utils"}, {"file": "components/navigation/GuidedOnboarding.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "components/navigation/GuidedOnboarding.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/navigation/GuidedOnboarding.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/navigation/GuidedOnboarding.tsx", "alias": "@/lib/monitoring/error-tracking"}, {"file": "components/navigation/NavigationFlowOptimizer.tsx", "alias": "@/lib/utils"}, {"file": "components/navigation/NavigationFlowOptimizer.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "components/navigation/NavigationFlowOptimizer.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/navigation/NavigationFlowOptimizer.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/navigation/NavigationFlowOptimizer.tsx", "alias": "@/lib/monitoring/error-tracking"}, {"file": "components/navigation/SmartNavigation.tsx", "alias": "@/lib/utils"}, {"file": "components/navigation/SmartNavigation.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/navigation/SmartNavigation.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "components/navigation/SmartNavigation.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/navigation/SmartNavigation.tsx", "alias": "@/lib/monitoring/error-tracking"}, {"file": "components/notifications/NotificationIntegrations.tsx", "alias": "@/lib/hooks/useNotificationIntegrations"}, {"file": "components/notifications/NotificationIntegrations.tsx", "alias": "@/lib/socket/SocketProvider"}, {"file": "components/notifications/NotificationIntegrations.tsx", "alias": "@/components/ui/NotificationSystem"}, {"file": "components/onboarding/InteractiveTutorial.tsx", "alias": "@/lib/utils"}, {"file": "components/onboarding/OnboardingFlow.tsx", "alias": "@/lib/utils"}, {"file": "components/performance/PerformanceOptimizer.tsx", "alias": "@/lib/utils/cssOptimization"}, {"file": "components/profile/UserProfile.tsx", "alias": "@/components/ui/button"}, {"file": "components/profile/UserProfile.tsx", "alias": "@/components/ui/input"}, {"file": "components/profile/UserProfile.tsx", "alias": "@/components/ui/textarea"}, {"file": "components/profile/UserProfile.tsx", "alias": "@/components/ui/avatar"}, {"file": "components/profile/UserProfile.tsx", "alias": "@/components/ui/badge"}, {"file": "components/profile/UserProfile.tsx", "alias": "@/components/ui/progress"}, {"file": "components/profile/UserProfile.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/profile/UserProfile.tsx", "alias": "@/lib/forms/form-handler"}, {"file": "components/providers/FallbackProvider.tsx", "alias": "@/components/error-handling/ErrorBoundary"}, {"file": "components/providers/FallbackProvider.tsx", "alias": "@/components/ui/ContextualHelp"}, {"file": "components/providers/FallbackProvider.tsx", "alias": "@/lib/hooks/useFeatureFlags"}, {"file": "components/providers/FallbackProvider.tsx", "alias": "@/lib/monitoring/error-tracking"}, {"file": "components/providers/FallbackProvider.tsx", "alias": "@/components/ui/EmptyState"}, {"file": "components/sections/EnhancedFeaturesShowcase.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/sections/EnhancedFeaturesShowcase.tsx", "alias": "@/components/ui/button"}, {"file": "components/sections/GamificationPreview.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/sections/GamificationPreview.tsx", "alias": "@/components/ui/button"}, {"file": "components/sections/GamificationPreview.tsx", "alias": "@/components/ui/progress"}, {"file": "components/sections/HeroSection.tsx", "alias": "@/components/ui/button"}, {"file": "components/sections/InteractiveDemoSection.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/sections/InteractiveDemoSection.tsx", "alias": "@/components/ui/button"}, {"file": "components/sections/InteractiveDemoSection.tsx", "alias": "@/components/ui/badge"}, {"file": "components/sections/TestimonialsSection.tsx", "alias": "@/components/ui/avatar"}, {"file": "components/settings/AccessibilitySection.tsx", "alias": "@/types/settings"}, {"file": "components/settings/AccessibilitySection.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/settings/AccessibilitySection.tsx", "alias": "@/lib/utils"}, {"file": "components/settings/LearningPreferencesSection.tsx", "alias": "@/types/settings"}, {"file": "components/settings/LearningPreferencesSection.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/settings/LearningPreferencesSection.tsx", "alias": "@/lib/utils"}, {"file": "components/settings/NotificationSection.tsx", "alias": "@/types/settings"}, {"file": "components/settings/NotificationSection.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/settings/NotificationSection.tsx", "alias": "@/lib/utils"}, {"file": "components/settings/PrivacySection.tsx", "alias": "@/types/settings"}, {"file": "components/settings/PrivacySection.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/settings/PrivacySection.tsx", "alias": "@/lib/utils"}, {"file": "components/settings/ProfileSection.tsx", "alias": "@/types/settings"}, {"file": "components/settings/ProfileSection.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/settings/ProfileSection.tsx", "alias": "@/lib/utils"}, {"file": "components/settings/SecuritySection.tsx", "alias": "@/types/settings"}, {"file": "components/settings/SecuritySection.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/settings/SecuritySection.tsx", "alias": "@/lib/utils"}, {"file": "components/settings/SettingsPage.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/settings/SettingsPage.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/settings/SettingsPage.tsx", "alias": "@/lib/utils"}, {"file": "components/settings/__tests__/ProfileSection.test.tsx", "alias": "@/types/settings"}, {"file": "components/settings/__tests__/SecuritySection.test.tsx", "alias": "@/types/settings"}, {"file": "components/settings/__tests__/SettingsPage.test.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/testing/FeedbackCollectionSystem.tsx", "alias": "@/components/ui/button"}, {"file": "components/testing/FeedbackCollectionSystem.tsx", "alias": "@/components/ui/card"}, {"file": "components/testing/FeedbackCollectionSystem.tsx", "alias": "@/components/ui/textarea"}, {"file": "components/testing/FeedbackCollectionSystem.tsx", "alias": "@/components/ui/input"}, {"file": "components/testing/FeedbackCollectionSystem.tsx", "alias": "@/components/ui/label"}, {"file": "components/testing/FeedbackCollectionSystem.tsx", "alias": "@/components/ui/slider"}, {"file": "components/testing/FeedbackCollectionSystem.tsx", "alias": "@/components/ui/checkbox"}, {"file": "components/testing/FeedbackCollectionSystem.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/testing/NotificationTestingPage.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/testing/NotificationTestingPage.tsx", "alias": "@/components/ui/NotificationSystem"}, {"file": "components/testing/NotificationTestingPage.tsx", "alias": "@/components/notifications/NotificationIntegrations"}, {"file": "components/testing/NotificationTestingPage.tsx", "alias": "@/lib/hooks/useNotificationSocket"}, {"file": "components/testing/NotificationTestingPage.tsx", "alias": "@/lib/utils"}, {"file": "components/testing/UATDashboard.tsx", "alias": "@/components/ui/card"}, {"file": "components/testing/UATDashboard.tsx", "alias": "@/components/ui/button"}, {"file": "components/testing/UATDashboard.tsx", "alias": "@/components/ui/input"}, {"file": "components/testing/UATDashboard.tsx", "alias": "@/components/ui/badge"}, {"file": "components/testing/UATDashboard.tsx", "alias": "@/components/ui/progress"}, {"file": "components/testing/UATDashboard.tsx", "alias": "@/components/ui/tabs"}, {"file": "components/testing/UATDashboard.tsx", "alias": "@/components/ui/select"}, {"file": "components/testing/UATDashboard.tsx", "alias": "@/lib/testing/uatScenarios"}, {"file": "components/testing/UATDashboard.tsx", "alias": "@/hooks/use-toast"}, {"file": "components/ui/AccessibleForm.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/alert.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/avatar.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/badge.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/button.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/ButtonTesting.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/card.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/CelebrationAnimations.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/CelebrationAnimations.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/ui/CelebrationAnimations.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/ui/ContextualHelp.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/ui/ContextualHelp.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/dialog.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/dropdown-menu.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/EmptyState.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/ui/EmptyState.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/EnhancedButton.tsx", "alias": "@/components/ui/button"}, {"file": "components/ui/EnhancedButton.tsx", "alias": "@/lib/animations/micro-interactions"}, {"file": "components/ui/EnhancedButton.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/EnhancedButton.tsx", "alias": "@/lib/hooks/useAsyncButton"}, {"file": "components/ui/EnhancedButton.tsx", "alias": "@/components/ui/Tooltip"}, {"file": "components/ui/EnhancedLoadingStates.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/EnhancedLoadingStates.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/ui/EnhancedLoadingStates.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/ui/EnhancedProgress.tsx", "alias": "@/lib/animations/micro-interactions"}, {"file": "components/ui/EnhancedProgress.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/ErrorMessage.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/ErrorMessage.tsx", "alias": "@/lib/errors/types"}, {"file": "components/ui/ErrorTesting.tsx", "alias": "@/lib/errors/types"}, {"file": "components/ui/ErrorTesting.tsx", "alias": "@/lib/errors/ErrorContext"}, {"file": "components/ui/ErrorTesting.tsx", "alias": "@/lib/hooks/useErrorRecovery"}, {"file": "components/ui/ErrorTesting.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/FeatureState.tsx", "alias": "@/lib/features/feature-flags"}, {"file": "components/ui/FeatureState.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/ui/FeatureState.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/FeedbackIndicators.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/FeedbackIndicators.tsx", "alias": "@/lib/errors/types"}, {"file": "components/ui/GlassmorphismButtons.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/input.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/label.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/LazyLoadingComponents.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/LazyLoadingComponents.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/ui/LazyLoadingComponents.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/ui/LoadingSkeletons.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/LoadingSpinner.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/LoadingStates.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/ui/LoadingStates.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/ui/LoadingStates.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/NotificationCenter.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/NotificationCenter.tsx", "alias": "@/lib/accessibility/contrast-utils"}, {"file": "components/ui/NotificationHistory.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/NotificationPreferences.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/NotificationSystem.tsx", "alias": "@/lib/animations/micro-interactions"}, {"file": "components/ui/NotificationSystem.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/NotificationSystem.tsx", "alias": "@/lib/accessibility/contrast-utils"}, {"file": "components/ui/NotificationSystem.tsx", "alias": "@/lib/utils/accessibility"}, {"file": "components/ui/OptimizedImage.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/progress.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/SaveStatusIndicator.tsx", "alias": "@/lib/storage/CodePersistence"}, {"file": "components/ui/SaveStatusIndicator.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/scroll-area.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/select.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/separator.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/SessionStatusIndicator.tsx", "alias": "@/lib/auth/sessionManager"}, {"file": "components/ui/SessionStatusIndicator.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "components/ui/SessionStatusIndicator.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/SkeletonLoader.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/SkeletonLoader.tsx", "alias": "@/lib/hooks/useSettings"}, {"file": "components/ui/SmartSearch.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/ui/SmartSearch.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/switch.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/tabs.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/textarea.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/toast.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/toaster.tsx", "alias": "@/components/ui/toast"}, {"file": "components/ui/toaster.tsx", "alias": "@/components/ui/use-toast"}, {"file": "components/ui/Typography.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/UserAvatar.tsx", "alias": "@/lib/hooks/useAuth"}, {"file": "components/ui/UserAvatar.tsx", "alias": "@/lib/auth/sessionManager"}, {"file": "components/ui/UserAvatar.tsx", "alias": "@/lib/context/LearningContext"}, {"file": "components/ui/UserAvatar.tsx", "alias": "@/components/xp/XPCounter"}, {"file": "components/ui/UserAvatar.tsx", "alias": "@/components/xp/ProgressBar"}, {"file": "components/ui/UserAvatar.tsx", "alias": "@/lib/achievements/data"}, {"file": "components/ui/UserAvatar.tsx", "alias": "@/lib/utils"}, {"file": "components/ui/VisualFeedbackSystem.tsx", "alias": "@/lib/utils"}, {"file": "components/vcs/VersionControlInterface.tsx", "alias": "@/lib/hooks/useSolidityVersionControl"}, {"file": "components/vcs/VersionControlInterface.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/vcs/VersionControlInterface.tsx", "alias": "@/lib/utils"}, {"file": "components/xp/LevelUpCelebration.tsx", "alias": "@/components/ui/Glassmorphism"}, {"file": "components/xp/LevelUpCelebration.tsx", "alias": "@/components/ui/EnhancedButton"}, {"file": "components/xp/LevelUpCelebration.tsx", "alias": "@/lib/utils"}, {"file": "components/xp/ProgressBar.tsx", "alias": "@/lib/utils"}, {"file": "components/xp/XPCounter.tsx", "alias": "@/lib/utils"}, {"file": "components/xp/XPNotification.tsx", "alias": "@/lib/utils"}, {"file": "lib/achievements/manager.ts", "alias": "@/lib/errors/recovery"}, {"file": "lib/api/integration.ts", "alias": "@/lib/monitoring/error-tracking"}, {"file": "lib/api/integration.ts", "alias": "@/lib/features/feature-flags"}, {"file": "lib/api/integration.ts", "alias": "@/lib/utils/fallback-integration"}, {"file": "lib/api/integration.ts", "alias": "@/types/settings"}, {"file": "lib/api/logging.ts", "alias": "@/lib/monitoring/error-tracking"}, {"file": "lib/api/optimizedApiClient.ts", "alias": "@/lib/monitoring/apiPerformance"}, {"file": "lib/auth/config.ts", "alias": "@/lib/prisma"}, {"file": "lib/auth/config.ts", "alias": "@/lib/auth/password"}, {"file": "lib/auth/sessionManager.ts", "alias": "@/lib/errors/types"}, {"file": "lib/auth/sessionManager.ts", "alias": "@/lib/errors/recovery"}, {"file": "lib/context/CollaborationContext.tsx", "alias": "@/lib/collaboration/CollaborationClient"}, {"file": "lib/context/CollaborationContext.tsx", "alias": "@/lib/collaboration/OperationalTransform"}, {"file": "lib/context/CollaborationContext.tsx", "alias": "@/hooks/useCollaborationConnection"}, {"file": "lib/context/CollaborationContext.tsx", "alias": "@/hooks/useUserPresence"}, {"file": "lib/context/CollaborationContext.tsx", "alias": "@/components/collaboration/CollaborationChat"}, {"file": "lib/context/CollaborationContext.tsx", "alias": "@/components/collaboration/SessionRecovery"}, {"file": "lib/context/LearningContext.tsx", "alias": "@/lib/achievements/manager"}, {"file": "lib/context/LearningContext.tsx", "alias": "@/lib/achievements/types"}, {"file": "lib/database/cleanup.ts", "alias": "@/lib/api/logger"}, {"file": "lib/database/data-removal.ts", "alias": "@/lib/api/logger"}, {"file": "lib/database/data-removal.ts", "alias": "@/lib/api/cache"}, {"file": "lib/database/maintenance.ts", "alias": "@/lib/api/logger"}, {"file": "lib/database/migrations.ts", "alias": "@/lib/api/logger"}, {"file": "lib/database/orphaned-data.ts", "alias": "@/lib/api/logger"}, {"file": "lib/errors/ErrorContext.tsx", "alias": "@/components/ui/ErrorMessage"}, {"file": "lib/forms/form-handler.ts", "alias": "@/components/ui/use-toast"}, {"file": "lib/hooks/useAchievements.ts", "alias": "@/lib/achievements/types"}, {"file": "lib/hooks/useAchievements.ts", "alias": "@/lib/achievements/manager"}, {"file": "lib/hooks/useAchievements.ts", "alias": "@/lib/errors/ErrorContext"}, {"file": "lib/hooks/useAdvancedCollaborativeEditor.ts", "alias": "@/components/ui/NotificationSystem"}, {"file": "lib/hooks/useApiData.ts", "alias": "@/components/auth/EnhancedAuthProvider"}, {"file": "lib/hooks/useApiData.ts", "alias": "@/components/ui/use-toast"}, {"file": "lib/hooks/useAuth.ts", "alias": "@/lib/auth/sessionManager"}, {"file": "lib/hooks/useAuth.ts", "alias": "@/lib/errors/types"}, {"file": "lib/hooks/useAuth.ts", "alias": "@/lib/errors/ErrorContext"}, {"file": "lib/hooks/useAuth.ts", "alias": "@/lib/hooks/useErrorRecovery"}, {"file": "lib/hooks/useEnhancedKeyboardNavigation.ts", "alias": "@/lib/utils/accessibility"}, {"file": "lib/hooks/useErrorRecovery.ts", "alias": "@/lib/errors/recovery"}, {"file": "lib/hooks/useErrorRecovery.ts", "alias": "@/lib/errors/ErrorContext"}, {"file": "lib/hooks/useFeatureFlags.tsx", "alias": "@/lib/features/feature-flags"}, {"file": "lib/hooks/useNotificationIntegrations.ts", "alias": "@/components/ui/NotificationSystem"}, {"file": "lib/hooks/useNotificationIntegrations.ts", "alias": "@/lib/errors/ErrorContext"}, {"file": "lib/hooks/useNotificationSocket.ts", "alias": "@/lib/socket/SocketProvider"}, {"file": "lib/hooks/useNotificationSocket.ts", "alias": "@/lib/socket/NotificationSocketService"}, {"file": "lib/hooks/useNotificationSocket.ts", "alias": "@/components/ui/NotificationSystem"}, {"file": "lib/hooks/useRealTimeXP.ts", "alias": "@/lib/context/LearningContext"}, {"file": "lib/hooks/useRealTimeXP.ts", "alias": "@/lib/achievements/data"}, {"file": "lib/hooks/useRealTimeXP.ts", "alias": "@/components/xp/XPNotification"}, {"file": "lib/hooks/useRealTimeXP.ts", "alias": "@/components/xp/LevelUpCelebration"}, {"file": "lib/hooks/useSessionStatus.ts", "alias": "@/lib/auth/sessionManager"}, {"file": "lib/hooks/useSessionStatus.ts", "alias": "@/lib/errors/ErrorContext"}, {"file": "lib/hooks/useSessionStatus.ts", "alias": "@/lib/errors/types"}, {"file": "lib/hooks/useSettings.ts", "alias": "@/types/settings"}, {"file": "lib/hooks/useSettings.ts", "alias": "@/lib/services/SettingsService"}, {"file": "lib/hooks/useSettings.ts", "alias": "@/components/ui/NotificationSystem"}, {"file": "lib/hooks/useSettings.ts", "alias": "@/lib/errors/ErrorContext"}, {"file": "lib/hooks/useSolidityAnalyzer.ts", "alias": "@/components/ui/NotificationSystem"}, {"file": "lib/hooks/useSolidityDebugger.ts", "alias": "@/components/ui/NotificationSystem"}, {"file": "lib/hooks/useSolidityVersionControl.ts", "alias": "@/components/ui/NotificationSystem"}, {"file": "lib/monitoring/analytics.ts", "alias": "@/lib/config/environment"}, {"file": "lib/monitoring/errorTracking.ts", "alias": "@/lib/config/environment"}, {"file": "lib/monitoring/logger.ts", "alias": "@/lib/config/environment"}, {"file": "lib/security/config.ts", "alias": "@/lib/config/environment"}, {"file": "lib/security/headers.ts", "alias": "@/lib/config/environment"}, {"file": "lib/security/middleware.ts", "alias": "@/lib/api/rate-limit"}, {"file": "lib/security/middleware.ts", "alias": "@/lib/monitoring/logger"}, {"file": "lib/security/rateLimiting.ts", "alias": "@/lib/config/environment"}, {"file": "lib/security/session.ts", "alias": "@/lib/auth/config"}, {"file": "lib/security/session.ts", "alias": "@/lib/config/environment"}, {"file": "lib/services/SettingsService.ts", "alias": "@/types/settings"}, {"file": "lib/socket/NotificationSocketService.ts", "alias": "@/components/ui/NotificationSystem"}, {"file": "lib/socket/server.ts", "alias": "@/lib/prisma"}, {"file": "lib/utils/fallback-integration.ts", "alias": "@/types/settings"}, {"file": "lib/utils/fallback-integration.ts", "alias": "@/lib/features/feature-flags"}, {"file": "lib/utils/fallback-integration.ts", "alias": "@/lib/monitoring/error-tracking"}, {"file": "hooks/useAutoSave.ts", "alias": "@/lib/storage/CodePersistence"}, {"file": "hooks/useCollaborationConnection.ts", "alias": "@/lib/collaboration/CollaborationClient"}, {"file": "hooks/useCollaborationConnection.ts", "alias": "@/lib/collaboration/ConnectionManager"}, {"file": "hooks/useCollaborationConnection.ts", "alias": "@/lib/collaboration/OperationalTransform"}, {"file": "hooks/useGitIntegration.ts", "alias": "@/lib/git/GitIntegration"}, {"file": "hooks/useLessonProgress.ts", "alias": "@/lib/context/LearningContext"}, {"file": "hooks/useUserPresence.ts", "alias": "@/lib/collaboration/CollaborationClient"}]}}, "recommendations": ["Add index.ts files to: app, components, lib, hooks, utils, types, services, stores", "Standardize import patterns (prefer aliased imports over relative)"]}