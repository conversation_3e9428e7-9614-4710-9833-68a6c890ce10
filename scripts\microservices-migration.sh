#!/bin/bash

# =============================================================================
# Microservices Migration Script
# =============================================================================
# This script helps migrate the monolithic Next.js application to microservices
# architecture by extracting services incrementally and setting up infrastructure.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="solidity-learning-microservices"
BACKUP_DIR="./backup-$(date +%Y%m%d-%H%M%S)"
SERVICES_DIR="./services"
INFRASTRUCTURE_DIR="./infrastructure"

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed. Please install Node.js first."
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        error "npm is not installed. Please install npm first."
    fi
    
    log "Prerequisites check passed!"
}

# Create backup of current monolithic application
create_backup() {
    log "Creating backup of current application..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Copy important files and directories
    cp -r app "$BACKUP_DIR/" 2>/dev/null || true
    cp -r components "$BACKUP_DIR/" 2>/dev/null || true
    cp -r lib "$BACKUP_DIR/" 2>/dev/null || true
    cp -r types "$BACKUP_DIR/" 2>/dev/null || true
    cp -r prisma "$BACKUP_DIR/" 2>/dev/null || true
    cp package.json "$BACKUP_DIR/" 2>/dev/null || true
    cp package-lock.json "$BACKUP_DIR/" 2>/dev/null || true
    cp next.config.js "$BACKUP_DIR/" 2>/dev/null || true
    cp tsconfig.json "$BACKUP_DIR/" 2>/dev/null || true
    cp tailwind.config.js "$BACKUP_DIR/" 2>/dev/null || true
    cp .env.local "$BACKUP_DIR/" 2>/dev/null || true
    cp .env.example "$BACKUP_DIR/" 2>/dev/null || true
    
    log "Backup created at: $BACKUP_DIR"
}

# Create microservices directory structure
create_directory_structure() {
    log "Creating microservices directory structure..."
    
    # Create main directories
    mkdir -p "$SERVICES_DIR"
    mkdir -p "$INFRASTRUCTURE_DIR"
    mkdir -p shared/{types,utils,middleware,schemas}
    mkdir -p scripts
    
    # Create service directories
    services=(
        "auth-service"
        "user-service"
        "content-service"
        "learning-service"
        "analytics-service"
        "gamification-service"
        "collaboration-service"
        "ai-service"
        "notification-service"
        "blockchain-service"
        "api-gateway"
        "frontend-service"
    )
    
    for service in "${services[@]}"; do
        mkdir -p "$SERVICES_DIR/$service"/{src,tests,docs}
        mkdir -p "$SERVICES_DIR/$service/src"/{controllers,services,models,middleware,routes,utils}
    done
    
    # Create infrastructure directories
    mkdir -p "$INFRASTRUCTURE_DIR"/{docker,kubernetes,terraform,monitoring}
    
    log "Directory structure created!"
}

# Generate service templates
generate_service_templates() {
    log "Generating service templates..."
    
    # Generate package.json template for Node.js services
    cat > "$SERVICES_DIR/auth-service/package.json" << 'EOF'
{
  "name": "auth-service",
  "version": "1.0.0",
  "description": "Authentication microservice for Solidity Learning Platform",
  "main": "src/index.js",
  "scripts": {
    "start": "node src/index.js",
    "dev": "nodemon src/index.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "build": "tsc",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "helmet": "^7.0.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.2",
    "joi": "^17.9.2",
    "pg": "^8.11.3",
    "redis": "^4.6.7",
    "winston": "^3.10.0",
    "express-rate-limit": "^6.8.1",
    "passport": "^0.6.0",
    "passport-github2": "^0.1.12",
    "passport-google-oauth20": "^2.0.0"
  },
  "devDependencies": {
    "@types/node": "^20.4.5",
    "@types/express": "^4.17.17",
    "@types/bcryptjs": "^2.4.2",
    "@types/jsonwebtoken": "^9.0.2",
    "@types/jest": "^29.5.3",
    "typescript": "^5.1.6",
    "nodemon": "^3.0.1",
    "jest": "^29.6.1",
    "eslint": "^8.45.0",
    "prettier": "^3.0.0"
  }
}
EOF

    # Generate Dockerfile template
    cat > "$SERVICES_DIR/auth-service/Dockerfile" << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY src/ ./src/

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001

# Change ownership
RUN chown -R nodejs:nodejs /app
USER nodejs

# Expose port
EXPOSE 3001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# Start the application
CMD ["node", "src/index.js"]
EOF

    # Generate basic Express server template
    cat > "$SERVICES_DIR/auth-service/src/index.js" << 'EOF'
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100 // limit each IP to 100 requests per windowMs
});
app.use(limiter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy', 
    service: 'auth-service',
    timestamp: new Date().toISOString()
  });
});

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Authentication Service API' });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: 'Something went wrong!' });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

app.listen(PORT, () => {
  console.log(`Auth service running on port ${PORT}`);
});
EOF

    log "Service templates generated!"
}

# Extract shared utilities and types
extract_shared_code() {
    log "Extracting shared code..."
    
    # Copy shared types
    if [ -d "types" ]; then
        cp -r types/* shared/types/ 2>/dev/null || true
    fi
    
    # Copy shared utilities
    if [ -d "lib/utils" ]; then
        cp -r lib/utils/* shared/utils/ 2>/dev/null || true
    fi
    
    # Copy middleware
    if [ -d "lib/middleware" ]; then
        cp -r lib/middleware/* shared/middleware/ 2>/dev/null || true
    fi
    
    # Create shared package.json
    cat > shared/package.json << 'EOF'
{
  "name": "@solidity-learning/shared",
  "version": "1.0.0",
  "description": "Shared utilities and types for Solidity Learning Platform microservices",
  "main": "index.js",
  "scripts": {
    "build": "tsc",
    "test": "jest"
  },
  "dependencies": {
    "zod": "^3.21.4",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "@types/node": "^20.4.5",
    "@types/lodash": "^4.14.195",
    "typescript": "^5.1.6",
    "jest": "^29.6.1"
  }
}
EOF

    log "Shared code extracted!"
}

# Setup environment files
setup_environment() {
    log "Setting up environment files..."
    
    # Create .env.example for microservices
    cat > .env.microservices.example << 'EOF'
# =============================================================================
# MICROSERVICES ENVIRONMENT CONFIGURATION
# =============================================================================

# Node Environment
NODE_ENV=development

# Database Configuration
POSTGRES_PASSWORD=dev_password_123
MONGO_USERNAME=admin
MONGO_PASSWORD=dev_mongo_123
REDIS_PASSWORD=dev_redis_123
CLICKHOUSE_USER=analytics
CLICKHOUSE_PASSWORD=dev_clickhouse_123

# JWT Configuration
JWT_SECRET=dev_jwt_secret_key_123
NEXTAUTH_SECRET=dev_nextauth_secret_123

# OAuth Configuration
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# AWS Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
S3_BUCKET=solidity-learning-content

# External APIs
OPENAI_API_KEY=your_openai_api_key
GOOGLE_AI_API_KEY=your_google_ai_api_key
HUGGINGFACE_API_KEY=your_huggingface_api_key
SENDGRID_API_KEY=your_sendgrid_api_key
INFURA_PROJECT_ID=your_infura_project_id
ALCHEMY_API_KEY=your_alchemy_api_key

# Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_PRIVATE_KEY=your_firebase_private_key
FIREBASE_CLIENT_EMAIL=your_firebase_client_email

# Blockchain Configuration
BLOCKCHAIN_PRIVATE_KEY=your_blockchain_private_key
BLOCKCHAIN_NETWORK=sepolia

# Analytics
GOOGLE_ANALYTICS_ID=your_google_analytics_id

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
EOF

    # Copy existing .env.local if it exists
    if [ -f ".env.local" ]; then
        cp .env.local .env.microservices.local
        warn "Copied existing .env.local to .env.microservices.local. Please review and update for microservices."
    fi
    
    log "Environment files setup complete!"
}

# Generate migration documentation
generate_documentation() {
    log "Generating migration documentation..."
    
    cat > MICROSERVICES_MIGRATION_GUIDE.md << 'EOF'
# Microservices Migration Guide

## Overview
This guide helps you migrate from the monolithic Next.js application to a microservices architecture.

## Migration Steps

### 1. Infrastructure Setup
```bash
# Start infrastructure services
docker-compose -f docker-compose.microservices.yml up -d postgres redis mongodb kafka clickhouse elasticsearch

# Wait for services to be ready
./scripts/wait-for-services.sh
```

### 2. Database Migration
```bash
# Run database migrations for each service
./scripts/migrate-databases.sh
```

### 3. Service Deployment
```bash
# Build and start all services
docker-compose -f docker-compose.microservices.yml up --build
```

### 4. Frontend Migration
```bash
# Update frontend to use API Gateway
# Update environment variables
# Test all functionality
```

## Service URLs
- API Gateway: http://localhost:8080
- Frontend: http://localhost:3000
- Auth Service: http://localhost:3001
- User Service: http://localhost:3002
- Content Service: http://localhost:3003
- Learning Service: http://localhost:3004
- Analytics Service: http://localhost:3005
- Gamification Service: http://localhost:3006
- Collaboration Service: http://localhost:3007
- AI Service: http://localhost:3008
- Notification Service: http://localhost:3009
- Blockchain Service: http://localhost:3010

## Monitoring
- Prometheus: http://localhost:9090
- Grafana: http://localhost:3001
- Jaeger: http://localhost:16686

## Troubleshooting
See the troubleshooting section in the main documentation.
EOF

    log "Documentation generated!"
}

# Main migration function
main() {
    log "Starting microservices migration..."
    
    check_prerequisites
    create_backup
    create_directory_structure
    generate_service_templates
    extract_shared_code
    setup_environment
    generate_documentation
    
    log "Migration setup complete!"
    log "Next steps:"
    log "1. Review the generated files and customize as needed"
    log "2. Update environment variables in .env.microservices.local"
    log "3. Start infrastructure: docker-compose -f docker-compose.microservices.yml up -d"
    log "4. Begin service-by-service migration following the documentation"
    log "5. Test each service thoroughly before proceeding to the next"
    
    warn "This is a complex migration. Please test thoroughly in a development environment first."
}

# Script execution
case "${1:-}" in
    "backup")
        create_backup
        ;;
    "structure")
        create_directory_structure
        ;;
    "templates")
        generate_service_templates
        ;;
    "shared")
        extract_shared_code
        ;;
    "env")
        setup_environment
        ;;
    "docs")
        generate_documentation
        ;;
    *)
        main
        ;;
esac
