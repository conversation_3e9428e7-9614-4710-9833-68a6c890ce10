/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import '@testing-library/jest-dom';

import {
  PerformanceAnalyticsIntegration,
  PerformanceAnalyticsDashboard,
  useComprehensiveAnalytics
} from '@/components/analytics/PerformanceAnalyticsIntegration';

// Mock the analytics hooks
jest.mock('@/components/analytics/AnalyticsIntegration', () => ({
  useEnhancedTracking: jest.fn(() => ({
    trackEvent: jest.fn(),
    trackConversionTouchpoint: jest.fn(),
    trackUserEngagement: jest.fn(),
    trackLearningProgress: jest.fn(),
    isAnalyticsReady: true,
    isHeatmapReady: true
  }))
}));

jest.mock('@/components/analytics/ConversionFunnelSystem', () => ({
  useFeedbackWidget: jest.fn(() => ({
    triggerWidget: jest.fn(),
    activeWidget: null,
    isVisible: false,
    submitFeedback: jest.fn(),
    dismissWidget: jest.fn()
  }))
}));

// Mock child components
jest.mock('@/components/analytics/RealTimeDashboard', () => ({
  RealTimeDashboard: () => <div data-testid="real-time-dashboard">Real Time Dashboard</div>
}));

jest.mock('@/components/analytics/ABTestingFramework', () => ({
  ABTestingProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="ab-testing-provider">{children}</div>
  ),
  ABTestDashboard: () => <div data-testid="ab-test-dashboard">A/B Test Dashboard</div>
}));

jest.mock('@/components/analytics/ConversionFunnelSystem', () => ({
  ...jest.requireActual('@/components/analytics/ConversionFunnelSystem'),
  FunnelVisualization: () => <div data-testid="funnel-visualization">Funnel Visualization</div>
}));

jest.mock('@/components/analytics/PerformanceMonitoring', () => ({
  PerformanceDashboard: () => <div data-testid="performance-dashboard">Performance Dashboard</div>,
  BundleAnalysisView: () => <div data-testid="bundle-analysis">Bundle Analysis</div>,
  LighthouseScores: () => <div data-testid="lighthouse-scores">Lighthouse Scores</div>
}));

jest.mock('@/components/performance/PWAUtils', () => ({
  PWAProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="pwa-provider">{children}</div>
  )
}));

describe('PerformanceAnalyticsIntegration', () => {
  const defaultConfig = {
    enableAnalytics: true,
    enableHeatmaps: true,
    enableABTesting: true,
    enablePerformanceMonitoring: true,
    enableFeedbackWidgets: true,
    enablePWA: true,
    enableAdvancedLoading: true,
    googleAnalyticsId: 'GA-TEST-123',
    hotjarId: 'HJ-TEST-456',
    environment: 'development' as const
  };

  beforeEach(() => {
    // Reset analytics mocks
    global.analyticsTestUtils.resetMocks();
    
    // Mock performance API
    global.analyticsTestUtils.mockPerformanceEntries([
      {
        name: 'navigation',
        entryType: 'navigation',
        startTime: 0,
        duration: 1000,
        loadEventEnd: 1000,
        navigationStart: 0,
        domContentLoadedEventEnd: 500,
        responseStart: 200,
        requestStart: 100
      }
    ]);
  });

  describe('Component Rendering', () => {
    it('renders with all features enabled', () => {
      render(
        <PerformanceAnalyticsIntegration config={defaultConfig}>
          <div data-testid="test-content">Test Content</div>
        </PerformanceAnalyticsIntegration>
      );

      expect(screen.getByTestId('pwa-provider')).toBeInTheDocument();
      expect(screen.getByTestId('ab-testing-provider')).toBeInTheDocument();
      expect(screen.getByTestId('test-content')).toBeInTheDocument();
    });

    it('renders without PWA when disabled', () => {
      const configWithoutPWA = { ...defaultConfig, enablePWA: false };
      
      render(
        <PerformanceAnalyticsIntegration config={configWithoutPWA}>
          <div data-testid="test-content">Test Content</div>
        </PerformanceAnalyticsIntegration>
      );

      expect(screen.queryByTestId('pwa-provider')).not.toBeInTheDocument();
      expect(screen.getByTestId('ab-testing-provider')).toBeInTheDocument();
      expect(screen.getByTestId('test-content')).toBeInTheDocument();
    });

    it('conditionally renders feedback widgets', () => {
      const { rerender } = render(
        <PerformanceAnalyticsIntegration config={defaultConfig}>
          <div>Content</div>
        </PerformanceAnalyticsIntegration>
      );

      // Should render with feedback widgets enabled
      expect(screen.getByTestId('ab-testing-provider')).toBeInTheDocument();

      // Re-render without feedback widgets
      const configWithoutFeedback = { ...defaultConfig, enableFeedbackWidgets: false };
      rerender(
        <PerformanceAnalyticsIntegration config={configWithoutFeedback}>
          <div>Content</div>
        </PerformanceAnalyticsIntegration>
      );

      expect(screen.getByTestId('ab-testing-provider')).toBeInTheDocument();
    });
  });

  describe('Performance Monitoring', () => {
    it('tracks page performance when enabled', async () => {
      const mockTrackEvent = jest.fn();
      
      // Mock the analytics hook to return our mock function
      require('@/components/analytics/AnalyticsIntegration').useEnhancedTracking.mockReturnValue({
        trackEvent: mockTrackEvent,
        isAnalyticsReady: true,
        isHeatmapReady: true
      });

      render(
        <PerformanceAnalyticsIntegration config={defaultConfig}>
          <div>Content</div>
        </PerformanceAnalyticsIntegration>
      );

      // Wait for performance tracking to initialize
      await waitFor(() => {
        expect(mockTrackEvent).toHaveBeenCalledWith(
          expect.objectContaining({
            event: 'page_performance',
            category: 'performance',
            action: 'page_load'
          })
        );
      });
    });

    it('does not track performance when monitoring is disabled', () => {
      const mockTrackEvent = jest.fn();
      const configWithoutPerformance = { ...defaultConfig, enablePerformanceMonitoring: false };
      
      require('@/components/analytics/AnalyticsIntegration').useEnhancedTracking.mockReturnValue({
        trackEvent: mockTrackEvent,
        isAnalyticsReady: true,
        isHeatmapReady: true
      });

      render(
        <PerformanceAnalyticsIntegration config={configWithoutPerformance}>
          <div>Content</div>
        </PerformanceAnalyticsIntegration>
      );

      // Should not track performance events
      expect(mockTrackEvent).not.toHaveBeenCalledWith(
        expect.objectContaining({
          category: 'performance'
        })
      );
    });
  });

  describe('Analytics Configuration', () => {
    it('passes correct configuration to analytics hooks', () => {
      const useEnhancedTrackingMock = require('@/components/analytics/AnalyticsIntegration').useEnhancedTracking;
      
      render(
        <PerformanceAnalyticsIntegration config={defaultConfig}>
          <div>Content</div>
        </PerformanceAnalyticsIntegration>
      );

      expect(useEnhancedTrackingMock).toHaveBeenCalledWith({
        googleAnalyticsId: 'GA-TEST-123',
        hotjarId: 'HJ-TEST-456',
        enableHeatmaps: true,
        enableSessionRecordings: true,
        enableUserConsent: true,
        privacyCompliant: true,
        trackingLevel: 'standard'
      });
    });

    it('uses enhanced tracking level in production', () => {
      const useEnhancedTrackingMock = require('@/components/analytics/AnalyticsIntegration').useEnhancedTracking;
      const productionConfig = { ...defaultConfig, environment: 'production' as const };
      
      render(
        <PerformanceAnalyticsIntegration config={productionConfig}>
          <div>Content</div>
        </PerformanceAnalyticsIntegration>
      );

      expect(useEnhancedTrackingMock).toHaveBeenCalledWith(
        expect.objectContaining({
          trackingLevel: 'enhanced'
        })
      );
    });
  });
});

describe('PerformanceAnalyticsDashboard', () => {
  it('renders all dashboard sections by default', () => {
    render(<PerformanceAnalyticsDashboard />);

    expect(screen.getByTestId('real-time-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('ab-test-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('funnel-visualization')).toBeInTheDocument();
    expect(screen.getByTestId('performance-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('bundle-analysis')).toBeInTheDocument();
    expect(screen.getByTestId('lighthouse-scores')).toBeInTheDocument();
  });

  it('conditionally renders sections based on props', () => {
    render(
      <PerformanceAnalyticsDashboard
        showRealTime={false}
        showABTests={false}
        showFunnel={true}
        showPerformance={true}
      />
    );

    expect(screen.queryByTestId('real-time-dashboard')).not.toBeInTheDocument();
    expect(screen.queryByTestId('ab-test-dashboard')).not.toBeInTheDocument();
    expect(screen.getByTestId('funnel-visualization')).toBeInTheDocument();
    expect(screen.getByTestId('performance-dashboard')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <PerformanceAnalyticsDashboard className="custom-dashboard" />
    );

    expect(container.firstChild).toHaveClass('custom-dashboard');
  });
});

describe('useComprehensiveAnalytics', () => {
  const TestComponent = () => {
    const analytics = useComprehensiveAnalytics();
    
    return (
      <div>
        <button 
          onClick={() => analytics.trackUserJourney('test-step')}
          data-testid="track-journey"
        >
          Track Journey
        </button>
        <button 
          onClick={() => analytics.trackConversionEvent('test-conversion', 100)}
          data-testid="track-conversion"
        >
          Track Conversion
        </button>
        <button 
          onClick={() => analytics.trackPerformanceIssue('slow-load', 'high')}
          data-testid="track-performance"
        >
          Track Performance Issue
        </button>
      </div>
    );
  };

  beforeEach(() => {
    // Mock the feedback widget hook
    require('@/components/analytics/ConversionFunnelSystem').useFeedbackWidget.mockReturnValue({
      triggerWidget: jest.fn()
    });
  });

  it('tracks user journey events', async () => {
    const mockTrackEvent = jest.fn();
    require('@/components/analytics/AnalyticsIntegration').useEnhancedTracking.mockReturnValue({
      trackEvent: mockTrackEvent,
      trackConversionTouchpoint: jest.fn(),
      isAnalyticsReady: true
    });

    render(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('track-journey'));

    await waitFor(() => {
      expect(mockTrackEvent).toHaveBeenCalledWith({
        event: 'user_journey',
        category: 'engagement',
        action: 'test-step',
        customParameters: expect.objectContaining({
          timestamp: expect.any(Number),
          page: '/'
        })
      });
    });
  });

  it('tracks conversion events and triggers feedback widgets', async () => {
    const mockTrackConversionTouchpoint = jest.fn();
    const mockTriggerWidget = jest.fn();
    
    require('@/components/analytics/AnalyticsIntegration').useEnhancedTracking.mockReturnValue({
      trackEvent: jest.fn(),
      trackConversionTouchpoint: mockTrackConversionTouchpoint,
      isAnalyticsReady: true
    });

    require('@/components/analytics/ConversionFunnelSystem').useFeedbackWidget.mockReturnValue({
      triggerWidget: mockTriggerWidget
    });

    render(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('track-conversion'));

    await waitFor(() => {
      expect(mockTrackConversionTouchpoint).toHaveBeenCalledWith(
        'test-conversion',
        'comprehensive_system',
        100
      );
    });
  });

  it('tracks performance issues', async () => {
    const mockTrackEvent = jest.fn();
    require('@/components/analytics/AnalyticsIntegration').useEnhancedTracking.mockReturnValue({
      trackEvent: mockTrackEvent,
      isAnalyticsReady: true
    });

    render(<TestComponent />);
    
    fireEvent.click(screen.getByTestId('track-performance'));

    await waitFor(() => {
      expect(mockTrackEvent).toHaveBeenCalledWith({
        event: 'performance_issue',
        category: 'technical',
        action: 'slow-load',
        customParameters: expect.objectContaining({
          severity: 'high',
          user_agent: expect.any(String),
          timestamp: expect.any(Number)
        })
      });
    });
  });
});

describe('Integration Tests', () => {
  it('integrates all analytics systems correctly', async () => {
    const mockTrackEvent = jest.fn();
    const mockTriggerWidget = jest.fn();
    
    require('@/components/analytics/AnalyticsIntegration').useEnhancedTracking.mockReturnValue({
      trackEvent: mockTrackEvent,
      trackConversionTouchpoint: jest.fn(),
      isAnalyticsReady: true,
      isHeatmapReady: true
    });

    require('@/components/analytics/ConversionFunnelSystem').useFeedbackWidget.mockReturnValue({
      triggerWidget: mockTriggerWidget,
      activeWidget: null,
      isVisible: false
    });

    const { container } = render(
      <PerformanceAnalyticsIntegration config={defaultConfig}>
        <PerformanceAnalyticsDashboard />
      </PerformanceAnalyticsIntegration>
    );

    // Verify all components are rendered
    expect(screen.getByTestId('pwa-provider')).toBeInTheDocument();
    expect(screen.getByTestId('ab-testing-provider')).toBeInTheDocument();
    expect(screen.getByTestId('real-time-dashboard')).toBeInTheDocument();
    expect(screen.getByTestId('performance-dashboard')).toBeInTheDocument();

    // Verify analytics initialization
    await waitFor(() => {
      expect(mockTrackEvent).toHaveBeenCalled();
    });
  });

  it('handles errors gracefully', () => {
    // Mock console.error to avoid noise in test output
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
    
    // Force an error in analytics
    require('@/components/analytics/AnalyticsIntegration').useEnhancedTracking.mockImplementation(() => {
      throw new Error('Analytics initialization failed');
    });

    expect(() => {
      render(
        <PerformanceAnalyticsIntegration config={defaultConfig}>
          <div>Content</div>
        </PerformanceAnalyticsIntegration>
      );
    }).toThrow('Analytics initialization failed');

    consoleSpy.mockRestore();
  });
});
