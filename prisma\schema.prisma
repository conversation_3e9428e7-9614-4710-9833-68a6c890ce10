// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String?   @unique
  emailVerified DateTime?
  password      String?   // For email/password authentication
  image         String?
  role          UserRole  @default(STUDENT)
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts       Account[]
  sessions       Session[]
  profile        UserProfile?
  progress       UserProgress[]
  achievements   UserAchievement[]
  submissions    CodeSubmission[]
  collaborations Collaboration[]
  mentorships    Mentorship[]      @relation("MentorRelation")
  menteeships    Mentorship[]      @relation("MenteeRelation")
  chatMessages   ChatMessage[]
  feedback       Feedback[]
  uatSessions    UATSession[]
  enrollments    CourseEnrollment[]
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model UserProfile {
  id              String     @id @default(cuid())
  userId          String     @unique
  bio             String?
  githubUsername  String?
  twitterUsername String?
  linkedinUrl     String?
  websiteUrl      String?
  skillLevel      SkillLevel @default(BEGINNER)
  totalXP         Int        @default(0)
  currentLevel    Int        @default(1)
  streak          Int        @default(0)
  lastActiveDate  DateTime   @default(now())
  preferences     Json?
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Course {
  id             String     @id @default(cuid())
  title          String
  description    String
  difficulty     SkillLevel
  estimatedHours Int
  isPublished    Boolean    @default(false)
  order          Int
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt

  modules     Module[]
  progress    UserProgress[]
  enrollments CourseEnrollment[]
}

model CourseEnrollment {
  id         String   @id @default(cuid())
  userId     String
  courseId   String
  enrolledAt DateTime @default(now())
  completedAt DateTime?
  progress   Float    @default(0.0) // 0.0 to 1.0
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  course Course @relation(fields: [courseId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId])
}

model Module {
  id          String   @id @default(cuid())
  courseId    String
  title       String
  description String
  content     Json
  order       Int
  xpReward    Int      @default(100)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  course   Course         @relation(fields: [courseId], references: [id], onDelete: Cascade)
  lessons  Lesson[]
  progress UserProgress[]

  @@unique([courseId, title])
}

model Lesson {
  id          String     @id @default(cuid())
  moduleId    String
  title       String
  description String
  content     Json
  type        LessonType
  order       Int
  xpReward    Int        @default(50)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt

  module      Module           @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  submissions CodeSubmission[]
  progress    UserProgress[]

  @@unique([moduleId, title])
}

model UserProgress {
  id          String         @id @default(cuid())
  userId      String
  courseId    String?
  moduleId    String?
  lessonId    String?
  status      ProgressStatus @default(NOT_STARTED)
  score       Int?
  timeSpent   Int            @default(0) // in minutes
  completedAt DateTime?
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  course Course? @relation(fields: [courseId], references: [id], onDelete: Cascade)
  module Module? @relation(fields: [moduleId], references: [id], onDelete: Cascade)
  lesson Lesson? @relation(fields: [lessonId], references: [id], onDelete: Cascade)

  @@unique([userId, courseId, moduleId, lessonId])
}

model Achievement {
  id          String              @id @default(cuid())
  title       String
  description String
  icon        String
  category    AchievementCategory
  requirement Json
  xpReward    Int                 @default(0)
  badgeUrl    String?
  isActive    Boolean             @default(true)
  createdAt   DateTime            @default(now())

  userAchievements UserAchievement[]
}

model UserAchievement {
  id            String   @id @default(cuid())
  userId        String
  achievementId String
  unlockedAt    DateTime @default(now())
  progress      Int      @default(0)
  isCompleted   Boolean  @default(false)

  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  achievement Achievement @relation(fields: [achievementId], references: [id], onDelete: Cascade)

  @@unique([userId, achievementId])
}

model CodeSubmission {
  id           String           @id @default(cuid())
  userId       String
  lessonId     String
  code         String
  language     String           @default("solidity")
  status       SubmissionStatus @default(PENDING)
  score        Int?
  feedback     String?
  gasUsed      String?
  deploymentTx String?
  testResults  Json?
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  lesson Lesson @relation(fields: [lessonId], references: [id], onDelete: Cascade)
}

model Collaboration {
  id              String              @id @default(cuid())
  title           String
  description     String?
  type            CollaborationType
  status          CollaborationStatus @default(ACTIVE)
  maxParticipants Int                 @default(2)
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  participants User[]
  chatMessages ChatMessage[]
}

model Mentorship {
  id        String           @id @default(cuid())
  mentorId  String
  menteeId  String
  status    MentorshipStatus @default(PENDING)
  startDate DateTime?
  endDate   DateTime?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  mentor User @relation("MentorRelation", fields: [mentorId], references: [id], onDelete: Cascade)
  mentee User @relation("MenteeRelation", fields: [menteeId], references: [id], onDelete: Cascade)

  @@unique([mentorId, menteeId])
}

model ChatMessage {
  id              String      @id @default(cuid())
  content         String
  userId          String
  collaborationId String?
  type            MessageType @default(TEXT)
  metadata        Json?
  createdAt       DateTime    @default(now())

  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  collaboration Collaboration? @relation(fields: [collaborationId], references: [id], onDelete: Cascade)
}

model Feedback {
  id               String   @id @default(cuid())
  type             String // 'rating', 'survey', 'bug_report', 'feature_request', 'usability'
  category         String
  title            String
  description      String
  rating           Int?
  severity         String? // 'low', 'medium', 'high', 'critical'
  priority         String // 'low', 'medium', 'high', 'critical'
  steps            String[] // Array of reproduction steps
  expectedBehavior String?
  actualBehavior   String?
  browserInfo      String?
  screenRecording  Boolean  @default(false)
  contactEmail     String?
  allowFollowUp    Boolean  @default(false)
  page             String
  sessionId        String
  uatSessionId     String?   // For UAT feedback
  userId           String?
  timestamp        DateTime @default(now())
  ipAddress        String?
  userAgent        String?
  assignedTeam     String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  user       User?       @relation(fields: [userId], references: [id], onDelete: SetNull)
  uatSession UATSession? @relation(fields: [uatSessionId], references: [id], onDelete: SetNull)
}

model UATSession {
  id                String    @id @default(cuid())
  testerId          String
  testerExperience  String? // 'beginner', 'intermediate', 'expert'
  testerBackground  String?
  deviceInfo        String?
  browserInfo       String?
  assignedTasks     String[] // Array of task IDs
  startTime         DateTime  @default(now())
  endTime           DateTime?
  status            String    @default("not_started") // 'not_started', 'in_progress', 'completed', 'abandoned'
  errorsEncountered Int       @default(0)
  helpRequests      Int       @default(0)
  feedbackCount     Int       @default(0)
  lastFeedbackAt    DateTime?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  tester      User            @relation(fields: [testerId], references: [id], onDelete: Cascade)
  feedback    Feedback[]
  taskResults UATTaskResult[]
}

model UATTaskResult {
  id         String   @id @default(cuid())
  sessionId  String
  taskId     String
  completed  Boolean  @default(false)
  timeSpent  Int? // in seconds
  errorCount Int      @default(0)
  helpUsed   Boolean  @default(false)
  rating     Int? // 1-5 rating
  comments   String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  session UATSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@unique([sessionId, taskId])
}

// Enums
enum UserRole {
  STUDENT
  MENTOR
  INSTRUCTOR
  ADMIN
}

enum SkillLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum LessonType {
  THEORY
  CODING
  QUIZ
  PROJECT
  INTERACTIVE
}

enum ProgressStatus {
  NOT_STARTED
  IN_PROGRESS
  COMPLETED
  FAILED
}

enum AchievementCategory {
  LEARNING
  CODING
  SOCIAL
  STREAK
  SPECIAL
}

enum SubmissionStatus {
  PENDING
  COMPILING
  COMPILED
  TESTING
  PASSED
  FAILED
  ERROR
}

enum CollaborationType {
  PAIR_PROGRAMMING
  CODE_REVIEW
  PROJECT
  STUDY_GROUP
}

enum CollaborationStatus {
  ACTIVE
  COMPLETED
  CANCELLED
}

enum MentorshipStatus {
  PENDING
  ACTIVE
  COMPLETED
  CANCELLED
}

enum MessageType {
  TEXT
  CODE
  SYSTEM
  FILE
}
