'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChevronRight, 
  Home, 
  BookOpen, 
  Code, 
  Award, 
  Settings,
  ArrowLeft,
  MoreHorizontal,
  Clock,
  CheckCircle,
  Circle,
  Play
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface BreadcrumbItem {
  id: string;
  label: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  isActive?: boolean;
  progress?: {
    current: number;
    total: number;
    percentage: number;
  };
  metadata?: {
    type: 'course' | 'lesson' | 'exercise' | 'quiz' | 'project';
    duration?: string;
    difficulty?: 'beginner' | 'intermediate' | 'advanced';
    isCompleted?: boolean;
    isLocked?: boolean;
  };
}

interface NavigationContext {
  currentPath: string;
  learningPath?: {
    courseId: string;
    courseName: string;
    moduleId?: string;
    moduleName?: string;
    lessonId?: string;
    lessonName?: string;
  };
  userProgress?: {
    overallProgress: number;
    currentStreak: number;
    completedLessons: number;
    totalLessons: number;
  };
}

interface EnhancedBreadcrumbsProps {
  items: BreadcrumbItem[];
  context?: NavigationContext;
  showProgress?: boolean;
  showKeyboardShortcuts?: boolean;
  maxVisibleItems?: number;
  className?: string;
  onNavigate?: (item: BreadcrumbItem) => void;
}

export function EnhancedBreadcrumbs({
  items,
  context,
  showProgress = true,
  showKeyboardShortcuts = true,
  maxVisibleItems = 5,
  className,
  onNavigate
}: EnhancedBreadcrumbsProps) {
  const [collapsedItems, setCollapsedItems] = useState<BreadcrumbItem[]>([]);
  const [visibleItems, setVisibleItems] = useState<BreadcrumbItem[]>([]);
  const [showTooltip, setShowTooltip] = useState<string | null>(null);

  // Handle keyboard shortcuts
  useEffect(() => {
    if (!showKeyboardShortcuts) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      // Alt + Home: Navigate to home
      if (event.altKey && event.key === 'Home') {
        event.preventDefault();
        const homeItem = items.find(item => item.href === '/');
        if (homeItem && onNavigate) {
          onNavigate(homeItem);
        }
      }

      // Alt + ArrowLeft: Navigate back
      if (event.altKey && event.key === 'ArrowLeft') {
        event.preventDefault();
        const currentIndex = items.findIndex(item => item.isActive);
        if (currentIndex > 0 && onNavigate) {
          onNavigate(items[currentIndex - 1]);
        }
      }

      // Alt + ArrowRight: Navigate forward (if available)
      if (event.altKey && event.key === 'ArrowRight') {
        event.preventDefault();
        const currentIndex = items.findIndex(item => item.isActive);
        if (currentIndex < items.length - 1 && onNavigate) {
          const nextItem = items[currentIndex + 1];
          if (!nextItem.metadata?.isLocked) {
            onNavigate(nextItem);
          }
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [items, onNavigate, showKeyboardShortcuts]);

  // Handle item collapsing for responsive design
  useEffect(() => {
    if (items.length <= maxVisibleItems) {
      setVisibleItems(items);
      setCollapsedItems([]);
      return;
    }

    // Always show first item (home), last item (current), and items around current
    const activeIndex = items.findIndex(item => item.isActive);
    const lastIndex = items.length - 1;

    if (activeIndex === -1) {
      // No active item, show first few and last
      setVisibleItems([
        items[0],
        ...items.slice(Math.max(1, lastIndex - maxVisibleItems + 2), lastIndex + 1)
      ]);
      setCollapsedItems(items.slice(1, Math.max(1, lastIndex - maxVisibleItems + 2)));
    } else {
      // Show items around active item
      const start = Math.max(1, activeIndex - Math.floor((maxVisibleItems - 2) / 2));
      const end = Math.min(lastIndex, start + maxVisibleItems - 3);
      
      const visible = [items[0]]; // Always include home
      if (start > 1) {
        setCollapsedItems(items.slice(1, start));
      } else {
        setCollapsedItems([]);
      }
      
      visible.push(...items.slice(start, end + 1));
      if (end < lastIndex) {
        visible.push(items[lastIndex]);
      }
      
      setVisibleItems(visible);
    }
  }, [items, maxVisibleItems]);

  const getItemIcon = (item: BreadcrumbItem) => {
    if (item.icon) return item.icon;
    
    switch (item.metadata?.type) {
      case 'course':
        return BookOpen;
      case 'lesson':
        return Play;
      case 'exercise':
        return Code;
      case 'quiz':
        return CheckCircle;
      case 'project':
        return Award;
      default:
        return Circle;
    }
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'text-green-400';
      case 'intermediate':
        return 'text-yellow-400';
      case 'advanced':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const renderBreadcrumbItem = (item: BreadcrumbItem, index: number, isLast: boolean) => {
    const Icon = getItemIcon(item);
    const isClickable = !item.isActive && !item.metadata?.isLocked;

    return (
      <div key={item.id} className="flex items-center">
        <motion.div
          className={cn(
            'flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 relative',
            item.isActive 
              ? 'bg-blue-600 text-white' 
              : isClickable
                ? 'hover:bg-white/10 text-gray-300 hover:text-white cursor-pointer'
                : 'text-gray-500 cursor-not-allowed',
            item.metadata?.isLocked && 'opacity-50'
          )}
          onClick={() => isClickable && onNavigate?.(item)}
          onMouseEnter={() => setShowTooltip(item.id)}
          onMouseLeave={() => setShowTooltip(null)}
          whileHover={isClickable ? { scale: 1.05 } : {}}
          whileTap={isClickable ? { scale: 0.95 } : {}}
        >
          <Icon className="w-4 h-4" />
          <span className="text-sm font-medium">{item.label}</span>
          
          {/* Progress indicator */}
          {item.progress && showProgress && (
            <div className="flex items-center space-x-1 ml-2">
              <span className="text-xs">
                {item.progress.current}/{item.progress.total}
              </span>
              <div className="w-8 h-1 bg-gray-600 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-green-400 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${item.progress.percentage}%` }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            </div>
          )}

          {/* Completion indicator */}
          {item.metadata?.isCompleted && (
            <CheckCircle className="w-3 h-3 text-green-400 ml-1" />
          )}

          {/* Tooltip */}
          <AnimatePresence>
            {showTooltip === item.id && (
              <motion.div
                className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 z-50"
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                <div className="bg-gray-900 border border-gray-700 rounded-lg p-3 shadow-xl min-w-[200px]">
                  <div className="font-medium text-white mb-1">{item.label}</div>
                  {item.metadata && (
                    <div className="space-y-1 text-xs">
                      <div className="flex items-center justify-between">
                        <span className="text-gray-400">Type:</span>
                        <span className="text-gray-300 capitalize">{item.metadata.type}</span>
                      </div>
                      {item.metadata.duration && (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">Duration:</span>
                          <span className="text-gray-300">{item.metadata.duration}</span>
                        </div>
                      )}
                      {item.metadata.difficulty && (
                        <div className="flex items-center justify-between">
                          <span className="text-gray-400">Difficulty:</span>
                          <span className={cn('capitalize', getDifficultyColor(item.metadata.difficulty))}>
                            {item.metadata.difficulty}
                          </span>
                        </div>
                      )}
                      {item.metadata.isCompleted && (
                        <div className="flex items-center space-x-1 text-green-400">
                          <CheckCircle className="w-3 h-3" />
                          <span>Completed</span>
                        </div>
                      )}
                      {item.metadata.isLocked && (
                        <div className="text-yellow-400 text-xs">
                          Complete previous lessons to unlock
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Separator */}
        {!isLast && (
          <ChevronRight className="w-4 h-4 text-gray-500 mx-2" />
        )}
      </div>
    );
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Main Breadcrumb Navigation */}
      <nav className="flex items-center space-x-1 overflow-x-auto scrollbar-hide" aria-label="Breadcrumb">
        <div className="flex items-center space-x-1 min-w-max">
          {/* Render visible items */}
          {visibleItems.map((item, index) => {
            // Check if we need to show collapsed indicator
            if (index === 1 && collapsedItems.length > 0) {
              return (
                <React.Fragment key={`collapsed-${index}`}>
                  {/* Collapsed items indicator */}
                  <div className="flex items-center">
                    <motion.button
                      className="flex items-center space-x-1 px-2 py-1 rounded text-gray-400 hover:text-white hover:bg-white/10 transition-colors"
                      onClick={() => {
                        // Show collapsed items in a dropdown or expand
                        console.log('Show collapsed items:', collapsedItems);
                      }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <MoreHorizontal className="w-4 h-4" />
                      <span className="text-xs">+{collapsedItems.length}</span>
                    </motion.button>
                    <ChevronRight className="w-4 h-4 text-gray-500 mx-2" />
                  </div>
                  {renderBreadcrumbItem(item, index, index === visibleItems.length - 1)}
                </React.Fragment>
              );
            }
            
            return renderBreadcrumbItem(item, index, index === visibleItems.length - 1);
          })}
        </div>
      </nav>

      {/* Learning Path Context */}
      {context?.learningPath && (
        <motion.div
          className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div>
                <h3 className="font-medium text-white">{context.learningPath.courseName}</h3>
                {context.learningPath.moduleName && (
                  <p className="text-sm text-gray-400">
                    Module: {context.learningPath.moduleName}
                  </p>
                )}
                {context.learningPath.lessonName && (
                  <p className="text-sm text-gray-400">
                    Current: {context.learningPath.lessonName}
                  </p>
                )}
              </div>
              
              {context.userProgress && showProgress && (
                <div className="flex items-center space-x-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-blue-400" />
                    <span className="text-gray-300">
                      {context.userProgress.completedLessons}/{context.userProgress.totalLessons} lessons
                    </span>
                  </div>
                  <div className="w-24 h-2 bg-gray-700 rounded-full overflow-hidden">
                    <motion.div
                      className="h-full bg-blue-500 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${context.userProgress.overallProgress}%` }}
                      transition={{ duration: 0.8 }}
                    />
                  </div>
                  <span className="text-blue-400 font-medium">
                    {Math.round(context.userProgress.overallProgress)}%
                  </span>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="flex items-center space-x-2">
              <EnhancedButton
                size="sm"
                variant="ghost"
                className="text-gray-400 hover:text-white"
                onClick={() => {
                  const currentIndex = items.findIndex(item => item.isActive);
                  if (currentIndex > 0 && onNavigate) {
                    onNavigate(items[currentIndex - 1]);
                  }
                }}
                disabled={items.findIndex(item => item.isActive) <= 0}
              >
                <ArrowLeft className="w-4 h-4 mr-1" />
                Previous
              </EnhancedButton>
              
              <EnhancedButton
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => {
                  const currentIndex = items.findIndex(item => item.isActive);
                  if (currentIndex < items.length - 1 && onNavigate) {
                    const nextItem = items[currentIndex + 1];
                    if (!nextItem.metadata?.isLocked) {
                      onNavigate(nextItem);
                    }
                  }
                }}
                disabled={
                  items.findIndex(item => item.isActive) >= items.length - 1 ||
                  items[items.findIndex(item => item.isActive) + 1]?.metadata?.isLocked
                }
              >
                Next
                <ChevronRight className="w-4 h-4 ml-1" />
              </EnhancedButton>
            </div>
          </div>
        </motion.div>
      )}

      {/* Keyboard Shortcuts Help */}
      {showKeyboardShortcuts && (
        <motion.div
          className="text-xs text-gray-500 flex items-center space-x-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <span>Shortcuts:</span>
          <span>Alt + Home (Home)</span>
          <span>Alt + ← (Back)</span>
          <span>Alt + → (Next)</span>
        </motion.div>
      )}
    </div>
  );
}

// Hook for breadcrumb navigation
export function useBreadcrumbNavigation() {
  const [navigationHistory, setNavigationHistory] = useState<BreadcrumbItem[]>([]);
  const [currentPath, setCurrentPath] = useState<string>('');

  const addToHistory = useCallback((item: BreadcrumbItem) => {
    setNavigationHistory(prev => {
      const filtered = prev.filter(historyItem => historyItem.id !== item.id);
      return [...filtered, item].slice(-10); // Keep last 10 items
    });
    setCurrentPath(item.href);
  }, []);

  const generateBreadcrumbs = useCallback((path: string): BreadcrumbItem[] => {
    const segments = path.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      {
        id: 'home',
        label: 'Home',
        href: '/',
        icon: Home,
        isActive: path === '/'
      }
    ];

    let currentPath = '';
    segments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === segments.length - 1;
      
      breadcrumbs.push({
        id: `segment-${index}`,
        label: segment.charAt(0).toUpperCase() + segment.slice(1).replace(/-/g, ' '),
        href: currentPath,
        isActive: isLast
      });
    });

    return breadcrumbs;
  }, []);

  return {
    navigationHistory,
    currentPath,
    addToHistory,
    generateBreadcrumbs
  };
}
