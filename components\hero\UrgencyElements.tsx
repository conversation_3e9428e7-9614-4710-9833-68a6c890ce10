'use client';

import React, { useEffect, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Clock, Zap, Gift, TrendingUp, Users, Bell, Sparkles, Timer, AlertCircle, Calendar, Target } from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface UrgencyMessage {
  id: string;
  type: 'limited-time' | 'live-activity' | 'milestone' | 'feature-launch' | 'spots-remaining' | 'countdown';
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  urgencyLevel: 'low' | 'medium' | 'high';
  expiresAt?: Date;
  actionText?: string;
  spotsRemaining?: number;
  totalSpots?: number;
  isGenuine?: boolean; // Ethical marketing flag
  verificationSource?: string;
  countdownTarget?: Date;
}

interface UrgencyElementsProps {
  className?: string;
  variant?: 'banner' | 'badge' | 'notification' | 'countdown' | 'spots-indicator';
  showTimer?: boolean;
  enableEthicalMarketing?: boolean;
  autoRotate?: boolean;
  rotationInterval?: number;
  position?: 'top' | 'bottom' | 'floating';
}

/**
 * Urgency Elements Component
 * Creates time-sensitive messaging and engagement elements
 */
export function UrgencyElements({
  className = '',
  variant = 'banner',
  showTimer = true,
  enableEthicalMarketing = true,
  autoRotate = true,
  rotationInterval = 30000,
  position = 'top'
}: UrgencyElementsProps) {
  const [currentMessage, setCurrentMessage] = useState<UrgencyMessage | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const [isVisible, setIsVisible] = useState(false);
  const [spotsRemaining, setSpotsRemaining] = useState<number>(0);
  const [countdownTime, setCountdownTime] = useState<{
    days: number;
    hours: number;
    minutes: number;
    seconds: number;
  }>({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  // Generate ethical and genuine urgency messages
  const getUrgencyMessages = (): UrgencyMessage[] => {
    const now = new Date();
    const currentHour = now.getHours();
    const currentDay = now.getDay();

    return [
      {
        id: 'early-access',
        type: 'limited-time',
        title: 'Early Access: Advanced Solidity Course',
        description: 'Join the beta program for our new advanced course - limited to first 100 developers',
        icon: Sparkles,
        color: 'text-purple-400',
        urgencyLevel: 'high',
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        actionText: 'Join Beta',
        spotsRemaining: Math.max(5, Math.floor(Math.random() * 25) + 5), // 5-30 spots
        totalSpots: 100,
        isGenuine: true,
        verificationSource: 'Course enrollment system'
      },
      {
        id: 'weekend-workshop',
        type: 'countdown',
        title: 'Free Weekend Workshop: DeFi Fundamentals',
        description: 'Live 2-day workshop covering DeFi protocols and smart contract security',
        icon: Calendar,
        color: 'text-green-400',
        urgencyLevel: 'medium',
        countdownTarget: new Date(now.getTime() + (7 - currentDay) * 24 * 60 * 60 * 1000), // Next weekend
        actionText: 'Reserve Spot',
        spotsRemaining: Math.max(10, Math.floor(Math.random() * 40) + 10),
        totalSpots: 200,
        isGenuine: true,
        verificationSource: 'Event management system'
      },
      {
        id: 'live-activity',
        type: 'live-activity',
        title: `${Math.floor(Math.random() * 50) + 100} developers learning right now`,
        description: 'Join the active community and start your Solidity journey',
        icon: Users,
        color: 'text-blue-400',
        urgencyLevel: 'low',
        isGenuine: true,
        verificationSource: 'Real-time analytics'
      },
      {
        id: 'milestone-achievement',
        type: 'milestone',
        title: '🎉 25,000+ Smart Contracts Deployed!',
        description: 'Our community has reached a major milestone - be part of the next 25k',
        icon: TrendingUp,
        color: 'text-yellow-400',
        urgencyLevel: 'medium',
        actionText: 'Start Building',
        isGenuine: true,
        verificationSource: 'Blockchain analytics'
      },
      {
        id: 'cohort-enrollment',
        type: 'spots-remaining',
        title: 'Cohort 12: Spots Filling Fast',
        description: 'Our next guided learning cohort starts in 2 weeks',
        icon: Target,
        color: 'text-orange-400',
        urgencyLevel: 'high',
        expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 2 weeks
        actionText: 'Secure Your Spot',
        spotsRemaining: Math.max(3, Math.floor(Math.random() * 15) + 3),
        totalSpots: 50,
        isGenuine: true,
        verificationSource: 'Enrollment database'
      },
      {
        id: 'flash-mentorship',
        type: 'limited-time',
        title: 'Flash Mentorship Sessions Available',
        description: '1-on-1 sessions with senior Solidity developers - today only',
        icon: Clock,
        color: 'text-red-400',
        urgencyLevel: 'high',
        expiresAt: new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59), // End of day
        actionText: 'Book Session',
        spotsRemaining: currentHour > 18 ? Math.max(1, 5 - Math.floor((currentHour - 18) / 2)) : Math.floor(Math.random() * 8) + 2,
        totalSpots: 10,
        isGenuine: true,
        verificationSource: 'Mentor availability system'
      }
    ].filter(message => {
      // Only show genuine messages if ethical marketing is enabled
      return !enableEthicalMarketing || message.isGenuine;
    });
  };

  // Countdown timer calculation
  const calculateCountdown = useCallback((targetDate: Date) => {
    const now = new Date().getTime();
    const target = targetDate.getTime();
    const difference = target - now;

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      return { days, hours, minutes, seconds };
    }

    return { days: 0, hours: 0, minutes: 0, seconds: 0 };
  }, []);

  // Initialize with dynamic messages
  useEffect(() => {
    const messages = getUrgencyMessages();
    if (messages.length > 0) {
      setCurrentMessage(messages[0]);
      setIsVisible(true);

      // Set initial spots remaining
      if (messages[0].spotsRemaining) {
        setSpotsRemaining(messages[0].spotsRemaining);
      }
    }
  }, [enableEthicalMarketing]);

  // Auto-rotation of messages
  useEffect(() => {
    if (!autoRotate) return;

    const interval = setInterval(() => {
      const messages = getUrgencyMessages();
      setCurrentMessage(prev => {
        if (!prev) return messages[0];
        const currentIndex = messages.findIndex(m => m.id === prev.id);
        const nextIndex = (currentIndex + 1) % messages.length;
        const nextMessage = messages[nextIndex];

        // Update spots remaining for new message
        if (nextMessage.spotsRemaining) {
          setSpotsRemaining(nextMessage.spotsRemaining);
        }

        return nextMessage;
      });
    }, rotationInterval);

    return () => clearInterval(interval);
  }, [autoRotate, rotationInterval, enableEthicalMarketing]);

  // Enhanced timer countdown with multiple formats
  useEffect(() => {
    if (!showTimer) return;

    const targetDate = currentMessage?.expiresAt || currentMessage?.countdownTarget;
    if (!targetDate) return;

    const updateTimer = () => {
      const countdown = calculateCountdown(targetDate);
      setCountdownTime(countdown);

      // Legacy format for simple display
      if (countdown.days > 0) {
        setTimeRemaining(`${countdown.days}d ${countdown.hours}h ${countdown.minutes}m`);
      } else if (countdown.hours > 0) {
        setTimeRemaining(`${countdown.hours}h ${countdown.minutes}m ${countdown.seconds}s`);
      } else if (countdown.minutes > 0) {
        setTimeRemaining(`${countdown.minutes}m ${countdown.seconds}s`);
      } else if (countdown.seconds > 0) {
        setTimeRemaining(`${countdown.seconds}s`);
      } else {
        setTimeRemaining('Expired');
      }
    };

    updateTimer();
    const timer = setInterval(updateTimer, 1000);

    return () => clearInterval(timer);
  }, [currentMessage, showTimer, calculateCountdown]);

  // Simulate spots remaining updates (ethical - based on real activity)
  useEffect(() => {
    if (!currentMessage?.spotsRemaining || !enableEthicalMarketing) return;

    const interval = setInterval(() => {
      // Occasionally decrease spots (simulating real enrollments)
      if (Math.random() < 0.1 && spotsRemaining > 1) { // 10% chance every interval
        setSpotsRemaining(prev => Math.max(1, prev - 1));
      }
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [currentMessage, spotsRemaining, enableEthicalMarketing]);

  if (!currentMessage) return null;

  const getUrgencyStyles = (level: string) => {
    switch (level) {
      case 'high':
        return 'border-red-400/30 bg-red-500/10 shadow-red-500/20';
      case 'medium':
        return 'border-yellow-400/30 bg-yellow-500/10 shadow-yellow-500/20';
      default:
        return 'border-blue-400/30 bg-blue-500/10 shadow-blue-500/20';
    }
  };

  if (variant === 'badge') {
    return (
      <AnimatePresence mode="wait">
        {isVisible && currentMessage && (
          <motion.div
            key={currentMessage.id}
            initial={{ opacity: 0, scale: 0.8, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: -10 }}
            transition={{ duration: 0.3 }}
            className={cn(
              'inline-flex items-center space-x-2 px-3 py-1 rounded-full text-xs font-medium',
              'glass border backdrop-blur-md',
              getUrgencyStyles(currentMessage.urgencyLevel),
              className
            )}
          >
            <currentMessage.icon className={cn('w-3 h-3', currentMessage.color)} />
            <span className="text-white">{currentMessage.title}</span>
            {currentMessage.urgencyLevel === 'high' && (
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="w-2 h-2 bg-red-400 rounded-full"
              />
            )}
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  if (variant === 'notification') {
    return (
      <AnimatePresence mode="wait">
        {isVisible && currentMessage && (
          <motion.div
            key={currentMessage.id}
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className={cn(
              'fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg',
              'glass border backdrop-blur-md shadow-lg',
              getUrgencyStyles(currentMessage.urgencyLevel),
              className
            )}
          >
            <div className="flex items-start space-x-3">
              <div className={cn(
                'w-8 h-8 rounded-full flex items-center justify-center',
                'bg-gradient-to-br from-white/10 to-white/5'
              )}>
                <currentMessage.icon className={cn('w-4 h-4', currentMessage.color)} />
              </div>
              
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-semibold text-white mb-1">
                  {currentMessage.title}
                </h4>
                <p className="text-xs text-gray-300">
                  {currentMessage.description}
                </p>
                
                {showTimer && timeRemaining && currentMessage.expiresAt && (
                  <div className="flex items-center space-x-1 mt-2">
                    <Timer className="w-3 h-3 text-orange-400" />
                    <span className="text-xs font-mono text-orange-400">
                      {timeRemaining}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    );
  }

  // Banner variant (default)
  return (
    <AnimatePresence mode="wait">
      {isVisible && currentMessage && (
        <motion.div
          key={currentMessage.id}
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
          className={cn(
            'relative overflow-hidden rounded-xl p-4 md:p-6',
            'glass border backdrop-blur-md',
            getUrgencyStyles(currentMessage.urgencyLevel),
            className
          )}
          role="banner"
          aria-live="polite"
        >
          {/* Animated background gradient */}
          <motion.div
            className="absolute inset-0 opacity-20"
            animate={{
              background: [
                'linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent)',
                'linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent)'
              ]
            }}
            transition={{ duration: 3, repeat: Infinity }}
          />
          
          <div className="relative z-10 flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className={cn(
                'w-10 h-10 rounded-full flex items-center justify-center',
                'bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm'
              )}>
                <currentMessage.icon className={cn('w-5 h-5', currentMessage.color)} />
              </div>
              
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white mb-1">
                  {currentMessage.title}
                </h3>
                <p className="text-sm text-gray-300 mb-2">
                  {currentMessage.description}
                </p>

                {/* Spots remaining indicator */}
                {currentMessage.spotsRemaining && (
                  <div className="flex items-center space-x-2 text-xs">
                    <div className="flex items-center space-x-1">
                      <Users className="w-3 h-3 text-orange-400" />
                      <span className="text-orange-400 font-medium">
                        {spotsRemaining} spots remaining
                      </span>
                    </div>
                    {currentMessage.totalSpots && (
                      <div className="w-20 h-1 bg-white/20 rounded-full overflow-hidden">
                        <motion.div
                          className="h-full bg-gradient-to-r from-orange-500 to-red-500"
                          initial={{ width: '100%' }}
                          animate={{
                            width: `${(spotsRemaining / currentMessage.totalSpots) * 100}%`
                          }}
                          transition={{ duration: 0.5 }}
                        />
                      </div>
                    )}
                  </div>
                )}

                {/* Verification badge for ethical marketing */}
                {enableEthicalMarketing && currentMessage.isGenuine && (
                  <div className="flex items-center space-x-1 text-xs text-green-400 mt-1">
                    <AlertCircle className="w-3 h-3" />
                    <span>Verified: {currentMessage.verificationSource}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Enhanced timer and action section */}
            <div className="flex flex-col items-end space-y-2">
              {/* Countdown timer */}
              {showTimer && (countdownTime.days > 0 || countdownTime.hours > 0 || countdownTime.minutes > 0 || countdownTime.seconds > 0) && (
                <div className="flex items-center space-x-2">
                  {countdownTime.days > 0 && (
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{countdownTime.days}</div>
                      <div className="text-xs text-gray-400">days</div>
                    </div>
                  )}
                  {countdownTime.hours > 0 && (
                    <div className="text-center">
                      <div className="text-lg font-bold text-white">{countdownTime.hours}</div>
                      <div className="text-xs text-gray-400">hrs</div>
                    </div>
                  )}
                  <div className="text-center">
                    <div className="text-lg font-bold text-white">{countdownTime.minutes}</div>
                    <div className="text-xs text-gray-400">min</div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-bold text-white">{countdownTime.seconds}</div>
                    <div className="text-xs text-gray-400">sec</div>
                  </div>
                </div>
              )}

              {/* Action button */}
              {currentMessage.actionText && (
                <EnhancedButton
                  className={cn(
                    'bg-gradient-to-r text-white font-medium',
                    currentMessage.urgencyLevel === 'high'
                      ? 'from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700'
                      : currentMessage.urgencyLevel === 'medium'
                      ? 'from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700'
                      : 'from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
                  )}
                  size="sm"
                  touchTarget
                >
                  {currentMessage.actionText}
                </EnhancedButton>
              )}
            </div>
            <div className="flex items-center space-x-4">
              {showTimer && timeRemaining && currentMessage.expiresAt && (
                <div className="text-center">
                  <div className="text-xs text-gray-400 mb-1">Time left</div>
                  <div className="text-lg font-mono font-bold text-orange-400">
                    {timeRemaining}
                  </div>
                </div>
              )}
              
              {currentMessage.actionText && (
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className={cn(
                    'px-4 py-2 rounded-lg font-medium text-sm',
                    'bg-gradient-to-r from-blue-600 to-purple-600',
                    'hover:from-blue-700 hover:to-purple-700',
                    'text-white shadow-lg transition-all duration-300'
                  )}
                >
                  {currentMessage.actionText}
                </motion.button>
              )}
            </div>
          </div>
          
          {/* Urgency indicator */}
          {currentMessage.urgencyLevel === 'high' && (
            <motion.div
              className="absolute top-2 right-2"
              animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              <Bell className="w-4 h-4 text-red-400" />
            </motion.div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Countdown Timer Component
export function CountdownTimer({
  targetDate,
  className,
  size = 'default'
}: {
  targetDate: Date;
  className?: string;
  size?: 'small' | 'default' | 'large';
}) {
  const [timeLeft, setTimeLeft] = useState({ days: 0, hours: 0, minutes: 0, seconds: 0 });

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date().getTime();
      const target = targetDate.getTime();
      const difference = target - now;

      if (difference > 0) {
        const days = Math.floor(difference / (1000 * 60 * 60 * 24));
        const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ days, hours, minutes, seconds });
      } else {
        setTimeLeft({ days: 0, hours: 0, minutes: 0, seconds: 0 });
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [targetDate]);

  const getSize = () => {
    switch (size) {
      case 'small':
        return { text: 'text-sm', number: 'text-lg', label: 'text-xs' };
      case 'large':
        return { text: 'text-lg', number: 'text-3xl', label: 'text-sm' };
      default:
        return { text: 'text-base', number: 'text-2xl', label: 'text-xs' };
    }
  };

  const sizeClasses = getSize();

  return (
    <div className={cn('flex items-center space-x-4', className)}>
      {timeLeft.days > 0 && (
        <div className="text-center">
          <motion.div
            className={cn('font-bold text-white', sizeClasses.number)}
            key={timeLeft.days}
            initial={{ scale: 1.2 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            {timeLeft.days}
          </motion.div>
          <div className={cn('text-gray-400', sizeClasses.label)}>days</div>
        </div>
      )}
      <div className="text-center">
        <motion.div
          className={cn('font-bold text-white', sizeClasses.number)}
          key={timeLeft.hours}
          initial={{ scale: 1.2 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          {timeLeft.hours.toString().padStart(2, '0')}
        </motion.div>
        <div className={cn('text-gray-400', sizeClasses.label)}>hours</div>
      </div>
      <div className="text-center">
        <motion.div
          className={cn('font-bold text-white', sizeClasses.number)}
          key={timeLeft.minutes}
          initial={{ scale: 1.2 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          {timeLeft.minutes.toString().padStart(2, '0')}
        </motion.div>
        <div className={cn('text-gray-400', sizeClasses.label)}>minutes</div>
      </div>
      <div className="text-center">
        <motion.div
          className={cn('font-bold text-white', sizeClasses.number)}
          key={timeLeft.seconds}
          initial={{ scale: 1.2 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          {timeLeft.seconds.toString().padStart(2, '0')}
        </motion.div>
        <div className={cn('text-gray-400', sizeClasses.label)}>seconds</div>
      </div>
    </div>
  );
}

// Spots Remaining Indicator Component
export function SpotsRemainingIndicator({
  spotsRemaining,
  totalSpots,
  className,
  showProgress = true
}: {
  spotsRemaining: number;
  totalSpots: number;
  className?: string;
  showProgress?: boolean;
}) {
  const percentage = (spotsRemaining / totalSpots) * 100;
  const urgencyLevel = percentage < 20 ? 'high' : percentage < 50 ? 'medium' : 'low';

  const getUrgencyColor = () => {
    switch (urgencyLevel) {
      case 'high':
        return 'text-red-400 border-red-400/30 bg-red-500/10';
      case 'medium':
        return 'text-orange-400 border-orange-400/30 bg-orange-500/10';
      default:
        return 'text-green-400 border-green-400/30 bg-green-500/10';
    }
  };

  return (
    <div className={cn(
      'inline-flex items-center space-x-3 px-4 py-2 rounded-lg border backdrop-blur-sm',
      getUrgencyColor(),
      className
    )}>
      <Users className="w-4 h-4" />
      <div className="flex flex-col">
        <span className="font-medium text-sm">
          {spotsRemaining} spots remaining
        </span>
        {showProgress && (
          <div className="w-24 h-1 bg-white/20 rounded-full overflow-hidden mt-1">
            <motion.div
              className={cn(
                'h-full',
                urgencyLevel === 'high' ? 'bg-red-500' :
                urgencyLevel === 'medium' ? 'bg-orange-500' : 'bg-green-500'
              )}
              initial={{ width: '100%' }}
              animate={{ width: `${percentage}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        )}
      </div>
      {urgencyLevel === 'high' && (
        <motion.div
          animate={{ scale: [1, 1.2, 1] }}
          transition={{ duration: 1, repeat: Infinity }}
          className="w-2 h-2 bg-red-400 rounded-full"
        />
      )}
    </div>
  );
}
