'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Shield, Star, Award, Users, CheckCircle, Building, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TrustIndicator {
  id: string;
  type: 'certification' | 'rating' | 'company' | 'achievement';
  title: string;
  subtitle: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  verified?: boolean;
}

interface SocialProofIndicatorsProps {
  className?: string;
  variant?: 'full' | 'compact' | 'minimal';
  showCompanyLogos?: boolean;
}

/**
 * Social Proof Indicators Component
 * Displays trust signals, ratings, and credibility indicators
 */
export function SocialProofIndicators({ 
  className = '', 
  variant = 'full',
  showCompanyLogos = true 
}: SocialProofIndicatorsProps) {
  const [currentIndicatorIndex, setCurrentIndicatorIndex] = useState(0);

  const trustIndicators: TrustIndicator[] = [
    {
      id: 'completion-rate',
      type: 'achievement',
      title: '94% Completion Rate',
      subtitle: 'Industry-leading success rate',
      icon: Award,
      color: 'text-yellow-400',
      verified: true
    },
    {
      id: 'rating',
      type: 'rating',
      title: '4.9/5 Rating',
      subtitle: 'From 2,500+ reviews',
      icon: Star,
      color: 'text-yellow-400',
      verified: true
    },
    {
      id: 'certification',
      type: 'certification',
      title: 'Certified Platform',
      subtitle: 'Blockchain education verified',
      icon: Shield,
      color: 'text-green-400',
      verified: true
    },
    {
      id: 'community',
      type: 'achievement',
      title: '10,000+ Developers',
      subtitle: 'Active learning community',
      icon: Users,
      color: 'text-blue-400',
      verified: true
    }
  ];

  const companyLogos = [
    { name: 'ConsenSys', logo: '🔷' },
    { name: 'Chainlink', logo: '🔗' },
    { name: 'Polygon', logo: '🟣' },
    { name: 'Ethereum Foundation', logo: '💎' },
    { name: 'OpenZeppelin', logo: '🛡️' },
    { name: 'Alchemy', logo: '⚗️' }
  ];

  // Rotate through indicators
  useEffect(() => {
    if (variant === 'minimal') return;
    
    const interval = setInterval(() => {
      setCurrentIndicatorIndex((prev) => 
        (prev + 1) % trustIndicators.length
      );
    }, 4000);

    return () => clearInterval(interval);
  }, [variant, trustIndicators.length]);

  if (variant === 'minimal') {
    return (
      <motion.div
        className={cn('flex items-center justify-center space-x-6', className)}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1.8, duration: 0.6 }}
      >
        <div className="flex items-center space-x-2">
          <Star className="w-4 h-4 text-yellow-400 fill-current" />
          <span className="text-sm font-medium text-gray-300">4.9/5 rated</span>
        </div>
        <div className="w-1 h-1 bg-gray-500 rounded-full" />
        <div className="flex items-center space-x-2">
          <Shield className="w-4 h-4 text-green-400" />
          <span className="text-sm font-medium text-gray-300">Verified platform</span>
        </div>
        <div className="w-1 h-1 bg-gray-500 rounded-full" />
        <div className="flex items-center space-x-2">
          <Users className="w-4 h-4 text-blue-400" />
          <span className="text-sm font-medium text-gray-300">10k+ learners</span>
        </div>
      </motion.div>
    );
  }

  if (variant === 'compact') {
    return (
      <motion.div
        className={cn('space-y-4', className)}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1.6, duration: 0.8 }}
      >
        {/* Rotating Trust Indicator */}
        <div className="relative h-16 overflow-hidden">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentIndicatorIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.5 }}
              className="absolute inset-0 flex items-center justify-center"
            >
              <TrustIndicatorCard 
                indicator={trustIndicators[currentIndicatorIndex]} 
                compact 
              />
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Company Logos */}
        {showCompanyLogos && (
          <motion.div
            className="text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2, duration: 0.6 }}
          >
            <p className="text-xs text-gray-400 mb-2">Trusted by developers at</p>
            <div className="flex items-center justify-center space-x-4">
              {companyLogos.slice(0, 4).map((company, index) => (
                <motion.div
                  key={company.name}
                  className="flex items-center space-x-1 text-xs text-gray-300"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 2.2 + index * 0.1, duration: 0.4 }}
                >
                  <span className="text-sm">{company.logo}</span>
                  <span className="hidden sm:inline">{company.name}</span>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </motion.div>
    );
  }

  // Full variant
  return (
    <motion.div
      className={cn('space-y-6', className)}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1.4, duration: 0.8 }}
    >
      {/* Trust Indicators Grid */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {trustIndicators.map((indicator, index) => (
          <motion.div
            key={indicator.id}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1.6 + index * 0.1, duration: 0.6 }}
          >
            <TrustIndicatorCard indicator={indicator} />
          </motion.div>
        ))}
      </div>

      {/* Company Logos */}
      {showCompanyLogos && (
        <motion.div
          className="text-center space-y-3"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2.2, duration: 0.6 }}
        >
          <p className="text-sm text-gray-400">Trusted by developers at leading Web3 companies</p>
          <div className="flex items-center justify-center flex-wrap gap-6">
            {companyLogos.map((company, index) => (
              <motion.div
                key={company.name}
                className="flex items-center space-x-2 text-sm text-gray-300 hover:text-white transition-colors duration-300"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 2.4 + index * 0.1, duration: 0.4 }}
                whileHover={{ scale: 1.05 }}
              >
                <span className="text-lg">{company.logo}</span>
                <span className="font-medium">{company.name}</span>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}
    </motion.div>
  );
}

/**
 * Individual Trust Indicator Card
 */
function TrustIndicatorCard({ 
  indicator, 
  compact = false 
}: { 
  indicator: TrustIndicator; 
  compact?: boolean; 
}) {
  const IconComponent = indicator.icon;

  return (
    <div className={cn(
      'glass rounded-lg border border-white/10 hover:border-white/20 transition-all duration-300',
      'backdrop-blur-md bg-white/5 group cursor-default',
      compact ? 'p-3' : 'p-4'
    )}>
      <div className={cn(
        'flex items-center',
        compact ? 'space-x-3' : 'flex-col space-y-2 text-center'
      )}>
        <div className={cn(
          'rounded-full flex items-center justify-center',
          'bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm',
          'group-hover:scale-110 transition-transform duration-300',
          compact ? 'w-8 h-8' : 'w-10 h-10'
        )}>
          <IconComponent className={cn(
            indicator.color,
            compact ? 'w-4 h-4' : 'w-5 h-5'
          )} />
          {indicator.verified && (
            <CheckCircle className="w-3 h-3 text-green-400 absolute -top-1 -right-1" />
          )}
        </div>
        
        <div className={cn(compact && 'flex-1')}>
          <div className={cn(
            'font-semibold',
            indicator.color,
            compact ? 'text-sm' : 'text-base'
          )}>
            {indicator.title}
          </div>
          <div className={cn(
            'text-gray-400',
            compact ? 'text-xs' : 'text-sm'
          )}>
            {indicator.subtitle}
          </div>
        </div>
      </div>
    </div>
  );
}
