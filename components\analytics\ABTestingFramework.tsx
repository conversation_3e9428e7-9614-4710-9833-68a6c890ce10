'use client';

import React, { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BarChart3, TrendingUp, Users, Target, Zap, AlertCircle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ABTest {
  id: string;
  name: string;
  description: string;
  status: 'draft' | 'running' | 'completed' | 'paused';
  variants: ABVariant[];
  trafficAllocation: number; // Percentage of users to include
  startDate: Date;
  endDate?: Date;
  targetMetric: string;
  minimumSampleSize: number;
  confidenceLevel: number;
  statisticalSignificance?: number;
  winner?: string;
}

interface ABVariant {
  id: string;
  name: string;
  description: string;
  weight: number; // Traffic split percentage
  conversions: number;
  visitors: number;
  conversionRate: number;
  isControl: boolean;
  config: Record<string, any>;
}

interface ABTestResult {
  testId: string;
  variant: string;
  isStatisticallySignificant: boolean;
  confidenceInterval: [number, number];
  pValue: number;
  lift: number;
  recommendation: 'winner' | 'loser' | 'inconclusive';
}

interface UserSegment {
  id: string;
  name: string;
  criteria: Record<string, any>;
  size: number;
  conversionRate: number;
}

// A/B Testing Context
const ABTestingContext = createContext<{
  activeTests: ABTest[];
  userVariants: Record<string, string>;
  assignVariant: (testId: string) => string;
  trackConversion: (testId: string, variant: string) => void;
  getVariantConfig: (testId: string) => any;
} | null>(null);

// Statistical calculations
function calculateStatisticalSignificance(
  controlConversions: number,
  controlVisitors: number,
  variantConversions: number,
  variantVisitors: number
): { pValue: number; isSignificant: boolean; confidenceInterval: [number, number] } {
  const controlRate = controlConversions / controlVisitors;
  const variantRate = variantConversions / variantVisitors;
  
  // Simplified z-test calculation
  const pooledRate = (controlConversions + variantConversions) / (controlVisitors + variantVisitors);
  const standardError = Math.sqrt(pooledRate * (1 - pooledRate) * (1/controlVisitors + 1/variantVisitors));
  
  const zScore = Math.abs(variantRate - controlRate) / standardError;
  const pValue = 2 * (1 - normalCDF(Math.abs(zScore)));
  
  // 95% confidence interval
  const marginOfError = 1.96 * Math.sqrt((variantRate * (1 - variantRate)) / variantVisitors);
  const confidenceInterval: [number, number] = [
    Math.max(0, variantRate - marginOfError),
    Math.min(1, variantRate + marginOfError)
  ];
  
  return {
    pValue,
    isSignificant: pValue < 0.05,
    confidenceInterval
  };
}

// Normal CDF approximation
function normalCDF(x: number): number {
  return 0.5 * (1 + erf(x / Math.sqrt(2)));
}

function erf(x: number): number {
  // Approximation of error function
  const a1 =  0.254829592;
  const a2 = -0.284496736;
  const a3 =  1.421413741;
  const a4 = -1.453152027;
  const a5 =  1.061405429;
  const p  =  0.3275911;

  const sign = x < 0 ? -1 : 1;
  x = Math.abs(x);

  const t = 1.0 / (1.0 + p * x);
  const y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * Math.exp(-x * x);

  return sign * y;
}

// A/B Testing Hook
export function useABTesting() {
  const [tests, setTests] = useState<ABTest[]>([]);
  const [userVariants, setUserVariants] = useState<Record<string, string>>({});
  const [userSegment, setUserSegment] = useState<UserSegment | null>(null);

  useEffect(() => {
    // Load existing test assignments
    const savedVariants = localStorage.getItem('ab_test_variants');
    if (savedVariants) {
      try {
        setUserVariants(JSON.parse(savedVariants));
      } catch (error) {
        console.error('Failed to load A/B test variants:', error);
      }
    }

    // Load user segment
    const savedSegment = localStorage.getItem('user_segment');
    if (savedSegment) {
      try {
        setUserSegment(JSON.parse(savedSegment));
      } catch (error) {
        console.error('Failed to load user segment:', error);
      }
    }

    // Initialize with mock tests
    setTests([
      {
        id: 'hero-cta-test',
        name: 'Hero CTA Button Text',
        description: 'Testing different CTA button text on hero section',
        status: 'running',
        variants: [
          {
            id: 'control',
            name: 'Start Free Trial',
            description: 'Original button text',
            weight: 50,
            conversions: 45,
            visitors: 1200,
            conversionRate: 3.75,
            isControl: true,
            config: { buttonText: 'Start Free Trial', buttonColor: 'blue' }
          },
          {
            id: 'variant-a',
            name: 'Begin Learning Now',
            description: 'Action-oriented text',
            weight: 50,
            conversions: 58,
            visitors: 1180,
            conversionRate: 4.92,
            isControl: false,
            config: { buttonText: 'Begin Learning Now', buttonColor: 'blue' }
          }
        ],
        trafficAllocation: 80,
        startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        targetMetric: 'trial_signup',
        minimumSampleSize: 1000,
        confidenceLevel: 95
      },
      {
        id: 'urgency-timer-test',
        name: 'Urgency Timer Messaging',
        description: 'Testing different urgency timer messages',
        status: 'running',
        variants: [
          {
            id: 'control',
            name: 'Limited Time Offer',
            description: 'Generic urgency message',
            weight: 33,
            conversions: 23,
            visitors: 800,
            conversionRate: 2.88,
            isControl: true,
            config: { message: 'Limited time offer expires soon!', color: 'red' }
          },
          {
            id: 'variant-a',
            name: 'Specific Deadline',
            description: 'Specific time-based urgency',
            weight: 33,
            conversions: 31,
            visitors: 790,
            conversionRate: 3.92,
            isControl: false,
            config: { message: 'Offer expires in 23:45:12', color: 'orange' }
          },
          {
            id: 'variant-b',
            name: 'Social Proof Urgency',
            description: 'Combines urgency with social proof',
            weight: 34,
            conversions: 35,
            visitors: 810,
            conversionRate: 4.32,
            isControl: false,
            config: { message: '47 spots left - 23 people viewing', color: 'red' }
          }
        ],
        trafficAllocation: 60,
        startDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
        targetMetric: 'urgency_conversion',
        minimumSampleSize: 800,
        confidenceLevel: 95
      }
    ]);
  }, []);

  const assignVariant = useCallback((testId: string) => {
    // Check if user already has a variant assigned
    if (userVariants[testId]) {
      return userVariants[testId];
    }

    const test = tests.find(t => t.id === testId);
    if (!test || test.status !== 'running') {
      return 'control';
    }

    // Check if user should be included in test
    if (Math.random() * 100 > test.trafficAllocation) {
      return 'control';
    }

    // Assign variant based on weights
    const random = Math.random() * 100;
    let cumulative = 0;
    
    for (const variant of test.variants) {
      cumulative += variant.weight;
      if (random <= cumulative) {
        const newVariants = { ...userVariants, [testId]: variant.id };
        setUserVariants(newVariants);
        localStorage.setItem('ab_test_variants', JSON.stringify(newVariants));
        
        // Track assignment
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'ab_test_assignment', {
            test_id: testId,
            variant_id: variant.id,
            user_segment: userSegment?.id || 'unknown'
          });
        }
        
        return variant.id;
      }
    }
    
    return 'control';
  }, [tests, userVariants, userSegment]);

  const trackConversion = useCallback((testId: string, variantId: string) => {
    setTests(prev => prev.map(test => {
      if (test.id === testId) {
        return {
          ...test,
          variants: test.variants.map(variant => {
            if (variant.id === variantId) {
              const newConversions = variant.conversions + 1;
              return {
                ...variant,
                conversions: newConversions,
                conversionRate: (newConversions / variant.visitors) * 100
              };
            }
            return variant;
          })
        };
      }
      return test;
    }));

    // Track with analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'ab_test_conversion', {
        test_id: testId,
        variant_id: variantId,
        user_segment: userSegment?.id || 'unknown'
      });
    }
  }, [testId, variantId, userSegment]);

  return { trackConversion };
}

// Personalization Engine
export function usePersonalizationEngine() {
  const [userProfile, setUserProfile] = useState<{
    segment: string;
    experience: 'beginner' | 'intermediate' | 'advanced';
    interests: string[];
    behavior: {
      preferredLearningStyle: 'visual' | 'hands-on' | 'reading';
      sessionDuration: number;
      completionRate: number;
      engagementScore: number;
    };
    demographics: {
      role?: string;
      industry?: string;
      goals?: string[];
    };
  } | null>(null);

  const [personalizedContent, setPersonalizedContent] = useState<{
    heroMessage: string;
    ctaText: string;
    recommendedPath: string;
    urgencyLevel: 'low' | 'medium' | 'high';
    socialProofType: 'numbers' | 'testimonials' | 'achievements';
  }>({
    heroMessage: 'Learn Solidity Development',
    ctaText: 'Start Free Trial',
    recommendedPath: 'beginner',
    urgencyLevel: 'medium',
    socialProofType: 'numbers'
  });

  useEffect(() => {
    // Load user profile from localStorage
    const savedProfile = localStorage.getItem('user_profile');
    if (savedProfile) {
      try {
        const profile = JSON.parse(savedProfile);
        setUserProfile(profile);
        generatePersonalizedContent(profile);
      } catch (error) {
        console.error('Failed to load user profile:', error);
      }
    }
  }, []);

  const generatePersonalizedContent = useCallback((profile: any) => {
    if (!profile) return;

    let content = { ...personalizedContent };

    // Personalize based on experience level
    switch (profile.experience) {
      case 'beginner':
        content.heroMessage = 'Start Your Blockchain Development Journey';
        content.ctaText = 'Begin Learning';
        content.recommendedPath = 'fundamentals';
        content.urgencyLevel = 'low';
        break;
      case 'intermediate':
        content.heroMessage = 'Master Advanced Solidity Concepts';
        content.ctaText = 'Continue Learning';
        content.recommendedPath = 'intermediate';
        content.urgencyLevel = 'medium';
        break;
      case 'advanced':
        content.heroMessage = 'Build Production-Ready DApps';
        content.ctaText = 'Start Building';
        content.recommendedPath = 'advanced';
        content.urgencyLevel = 'high';
        break;
    }

    // Personalize based on role
    if (profile.demographics?.role) {
      switch (profile.demographics.role) {
        case 'developer':
          content.heroMessage = 'Transition to Blockchain Development';
          content.socialProofType = 'achievements';
          break;
        case 'student':
          content.heroMessage = 'Launch Your Tech Career with Blockchain';
          content.socialProofType = 'numbers';
          break;
        case 'entrepreneur':
          content.heroMessage = 'Build the Next Big DApp';
          content.socialProofType = 'testimonials';
          break;
      }
    }

    // Personalize based on behavior
    if (profile.behavior) {
      if (profile.behavior.engagementScore > 80) {
        content.urgencyLevel = 'high';
      } else if (profile.behavior.engagementScore < 40) {
        content.urgencyLevel = 'low';
      }

      if (profile.behavior.preferredLearningStyle === 'hands-on') {
        content.ctaText = 'Start Coding Now';
      } else if (profile.behavior.preferredLearningStyle === 'visual') {
        content.ctaText = 'Watch Demo';
      }
    }

    setPersonalizedContent(content);
  }, [personalizedContent]);

  const updateUserProfile = useCallback((updates: any) => {
    const newProfile = { ...userProfile, ...updates };
    setUserProfile(newProfile);
    localStorage.setItem('user_profile', JSON.stringify(newProfile));
    generatePersonalizedContent(newProfile);
  }, [userProfile, generatePersonalizedContent]);

  const trackBehavior = useCallback((action: string, metadata?: any) => {
    if (!userProfile) return;

    const behaviorUpdate = {
      behavior: {
        ...userProfile.behavior,
        engagementScore: Math.min(100, userProfile.behavior.engagementScore + 1),
        lastAction: action,
        lastActionTime: Date.now(),
        ...metadata
      }
    };

    updateUserProfile(behaviorUpdate);
  }, [userProfile, updateUserProfile]);

  return {
    userProfile,
    personalizedContent,
    updateUserProfile,
    trackBehavior,
    generatePersonalizedContent
  };
}

// Machine Learning Insights Hook
export function useMLInsights() {
  const [insights, setInsights] = useState<{
    predictedConversionProbability: number;
    recommendedActions: Array<{
      action: string;
      impact: number;
      confidence: number;
    }>;
    userLifetimeValue: number;
    churnRisk: 'low' | 'medium' | 'high';
    optimalTouchpoints: string[];
  }>({
    predictedConversionProbability: 0,
    recommendedActions: [],
    userLifetimeValue: 0,
    churnRisk: 'low',
    optimalTouchpoints: []
  });

  const generateInsights = useCallback((userProfile: any, behaviorData: any) => {
    // Simplified ML-like calculations
    let conversionProbability = 0.3; // Base probability

    if (userProfile?.experience === 'beginner') conversionProbability += 0.2;
    if (userProfile?.demographics?.role === 'developer') conversionProbability += 0.15;
    if (behaviorData?.engagementScore > 70) conversionProbability += 0.25;
    if (behaviorData?.sessionDuration > 300) conversionProbability += 0.1;

    conversionProbability = Math.min(0.95, conversionProbability);

    // Generate recommendations
    const recommendations = [];

    if (behaviorData?.engagementScore < 50) {
      recommendations.push({
        action: 'Show interactive demo',
        impact: 0.15,
        confidence: 0.8
      });
    }

    if (userProfile?.experience === 'advanced' && conversionProbability < 0.6) {
      recommendations.push({
        action: 'Highlight advanced features',
        impact: 0.2,
        confidence: 0.75
      });
    }

    if (behaviorData?.sessionDuration < 120) {
      recommendations.push({
        action: 'Reduce friction in onboarding',
        impact: 0.18,
        confidence: 0.85
      });
    }

    // Calculate churn risk
    let churnRisk: 'low' | 'medium' | 'high' = 'low';
    if (behaviorData?.engagementScore < 30) churnRisk = 'high';
    else if (behaviorData?.engagementScore < 60) churnRisk = 'medium';

    // Estimate lifetime value
    const baseLTV = 150; // Base LTV in dollars
    const experienceMultiplier = userProfile?.experience === 'advanced' ? 1.5 : 1.0;
    const engagementMultiplier = (behaviorData?.engagementScore || 50) / 50;
    const userLifetimeValue = baseLTV * experienceMultiplier * engagementMultiplier;

    setInsights({
      predictedConversionProbability: conversionProbability,
      recommendedActions: recommendations,
      userLifetimeValue,
      churnRisk,
      optimalTouchpoints: ['hero_cta', 'trial_signup', 'first_lesson']
    });
  }, []);

  return {
    insights,
    generateInsights
  };
}

  const getVariantConfig = useCallback((testId: string) => {
    const variantId = userVariants[testId] || 'control';
    const test = tests.find(t => t.id === testId);
    const variant = test?.variants.find(v => v.id === variantId);
    return variant?.config || {};
  }, [tests, userVariants]);

  const getTestResults = useCallback((testId: string): ABTestResult[] => {
    const test = tests.find(t => t.id === testId);
    if (!test) return [];

    const control = test.variants.find(v => v.isControl);
    if (!control) return [];

    return test.variants
      .filter(v => !v.isControl)
      .map(variant => {
        const stats = calculateStatisticalSignificance(
          control.conversions,
          control.visitors,
          variant.conversions,
          variant.visitors
        );

        const lift = ((variant.conversionRate - control.conversionRate) / control.conversionRate) * 100;

        let recommendation: 'winner' | 'loser' | 'inconclusive' = 'inconclusive';
        if (stats.isSignificant) {
          recommendation = lift > 0 ? 'winner' : 'loser';
        }

        return {
          testId,
          variant: variant.id,
          isStatisticallySignificant: stats.isSignificant,
          confidenceInterval: stats.confidenceInterval,
          pValue: stats.pValue,
          lift,
          recommendation
        };
      });
  }, [tests]);

  return {
    tests,
    userVariants,
    userSegment,
    assignVariant,
    trackConversion,
    getVariantConfig,
    getTestResults
  };
}

// A/B Test Results Component
export function ABTestResults({ testId }: { testId: string }) {
  const { tests, getTestResults } = useABTesting();
  const test = tests.find(t => t.id === testId);
  const results = getTestResults(testId);

  if (!test) return null;

  const control = test.variants.find(v => v.isControl);
  if (!control) return null;

  return (
    <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">{test.name}</h3>
        <div className={cn(
          'px-3 py-1 rounded-full text-xs font-medium',
          test.status === 'running' ? 'bg-green-500/20 text-green-400' :
          test.status === 'completed' ? 'bg-blue-500/20 text-blue-400' :
          'bg-gray-500/20 text-gray-400'
        )}>
          {test.status}
        </div>
      </div>

      {/* Control Variant */}
      <div className="mb-6">
        <h4 className="text-white font-medium mb-3">Control (Baseline)</h4>
        <div className="bg-white/5 rounded-lg p-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-white">{control.visitors.toLocaleString()}</div>
              <div className="text-gray-400 text-sm">Visitors</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-white">{control.conversions.toLocaleString()}</div>
              <div className="text-gray-400 text-sm">Conversions</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-white">{control.conversionRate.toFixed(2)}%</div>
              <div className="text-gray-400 text-sm">Conversion Rate</div>
            </div>
          </div>
        </div>
      </div>

      {/* Variant Results */}
      <div className="space-y-4">
        {results.map((result, index) => {
          const variant = test.variants.find(v => v.id === result.variant);
          if (!variant) return null;

          return (
            <motion.div
              key={result.variant}
              className="bg-white/5 rounded-lg p-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <div className="flex items-center justify-between mb-3">
                <h5 className="text-white font-medium">{variant.name}</h5>
                <div className="flex items-center space-x-2">
                  {result.isStatisticallySignificant ? (
                    <CheckCircle className="w-4 h-4 text-green-400" />
                  ) : (
                    <AlertCircle className="w-4 h-4 text-yellow-400" />
                  )}
                  <span className={cn(
                    'text-xs font-medium',
                    result.recommendation === 'winner' ? 'text-green-400' :
                    result.recommendation === 'loser' ? 'text-red-400' :
                    'text-yellow-400'
                  )}>
                    {result.recommendation}
                  </span>
                </div>
              </div>

              <div className="grid grid-cols-2 lg:grid-cols-5 gap-4 text-center">
                <div>
                  <div className="text-lg font-bold text-white">{variant.visitors.toLocaleString()}</div>
                  <div className="text-gray-400 text-xs">Visitors</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-white">{variant.conversions.toLocaleString()}</div>
                  <div className="text-gray-400 text-xs">Conversions</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-white">{variant.conversionRate.toFixed(2)}%</div>
                  <div className="text-gray-400 text-xs">Conv. Rate</div>
                </div>
                <div>
                  <div className={cn(
                    'text-lg font-bold',
                    result.lift > 0 ? 'text-green-400' : 'text-red-400'
                  )}>
                    {result.lift > 0 ? '+' : ''}{result.lift.toFixed(1)}%
                  </div>
                  <div className="text-gray-400 text-xs">Lift</div>
                </div>
                <div>
                  <div className="text-lg font-bold text-white">{(result.pValue * 100).toFixed(1)}%</div>
                  <div className="text-gray-400 text-xs">P-Value</div>
                </div>
              </div>

              {result.isStatisticallySignificant && (
                <div className="mt-3 pt-3 border-t border-white/10">
                  <div className="text-xs text-gray-400">
                    95% Confidence Interval: {(result.confidenceInterval[0] * 100).toFixed(2)}% - {(result.confidenceInterval[1] * 100).toFixed(2)}%
                  </div>
                </div>
              )}
            </motion.div>
          );
        })}
      </div>

      {/* Test Progress */}
      <div className="mt-6 pt-4 border-t border-white/10">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-400">
            Minimum sample size: {test.minimumSampleSize.toLocaleString()}
          </span>
          <span className="text-gray-400">
            Current sample: {test.variants.reduce((sum, v) => sum + v.visitors, 0).toLocaleString()}
          </span>
        </div>
        <div className="mt-2 w-full bg-gray-700 rounded-full h-2">
          <div 
            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
            style={{ 
              width: `${Math.min(100, (test.variants.reduce((sum, v) => sum + v.visitors, 0) / test.minimumSampleSize) * 100)}%` 
            }}
          />
        </div>
      </div>
    </div>
  );
}

// A/B Testing Provider
export function ABTestingProvider({ children }: { children: React.ReactNode }) {
  const abTesting = useABTesting();

  return (
    <ABTestingContext.Provider value={abTesting}>
      {children}
    </ABTestingContext.Provider>
  );
}

// Hook to use A/B testing context
export function useABTestingContext() {
  const context = useContext(ABTestingContext);
  if (!context) {
    throw new Error('useABTestingContext must be used within ABTestingProvider');
  }
  return context;
}

// A/B Test Dashboard
export function ABTestDashboard({ className }: { className?: string }) {
  const { tests } = useABTesting();

  return (
    <div className={cn('space-y-6', className)}>
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">A/B Testing Dashboard</h2>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-400">
            {tests.filter(t => t.status === 'running').length} active tests
          </div>
        </div>
      </div>

      <div className="space-y-6">
        {tests.map(test => (
          <ABTestResults key={test.id} testId={test.id} />
        ))}
      </div>
    </div>
  );
}
