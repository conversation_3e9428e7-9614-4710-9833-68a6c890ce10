#!/bin/bash

# Safe File Cleanup Script
# Removes confirmed unused files with high confidence
# Estimated bundle reduction: 180-220KB

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "\n${BLUE}============================================================${NC}"
    echo -e "${BLUE}  $1${NC}"
    echo -e "${BLUE}============================================================${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "components" ]; then
    log_error "This script must be run from the project root directory"
    exit 1
fi

# Create backup directory
BACKUP_DIR="./backups/cleanup-$(date +%Y%m%d-%H%M%S)"
log_info "Creating backup directory: $BACKUP_DIR"
mkdir -p "$BACKUP_DIR"

# Function to safely remove file with backup
safe_remove() {
    local file_path="$1"
    local reason="$2"
    
    if [ -f "$file_path" ]; then
        # Create backup
        local backup_path="$BACKUP_DIR/$file_path"
        mkdir -p "$(dirname "$backup_path")"
        cp "$file_path" "$backup_path"
        
        # Get file size for reporting
        local file_size=$(stat -f%z "$file_path" 2>/dev/null || stat -c%s "$file_path" 2>/dev/null || echo "unknown")
        
        # Remove the file
        rm "$file_path"
        log_success "Removed $file_path (${file_size} bytes) - $reason"
        
        return 0
    else
        log_warning "File not found: $file_path"
        return 1
    fi
}

# Main cleanup function
main() {
    log_header "Starting Safe File Cleanup"
    
    local total_removed=0
    local total_size=0
    
    # Remove duplicate components
    log_info "Removing duplicate components..."
    
    if safe_remove "components/AchievementsPage.tsx" "Duplicate of components/achievements/AchievementsPage.tsx"; then
        ((total_removed++))
    fi
    
    # Remove unused admin features
    log_info "Removing unused admin features..."
    
    if safe_remove "components/admin/CommunityControls.tsx" "Admin feature not implemented"; then
        ((total_removed++))
    fi
    
    if safe_remove "components/admin/ContentVersionControl.tsx" "Version control feature not used"; then
        ((total_removed++))
    fi
    
    if safe_remove "components/admin/PerformanceDashboard.tsx" "Performance dashboard not integrated"; then
        ((total_removed++))
    fi
    
    if safe_remove "components/admin/SafetyConfirmation.tsx" "Safety confirmation not used"; then
        ((total_removed++))
    fi
    
    if safe_remove "components/admin/UserAnalytics.tsx" "User analytics not implemented"; then
        ((total_removed++))
    fi
    
    # Remove unused AI components
    log_info "Removing unused AI components..."
    
    if safe_remove "components/ai/AICodeAnalyzer.tsx" "AI analyzer not integrated"; then
        ((total_removed++))
    fi
    
    if safe_remove "components/ai/AIContractGenerator.tsx" "Contract generator not used"; then
        ((total_removed++))
    fi
    
    # Remove unused notification system
    log_info "Removing unused notification systems..."
    
    if safe_remove "components/achievements/AchievementNotificationSystem.tsx" "Notification system not integrated"; then
        ((total_removed++))
    fi
    
    # Remove other confirmed unused files
    log_info "Removing other confirmed unused files..."
    
    # Add more files here as they are confirmed unused
    local other_unused_files=(
        "components/blockchain/ContractDeploymentWizard.tsx"
        "components/collaboration/VideoChat.tsx"
        "components/community/ForumModerationTools.tsx"
        "components/debugging/AdvancedDebugger.tsx"
        "components/discovery/AIRecommendations.tsx"
        "components/features/AdvancedAnalytics.tsx"
        "components/help/InteractiveGuide.tsx"
        "components/learning/AdaptiveLearning.tsx"
        "components/monitoring/RealTimeMetrics.tsx"
        "components/performance/OptimizationSuggestions.tsx"
        "components/progress/DetailedAnalytics.tsx"
        "components/settings/AdvancedPreferences.tsx"
        "components/vcs/GitIntegrationPanel.tsx"
    )
    
    for file in "${other_unused_files[@]}"; do
        if [ -f "$file" ]; then
            if safe_remove "$file" "Confirmed unused component"; then
                ((total_removed++))
            fi
        fi
    done
    
    # Clean up empty directories
    log_info "Cleaning up empty directories..."
    
    find components -type d -empty -delete 2>/dev/null || true
    find lib -type d -empty -delete 2>/dev/null || true
    
    # Generate cleanup report
    log_header "Cleanup Summary"
    log_success "Files removed: $total_removed"
    log_success "Backup created at: $BACKUP_DIR"
    
    # Estimate size reduction
    if [ "$total_removed" -gt 0 ]; then
        log_success "Estimated bundle size reduction: 180-220KB"
        log_info "Run 'npm run build' to see actual bundle size changes"
    else
        log_warning "No files were removed - they may have already been cleaned up"
    fi
    
    # Next steps
    log_header "Next Steps"
    log_info "1. Run tests to ensure nothing is broken: npm test"
    log_info "2. Build the project to check for errors: npm run build"
    log_info "3. If everything works, you can delete the backup: rm -rf $BACKUP_DIR"
    log_info "4. Consider running the debug cleanup script: ./scripts/remove-debug-statements.js"
    
    return 0
}

# Rollback function
rollback() {
    log_header "Rolling Back Changes"
    
    if [ -d "$BACKUP_DIR" ]; then
        log_info "Restoring files from backup..."
        cp -r "$BACKUP_DIR"/* ./ 2>/dev/null || true
        log_success "Files restored from backup"
        rm -rf "$BACKUP_DIR"
        log_success "Backup directory cleaned up"
    else
        log_error "No backup directory found"
        return 1
    fi
}

# Handle script arguments
case "${1:-}" in
    "rollback")
        rollback
        ;;
    "")
        main
        ;;
    *)
        echo "Usage: $0 [rollback]"
        echo "  (no args) - Run cleanup"
        echo "  rollback  - Restore from most recent backup"
        exit 1
        ;;
esac
