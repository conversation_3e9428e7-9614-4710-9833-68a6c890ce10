/**
 * Conversion Testing Setup
 * Configures mocks and utilities for testing conversion optimization components
 */

// Mock exit intent detection
global.mockExitIntent = {
  listeners: [],
  trigger: () => {
    global.mockExitIntent.listeners.forEach(listener => listener());
  },
  addListener: (callback) => {
    global.mockExitIntent.listeners.push(callback);
  },
  removeListener: (callback) => {
    const index = global.mockExitIntent.listeners.indexOf(callback);
    if (index > -1) {
      global.mockExitIntent.listeners.splice(index, 1);
    }
  },
  reset: () => {
    global.mockExitIntent.listeners = [];
  }
};

// Mock scroll tracking
global.mockScrollTracking = {
  scrollY: 0,
  documentHeight: 1000,
  windowHeight: 800,
  getScrollPercentage: () => {
    return (global.mockScrollTracking.scrollY / 
           (global.mockScrollTracking.documentHeight - global.mockScrollTracking.windowHeight)) * 100;
  },
  setScroll: (y) => {
    global.mockScrollTracking.scrollY = y;
    window.dispatchEvent(new Event('scroll'));
  },
  reset: () => {
    global.mockScrollTracking.scrollY = 0;
  }
};

// Mock window properties for conversion tracking
Object.defineProperty(window, 'scrollY', {
  get: () => global.mockScrollTracking.scrollY,
  configurable: true
});

Object.defineProperty(document.documentElement, 'scrollHeight', {
  get: () => global.mockScrollTracking.documentHeight,
  configurable: true
});

Object.defineProperty(window, 'innerHeight', {
  get: () => global.mockScrollTracking.windowHeight,
  configurable: true
});

// Mock mouse movement for exit intent
let mousePosition = { x: 0, y: 0 };
Object.defineProperty(window, 'addEventListener', {
  value: jest.fn((event, callback) => {
    if (event === 'mouseleave') {
      global.mockExitIntent.addListener(callback);
    }
  }),
  writable: true
});

// Mock urgency timers
global.mockUrgencyTimer = {
  timeLeft: 3600, // 1 hour in seconds
  isRunning: false,
  callbacks: [],
  start: () => {
    global.mockUrgencyTimer.isRunning = true;
  },
  stop: () => {
    global.mockUrgencyTimer.isRunning = false;
  },
  setTime: (seconds) => {
    global.mockUrgencyTimer.timeLeft = seconds;
  },
  tick: () => {
    if (global.mockUrgencyTimer.isRunning && global.mockUrgencyTimer.timeLeft > 0) {
      global.mockUrgencyTimer.timeLeft--;
      global.mockUrgencyTimer.callbacks.forEach(cb => cb(global.mockUrgencyTimer.timeLeft));
    }
  },
  onTick: (callback) => {
    global.mockUrgencyTimer.callbacks.push(callback);
  },
  reset: () => {
    global.mockUrgencyTimer.timeLeft = 3600;
    global.mockUrgencyTimer.isRunning = false;
    global.mockUrgencyTimer.callbacks = [];
  }
};

// Mock social proof data
global.mockSocialProof = {
  activeUsers: 1247,
  recentSignups: 23,
  testimonials: [
    {
      id: '1',
      name: 'John Doe',
      role: 'Developer',
      content: 'Great platform for learning Solidity!',
      rating: 5,
      avatar: '/avatars/john.jpg'
    },
    {
      id: '2',
      name: 'Jane Smith',
      role: 'Student',
      content: 'Helped me land my first blockchain job.',
      rating: 5,
      avatar: '/avatars/jane.jpg'
    }
  ],
  stats: {
    totalStudents: 15420,
    coursesCompleted: 8934,
    averageRating: 4.8,
    successRate: 92
  }
};

// Mock conversion tracking
global.mockConversionTracking = {
  events: [],
  track: (event, data = {}) => {
    global.mockConversionTracking.events.push({
      event,
      data,
      timestamp: Date.now()
    });
  },
  getEvents: (eventType) => {
    if (eventType) {
      return global.mockConversionTracking.events.filter(e => e.event === eventType);
    }
    return global.mockConversionTracking.events;
  },
  reset: () => {
    global.mockConversionTracking.events = [];
  }
};

// Mock A/B test variants for conversion
global.mockConversionVariants = {
  'hero-cta-test': 'variant-a',
  'urgency-timer-test': 'control',
  'social-proof-test': 'variant-b'
};

// Mock user session data
global.mockUserSession = {
  sessionId: 'test-session-123',
  startTime: Date.now() - 300000, // 5 minutes ago
  pageViews: 3,
  timeOnPage: 180, // 3 minutes
  interactions: [
    { type: 'scroll', depth: 25, timestamp: Date.now() - 240000 },
    { type: 'click', element: 'cta-button', timestamp: Date.now() - 120000 },
    { type: 'scroll', depth: 50, timestamp: Date.now() - 60000 }
  ],
  source: 'organic',
  campaign: null,
  device: 'desktop',
  browser: 'chrome'
};

// Mock form validation
global.mockFormValidation = {
  errors: {},
  isValid: true,
  setError: (field, message) => {
    global.mockFormValidation.errors[field] = message;
    global.mockFormValidation.isValid = false;
  },
  clearError: (field) => {
    delete global.mockFormValidation.errors[field];
    global.mockFormValidation.isValid = Object.keys(global.mockFormValidation.errors).length === 0;
  },
  reset: () => {
    global.mockFormValidation.errors = {};
    global.mockFormValidation.isValid = true;
  }
};

// Conversion test utilities
global.conversionTestUtils = {
  // Reset all conversion mocks
  resetMocks: () => {
    global.mockExitIntent.reset();
    global.mockScrollTracking.reset();
    global.mockUrgencyTimer.reset();
    global.mockConversionTracking.reset();
    global.mockFormValidation.reset();
  },

  // Simulate exit intent
  triggerExitIntent: () => {
    global.mockExitIntent.trigger();
  },

  // Simulate scroll to percentage
  scrollToPercentage: (percentage) => {
    const maxScroll = global.mockScrollTracking.documentHeight - global.mockScrollTracking.windowHeight;
    const scrollY = (percentage / 100) * maxScroll;
    global.mockScrollTracking.setScroll(scrollY);
  },

  // Simulate urgency timer countdown
  simulateUrgencyCountdown: (seconds = 10) => {
    global.mockUrgencyTimer.start();
    for (let i = 0; i < seconds; i++) {
      setTimeout(() => {
        global.mockUrgencyTimer.tick();
      }, i * 100); // Speed up for testing
    }
  },

  // Mock conversion event
  trackConversion: (event, data = {}) => {
    global.mockConversionTracking.track(event, data);
  },

  // Get conversion events
  getConversions: (eventType) => {
    return global.mockConversionTracking.getEvents(eventType);
  },

  // Mock form submission
  mockFormSubmission: (formData, shouldSucceed = true) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (shouldSucceed) {
          global.mockConversionTracking.track('form_submission', formData);
          resolve({ success: true, data: formData });
        } else {
          reject(new Error('Form submission failed'));
        }
      }, 100);
    });
  },

  // Mock trial signup
  mockTrialSignup: (userData, shouldSucceed = true) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (shouldSucceed) {
          global.mockConversionTracking.track('trial_signup', userData);
          resolve({ 
            success: true, 
            user: { 
              id: 'test-user-123', 
              ...userData,
              trialExpiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days
            }
          });
        } else {
          reject(new Error('Trial signup failed'));
        }
      }, 200);
    });
  },

  // Mock payment processing
  mockPayment: (paymentData, shouldSucceed = true) => {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (shouldSucceed) {
          global.mockConversionTracking.track('payment_success', paymentData);
          resolve({ 
            success: true, 
            transactionId: 'txn-123',
            amount: paymentData.amount
          });
        } else {
          reject(new Error('Payment processing failed'));
        }
      }, 300);
    });
  },

  // Wait for async operations
  waitForAsync: () => new Promise(resolve => setTimeout(resolve, 0)),

  // Mock viewport size
  mockViewport: (width = 1024, height = 768) => {
    Object.defineProperty(window, 'innerWidth', {
      value: width,
      writable: true
    });
    Object.defineProperty(window, 'innerHeight', {
      value: height,
      writable: true
    });
    global.mockScrollTracking.windowHeight = height;
  }
};

// Setup before each test
beforeEach(() => {
  // Reset all mocks
  global.conversionTestUtils.resetMocks();
  
  // Reset viewport to default
  global.conversionTestUtils.mockViewport();
  
  // Reset mouse position
  mousePosition = { x: 0, y: 0 };
});

// Cleanup after each test
afterEach(() => {
  // Clear all timers
  jest.clearAllTimers();
  
  // Reset all mocks
  global.conversionTestUtils.resetMocks();
});
