'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

// Import all social and gamification components
import { SiteWideNotificationProvider } from '@/components/achievements/SiteWideNotificationSystem';
import { FloatingNotificationContainer } from '@/components/achievements/FloatingNotifications';
import { SocialProofSystem } from '@/components/social/SocialProofSystem';
import { DeveloperSpotlight, LiveActivityFeed, HeroLiveActivity } from '@/components/user/DeveloperSpotlight';
import { SocialSharingAnalytics } from '@/components/social/SocialSharingHooks';

interface SocialGamificationIntegrationProps {
  className?: string;
  variant?: 'full' | 'hero' | 'compact';
  showSocialProof?: boolean;
  showLiveActivity?: boolean;
  showSpotlight?: boolean;
  showAnalytics?: boolean;
}

// Main integration component
export function SocialGamificationIntegration({
  className,
  variant = 'full',
  showSocialProof = true,
  showLiveActivity = true,
  showSpotlight = true,
  showAnalytics = false
}: SocialGamificationIntegrationProps) {
  
  if (variant === 'hero') {
    return (
      <div className={cn('space-y-6', className)}>
        {/* Hero-specific social proof */}
        {showSocialProof && (
          <SocialProofSystem 
            variant="hero"
            showReviews={false}
            showStats={true}
            showTrust={true}
            showStories={false}
          />
        )}
        
        {/* Live activity ticker */}
        {showLiveActivity && (
          <HeroLiveActivity className="justify-center" />
        )}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn('grid grid-cols-1 md:grid-cols-2 gap-6', className)}>
        {/* Developer spotlight */}
        {showSpotlight && (
          <DeveloperSpotlight />
        )}
        
        {/* Live activity feed */}
        {showLiveActivity && (
          <LiveActivityFeed />
        )}
        
        {/* Social proof compact */}
        {showSocialProof && (
          <div className="md:col-span-2">
            <SocialProofSystem 
              variant="compact"
              showReviews={true}
              showStats={true}
              showTrust={false}
              showStories={false}
            />
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={cn('space-y-12', className)}>
      {/* Social proof section */}
      {showSocialProof && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <SocialProofSystem 
            showReviews={true}
            showStats={true}
            showTrust={true}
            showStories={true}
          />
        </motion.section>
      )}

      {/* Community section */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="grid grid-cols-1 lg:grid-cols-2 gap-8"
      >
        {/* Developer spotlight */}
        {showSpotlight && (
          <DeveloperSpotlight />
        )}
        
        {/* Live activity feed */}
        {showLiveActivity && (
          <LiveActivityFeed />
        )}
      </motion.section>

      {/* Analytics section */}
      {showAnalytics && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <SocialSharingAnalytics />
        </motion.section>
      )}
    </div>
  );
}

// Provider wrapper for the entire app
export function SocialGamificationProvider({ children }: { children: React.ReactNode }) {
  return (
    <SiteWideNotificationProvider>
      {children}
      <FloatingNotificationContainer />
    </SiteWideNotificationProvider>
  );
}

// Hook for triggering social events
export function useSocialEvents() {
  const triggerAchievementShare = (achievementId: string) => {
    // Trigger achievement notification
    window.dispatchEvent(new CustomEvent('achievement_unlocked', {
      detail: { achievementId }
    }));
  };

  const triggerLevelUpShare = (newLevel: number) => {
    // Trigger level up notification and sharing
    window.dispatchEvent(new CustomEvent('level_up', {
      detail: { level: newLevel }
    }));
  };

  const triggerStreakShare = (streakDays: number) => {
    // Trigger streak milestone notification
    window.dispatchEvent(new CustomEvent('streak_milestone', {
      detail: { days: streakDays }
    }));
  };

  const triggerSocialInfluencerAchievement = (shareCount: number) => {
    if (shareCount === 5) {
      triggerAchievementShare('social-newcomer');
    } else if (shareCount === 25) {
      triggerAchievementShare('social-influencer');
    } else if (shareCount === 100) {
      triggerAchievementShare('social-legend');
    }
  };

  return {
    triggerAchievementShare,
    triggerLevelUpShare,
    triggerStreakShare,
    triggerSocialInfluencerAchievement
  };
}

// Analytics tracking for social features
export function useSocialAnalytics() {
  const trackSocialShare = (platform: string, contentType: string, contentId: string) => {
    // Track social sharing events
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'social_share', {
        platform,
        content_type: contentType,
        content_id: contentId
      });
    }
  };

  const trackAchievementView = (achievementId: string) => {
    // Track achievement notification views
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'achievement_view', {
        achievement_id: achievementId
      });
    }
  };

  const trackSocialProofInteraction = (element: string, action: string) => {
    // Track social proof interactions
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'social_proof_interaction', {
        element,
        action
      });
    }
  };

  const trackDeveloperSpotlightView = (developerId: string) => {
    // Track developer spotlight views
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'developer_spotlight_view', {
        developer_id: developerId
      });
    }
  };

  return {
    trackSocialShare,
    trackAchievementView,
    trackSocialProofInteraction,
    trackDeveloperSpotlightView
  };
}

// Success metrics tracking
export function useSocialSuccessMetrics() {
  const [metrics, setMetrics] = React.useState({
    socialShares: 0,
    achievementNotifications: 0,
    socialProofInteractions: 0,
    communityEngagement: 0,
    conversionRate: 0
  });

  React.useEffect(() => {
    // Load metrics from localStorage
    const savedMetrics = localStorage.getItem('social_success_metrics');
    if (savedMetrics) {
      setMetrics(JSON.parse(savedMetrics));
    }
  }, []);

  const updateMetric = (metric: keyof typeof metrics, value: number) => {
    setMetrics(prev => {
      const newMetrics = { ...prev, [metric]: value };
      localStorage.setItem('social_success_metrics', JSON.stringify(newMetrics));
      return newMetrics;
    });
  };

  const incrementMetric = (metric: keyof typeof metrics) => {
    setMetrics(prev => {
      const newMetrics = { ...prev, [metric]: prev[metric] + 1 };
      localStorage.setItem('social_success_metrics', JSON.stringify(newMetrics));
      return newMetrics;
    });
  };

  return {
    metrics,
    updateMetric,
    incrementMetric
  };
}

// Export all components for easy access
export {
  SocialProofSystem,
  DeveloperSpotlight,
  LiveActivityFeed,
  HeroLiveActivity,
  SocialSharingAnalytics
} from './index';

// Default export
export default SocialGamificationIntegration;
