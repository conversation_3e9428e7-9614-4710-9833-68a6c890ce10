'use client';

import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Share2, 
  Twitter, 
  Linkedin, 
  Github,
  Download,
  Copy,
  ExternalLink,
  MessageCircle,
  Users,
  Trophy,
  Star,
  Zap,
  Crown,
  Check,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { GlassCard } from '@/components/ui/Glassmorphism';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import { Achievement } from '@/lib/achievements/types';
import { LevelInfo } from './LevelProgressionRoadmap';

interface SocialSharingProps {
  achievement?: Achievement;
  levelInfo?: LevelInfo;
  userStats?: {
    username: string;
    avatar?: string;
    totalXP: number;
    currentLevel: number;
    achievementCount: number;
    streakDays: number;
  };
  onClose?: () => void;
  className?: string;
}

export function SocialSharing({
  achievement,
  levelInfo,
  userStats,
  onClose,
  className
}: SocialSharingProps) {
  const [copied, setCopied] = useState(false);
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);

  const generateShareText = (platform: string) => {
    if (achievement) {
      const baseText = `🎉 Just unlocked the "${achievement.title}" achievement on Solidity Learning Platform! ${achievement.description}`;
      
      switch (platform) {
        case 'twitter':
          return `${baseText} #Solidity #Blockchain #Web3 #Achievement`;
        case 'linkedin':
          return `${baseText}\n\nContinuing my journey in blockchain development and smart contract programming.`;
        case 'discord':
          return `🏆 **Achievement Unlocked!**\n**${achievement.title}**\n${achievement.description}\n\n+${achievement.rewards.xp} XP earned! 🚀`;
        default:
          return baseText;
      }
    }
    
    if (levelInfo) {
      const baseText = `🎯 Just reached Level ${levelInfo.level} - ${levelInfo.title} on Solidity Learning Platform!`;
      
      switch (platform) {
        case 'twitter':
          return `${baseText} ${levelInfo.description} #Solidity #LevelUp #Web3`;
        case 'linkedin':
          return `${baseText}\n\n${levelInfo.description}\n\nExcited to continue learning blockchain development!`;
        case 'discord':
          return `👑 **Level Up!**\n**Level ${levelInfo.level} - ${levelInfo.title}**\n${levelInfo.description}\n\nUnlocked ${levelInfo.rewards.length} new rewards! 🎁`;
        default:
          return baseText;
      }
    }
    
    return 'Check out my progress on Solidity Learning Platform!';
  };

  const generateShareUrl = () => {
    const baseUrl = 'https://solidity-learning.com';
    if (achievement) {
      return `${baseUrl}/achievements/${achievement.id}`;
    }
    if (levelInfo) {
      return `${baseUrl}/levels/${levelInfo.level}`;
    }
    return baseUrl;
  };

  const handleShare = async (platform: string) => {
    const text = generateShareText(platform);
    const url = generateShareUrl();
    
    switch (platform) {
      case 'twitter':
        window.open(
          `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`,
          '_blank',
          'width=550,height=420'
        );
        break;
        
      case 'linkedin':
        window.open(
          `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}&summary=${encodeURIComponent(text)}`,
          '_blank',
          'width=550,height=420'
        );
        break;
        
      case 'github':
        // For GitHub, we could create a gist or update a profile README
        const gistContent = {
          description: 'My Solidity Learning Achievement',
          public: true,
          files: {
            'achievement.md': {
              content: `# ${achievement?.title || `Level ${levelInfo?.level}`}\n\n${text}\n\n[View on Solidity Learning Platform](${url})`
            }
          }
        };
        
        // This would require GitHub API integration
        console.log('GitHub sharing:', gistContent);
        break;
        
      case 'discord':
        // Copy Discord-formatted text to clipboard
        await navigator.clipboard.writeText(text);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
        break;
        
      case 'copy':
        await navigator.clipboard.writeText(`${text}\n\n${url}`);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
        break;
    }
  };

  const downloadAsImage = async () => {
    if (!cardRef.current) return;
    
    setIsGeneratingImage(true);
    
    try {
      // This would use html2canvas or similar library
      // For now, we'll simulate the process
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // In a real implementation:
      // const canvas = await html2canvas(cardRef.current);
      // const link = document.createElement('a');
      // link.download = `achievement-${achievement?.id || 'level'}.png`;
      // link.href = canvas.toDataURL();
      // link.click();
      
      console.log('Image download simulated');
    } catch (error) {
      console.error('Failed to generate image:', error);
    } finally {
      setIsGeneratingImage(false);
    }
  };

  return (
    <div className={className}>
      <GlassCard className="p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <Share2 className="w-5 h-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-white">Share Your Achievement</h3>
                <p className="text-sm text-gray-400">Let others know about your progress!</p>
              </div>
            </div>
            
            {onClose && (
              <EnhancedButton
                onClick={onClose}
                variant="ghost"
                size="sm"
                className="w-8 h-8 p-0 text-gray-400 hover:text-white"
              >
                <X className="w-4 h-4" />
              </EnhancedButton>
            )}
          </div>

          {/* Shareable Card Preview */}
          <div ref={cardRef} className="relative">
            <ShareableCard
              achievement={achievement}
              levelInfo={levelInfo}
              userStats={userStats}
            />
          </div>

          {/* Sharing Options */}
          <div className="space-y-4">
            <h4 className="text-md font-semibold text-white">Share on social media</h4>
            
            <div className="grid grid-cols-2 gap-3">
              {/* Twitter */}
              <EnhancedButton
                onClick={() => handleShare('twitter')}
                className="flex items-center justify-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white"
                touchTarget
              >
                <Twitter className="w-4 h-4" />
                <span>Twitter</span>
              </EnhancedButton>

              {/* LinkedIn */}
              <EnhancedButton
                onClick={() => handleShare('linkedin')}
                className="flex items-center justify-center space-x-2 bg-blue-700 hover:bg-blue-800 text-white"
                touchTarget
              >
                <Linkedin className="w-4 h-4" />
                <span>LinkedIn</span>
              </EnhancedButton>

              {/* GitHub */}
              <EnhancedButton
                onClick={() => handleShare('github')}
                className="flex items-center justify-center space-x-2 bg-gray-800 hover:bg-gray-900 text-white"
                touchTarget
              >
                <Github className="w-4 h-4" />
                <span>GitHub</span>
              </EnhancedButton>

              {/* Discord */}
              <EnhancedButton
                onClick={() => handleShare('discord')}
                className="flex items-center justify-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white"
                touchTarget
              >
                <MessageCircle className="w-4 h-4" />
                <span>Discord</span>
              </EnhancedButton>
            </div>

            {/* Additional Options */}
            <div className="space-y-2">
              <EnhancedButton
                onClick={() => handleShare('copy')}
                variant="outline"
                className="w-full flex items-center justify-center space-x-2"
                touchTarget
              >
                {copied ? (
                  <>
                    <Check className="w-4 h-4 text-green-400" />
                    <span>Copied to clipboard!</span>
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    <span>Copy link</span>
                  </>
                )}
              </EnhancedButton>

              <EnhancedButton
                onClick={downloadAsImage}
                variant="outline"
                className="w-full flex items-center justify-center space-x-2"
                disabled={isGeneratingImage}
                touchTarget
              >
                <Download className="w-4 h-4" />
                <span>{isGeneratingImage ? 'Generating...' : 'Download as image'}</span>
              </EnhancedButton>
            </div>
          </div>
        </div>
      </GlassCard>
    </div>
  );
}

// Shareable Card Component
interface ShareableCardProps {
  achievement?: Achievement;
  levelInfo?: LevelInfo;
  userStats?: {
    username: string;
    avatar?: string;
    totalXP: number;
    currentLevel: number;
    achievementCount: number;
    streakDays: number;
  };
}

function ShareableCard({ achievement, levelInfo, userStats }: ShareableCardProps) {
  const getRarityColor = (rarity?: string) => {
    switch (rarity) {
      case 'legendary':
        return 'from-yellow-400 to-orange-500';
      case 'epic':
        return 'from-purple-400 to-purple-600';
      case 'rare':
        return 'from-blue-400 to-blue-600';
      case 'uncommon':
        return 'from-green-400 to-green-600';
      default:
        return 'from-gray-400 to-gray-600';
    }
  };

  return (
    <div className="relative bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 rounded-xl p-6 border border-gray-700 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20" />
        <div className="absolute top-0 left-0 w-full h-full bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.1"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]" />
      </div>

      <div className="relative z-10 space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {userStats?.avatar ? (
              <img 
                src={userStats.avatar} 
                alt={userStats.username}
                className="w-12 h-12 rounded-full border-2 border-gray-600"
              />
            ) : (
              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-lg font-bold text-white">
                {userStats?.username?.charAt(0).toUpperCase() || 'U'}
              </div>
            )}
            
            <div>
              <div className="font-semibold text-white">{userStats?.username || 'Anonymous'}</div>
              <div className="text-sm text-gray-400">Solidity Learning Platform</div>
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-sm text-gray-400">Level</div>
            <div className="text-xl font-bold text-white">{userStats?.currentLevel || 1}</div>
          </div>
        </div>

        {/* Main Content */}
        {achievement && (
          <div className="text-center space-y-3">
            <div className={cn(
              'w-20 h-20 rounded-full flex items-center justify-center mx-auto',
              'bg-gradient-to-br',
              getRarityColor(achievement.rarity),
              'shadow-lg'
            )}>
              <Trophy className="w-10 h-10 text-white" />
            </div>
            
            <div>
              <h3 className="text-xl font-bold text-white mb-1">Achievement Unlocked!</h3>
              <h4 className="text-lg font-semibold text-gray-200">{achievement.title}</h4>
              <p className="text-sm text-gray-400 mt-2">{achievement.description}</p>
            </div>
            
            <div className="flex items-center justify-center space-x-4">
              <div className="flex items-center space-x-1">
                <Zap className="w-4 h-4 text-yellow-400" />
                <span className="text-sm font-medium text-yellow-400">+{achievement.rewards.xp} XP</span>
              </div>
              
              <div className="flex items-center space-x-1">
                <Star className="w-4 h-4 text-purple-400" />
                <span className="text-sm font-medium text-purple-400 capitalize">{achievement.rarity}</span>
              </div>
            </div>
          </div>
        )}

        {levelInfo && (
          <div className="text-center space-y-3">
            <div className={cn(
              'w-20 h-20 rounded-full flex items-center justify-center mx-auto',
              'bg-gradient-to-br from-yellow-400 to-orange-500',
              'shadow-lg'
            )}>
              <Crown className="w-10 h-10 text-white" />
            </div>
            
            <div>
              <h3 className="text-xl font-bold text-white mb-1">Level Up!</h3>
              <h4 className="text-lg font-semibold text-gray-200">Level {levelInfo.level} - {levelInfo.title}</h4>
              <p className="text-sm text-gray-400 mt-2">{levelInfo.description}</p>
            </div>
            
            <div className="flex items-center justify-center space-x-4">
              <div className="flex items-center space-x-1">
                <Zap className="w-4 h-4 text-yellow-400" />
                <span className="text-sm font-medium text-yellow-400">{levelInfo.xpTotal.toLocaleString()} XP</span>
              </div>
              
              <div className="flex items-center space-x-1">
                <Trophy className="w-4 h-4 text-purple-400" />
                <span className="text-sm font-medium text-purple-400">{levelInfo.rewards.length} rewards</span>
              </div>
            </div>
          </div>
        )}

        {/* Stats Footer */}
        {userStats && (
          <div className="grid grid-cols-3 gap-4 pt-4 border-t border-gray-700">
            <div className="text-center">
              <div className="text-lg font-bold text-white">{userStats.totalXP.toLocaleString()}</div>
              <div className="text-xs text-gray-400">Total XP</div>
            </div>
            
            <div className="text-center">
              <div className="text-lg font-bold text-white">{userStats.achievementCount}</div>
              <div className="text-xs text-gray-400">Achievements</div>
            </div>
            
            <div className="text-center">
              <div className="text-lg font-bold text-white">{userStats.streakDays}</div>
              <div className="text-xs text-gray-400">Day Streak</div>
            </div>
          </div>
        )}

        {/* Branding */}
        <div className="text-center pt-2 border-t border-gray-700">
          <div className="text-xs text-gray-500">
            Join me on Solidity Learning Platform
          </div>
        </div>
      </div>
    </div>
  );
}

// GitHub Integration Component
interface GitHubIntegrationProps {
  userStats: {
    username: string;
    totalXP: number;
    currentLevel: number;
    achievementCount: number;
    streakDays: number;
    completedProjects: number;
  };
  achievements: Achievement[];
  onConnect?: () => void;
  onDisconnect?: () => void;
  isConnected?: boolean;
  className?: string;
}

export function GitHubIntegration({
  userStats,
  achievements,
  onConnect,
  onDisconnect,
  isConnected = false,
  className
}: GitHubIntegrationProps) {
  const [isUpdating, setIsUpdating] = useState(false);

  const generateReadmeContent = () => {
    const recentAchievements = achievements
      .filter(a => 'rarity' in a && a.rarity)
      .slice(0, 5);

    return `
# 🚀 Solidity Learning Journey

## 📊 Current Progress
- **Level:** ${userStats.currentLevel}
- **Total XP:** ${userStats.totalXP.toLocaleString()}
- **Learning Streak:** ${userStats.streakDays} days
- **Achievements Unlocked:** ${userStats.achievementCount}
- **Projects Completed:** ${userStats.completedProjects}

## 🏆 Recent Achievements
${recentAchievements.map(achievement =>
  `- **${achievement.title}** - ${achievement.description}`
).join('\n')}

## 🎯 Learning Stats
\`\`\`
Level Progress: ${'█'.repeat(Math.floor(userStats.currentLevel / 5))}${'░'.repeat(20 - Math.floor(userStats.currentLevel / 5))} ${userStats.currentLevel}/100
XP Earned:      ${userStats.totalXP.toLocaleString()} XP
Streak:         ${userStats.streakDays} days
\`\`\`

---
*Updated automatically from [Solidity Learning Platform](https://solidity-learning.com)*
`;
  };

  const updateGitHubProfile = async () => {
    setIsUpdating(true);
    try {
      // In a real implementation, this would call GitHub API
      const readmeContent = generateReadmeContent();
      console.log('Updating GitHub profile with:', readmeContent);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
    } catch (error) {
      console.error('Failed to update GitHub profile:', error);
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <GlassCard className={cn('p-6', className)}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center">
              <Github className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">GitHub Integration</h3>
              <p className="text-sm text-gray-400">Showcase your learning progress</p>
            </div>
          </div>

          <div className={cn(
            'px-3 py-1 rounded-full text-xs font-medium',
            isConnected
              ? 'bg-green-500/20 text-green-400 border border-green-500/30'
              : 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
          )}>
            {isConnected ? 'Connected' : 'Not Connected'}
          </div>
        </div>

        {/* Connection Status */}
        {!isConnected ? (
          <div className="text-center space-y-4">
            <div className="p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
              <h4 className="font-semibold text-blue-300 mb-2">Connect Your GitHub</h4>
              <p className="text-sm text-blue-200 mb-4">
                Automatically update your GitHub profile README with your learning progress and achievements.
              </p>

              <EnhancedButton
                onClick={onConnect}
                className="bg-gray-800 hover:bg-gray-900 text-white"
                touchTarget
              >
                <Github className="w-4 h-4 mr-2" />
                Connect GitHub
              </EnhancedButton>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Profile Preview */}
            <div className="p-4 bg-gray-800/50 border border-gray-700 rounded-xl">
              <h4 className="font-semibold text-white mb-3">Profile README Preview</h4>
              <div className="bg-gray-900 p-4 rounded-lg text-sm font-mono text-gray-300 max-h-40 overflow-y-auto">
                <pre className="whitespace-pre-wrap">{generateReadmeContent()}</pre>
              </div>
            </div>

            {/* Actions */}
            <div className="flex space-x-3">
              <EnhancedButton
                onClick={updateGitHubProfile}
                disabled={isUpdating}
                className="flex-1 bg-green-600 hover:bg-green-700 text-white"
                touchTarget
              >
                {isUpdating ? (
                  <>
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                      className="w-4 h-4 mr-2"
                    >
                      <Github className="w-4 h-4" />
                    </motion.div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Github className="w-4 h-4 mr-2" />
                    Update Profile
                  </>
                )}
              </EnhancedButton>

              <EnhancedButton
                onClick={onDisconnect}
                variant="outline"
                className="text-gray-400 hover:text-white"
              >
                Disconnect
              </EnhancedButton>
            </div>
          </div>
        )}
      </div>
    </GlassCard>
  );
}

// Hook for social sharing
export function useSocialSharing() {
  const [isSharing, setIsSharing] = useState(false);

  const shareAchievement = async (achievement: Achievement, platform: string) => {
    setIsSharing(true);
    try {
      // Implementation would depend on the platform
      console.log(`Sharing achievement ${achievement.id} on ${platform}`);
    } catch (error) {
      console.error('Failed to share achievement:', error);
    } finally {
      setIsSharing(false);
    }
  };

  const shareLevel = async (levelInfo: LevelInfo, platform: string) => {
    setIsSharing(true);
    try {
      console.log(`Sharing level ${levelInfo.level} on ${platform}`);
    } catch (error) {
      console.error('Failed to share level:', error);
    } finally {
      setIsSharing(false);
    }
  };

  return {
    isSharing,
    shareAchievement,
    shareLevel
  };
}
