{"timestamp": "2025-06-26T01:16:46.012Z", "type": "dry-run", "summary": {"filesAnalyzed": 8, "filesModified": 180, "statementsRemoved": 712, "sizeReductionBytes": 38780, "sizeReductionKB": 37.87}, "modifiedFiles": [{"file": "lib/hooks/useSolidityDebugger.ts", "removedCount": 34, "sizeDiff": 323, "statements": [{"statement": "console.error('Failed to get variable value:', error);", "line": 343}, {"statement": "debugger", "line": 355}, {"statement": "debugger", "line": 349}, {"statement": "debugger", "line": 341}, {"statement": "debugger", "line": 336}, {"statement": "debugger", "line": 327}, {"statement": "debugger", "line": 322}, {"statement": "debugger", "line": 313}, {"statement": "debugger", "line": 307}, {"statement": "debugger", "line": 298}, {"statement": "debugger", "line": 292}, {"statement": "debugger", "line": 283}, {"statement": "debugger", "line": 277}, {"statement": "debugger", "line": 268}, {"statement": "debugger", "line": 262}, {"statement": "debugger", "line": 253}, {"statement": "debugger", "line": 248}, {"statement": "debugger", "line": 240}, {"statement": "debugger", "line": 235}, {"statement": "debugger", "line": 221}, {"statement": "debugger", "line": 215}, {"statement": "debugger", "line": 202}, {"statement": "debugger", "line": 197}, {"statement": "debugger", "line": 174}, {"statement": "debugger", "line": 166}, {"statement": "debugger", "line": 159}, {"statement": "debugger", "line": 158}, {"statement": "debugger", "line": 145}, {"statement": "debugger", "line": 144}, {"statement": "debugger", "line": 130}, {"statement": "debugger", "line": 126}, {"statement": "debugger", "line": 64}, {"statement": "debugger", "line": 47}, {"statement": "debugger", "line": 35}]}, {"file": "services/geminiService.ts", "removedCount": 17, "sizeDiff": 1811, "statements": [{"statement": "console.error(\"<PERSON><PERSON><PERSON> getting topic explanation from <PERSON>:\", error);", "line": 419}, {"statement": "console.error(\"Error generating diagram with <PERSON>:\", error);", "line": 372}, {"statement": "console.error(\"Error sending message to <PERSON>:\", error);", "line": 325}, {"statement": "console.error(\"Error initializing Gemini chat:\", error);", "line": 298}, {"statement": "console.error(\"❌ Failed to initialize Gemini AI:\", error);", "line": 219}, {"statement": "console.warn(\"❌ Gemini API Key not found or invalid. AI features will be limited or unavailable.\");", "line": 215}, {"statement": "console.log(\"✅ Real Gemini AI initialized successfully\");", "line": 213}, {"statement": "console.log('- API Key configured:', !!GEMINI_API_KEY);", "line": 204}, {"statement": "console.log('🔧 Gemini Service - Real implementation');", "line": 203}, {"statement": "console.log('Image generation requested:', {\r\n        hasPrompt: !!processedConfig.prompt,\r\n        model: processedConfig.model,\r\n        numberOfImages: processedConfig.numberOfImages,\r\n        outputMimeType: processedConfig.outputMimeType,\r\n        aspectRatio: processedConfig.aspectRatio,\r\n        timestamp: Date.now()", "line": 160}, {"statement": "console.error('Error in generateContentStream:', error);", "line": 140}, {"statement": "console.log('Streaming completed:', { chunkCount, timestamp: Date.now()", "line": 137}, {"statement": "console.log('Streaming content generation started:', {\r\n          hasPrompt: !!processedConfig.prompt,\r\n          temperature: processedConfig.temperature,\r\n          maxTokens: processedConfig.maxTokens,\r\n          timestamp: Date.now()", "line": 103}, {"statement": "console.error('Error in generateContent:', error);", "line": 84}, {"statement": "console.error('Error in chat sendMessage:', error);", "line": 63}, {"statement": "console.log('Chat created with config:', {\r\n        model: processedConfig.model,\r\n        hasSystemInstruction: !!processedConfig.systemInstruction,\r\n        safetySettingsCount: processedConfig.safetySettings.length,\r\n        historyLength: processedConfig.history.length,\r\n        temperature: processedConfig.temperature,\r\n        timestamp: Date.now()", "line": 33}, {"statement": "console.log('Real GoogleGenAI initialized successfully');", "line": 16}]}, {"file": "lib/collaboration/ConnectionManager.ts", "removedCount": 16, "sizeDiff": 947, "statements": [{"statement": "console.error('Manual reconnection failed:', error);", "line": 344}, {"statement": "console.error('Periodic sync failed:', error);", "line": 335}, {"statement": "console.log('Performing periodic sync...');", "line": 330}, {"statement": "console.error('Failed to load recovery state:', error);", "line": 273}, {"statement": "console.error('Failed to save recovery state:', error);", "line": 254}, {"statement": "console.error('Session recovery failed:', error);", "line": 233}, {"statement": "console.log('Session recovery completed');", "line": 231}, {"statement": "console.log('Performing session recovery...');", "line": 222}, {"statement": "console.log('Offline queue flushed successfully');", "line": 201}, {"statement": "console.warn('Operation exceeded max retries, discarding:', offlineOp);", "line": 188}, {"statement": "console.error('Failed to flush operation:', error);", "line": 182}, {"statement": "console.log(`Flushing ${this.offlineQueue.length} offline operations...`);", "line": 166}, {"statement": "console.warn('Failed to send operation, queuing for offline:', error);", "line": 154}, {"statement": "console.error('Connection error:', error);", "line": 126}, {"statement": "console.log('Connection lost, entering offline mode...');", "line": 114}, {"statement": "console.log('Connection restored, starting recovery process...');", "line": 98}]}, {"file": "app/api/user/community-stats/route.ts", "removedCount": 14, "sizeDiff": 759, "statements": [{"statement": "console.error('Error processing community stats action:', error);", "line": 136}, {"statement": "console.log(`User ${session.user.id} answered question ${questionId} with answer:`, answer);", "line": 125}, {"statement": "console.log(`User ${session.user.id} voted helpful on ${contentType} ${contentId}`);", "line": 114}, {"statement": "console.log(`User ${session.user.id} unfollowed user ${unfollowUserId}`);", "line": 103}, {"statement": "console.log(`User ${session.user.id} followed user ${targetUserId}`);", "line": 88}, {"statement": "console.error('Error fetching community stats:', error);", "line": 64}, {"statement": "// TODO: Implement Q&A system", "line": 124}, {"statement": "// TODO: Implement voting system", "line": 113}, {"statement": "// TODO: Implement user unfollowing", "line": 102}, {"statement": "// TODO: Implement user following system", "line": 87}, {"statement": "// TODO: Implement mentoring system", "line": 49}, {"statement": "// TODO: Implement Q&A system", "line": 48}, {"statement": "// TODO: Implement voting system", "line": 47}, {"statement": "// TODO: Implement user following system in database", "line": 20}]}, {"file": "lib/ai/LearningAssistant.ts", "removedCount": 13, "sizeDiff": 722, "statements": [{"statement": "console.error('Learning path error:', error);", "line": 367}, {"statement": "console.error('Exercise generation error:', error);", "line": 331}, {"statement": "console.error('Debug assistance error:', error);", "line": 298}, {"statement": "console.error('Concept explanation error:', error);", "line": 257}, {"statement": "console.error('Code review error:', error);", "line": 226}, {"statement": "console.error('AI Assistant error:', error);", "line": 140}, {"statement": "console.warn('AI service unavailable, using mock response:', aiError);", "line": 133}, {"statement": "console.error('AI Assistant streaming error:', error);", "line": 109}, {"statement": "console.warn('Streaming unavailable, using mock response:', streamError);", "line": 92}, {"statement": "console.warn('❌ GEMINI_API_KEY not found. LearningAssistant will use mock responses.');", "line": 60}, {"statement": "console.error('❌ Failed to initialize Google Generative AI:', error);", "line": 54}, {"statement": "console.log('✅ LearningAssistant initialized with real Google Generative AI');", "line": 52}, {"statement": "debugger", "line": 444}]}, {"file": "app/api/projects/route.ts", "removedCount": 12, "sizeDiff": 822, "statements": [{"statement": "console.error('Error processing project action:', error);", "line": 311}, {"statement": "console.log(`User ${session.user.id} saved code for step ${stepId} in project ${projectId}`);", "line": 300}, {"statement": "console.log(`User ${session.user.id} deployed project ${projectId}`);", "line": 270}, {"statement": "console.log(`User ${session.user.id} completed step ${stepId} in project ${projectId}`);", "line": 241}, {"statement": "console.log(`User ${session.user.id} started project ${projectId}`);", "line": 228}, {"statement": "console.error('Error fetching projects:', error);", "line": 206}, {"statement": "// TODO: Implement code saving in database", "line": 299}, {"statement": "// TODO: Implement actual contract deployment", "line": 269}, {"statement": "// TODO: Implement step completion tracking in database", "line": 240}, {"statement": "// TODO: Implement project progress tracking in database", "line": 227}, {"statement": "// TODO: Replace with real database queries when Project model is implemented", "line": 194}, {"statement": "// TODO: Create Project model in Prisma schema and implement real database queries", "line": 18}]}, {"file": "app/api/user/study-schedule/route.ts", "removedCount": 12, "sizeDiff": 909, "statements": [{"statement": "console.error('Error getting recommended topics:', error);", "line": 235}, {"statement": "console.log(`Found ${_recentProgress.length} recent progress entries for user ${userId}`);", "line": 221}, {"statement": "console.error('Error processing study schedule action:', error);", "line": 192}, {"statement": "console.error('Error updating study preferences:', error);", "line": 182}, {"statement": "console.log(`User ${session.user.id} updated study preferences:`, userPreferences);", "line": 167}, {"statement": "console.error('Error completing study session:', error);", "line": 141}, {"statement": "console.log(`User ${session.user.id} completed study session ${sessionId}:`, completedSession);", "line": 127}, {"statement": "console.log(`User ${session.user.id} scheduled study session:`, {\n          date,\n          time,\n          topic,\n          duration: duration || 30,\n        });", "line": 100}, {"statement": "console.error('Error fetching study schedule:', error);", "line": 76}, {"statement": "// TODO: Use this data to provide personalized recommendations", "line": 201}, {"statement": "// TODO: Implement study session scheduling in database", "line": 99}, {"statement": "// TODO: Implement actual scheduling system", "line": 61}]}, {"file": "lib/context/CollaborationContext.tsx", "removedCount": 12, "sizeDiff": 581, "statements": [{"statement": "console.log('Triggering collaborative achievement:', achievementId, participants);", "line": 405}, {"statement": "console.log('Sharing XP reward:', amount, reason);", "line": 400}, {"statement": "console.log('Syncing lesson progress:', lessonId, progress);", "line": 395}, {"statement": "console.log('Deleting file:', fileId);", "line": 376}, {"statement": "console.log('Downloading file:', fileId);", "line": 371}, {"statement": "console.log('User status changed:', userId, status);", "line": 226}, {"statement": "console.log('User left:', userId);", "line": 223}, {"statement": "console.log('User joined:', user);", "line": 220}, {"statement": "console.error('Collaboration error:', error);", "line": 212}, {"statement": "console.log('Received compilation result:', result);", "line": 209}, {"statement": "console.log('Received chat message:', message);", "line": 205}, {"statement": "console.log('Received operation:', operation);", "line": 198}]}, {"file": "lib/api/cache.ts", "removedCount": 11, "sizeDiff": 471, "statements": [{"statement": "console.error('Cache middleware error:', error);", "line": 315}, {"statement": "console.error('<PERSON><PERSON> has error:', error);", "line": 209}, {"statement": "console.error('Cache clear error:', error);", "line": 197}, {"statement": "console.error('<PERSON><PERSON> delete error:', error);", "line": 185}, {"statement": "console.error('Cache set error:', error);", "line": 173}, {"statement": "console.error('<PERSON><PERSON> get error:', error);", "line": 160}, {"statement": "console.error('<PERSON><PERSON> exists error:', error);", "line": 127}, {"statement": "console.error('Redis clear error:', error);", "line": 118}, {"statement": "console.error('Redis delete error:', error);", "line": 110}, {"statement": "console.error('Redis set error:', error);", "line": 102}, {"statement": "console.error('<PERSON><PERSON> get error:', error);", "line": 92}]}, {"file": "lib/community/websocket.ts", "removedCount": 11, "sizeDiff": 706, "statements": [{"statement": "console.log('WebSocket connected, switching from polling');", "line": 355}, {"statement": "console.log('WebSocket disconnected, switching to polling');", "line": 351}, {"statement": "console.error(`Polling error for channel ${channel}:`, error);", "line": 286}, {"statement": "console.error('Error in WebSocket subscriber callback:', error);", "line": 181}, {"statement": "console.log(`Scheduling WebSocket reconnect attempt ${this.reconnectAttempts} in ${delay}ms`);", "line": 138}, {"statement": "console.log('Received unknown message type:', message.type);", "line": 100}, {"statement": "console.error('Community WebSocket error:', error);", "line": 86}, {"statement": "console.log('Community WebSocket disconnected:', event.code, event.reason);", "line": 75}, {"statement": "console.error('Failed to parse WebSocket message:', error);", "line": 70}, {"statement": "console.log('Community WebSocket connected');", "line": 58}, {"statement": "console.error('Failed to create WebSocket connection:', error);", "line": 49}]}, {"file": "lib/services/SettingsService.ts", "removedCount": 11, "sizeDiff": 547, "statements": [{"statement": "console.error('Error updating settings:', error);", "line": 460}, {"statement": "console.error('Error requesting account deletion:', error);", "line": 392}, {"statement": "console.error('Error requesting data export:', error);", "line": 367}, {"statement": "console.error('Error changing password:', error);", "line": 334}, {"statement": "console.error('Error disabling 2FA:', error);", "line": 292}, {"statement": "console.error('Error enabling 2FA:', error);", "line": 271}, {"statement": "console.error('Error setting up 2FA:', error);", "line": 250}, {"statement": "console.error('Error revoking session:', error);", "line": 226}, {"statement": "console.error('Error fetching sessions:', error);", "line": 206}, {"statement": "console.error('Error fetching audit log:', error);", "line": 182}, {"statement": "console.error('Error fetching user settings:', error);", "line": 65}]}, {"file": "lib/socket/client.ts", "removedCount": 11, "sizeDiff": 493, "statements": [{"statement": "console.error('Error joining session:', error);", "line": 380}, {"statement": "console.error('Error creating session:', error);", "line": 362}, {"statement": "console.error('Error fetching sessions:', error);", "line": 342}, {"statement": "console.error('Socket error:', error);", "line": 227}, {"statement": "console.log('User left:', data.userId);", "line": 139}, {"statement": "console.log('User joined:', data.user);", "line": 133}, {"statement": "console.log('Joined session:', data.session);", "line": 125}, {"statement": "console.error('Authentication failed');", "line": 116}, {"statement": "console.log('Authenticated successfully:', data.user);", "line": 112}, {"statement": "console.log('Disconnected from Socket.io server');", "line": 107}, {"statement": "console.log('Connected to Socket.io server');", "line": 96}]}, {"file": "lib/monitoring/analytics.ts", "removedCount": 10, "sizeDiff": 535, "statements": [{"statement": "console.log(`Sending ${events.length} analytics events to services`);", "line": 478}, {"statement": "console.log('Mixpanel event:', { name, properties, userId });", "line": 433}, {"statement": "console.log('PostHog event:', { name, properties, userId });", "line": 425}, {"statement": "console.error('❌ Failed to initialize Mixpanel:', error);", "line": 151}, {"statement": "console.log('✅ Mixpanel initialized');", "line": 149}, {"statement": "console.error('❌ Failed to initialize PostHog:', error);", "line": 138}, {"statement": "console.log('✅ PostHog initialized');", "line": 136}, {"statement": "console.error('❌ Failed to initialize Google Analytics:', error);", "line": 125}, {"statement": "console.log('✅ Google Analytics initialized');", "line": 123}, {"statement": "console.log('✅ Analytics initialized');", "line": 96}]}, {"file": "lib/storage/CodePersistence.ts", "removedCount": 10, "sizeDiff": 590, "statements": [{"statement": "console.error('Failed to cleanup old sessions:', error);", "line": 288}, {"statement": "console.log(`Cleaned up ${oldSessions.length} old code sessions`);", "line": 286}, {"statement": "console.error('Failed to delete session:', error);", "line": 241}, {"statement": "console.error('Failed to load all sessions from localStorage:', error);", "line": 228}, {"statement": "console.error('Failed to load all sessions:', error);", "line": 191}, {"statement": "console.error('localStorage load failed:', error);", "line": 178}, {"statement": "console.error('Failed to load code session:', error);", "line": 143}, {"statement": "console.error('Failed to save code session:', error);", "line": 98}, {"statement": "console.error('Error in save status callback:', error);", "line": 63}, {"statement": "console.warn('IndexedDB not available, falling back to localStorage');", "line": 33}]}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "removedCount": 9, "sizeDiff": 491, "statements": [{"statement": "console.error('Setting<PERSON> Error:', error, errorInfo);", "line": 292}, {"statement": "console.log('Network error detected, checking connection...');", "line": 237}, {"statement": "console.error('Network Component Error:', error, errorInfo);", "line": 234}, {"statement": "console.error('File Upload Error:', error, errorInfo);", "line": 197}, {"statement": "console.error('Auth Error:', error, errorInfo);", "line": 142}, {"statement": "console.log('Learning progress preserved:', progress);", "line": 93}, {"statement": "console.error('Learning Module Error:', error, errorInfo);", "line": 90}, {"statement": "console.log('Code backup available:', currentCode);", "line": 20}, {"statement": "console.error('Code Editor Error:', error, errorInfo);", "line": 16}]}, {"file": "components/learning/GamificationSystem.tsx", "removedCount": 9, "sizeDiff": 369, "statements": [{"statement": "console.log('Starting security challenge...');", "line": 333}, {"statement": "console.log('Star power activated!');", "line": 326}, {"statement": "console.log('Shield protection activated!');", "line": 323}, {"statement": "console.log('XP Boost activated!');", "line": 320}, {"statement": "console.error('Error claiming reward:', error);", "line": 307}, {"statement": "console.error('Error fetching gamification data:', error);", "line": 174}, {"statement": "// TODO: Calculate rank from leaderboard", "line": 114}, {"statement": "// TODO: Add challenges tracking", "line": 113}, {"statement": "// TODO: Add projects tracking", "line": 112}]}, {"file": "components/performance/ServiceWorkerManager.tsx", "removedCount": 9, "sizeDiff": 464, "statements": [{"statement": "console.log('Adding to offline queue:', action);", "line": 325}, {"statement": "console.log('Cache Info:', info);", "line": 275}, {"statement": "console.error('Failed to get cache info:', error);", "line": 217}, {"statement": "console.error('Failed to clear cache:', error);", "line": 175}, {"statement": "console.log('Unknown service worker message:', event.data);", "line": 115}, {"statement": "console.log('Cache updated:', payload);", "line": 105}, {"statement": "console.error('Service Worker registration failed:', error);", "line": 95}, {"statement": "console.log('Service Worker registered successfully:', registration);", "line": 64}, {"statement": "console.log('Service Workers not supported');", "line": 26}]}, {"file": "lib/config/secrets.ts", "removedCount": 9, "sizeDiff": 586, "statements": [{"statement": "console.log('✅ Secrets management initialized');", "line": 396}, {"statement": "console.error('❌ Critical secret validation failed:', error);", "line": 380}, {"statement": "console.info(`  - ${d.name} (in ${d.daysUntilRotation} days)", "line": 367}, {"statement": "console.info(`ℹ️  ${soonToRotate.length} secrets need rotation soon:`);", "line": 365}, {"statement": "console.warn(`  - ${d.name} (overdue by ${Math.abs(d.daysUntilRotation)", "line": 355}, {"statement": "console.warn(`⚠️  ${report.needingRotation} secrets need rotation:`);", "line": 351}, {"statement": "console.warn(`⚠️  Secret ${key} validation failed:`, validation.issues);", "line": 335}, {"statement": "console.log(`🔄 Rotated secret: ${secretName}`);", "line": 185}, {"statement": "console.log(`Secret ${secretName} last rotated ${daysAgo} days ago`);", "line": 88}]}, {"file": "app/api/user/activity-feed/route.ts", "removedCount": 8, "sizeDiff": 490, "statements": [{"statement": "console.error('Error processing activity feed action:', error);", "line": 189}, {"statement": "console.log(`User ${session.user.id} shared activity ${shareActivityId} on ${platform}`);", "line": 178}, {"statement": "console.log(`User ${session.user.id} hid activity ${hideActivityId}`);", "line": 167}, {"statement": "console.log(`User ${session.user.id} marked activity ${activityId} as read`);", "line": 156}, {"statement": "console.error('Error fetching activity feed:', error);", "line": 136}, {"statement": "// TODO: Implement activity sharing", "line": 177}, {"statement": "// TODO: Implement activity hiding", "line": 166}, {"statement": "// TODO: Implement activity read tracking", "line": 155}]}, {"file": "lib/community/statistics.ts", "removedCount": 8, "sizeDiff": 456, "statements": [{"statement": "console.error('Error fetching learning progress:', error);", "line": 287}, {"statement": "console.error('Error fetching engagement metrics:', error);", "line": 262}, {"statement": "console.error('Error exporting stats:', error);", "line": 232}, {"statement": "console.error('Error fetching community milestones:', error);", "line": 206}, {"statement": "console.error('Error fetching trending topics:', error);", "line": 181}, {"statement": "console.error('Error fetching community stats:', error);", "line": 135}, {"statement": "console.error('Error in stats subscriber callback:', error);", "line": 88}, {"statement": "console.error('Failed to refresh community stats:', error);", "line": 79}]}, {"file": "lib/context/LearningContext.tsx", "removedCount": 8, "sizeDiff": 438, "statements": [{"statement": "console.log('Streak saved locally for static export');", "line": 320}, {"statement": "console.log('XP saved locally for static export');", "line": 304}, {"statement": "console.error('Error in level listener:', error);", "line": 283}, {"statement": "console.error('Error in XP listener:', error);", "line": 272}, {"statement": "console.error('Failed to trigger achievement event:', error);", "line": 256}, {"statement": "console.log('Achievement saved locally for static export');", "line": 238}, {"statement": "console.log('Progress saved locally for static export');", "line": 222}, {"statement": "console.log('Using default progress values for static export');", "line": 193}]}, {"file": "hooks/useAutoSave.ts", "removedCount": 8, "sizeDiff": 389, "statements": [{"statement": "console.error('Failed to cleanup old sessions:', error);", "line": 246}, {"statement": "console.error('Failed to delete session:', error);", "line": 236}, {"statement": "console.error('Failed to load sessions:', error);", "line": 225}, {"statement": "console.error('Emergency save failed:', error);", "line": 193}, {"statement": "console.error('Failed to reset code:', error);", "line": 147}, {"statement": "console.error('Failed to load code:', error);", "line": 134}, {"statement": "console.error('Auto-save failed:', error);", "line": 99}, {"statement": "console.error('Failed to load existing code:', error);", "line": 69}]}, {"file": "app/api/errors/route.ts", "removedCount": 7, "sizeDiff": 328, "statements": [{"statement": "console.error('Clear errors error:', error);", "line": 239}, {"statement": "console.log('All error reports cleared');", "line": 225}, {"statement": "console.log(`Would clear errors older than ${cutoffDate.toISOString()", "line": 221}, {"statement": "console.error('Report error error:', error);", "line": 182}, {"statement": "console.error('Get errors error:', error);", "line": 100}, {"statement": "// TODO: Check if user has admin permissions", "line": 198}, {"statement": "// TODO: Check if user has admin permissions", "line": 35}]}, {"file": "components/collaboration/LiveChatSystem.tsx", "removedCount": 7, "sizeDiff": 360, "statements": [{"statement": "console.log('Show participants panel');", "line": 420}, {"statement": "console.error('Error deleting message:', error);", "line": 366}, {"statement": "console.error('Error pinning message:', error);", "line": 346}, {"statement": "console.log(`Mentioning user ${userId}`);", "line": 187}, {"statement": "console.log(`Opening options for message ${messageId}`);", "line": 161}, {"statement": "console.log(`Adding ${reaction} reaction to message ${messageId}`);", "line": 135}, {"statement": "console.error('Error adding reaction:', error);", "line": 129}]}, {"file": "lib/api/logging.ts", "removedCount": 7, "sizeDiff": 347, "statements": [{"statement": "console.error('Failed to send to external monitoring:', error);", "line": 355}, {"statement": "console.log('Would send to external monitoring:', logEntry.message);", "line": 353}, {"statement": "console.error('Failed to send error to tracker:', trackingError);", "line": 337}, {"statement": "console.debug(logMessage, context);", "line": 311}, {"statement": "console.info(logMessage, context);", "line": 308}, {"statement": "console.warn(logMessage, context);", "line": 305}, {"statement": "console.error(logMessage, context);", "line": 302}]}, {"file": "lib/security/session.ts", "removedCount": 7, "sizeDiff": 641, "statements": [{"statement": "console.warn('Suspicious activity detected:', {\n      sessionId,\n      reasons,\n      ipAddress,\n      timestamp: Date.now()", "line": 503}, {"statement": "console.log('Analyzing session activity:', {\n    sessionId,\n    ipAddress,\n    userAgent: userAgent.substring(0, 50)", "line": 456}, {"statement": "console.warn(`Session validation failed: ${validation.reason}`);", "line": 418}, {"statement": "console.log('Session created:', {\n    userId,\n    sessionId: sessionInfo.sessionId,\n    ipAddress,\n    userAgent: userAgent?.substring(0, 50)", "line": 363}, {"statement": "console.error('Session validation error:', error);", "line": 345}, {"statement": "console.log(`Cleaned up ${expiredTokens.length} expired CSRF tokens`);", "line": 263}, {"statement": "console.log(`Cleaned up ${expiredSessions.length} expired sessions`);", "line": 240}]}, {"file": "lib/socket/NotificationSocketService.ts", "removedCount": 7, "sizeDiff": 451, "statements": [{"statement": "console.warn('Cannot send gamification event: socket not connected');", "line": 325}, {"statement": "console.warn('Cannot send collaboration event: socket not connected');", "line": 307}, {"statement": "console.warn('Cannot leave room: socket not connected');", "line": 295}, {"statement": "console.warn('Cannot join room: socket not connected');", "line": 283}, {"statement": "console.warn('Cannot send notification: socket not connected');", "line": 265}, {"statement": "console.warn('Cannot send notification: socket not connected');", "line": 247}, {"statement": "console.error(`Error in notification socket handler for ${event}:`, error);", "line": 236}]}, {"file": "app/api/courses/[id]/route.ts", "removedCount": 6, "sizeDiff": 290, "statements": [{"statement": "console.error('Delete course error:', error);", "line": 245}, {"statement": "console.error('Update course error:', error);", "line": 193}, {"statement": "console.error('Get course error:', error);", "line": 126}, {"statement": "// TODO: Check if course has enrollments", "line": 223}, {"statement": "// TODO: Check if user has permission to delete this course", "line": 217}, {"statement": "// TODO: Check if user has permission to update this course", "line": 164}]}, {"file": "app/api/user/code-stats/route.ts", "removedCount": 6, "sizeDiff": 489, "statements": [{"statement": "console.error('Error processing code stats action:', error);", "line": 117}, {"statement": "console.log(`User ${session.user.id} deployed contract:`, {\n          contractAddress,\n          networkId,\n          gasUsed,\n        });", "line": 102}, {"statement": "console.log(`User ${session.user.id} code activity:`, {\n          linesAdded,\n          linesRemoved,\n          language,\n          projectId,\n        });", "line": 86}, {"statement": "console.error('Error fetching code stats:', error);", "line": 66}, {"statement": "// TODO: Track contract deployments", "line": 101}, {"statement": "// TODO: Implement real-time code tracking", "line": 85}]}, {"file": "app/api/user/progress-stats/route.ts", "removedCount": 6, "sizeDiff": 402, "statements": [{"statement": "console.error('Error processing progress stats action:', error);", "line": 223}, {"statement": "console.log(`User ${session.user.id} set study schedule:`, schedule);", "line": 212}, {"statement": "console.log(`User ${session.user.id} updated goal ${goalId} to ${completed ? 'completed' : 'incomplete'}`);", "line": 201}, {"statement": "console.error('Error fetching progress stats:', error);", "line": 181}, {"statement": "// TODO: Implement study schedule in database", "line": 211}, {"statement": "// TODO: Implement goal tracking in database", "line": 200}]}, {"file": "components/ui/NotificationSystem.tsx", "removedCount": 6, "sizeDiff": 370, "statements": [{"statement": "console.warn('Failed to clear notification history:', error);", "line": 495}, {"statement": "console.warn(`Notification throttled: ${notification.type}`);", "line": 360}, {"statement": "console.warn('Failed to save notification history:', error);", "line": 336}, {"statement": "console.warn('Failed to load notification history:', error);", "line": 327}, {"statement": "console.warn('Failed to save notification preferences:', error);", "line": 314}, {"statement": "console.warn('Failed to load notification preferences:', error);", "line": 305}]}, {"file": "lib/auth/sessionManager.ts", "removedCount": 6, "sizeDiff": 349, "statements": [{"statement": "console.error('Error in session status listener:', error);", "line": 623}, {"statement": "console.error('Error in session event listener:', error);", "line": 611}, {"statement": "console.error('Error in session listener:', error);", "line": 469}, {"statement": "console.error('Failed to remove session from storage:', error);", "line": 459}, {"statement": "console.error('Failed to load session from storage:', error);", "line": 446}, {"statement": "console.error('Failed to save session to storage:', error);", "line": 418}]}, {"file": "lib/compiler/SolidityCompiler.ts", "removedCount": 6, "sizeDiff": 367, "statements": [{"statement": "console.log(`Constructor args: ${constructorArgs.length} parameters`);", "line": 308}, {"statement": "console.log(`Source code length: ${sourceCode.length} characters`);", "line": 307}, {"statement": "console.log(`Verifying contract at ${address}`);", "line": 306}, {"statement": "console.error('Deployment error:', error);", "line": 275}, {"statement": "console.log(`Estimated gas: ${estimatedGas}`);", "line": 266}, {"statement": "console.log(`Deploying to ${network} (Chain ID: ${config.chainId})", "line": 262}]}, {"file": "lib/config/environment.ts", "removedCount": 6, "sizeDiff": 319, "statements": [{"statement": "console.log('✅ Environment configuration validated successfully');", "line": 350}, {"statement": "console.error(`  - ${error}`)", "line": 346}, {"statement": "console.error('❌ Critical configuration errors:');", "line": 345}, {"statement": "console.error('Invalid variables:', invalidVars);", "line": 181}, {"statement": "console.error('Missing required variables:', missingVars);", "line": 177}, {"statement": "console.error('❌ Environment validation failed:');", "line": 174}]}, {"file": "lib/socket/server.ts", "removedCount": 6, "sizeDiff": 291, "statements": [{"statement": "console.error('Error getting user from session:', error);", "line": 368}, {"statement": "console.log('User disconnected:', socket.id);", "line": 308}, {"statement": "console.error('Error sending message:', error);", "line": 294}, {"statement": "console.error('Error joining session:', error);", "line": 213}, {"statement": "console.error('Authentication error:', error);", "line": 108}, {"statement": "console.log('User connected:', socket.id);", "line": 94}]}, {"file": "app/api/chat/reactions/route.ts", "removedCount": 5, "sizeDiff": 315, "statements": [{"statement": "console.error('Error fetching reactions:', error);", "line": 129}, {"statement": "console.error('Error updating reaction:', error);", "line": 73}, {"statement": "console.log(`User ${session.user.id} reacted with ${emoji} to message ${messageId} in session ${sessionId}`);", "line": 41}, {"statement": "// TODO: Implement actual message reaction retrieval", "line": 110}, {"statement": "// TODO: Implement actual message reaction storage", "line": 43}]}, {"file": "app/api/learning-paths/route.ts", "removedCount": 5, "sizeDiff": 237, "statements": [{"statement": "console.error('Error processing learning path action:', error);", "line": 257}, {"statement": "console.error('Error fetching learning paths:', error);", "line": 121}, {"statement": "// TODO: Implement course rating system", "line": 113}, {"statement": "// TODO: Implement module unlocking logic", "line": 101}, {"statement": "// TODO: Implement lesson locking logic", "line": 86}]}, {"file": "app/api/settings/route.ts", "removedCount": 5, "sizeDiff": 349, "statements": [{"statement": "console.error('Update settings error:', error);", "line": 337}, {"statement": "console.log('Settings updated:', {\n      section,\n      userId: mockUserSettings.userId,\n      timestamp: mockUserSettings.updatedAt,\n      requestId\n    });", "line": 318}, {"statement": "console.error('Get settings error:', error);", "line": 259}, {"statement": "// TODO: Get user ID from authentication token", "line": 307}, {"statement": "// TODO: Get user ID from authentication token", "line": 253}]}, {"file": "app/api/socket/route.ts", "removedCount": 5, "sizeDiff": 252, "statements": [{"statement": "console.error('Socket.io API error:', error);", "line": 116}, {"statement": "console.log(`Socket.io server running on port ${port}`);", "line": 75}, {"statement": "console.log('User disconnected:', socket.id);", "line": 68}, {"statement": "console.log('User connected:', socket.id);", "line": 30}, {"statement": "console.log('Initializing Socket.io server...');", "line": 12}]}, {"file": "app/dashboard/page.tsx", "removedCount": 5, "sizeDiff": 242, "statements": [{"statement": "console.error('Failed to start lesson:', error);", "line": 107}, {"statement": "console.error('Failed to start module:', error);", "line": 93}, {"statement": "console.log('Navigate to lesson:', lesson.id);", "line": 75}, {"statement": "console.log('Navigate to module:', module.id);", "line": 70}, {"statement": "console.error('Failed to load user progress:', error);", "line": 59}]}, {"file": "components/community/CommunityStats.tsx", "removedCount": 5, "sizeDiff": 259, "statements": [{"statement": "console.error('Failed to export stats:', error);", "line": 459}, {"statement": "console.error('Failed to refresh data:', error);", "line": 424}, {"statement": "console.error('Failed to load milestones:', error);", "line": 405}, {"statement": "console.error('Failed to load trending topics:', error);", "line": 396}, {"statement": "console.error('Failed to load community stats:', error);", "line": 385}]}, {"file": "components/dev/AccessibilityTester.tsx", "removedCount": 5, "sizeDiff": 238, "statements": [{"statement": "console.error('Accessibility test failed:', error);", "line": 50}, {"statement": "console.warn('Accessibility violations found:', result.violations);", "line": 46}, {"statement": "console.log(accessibilityTester.generateReport(result)", "line": 44}, {"statement": "console.groupEnd();", "line": 48}, {"statement": "console.group('🔍 Accessibility Test Results');", "line": 43}]}, {"file": "components/progress/ProgressDashboard.tsx", "removedCount": 5, "sizeDiff": 266, "statements": [{"statement": "console.error('Failed to load progress stats:', error);", "line": 171}, {"statement": "console.error('Error loading study schedule:', error);", "line": 140}, {"statement": "console.error('Error loading activity feed:', error);", "line": 125}, {"statement": "console.error('Error loading community data:', error);", "line": 106}, {"statement": "console.error('Error loading code stats:', error);", "line": 82}]}, {"file": "lib/accessibility/AccessibilityTester.ts", "removedCount": 5, "sizeDiff": 219, "statements": [{"statement": "console.warn('Accessibility violations found:', result.violations);", "line": 290}, {"statement": "console.log(accessibilityTester.generateReport(result)", "line": 287}, {"statement": "console.error('Accessibility test failed:', error);", "line": 102}, {"statement": "console.groupEnd();", "line": 293}, {"statement": "console.group('🔍 Accessibility Test Results');", "line": 286}]}, {"file": "lib/admin/auditLogger.ts", "removedCount": 5, "sizeDiff": 309, "statements": [{"statement": "console.log(`Cleaned up ${initialCount - this.logs.length} old audit logs`);", "line": 384}, {"statement": "console.error('Failed to save audit logs to storage:', error);", "line": 366}, {"statement": "console.error('Failed to load audit logs from storage:', error);", "line": 357}, {"statement": "console.error('Failed to persist audit log:', error);", "line": 320}, {"statement": "console.log('<PERSON>t log persisted:', log);", "line": 315}]}, {"file": "lib/auth/config.ts", "removedCount": 5, "sizeDiff": 249, "statements": [{"statement": "console.error('Signature verification error:', error);", "line": 221}, {"statement": "console.log(`New user signed up: ${user.email}`);", "line": 208}, {"statement": "console.error('Error creating user profile:', error);", "line": 194}, {"statement": "console.error('MetaMask auth error:', error);", "line": 152}, {"statement": "console.error('Credentials auth error:', error);", "line": 92}]}, {"file": "lib/blockchain/Web3Provider.tsx", "removedCount": 5, "sizeDiff": 248, "statements": [{"statement": "console.error('Error sending transaction:', error);", "line": 253}, {"statement": "console.error('Error getting balance:', error);", "line": 233}, {"statement": "console.error('Error deploying contract:', error);", "line": 214}, {"statement": "console.error('Error connecting wallet:', error);", "line": 146}, {"statement": "console.error('Error checking connection:', error);", "line": 108}]}, {"file": "lib/errors/recovery.ts", "removedCount": 5, "sizeDiff": 287, "statements": [{"statement": "console.error('Failed to flush errors:', error);", "line": 361}, {"statement": "console.log('Would send errors to monitoring service:', errorsToFlush);", "line": 358}, {"statement": "console.error('Error tracked:', errorAnalytics);", "line": 343}, {"statement": "console.error('Error in offline status listener:', error);", "line": 230}, {"statement": "// TODO: Implement actual error monitoring service integration", "line": 368}]}, {"file": "lib/git/GitIntegration.ts", "removedCount": 5, "sizeDiff": 351, "statements": [{"statement": "console.log(`Created and switched to branch: ${this.currentBranch}`);", "line": 359}, {"statement": "console.log('Changes pushed to remote repository');", "line": 308}, {"statement": "console.log(`Executing git commit: ${commitMessage}`);", "line": 189}, {"statement": "console.warn('Git integration not available:', error);", "line": 67}, {"statement": "console.log('Git integration initialized on branch:', this.currentBranch);", "line": 65}]}, {"file": "lib/hooks/useAdvancedCollaborativeEditor.ts", "removedCount": 5, "sizeDiff": 215, "statements": [{"statement": "console.error('<PERSON><PERSON> failed:', error);", "line": 322}, {"statement": "console.error('Failed to apply change:', error);", "line": 295}, {"statement": "console.error('Auto-save failed:', error);", "line": 265}, {"statement": "console.log('Document auto-saved');", "line": 263}, {"statement": "console.log('Opening conflict resolution UI');", "line": 186}]}, {"file": "hooks/useGitIntegration.ts", "removedCount": 5, "sizeDiff": 267, "statements": [{"statement": "console.error('Auto-commit lesson completion failed:', error);", "line": 281}, {"statement": "console.error('Auto-commit failed:', error);", "line": 266}, {"statement": "console.error('TypeScript check failed:', error);", "line": 213}, {"statement": "console.error('Failed to get git status:', error);", "line": 72}, {"statement": "console.error('Failed to initialize git integration:', error);", "line": 56}]}, {"file": "app/api/auth/login/route.ts", "removedCount": 4, "sizeDiff": 356, "statements": [{"statement": "console.error('Login error:', error);", "line": 294}, {"statement": "console.log('Login successful:', {\n      userId: user.id,\n      email: user.email,\n      ip: clientIP,\n      browser,\n      os,\n      rememberMe,\n      timestamp: new Date()", "line": 250}, {"statement": "console.error('JWT_SECRET is too short. Must be at least 32 characters.');", "line": 206}, {"statement": "// TODO: Remove this mock data and implement proper database integration", "line": 25}]}, {"file": "app/api/community/stats/route.ts", "removedCount": 4, "sizeDiff": 238, "statements": [{"statement": "console.error('Error processing community action:', error);", "line": 282}, {"statement": "console.log(`User ${session.user.id} requested mentorship`);", "line": 249}, {"statement": "console.error('Error fetching community stats:', error);", "line": 165}, {"statement": "// TODO: Implement mentorship matching system", "line": 248}]}, {"file": "app/api/leaderboard/route.ts", "removedCount": 4, "sizeDiff": 289, "statements": [{"statement": "console.error('Error processing leaderboard action:', error);", "line": 218}, {"statement": "console.log(`${session.user.id} follows ${followUserId}`);", "line": 207}, {"statement": "console.log(`Challenge created: ${session.user.id} challenges ${targetUserId} in ${challengeType}`);", "line": 191}, {"statement": "console.error('Error fetching leaderboard:', error);", "line": 151}]}, {"file": "app/api/v1/admin/maintenance/schedules/[id]/route.ts", "removedCount": 4, "sizeDiff": 248, "statements": [{"statement": "console.error('Run maintenance schedule error:', error);", "line": 168}, {"statement": "console.error('Delete maintenance schedule error:', error);", "line": 132}, {"statement": "console.error('Update maintenance schedule error:', error);", "line": 96}, {"statement": "console.error('Get maintenance schedule error:', error);", "line": 62}]}, {"file": "components/auth/EnhancedAuthProvider.tsx", "removedCount": 4, "sizeDiff": 175, "statements": [{"statement": "console.error('Profile update error:', error);", "line": 187}, {"statement": "console.error('Logout error:', error);", "line": 155}, {"statement": "console.error('Login error:', error);", "line": 146}, {"statement": "console.error('Error fetching user profile:', error);", "line": 102}]}, {"file": "components/collaboration/CollaborationChat.tsx", "removedCount": 4, "sizeDiff": 195, "statements": [{"statement": "console.error('Failed to save chat messages:', error);", "line": 605}, {"statement": "console.error('Failed to load chat messages:', error);", "line": 590}, {"statement": "console.error('File upload failed:', error);", "line": 189}, {"statement": "console.log('🔔 New message notification');", "line": 141}]}, {"file": "components/learning/ComprehensiveLearningPlatform.tsx", "removedCount": 4, "sizeDiff": 245, "statements": [{"statement": "console.log('Quick learning boost activated!');", "line": 154}, {"statement": "console.error('Error performing community action:', error);", "line": 148}, {"statement": "console.error('Community action failed:', await response.text()", "line": 145}, {"statement": "console.log('Community action completed:', result);", "line": 138}]}, {"file": "components/learning/InteractiveCodeEditor.tsx", "removedCount": 4, "sizeDiff": 204, "statements": [{"statement": "console.warn('Git commit failed:', gitError);", "line": 903}, {"statement": "console.warn('Git commit failed:', gitError);", "line": 875}, {"statement": "console.error('Failed to initialize collaboration:', error);", "line": 473}, {"statement": "console.warn('Editor performance degraded:', summary);", "line": 398}]}, {"file": "components/performance/PerformanceOptimizer.tsx", "removedCount": 4, "sizeDiff": 187, "statements": [{"statement": "console.log('CLS:', entry.value);", "line": 266}, {"statement": "console.log('FID:', entry.processingStart - entry.startTime);", "line": 263}, {"statement": "console.log('LCP:', entry.startTime);", "line": 260}, {"statement": "console.error('Performance optimization error:', error);", "line": 80}]}, {"file": "components/testing/UATDashboard.tsx", "removedCount": 4, "sizeDiff": 228, "statements": [{"statement": "console.log('Date Range Picker clicked - Advanced date filtering coming soon!');", "line": 519}, {"statement": "console.error('Failed to export report:', error);", "line": 163}, {"statement": "console.error('Failed to load UAT data:', error);", "line": 137}, {"statement": "console.log('UAT Dashboard loaded:', loadingData);", "line": 115}]}, {"file": "lib/api/logger.ts", "removedCount": 4, "sizeDiff": 110, "statements": [{"statement": "console.debug(formattedLog);", "line": 160}, {"statement": "console.info(formattedLog);", "line": 157}, {"statement": "console.warn(formattedLog);", "line": 154}, {"statement": "console.error(formattedLog);", "line": 151}]}, {"file": "lib/api/rate-limiting.ts", "removedCount": 4, "sizeDiff": 195, "statements": [{"statement": "console.error('<PERSON><PERSON> reset key error:', error);", "line": 95}, {"statement": "console.error('Redis decrement error:', error);", "line": 83}, {"statement": "console.error('Redis increment error:', error);", "line": 69}, {"statement": "console.error('Redis rate limit store error:', error);", "line": 47}]}, {"file": "lib/community/leaderboard.ts", "removedCount": 4, "sizeDiff": 219, "statements": [{"statement": "console.error('Error refreshing leaderboard:', error);", "line": 265}, {"statement": "console.error('Error fetching user rank:', error);", "line": 237}, {"statement": "console.error('Error fetching leaderboard categories:', error);", "line": 206}, {"statement": "console.error('Error fetching leaderboard:', error);", "line": 186}]}, {"file": "lib/curriculum/manager.ts", "removedCount": 4, "sizeDiff": 232, "statements": [{"statement": "console.error('Error in unlock listener:', error);", "line": 312}, {"statement": "console.error('Error in progress listener:', error);", "line": 118}, {"statement": "console.warn('Failed to save progress to localStorage:', error);", "line": 109}, {"statement": "console.warn('Failed to load progress from localStorage:', error);", "line": 64}]}, {"file": "lib/debugging/SolidityDebugger.ts", "removedCount": 4, "sizeDiff": 42, "statements": [{"statement": "debugger", "line": 429}, {"statement": "debugger", "line": 92}, {"statement": "debugger", "line": 81}, {"statement": "debugger", "line": 68}]}, {"file": "lib/git/CommitManager.ts", "removedCount": 4, "sizeDiff": 224, "statements": [{"statement": "console.log(`Files: ${files.join(', ')", "line": 326}, {"statement": "console.log(`Git commit: ${message}`);", "line": 324}, {"statement": "console.error(`❌ Failed to commit batch: ${batch.prefix}`, error);", "line": 179}, {"statement": "console.log(`✅ Committed batch: ${batch.prefix} (${batch.messages.length} changes)", "line": 177}]}, {"file": "lib/hooks/useAchievements.ts", "removedCount": 4, "sizeDiff": 227, "statements": [{"statement": "console.error('Failed to get filtered achievements:', err);", "line": 186}, {"statement": "console.error('Failed to mark notification as read:', err);", "line": 170}, {"statement": "console.error('Achievement event error:', err);", "line": 155}, {"statement": "console.error('Error in achievement unlock callback:', error);", "line": 121}]}, {"file": "lib/hooks/useApiData.ts", "removedCount": 4, "sizeDiff": 253, "statements": [{"statement": "console.log('Community stats fetch error:', errorMessage);", "line": 384}, {"statement": "console.error(`API Error in ${operation}:`, error);", "line": 365}, {"statement": "console.log('Projects fetch - authenticated:', isAuthenticated);", "line": 308}, {"statement": "console.log('Learning progress fetch - authenticated:', isAuthenticated);", "line": 262}]}, {"file": "lib/hooks/useNotificationIntegrations.ts", "removedCount": 4, "sizeDiff": 162, "statements": [{"statement": "console.log(`Starting ${topic} lesson...`);", "line": 339}, {"statement": "console.log('Viewing analysis details...');", "line": 303}, {"statement": "console.log('Connecting to mentor...');", "line": 248}, {"statement": "console.log('Retrying operation...');", "line": 39}]}, {"file": "lib/performance/PerformanceMonitor.ts", "removedCount": 4, "sizeDiff": 187, "statements": [{"statement": "console.warn(`  💡 ${suggestion}`)", "line": 307}, {"statement": "console.warn(`Performance Alert [${severity.toUpperCase()", "line": 306}, {"statement": "console.warn('Navigation Timing API not supported');", "line": 247}, {"statement": "console.warn('Long Task API not supported');", "line": 207}]}, {"file": "lib/security/rateLimiting.ts", "removedCount": 4, "sizeDiff": 239, "statements": [{"statement": "console.error('Error getting Redis keys:', error);", "line": 433}, {"statement": "console.error('Rate limiting error:', error);", "line": 280}, {"statement": "console.log('Falling back to memory-based rate limiting');", "line": 46}, {"statement": "console.warn('Failed to initialize Redis for rate limiting:', error);", "line": 45}]}, {"file": "lib/testing/ux-testing.ts", "removedCount": 4, "sizeDiff": 252, "statements": [{"statement": "console.error(`❌ UX test scenario failed: ${scenario.name}`, error);", "line": 561}, {"statement": "console.log(`✅ UX test scenario passed: ${scenario.name}`);", "line": 558}, {"statement": "console.log(`Executing step: ${step.action}`);", "line": 528}, {"statement": "console.log(`Running UX test scenario: ${scenario.name}`);", "line": 508}]}, {"file": "lib/utils/cssOptimization.ts", "removedCount": 4, "sizeDiff": 207, "statements": [{"statement": "console.error('Error finding unused selectors:', error);", "line": 132}, {"statement": "console.warn('Could not access stylesheet:', e);", "line": 108}, {"statement": "console.error('Error extracting critical CSS:', error);", "line": 63}, {"statement": "console.warn('Could not access stylesheet:', e);", "line": 59}]}, {"file": "app/api/chat/delete/route.ts", "removedCount": 3, "sizeDiff": 192, "statements": [{"statement": "console.error('Error deleting message:', error);", "line": 82}, {"statement": "console.log(`User ${session.user.id} deleted message ${messageId} in session ${sessionId}`);", "line": 41}, {"statement": "// TODO: Implement actual message deletion", "line": 39}]}, {"file": "app/api/chat/pin/route.ts", "removedCount": 3, "sizeDiff": 210, "statements": [{"statement": "console.error('Error updating message pin status:', error);", "line": 65}, {"statement": "console.log(`User ${session.user.id} pinned/unpinned message ${messageId} in session ${sessionId}`);", "line": 41}, {"statement": "// TODO: Implement actual message pinning", "line": 39}]}, {"file": "app/api/collaboration/route.ts", "removedCount": 3, "sizeDiff": 163, "statements": [{"statement": "console.error('Error updating collaboration:', error);", "line": 228}, {"statement": "console.error('Error creating collaboration:', error);", "line": 117}, {"statement": "console.error('Error fetching collaborations:', error);", "line": 73}]}, {"file": "app/api/courses/route.ts", "removedCount": 3, "sizeDiff": 134, "statements": [{"statement": "console.error('Create course error:', error);", "line": 297}, {"statement": "console.error('Get courses error:', error);", "line": 231}, {"statement": "// TODO: Get instructor ID from authentication", "line": 262}]}, {"file": "app/api/monitoring/performance/route.ts", "removedCount": 3, "sizeDiff": 167, "statements": [{"statement": "console.error('Error clearing metrics:', error);", "line": 122}, {"statement": "console.error('Error recording performance metric:', error);", "line": 104}, {"statement": "console.error('Error getting performance metrics:', error);", "line": 74}]}, {"file": "app/api/user/profile/route.ts", "removedCount": 3, "sizeDiff": 163, "statements": [{"statement": "console.error('Error processing profile action:', error);", "line": 228}, {"statement": "console.error('Error updating user profile:', error);", "line": 139}, {"statement": "console.error('Error fetching user profile:', error);", "line": 97}]}, {"file": "app/api/v1/auth/register/route.ts", "removedCount": 3, "sizeDiff": 160, "statements": [{"statement": "console.error('Registration error:', error);", "line": 132}, {"statement": "console.log(`Verification email sent to ${user.email}`);", "line": 66}, {"statement": "console.log(`Welcome email sent to ${user.email}`);", "line": 61}]}, {"file": "app/api/v1/users/[id]/route.ts", "removedCount": 3, "sizeDiff": 153, "statements": [{"statement": "console.error('Delete user error:', error);", "line": 270}, {"statement": "console.error('Update user error:', error);", "line": 219}, {"statement": "console.error('Get user error:', error);", "line": 148}]}, {"file": "components/collaboration/AdvancedUserPresence.tsx", "removedCount": 3, "sizeDiff": 159, "statements": [{"statement": "console.warn('Failed to parse stored session data:', error);", "line": 197}, {"statement": "console.log(`User ${userId} performed ${action}`);", "line": 142}, {"statement": "console.log(`Session ${sessionId}: ${activity}`);", "line": 136}]}, {"file": "components/collaboration/CollaborativeEditor.tsx", "removedCount": 3, "sizeDiff": 137, "statements": [{"statement": "console.log('Camera access granted');", "line": 269}, {"statement": "console.log('Microphone access granted');", "line": 246}, {"statement": "console.log('New participant added:', newParticipant);", "line": 170}]}, {"file": "components/collaboration/MonacoCollaborativeEditor.tsx", "removedCount": 3, "sizeDiff": 146, "statements": [{"statement": "console.log('Open editor settings');", "line": 616}, {"statement": "console.error('Error applying operation:', error);", "line": 249}, {"statement": "console.log('Session activity tracked:', activityData);", "line": 113}]}, {"file": "components/community/Leaderboards.tsx", "removedCount": 3, "sizeDiff": 158, "statements": [{"statement": "console.error('Failed to refresh leaderboard:', error);", "line": 452}, {"statement": "console.error('Failed to load leaderboard:', error);", "line": 412}, {"statement": "console.error('Failed to load categories:', error);", "line": 377}]}, {"file": "components/debugging/SolidityDebuggerInterface.tsx", "removedCount": 3, "sizeDiff": 106, "statements": [{"statement": "console.error('Missing required debugging parameters');", "line": 101}, {"statement": "console.log('Breakpoint hit:', breakpoint);", "line": 82}, {"statement": "debugger", "line": 149}]}, {"file": "components/discovery/DiscoveryProvider.tsx", "removedCount": 3, "sizeDiff": 163, "statements": [{"statement": "console.log(`Feature interaction: ${featureId} - ${action}`);", "line": 171}, {"statement": "console.log(`Feature dismissed: ${featureId}`);", "line": 120}, {"statement": "console.error('Failed to load discovery data:', error);", "line": 61}]}, {"file": "components/error-handling/ErrorBoundary.tsx", "removedCount": 3, "sizeDiff": 162, "statements": [{"statement": "console.error('Error logged:', errorData);", "line": 202}, {"statement": "console.error('<PERSON><PERSON> failed:', retryError);", "line": 130}, {"statement": "console.error('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> caught an error:', error, errorInfo);", "line": 73}]}, {"file": "components/errors/ErrorBoundary.tsx", "removedCount": 3, "sizeDiff": 165, "statements": [{"statement": "console.log('Error report:', errorReport);", "line": 89}, {"statement": "console.error(`Error Bo<PERSON>ry (${this.props.level})", "line": 59}, {"statement": "// TODO: Integrate with Sentry, LogRocket, or other monitoring service", "line": 88}]}, {"file": "components/monitoring/PerformanceMonitor.tsx", "removedCount": 3, "sizeDiff": 188, "statements": [{"statement": "console.log('Resource Timings:', resourceTimings);", "line": 228}, {"statement": "console.log('Performance Metrics:', metrics);", "line": 227}, {"statement": "console.error('Failed to send performance metrics:', error);", "line": 139}]}, {"file": "lib/admin/auth.ts", "removedCount": 3, "sizeDiff": 149, "statements": [{"statement": "console.error('Failed to load user permissions:', error);", "line": 193}, {"statement": "console.log('Critical security event:', event);", "line": 184}, {"statement": "console.log('Security event logged:', event);", "line": 179}]}, {"file": "lib/analysis/SolidityCodeAnalyzer.ts", "removedCount": 3, "sizeDiff": 179, "statements": [{"statement": "console.error(`Style rule ${ruleName} failed:`, error);", "line": 121}, {"statement": "console.error(`Gas optimization rule ${ruleName} failed:`, error);", "line": 111}, {"statement": "console.error(`Security rule ${ruleName} failed:`, error);", "line": 101}]}, {"file": "lib/api/middleware.ts", "removedCount": 3, "sizeDiff": 247, "statements": [{"statement": "console.error('Unhandled API Error:', {\n    error: error.message,\n    stack: error.stack,\n    url: request.url,\n    method: request.method,\n    timestamp: new Date()", "line": 349}, {"statement": "console.log('API Request:', logData);", "line": 61}, {"statement": "console.error('API Request Error:', logData);", "line": 59}]}, {"file": "lib/collaboration/CollaborationClient.ts", "removedCount": 3, "sizeDiff": 150, "statements": [{"statement": "console.log('Presence update:', message.data);", "line": 257}, {"statement": "console.error('WebSocket error:', error);", "line": 236}, {"statement": "console.error('Failed to parse collaboration message:', error);", "line": 217}]}, {"file": "lib/collaboration/CollaborativeEditor.ts", "removedCount": 3, "sizeDiff": 170, "statements": [{"statement": "console.error('Failed to apply remote operation:', error);", "line": 242}, {"statement": "console.error('Collaboration error:', error);", "line": 116}, {"statement": "console.error('Failed to initialize collaborative editor:', error);", "line": 77}]}, {"file": "lib/errors/ErrorContext.tsx", "removedCount": 3, "sizeDiff": 163, "statements": [{"statement": "console.error('<PERSON>rror added:', error);", "line": 169}, {"statement": "console.error('<PERSON><PERSON> failed:', retryError);", "line": 138}, {"statement": "// TODO: Integrate with error monitoring service (Sentry, LogRocket, etc.)", "line": 173}]}, {"file": "lib/hooks/useRealTimeXP.ts", "removedCount": 3, "sizeDiff": 148, "statements": [{"statement": "console.error('Error in level listener:', error);", "line": 367}, {"statement": "console.error('Error in XP listener:', error);", "line": 357}, {"statement": "console.error('Error processing XP updates:', error);", "line": 153}]}, {"file": "lib/monitoring/apiPerformance.ts", "removedCount": 3, "sizeDiff": 288, "statements": [{"statement": "console.error('Failed to send metrics to monitoring service:', error);", "line": 133}, {"statement": "console.error(`Critical slow API call: ${metric.method} ${metric.endpoint} took ${metric.duration}ms`);", "line": 46}, {"statement": "console.warn(`Slow API call detected: ${metric.method} ${metric.endpoint} took ${metric.duration}ms`);", "line": 42}]}, {"file": "lib/monitoring/errorTracking.ts", "removedCount": 3, "sizeDiff": 180, "statements": [{"statement": "console.error('❌ Failed to initialize Sentry:', error);", "line": 118}, {"statement": "console.log('✅ Sentry error tracking initialized');", "line": 113}, {"statement": "console.warn('Sentry DSN not configured, error tracking disabled');", "line": 53}]}, {"file": "lib/socket/SocketProvider.tsx", "removedCount": 3, "sizeDiff": 127, "statements": [{"statement": "console.error('Socket error:', error);", "line": 65}, {"statement": "console.log('Disconnected from socket server');", "line": 60}, {"statement": "console.log('Connected to socket server');", "line": 55}]}, {"file": "app/api/achievements/route.ts", "removedCount": 2, "sizeDiff": 107, "statements": [{"statement": "console.error('Error processing achievement:', error);", "line": 120}, {"statement": "console.error('Error fetching achievements:', error);", "line": 46}]}, {"file": "app/api/ai/assistant/route.ts", "removedCount": 2, "sizeDiff": 100, "statements": [{"statement": "console.error('Error fetching AI interactions:', error);", "line": 175}, {"statement": "console.error('AI Assistant error:', error);", "line": 135}]}, {"file": "app/api/community/enhanced-stats/route.ts", "removedCount": 2, "sizeDiff": 112, "statements": [{"statement": "console.error('Error fetching community stats:', error);", "line": 182}, {"statement": "console.error('Error fetching community stats:', error);", "line": 168}]}, {"file": "app/api/compile/route.ts", "removedCount": 2, "sizeDiff": 95, "statements": [{"statement": "console.error('Error fetching submissions:', error);", "line": 111}, {"statement": "console.error('Compilation error:', error);", "line": 72}]}, {"file": "app/api/deployments/route.ts", "removedCount": 2, "sizeDiff": 101, "statements": [{"statement": "console.error('Error fetching deployments:', error);", "line": 72}, {"statement": "console.error('Error saving deployment:', error);", "line": 54}]}, {"file": "app/api/feedback/submit/route.ts", "removedCount": 2, "sizeDiff": 142, "statements": [{"statement": "console.log(`Session ${feedback.sessionId} feedback count updated`);", "line": 390}, {"statement": "console.log(`Feedback ${feedback.id} assigned to team: ${assignedTeam}`);", "line": 363}]}, {"file": "app/api/metrics/route.ts", "removedCount": 2, "sizeDiff": 87, "statements": [{"statement": "console.error('Get metrics error:', error);", "line": 241}, {"statement": "// TODO: Check if user has admin permissions", "line": 82}]}, {"file": "app/api/user/progress/route.ts", "removedCount": 2, "sizeDiff": 103, "statements": [{"statement": "console.error('Error updating progress:', error);", "line": 171}, {"statement": "console.error('Error fetching user progress:', error);", "line": 95}]}, {"file": "app/api/v1/admin/maintenance/route.ts", "removedCount": 2, "sizeDiff": 121, "statements": [{"statement": "console.error('Execute maintenance action error:', error);", "line": 208}, {"statement": "console.error('Get maintenance status error:', error);", "line": 120}]}, {"file": "app/api/v1/admin/maintenance/schedules/route.ts", "removedCount": 2, "sizeDiff": 125, "statements": [{"statement": "console.error('Create maintenance schedule error:', error);", "line": 73}, {"statement": "console.error('Get maintenance schedules error:', error);", "line": 48}]}, {"file": "app/api/v1/lessons/route.ts", "removedCount": 2, "sizeDiff": 101, "statements": [{"statement": "console.error('Create lesson error:', error);", "line": 261}, {"statement": "console.error('Get lessons error:', error);", "line": 217}]}, {"file": "app/api/v1/users/route.ts", "removedCount": 2, "sizeDiff": 93, "statements": [{"statement": "console.error('Create user error:', error);", "line": 245}, {"statement": "console.error('Get users error:', error);", "line": 187}]}, {"file": "app/session-expired/page.tsx", "removedCount": 2, "sizeDiff": 92, "statements": [{"statement": "console.error('Manual refresh failed:', error);", "line": 85}, {"statement": "console.error('Auto-refresh failed:', error);", "line": 63}]}, {"file": "components/admin/CommunityManagement.tsx", "removedCount": 2, "sizeDiff": 134, "statements": [{"statement": "console.log(`Report ${reportId} action: ${action}`, { resolution });", "line": 360}, {"statement": "console.error('Failed to process report action:', error);", "line": 60}]}, {"file": "components/admin/ContentManagement.tsx", "removedCount": 2, "sizeDiff": 132, "statements": [{"statement": "console.log(`Bulk action ${action} on content:`, Array.from(selectedContent)", "line": 389}, {"statement": "console.log(`Action ${action} on content ${contentId}`);", "line": 384}]}, {"file": "components/admin/UserManagement.tsx", "removedCount": 2, "sizeDiff": 122, "statements": [{"statement": "console.log(`Bulk action ${action} on users:`, Array.from(selectedUsers)", "line": 379}, {"statement": "console.log(`Action ${action} on user ${userId}`);", "line": 374}]}, {"file": "components/auth/EnhancedLoginModal.tsx", "removedCount": 2, "sizeDiff": 82, "statements": [{"statement": "console.error('<PERSON><PERSON> login failed:', error);", "line": 175}, {"statement": "console.error('<PERSON><PERSON> failed:', error);", "line": 155}]}, {"file": "components/blockchain/BlockchainIntegration.tsx", "removedCount": 2, "sizeDiff": 106, "statements": [{"statement": "console.error('Failed to connect wallet:', error);", "line": 119}, {"statement": "console.error('Failed to load blockchain data:', error);", "line": 106}]}, {"file": "components/collaboration/RealTimeCodeEditor.tsx", "removedCount": 2, "sizeDiff": 83, "statements": [{"statement": "console.log('Open settings');", "line": 463}, {"statement": "console.log('Session event tracked:', eventData);", "line": 100}]}, {"file": "components/collaboration/SessionRecovery.tsx", "removedCount": 2, "sizeDiff": 88, "statements": [{"statement": "console.error('Force sync failed:', error);", "line": 106}, {"statement": "console.error('Reconnection failed:', error);", "line": 95}]}, {"file": "components/discovery/FeatureSpotlight.tsx", "removedCount": 2, "sizeDiff": 70, "statements": [{"statement": "console.log('Starting collaboration');", "line": 360}, {"statement": "console.log('Opening AI tutor');", "line": 322}]}, {"file": "components/learning/LessonProgressTracker.tsx", "removedCount": 2, "sizeDiff": 112, "statements": [{"statement": "console.error('Failed to save lesson progress:', error);", "line": 92}, {"statement": "console.error('Failed to load lesson progress:', error);", "line": 76}]}, {"file": "components/ui/AnimationShowcase.tsx", "removedCount": 2, "sizeDiff": 71, "statements": [{"statement": "console.log('SVG button clicked!')", "line": 280}, {"statement": "console.log('<PERSON><PERSON> button clicked!')", "line": 179}]}, {"file": "components/ui/ErrorMessage.tsx", "removedCount": 2, "sizeDiff": 88, "statements": [{"statement": "console.error('Action failed:', actionError);", "line": 124}, {"statement": "console.error('<PERSON><PERSON> failed:', retryError);", "line": 112}]}, {"file": "lib/achievements/manager.ts", "removedCount": 2, "sizeDiff": 123, "statements": [{"statement": "console.error('Error in achievement unlock listener:', error);", "line": 400}, {"statement": "console.error('Error in achievement event listener:', error);", "line": 143}]}, {"file": "lib/api/integration.ts", "removedCount": 2, "sizeDiff": 119, "statements": [{"statement": "console.error('Failed to apply accessibility settings:', error);", "line": 360}, {"statement": "console.error('Failed to fetch user settings:', error);", "line": 274}]}, {"file": "lib/api/optimizedApiClient.ts", "removedCount": 2, "sizeDiff": 81, "statements": [{"statement": "console.error('Mu<PERSON> failed:', error);", "line": 293}, {"statement": "console.warn('Prefetch failed:', error);", "line": 216}]}, {"file": "lib/editor/MonacoSoliditySetup.ts", "removedCount": 2, "sizeDiff": 105, "statements": [{"statement": "console.error('Failed to update IntelliSense:', error);", "line": 428}, {"statement": "console.error('Semantic analysis failed:', error);", "line": 225}]}, {"file": "lib/hooks/useSolidityVersionControl.ts", "removedCount": 2, "sizeDiff": 98, "statements": [{"statement": "console.error('Failed to get branch history:', error);", "line": 343}, {"statement": "console.error('Failed to get diff:', error);", "line": 331}]}, {"file": "lib/monitoring/error-tracking.ts", "removedCount": 2, "sizeDiff": 96, "statements": [{"statement": "console.error('Error tracked:', event);", "line": 302}, {"statement": "console.error('Error in error tracker listener:', error);", "line": 282}]}, {"file": "lib/monitoring/logger.ts", "removedCount": 2, "sizeDiff": 123, "statements": [{"statement": "console.error('CRITICAL SECURITY ALERT:', event);", "line": 477}, {"statement": "console.log(`Sending ${type} data to monitoring service:`, data);", "line": 468}]}, {"file": "lib/security/headers.ts", "removedCount": 2, "sizeDiff": 162, "statements": [{"statement": "console.error('Security middleware error:', error);", "line": 369}, {"statement": "console.log('Security middleware applied:', {\n      hasSecurityConfig: Object.keys(securityConfig)", "line": 312}]}, {"file": "hooks/useCollaborationConnection.ts", "removedCount": 2, "sizeDiff": 99, "statements": [{"statement": "console.error('Auto-reconnect failed:', error);", "line": 181}, {"statement": "console.warn('Data loss detected:', lostOperations);", "line": 123}]}, {"file": "hooks/useLessonProgress.ts", "removedCount": 2, "sizeDiff": 112, "statements": [{"statement": "console.error('Failed to save lesson progress:', error);", "line": 114}, {"statement": "console.error('Failed to load lesson progress:', error);", "line": 91}]}, {"file": "hooks/useProgress.ts", "removedCount": 2, "sizeDiff": 132, "statements": [{"statement": "console.error(\"Failed to save progress to localStorage:\", error);", "line": 24}, {"statement": "console.error(\"Failed to load progress from localStorage:\", error);", "line": 15}]}, {"file": "app/api/community/leaderboard/categories/route.ts", "removedCount": 1, "sizeDiff": 63, "statements": [{"statement": "console.error('Error fetching leaderboard categories:', error);", "line": 88}]}, {"file": "app/api/community/leaderboard/route.ts", "removedCount": 1, "sizeDiff": 52, "statements": [{"statement": "console.error('Error fetching leaderboard:', error);", "line": 235}]}, {"file": "app/api/community/milestones/route.ts", "removedCount": 1, "sizeDiff": 61, "statements": [{"statement": "console.error('Error fetching community milestones:', error);", "line": 128}]}, {"file": "app/api/community/trending/route.ts", "removedCount": 1, "sizeDiff": 56, "statements": [{"statement": "console.error('Error fetching trending topics:', error);", "line": 139}]}, {"file": "app/api/user/xp/route.ts", "removedCount": 1, "sizeDiff": 43, "statements": [{"statement": "console.error('Error updating XP:', error);", "line": 94}]}, {"file": "app/api/v1/auth/login/route.ts", "removedCount": 1, "sizeDiff": 46, "statements": [{"statement": "console.error('Login error:', error);", "line": 163}]}, {"file": "app/api/v1/auth/refresh/route.ts", "removedCount": 1, "sizeDiff": 54, "statements": [{"statement": "console.error('Token refresh error:', error);", "line": 101}]}, {"file": "app/api/v1/health/route.ts", "removedCount": 1, "sizeDiff": 53, "statements": [{"statement": "console.error('Health check error:', error);", "line": 177}]}, {"file": "app/cookies/page.tsx", "removedCount": 1, "sizeDiff": 61, "statements": [{"statement": "console.log('Saving cookie preferences:', cookiePreferences);", "line": 65}]}, {"file": "app/unauthorized/page.tsx", "removedCount": 1, "sizeDiff": 47, "statements": [{"statement": "console.error('Access request failed:', error);", "line": 99}]}, {"file": "components/admin/AuditLogViewer.tsx", "removedCount": 1, "sizeDiff": 51, "statements": [{"statement": "console.error('Failed to load audit logs:', error);", "line": 251}]}, {"file": "components/admin/SecurityManagement.tsx", "removedCount": 1, "sizeDiff": 58, "statements": [{"statement": "console.error('Failed to resolve security event:', error);", "line": 65}]}, {"file": "components/ai/AILearningPath.tsx", "removedCount": 1, "sizeDiff": 59, "statements": [{"statement": "console.error('Failed to generate learning paths:', error);", "line": 247}]}, {"file": "components/auth/ProtectedRoute.tsx", "removedCount": 1, "sizeDiff": 46, "statements": [{"statement": "console.error('Session check failed:', error);", "line": 127}]}, {"file": "components/blockchain/ContractDeployer.tsx", "removedCount": 1, "sizeDiff": 42, "statements": [{"statement": "console.error('Deployment error:', error);", "line": 126}]}, {"file": "components/collaboration/CollaborationHub.tsx", "removedCount": 1, "sizeDiff": 68, "statements": [{"statement": "console.log('User interaction tracked:', interactionData);", "line": 61}]}, {"file": "components/collaboration/FileSharing.tsx", "removedCount": 1, "sizeDiff": 44, "statements": [{"statement": "console.error('Failed to copy URL:', error);", "line": 198}]}, {"file": "components/CopyButton.tsx", "removedCount": 1, "sizeDiff": 45, "statements": [{"statement": "console.error('Failed to copy text: ', err);", "line": 31}]}, {"file": "components/error-handling/AsyncErrorBoundary.tsx", "removedCount": 1, "sizeDiff": 43, "statements": [{"statement": "console.error('<PERSON><PERSON> failed:', retryError);", "line": 144}]}, {"file": "components/error-handling/NotFoundPage.tsx", "removedCount": 1, "sizeDiff": 63, "statements": [{"statement": "console.log('404 Error tracked:', { path, timestamp: new Date()", "line": 144}]}, {"file": "components/forms/ContactForm.tsx", "removedCount": 1, "sizeDiff": 56, "statements": [{"statement": "console.error('Contact form submission failed:', error);", "line": 197}]}, {"file": "components/learning/LearningDashboard.tsx", "removedCount": 1, "sizeDiff": 55, "statements": [{"statement": "console.error('Error fetching dashboard data:', error);", "line": 85}]}, {"file": "components/learning/StructuredCurriculum.tsx", "removedCount": 1, "sizeDiff": 61, "statements": [{"statement": "console.log(`Quick action ${action} for module ${moduleId}`);", "line": 161}]}, {"file": "components/navigation/GuidedOnboarding.tsx", "removedCount": 1, "sizeDiff": 55, "statements": [{"statement": "console.error('Onboarding step action failed:', error);", "line": 288}]}, {"file": "components/navigation/SmartNavigation.tsx", "removedCount": 1, "sizeDiff": 42, "statements": [{"statement": "console.error('Navigation error:', error);", "line": 78}]}, {"file": "components/notifications/NotificationIntegrations.tsx", "removedCount": 1, "sizeDiff": 54, "statements": [{"statement": "console.warn('Could not check storage quota:', error);", "line": 262}]}, {"file": "components/settings/SecuritySection.tsx", "removedCount": 1, "sizeDiff": 53, "statements": [{"statement": "console.error('Failed to copy to clipboard:', error);", "line": 194}]}, {"file": "components/ui/ErrorHandling.tsx", "removedCount": 1, "sizeDiff": 61, "statements": [{"statement": "console.error('Error caught by boundary:', error, errorInfo);", "line": 48}]}, {"file": "components/ui/GlassNeumorphDemo.tsx", "removedCount": 1, "sizeDiff": 27, "statements": [{"statement": "console.log('Card clicked')", "line": 140}]}, {"file": "components/ui/SessionStatusIndicator.tsx", "removedCount": 1, "sizeDiff": 51, "statements": [{"statement": "console.error('Failed to refresh session:', error);", "line": 177}]}, {"file": "components/ui/SmartSearch.tsx", "removedCount": 1, "sizeDiff": 54, "statements": [{"statement": "console.error('Failed to save recent search:', error);", "line": 292}]}, {"file": "lib/accessibility/contrast-utils.ts", "removedCount": 1, "sizeDiff": 78, "statements": [{"statement": "console.debug(`Accessibility audit found ${totalElements} elements to check`);", "line": 381}]}, {"file": "lib/api/documentation.ts", "removedCount": 1, "sizeDiff": 57, "statements": [{"statement": "console.warn(`Failed to convert schema ${name}:`, error);", "line": 378}]}, {"file": "lib/api/security.ts", "removedCount": 1, "sizeDiff": 50, "statements": [{"statement": "console.warn('CORS: Origin not allowed:', origin);", "line": 108}]}, {"file": "lib/api/utils.ts", "removedCount": 1, "sizeDiff": 48, "statements": [{"statement": "console.error('API Error:', error);", "line": 375}]}, {"file": "lib/api/validation.ts", "removedCount": 1, "sizeDiff": 52, "statements": [{"statement": "console.error('Response validation failed:', error);", "line": 582}]}, {"file": "lib/collaboration/AdvancedCollaborativeEditor.ts", "removedCount": 1, "sizeDiff": 59, "statements": [{"statement": "console.log(`Auto-resolving conflict: ${conflict.reason}`);", "line": 257}]}, {"file": "lib/features/feature-flags.ts", "removedCount": 1, "sizeDiff": 54, "statements": [{"statement": "console.warn(`Feature flag not found: ${featureKey}`);", "line": 188}]}, {"file": "lib/hooks/useAuth.ts", "removedCount": 1, "sizeDiff": 48, "statements": [{"statement": "console.error('Session refresh failed:', error);", "line": 200}]}, {"file": "lib/hooks/useErrorRecovery.ts", "removedCount": 1, "sizeDiff": 66, "statements": [{"statement": "console.log(`Retrying operation (attempt ${attempt}/${maxRetries})", "line": 270}]}, {"file": "lib/hooks/useSessionStatus.ts", "removedCount": 1, "sizeDiff": 48, "statements": [{"statement": "console.error('Session refresh failed:', error);", "line": 75}]}, {"file": "lib/utils/assetOptimization.ts", "removedCount": 1, "sizeDiff": 48, "statements": [{"statement": "console.warn('Could not access stylesheet:', e);", "line": 215}]}, {"file": "lib/utils/redirects.ts", "removedCount": 1, "sizeDiff": 33, "statements": [{"statement": "console.log('404 Error:', event);", "line": 284}]}, {"file": "hooks/usePerformance.ts", "removedCount": 1, "sizeDiff": 151, "statements": [{"statement": "console.log(`[Performance] ${componentName}:`, {\n        renderTime: `${renderTime}ms`,\n        mountTime: `${metrics.componentMountTime}ms`,\n      });", "line": 179}]}]}