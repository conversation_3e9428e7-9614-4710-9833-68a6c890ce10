#!/usr/bin/env node

/**
 * Comprehensive Performance and Accessibility Audit
 * Analyzes bundle sizes, performance metrics, and accessibility compliance
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Audit configuration
const AUDIT_CONFIG = {
  baseUrl: 'http://localhost:3000',
  pages: [
    { path: '/', name: 'Homepage', budget: 'default' },
    { path: '/dashboard', name: 'Dashboard', budget: 'dashboard' },
    { path: '/learn', name: 'Learn Page', budget: 'learn' },
    { path: '/code', name: 'Code Editor', budget: 'code' },
  ],
  performanceBudget: null, // Will be loaded from performance-budget.json
  reportDir: './reports',
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Load performance budget configuration
function loadPerformanceBudget() {
  try {
    const budgetPath = path.join(process.cwd(), 'performance-budget.json');
    if (fs.existsSync(budgetPath)) {
      const budget = JSON.parse(fs.readFileSync(budgetPath, 'utf8'));
      AUDIT_CONFIG.performanceBudget = budget;
      logSuccess('Performance budget configuration loaded');
      return budget;
    } else {
      logWarning('performance-budget.json not found, using default thresholds');
      return null;
    }
  } catch (error) {
    logError(`Failed to load performance budget: ${error.message}`);
    return null;
  }
}

// Analyze bundle sizes
function analyzeBundleSizes() {
  logHeader('Bundle Size Analysis');
  
  const results = {
    buildExists: false,
    bundleSizes: {},
    budgetViolations: [],
    recommendations: []
  };

  try {
    // Check if .next build directory exists
    const buildDir = path.join(process.cwd(), '.next');
    if (!fs.existsSync(buildDir)) {
      logWarning('Build directory not found. Running build...');
      try {
        execSync('npm run build', { stdio: 'inherit', timeout: 300000 });
        results.buildExists = true;
      } catch (error) {
        logError('Build failed. Cannot analyze bundle sizes.');
        return results;
      }
    } else {
      results.buildExists = true;
    }

    // Analyze static directory for built assets
    const staticDir = path.join(buildDir, 'static');
    if (fs.existsSync(staticDir)) {
      const chunks = path.join(staticDir, 'chunks');
      const css = path.join(staticDir, 'css');
      
      if (fs.existsSync(chunks)) {
        const jsFiles = fs.readdirSync(chunks).filter(f => f.endsWith('.js'));
        let totalJsSize = 0;
        
        jsFiles.forEach(file => {
          const filePath = path.join(chunks, file);
          const stats = fs.statSync(filePath);
          totalJsSize += stats.size;
        });
        
        results.bundleSizes.javascript = Math.round(totalJsSize / 1024); // KB
        log(`JavaScript Bundle Size: ${results.bundleSizes.javascript} KB`, 'blue');
      }
      
      if (fs.existsSync(css)) {
        const cssFiles = fs.readdirSync(css).filter(f => f.endsWith('.css'));
        let totalCssSize = 0;
        
        cssFiles.forEach(file => {
          const filePath = path.join(css, file);
          const stats = fs.statSync(filePath);
          totalCssSize += stats.size;
        });
        
        results.bundleSizes.css = Math.round(totalCssSize / 1024); // KB
        log(`CSS Bundle Size: ${results.bundleSizes.css} KB`, 'blue');
      }
    }

    // Check against performance budget
    if (AUDIT_CONFIG.performanceBudget) {
      const budget = AUDIT_CONFIG.performanceBudget.budget[0]; // Default budget
      
      if (budget.resourceSizes) {
        budget.resourceSizes.forEach(resource => {
          const actualSize = results.bundleSizes[resource.resourceType === 'script' ? 'javascript' : resource.resourceType];
          
          if (actualSize && actualSize > resource.budget) {
            results.budgetViolations.push({
              type: resource.resourceType,
              actual: actualSize,
              budget: resource.budget,
              excess: actualSize - resource.budget
            });
            
            logError(`${resource.resourceType} budget exceeded: ${actualSize}KB > ${resource.budget}KB`);
          } else if (actualSize) {
            logSuccess(`${resource.resourceType} within budget: ${actualSize}KB <= ${resource.budget}KB`);
          }
        });
      }
    }

    // Generate recommendations
    if (results.bundleSizes.javascript > 400) {
      results.recommendations.push({
        type: 'javascript',
        priority: 'high',
        issue: 'JavaScript bundle size exceeds 400KB',
        suggestion: 'Implement code splitting, remove unused dependencies, and use dynamic imports'
      });
    }

    if (results.bundleSizes.css > 100) {
      results.recommendations.push({
        type: 'css',
        priority: 'medium',
        issue: 'CSS bundle size exceeds 100KB',
        suggestion: 'Remove unused CSS, optimize Tailwind configuration, and consider CSS-in-JS'
      });
    }

  } catch (error) {
    logError(`Bundle analysis failed: ${error.message}`);
  }

  return results;
}

// Analyze package.json dependencies
function analyzeDependencies() {
  logHeader('Dependency Analysis');
  
  const results = {
    totalDependencies: 0,
    devDependencies: 0,
    heavyDependencies: [],
    securityIssues: [],
    recommendations: []
  };

  try {
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const deps = packageJson.dependencies || {};
    const devDeps = packageJson.devDependencies || {};
    
    results.totalDependencies = Object.keys(deps).length;
    results.devDependencies = Object.keys(devDeps).length;
    
    log(`Production Dependencies: ${results.totalDependencies}`, 'blue');
    log(`Development Dependencies: ${results.devDependencies}`, 'blue');

    // Check for heavy dependencies
    const heavyPackages = [
      'monaco-editor', 'three', '@google/genai', 'framer-motion', 
      'react-syntax-highlighter', 'chart.js', 'lodash'
    ];
    
    heavyPackages.forEach(pkg => {
      if (deps[pkg]) {
        results.heavyDependencies.push(pkg);
        logWarning(`Heavy dependency detected: ${pkg}`);
      }
    });

    // Security recommendations
    if (deps['lodash']) {
      results.recommendations.push({
        type: 'security',
        priority: 'medium',
        issue: 'Lodash dependency detected',
        suggestion: 'Consider using native JavaScript methods or lodash-es for tree shaking'
      });
    }

    if (results.totalDependencies > 50) {
      results.recommendations.push({
        type: 'performance',
        priority: 'medium',
        issue: `High number of dependencies (${results.totalDependencies})`,
        suggestion: 'Audit dependencies and remove unused packages'
      });
    }

  } catch (error) {
    logError(`Dependency analysis failed: ${error.message}`);
  }

  return results;
}

// Check accessibility compliance
function checkAccessibilityCompliance() {
  logHeader('Accessibility Compliance Check');
  
  const results = {
    wcagCompliance: 'unknown',
    issues: [],
    recommendations: []
  };

  try {
    // Check for accessibility-related dependencies
    const packagePath = path.join(process.cwd(), 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    
    const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    if (deps['@axe-core/react'] || deps['axe-core']) {
      logSuccess('Axe-core accessibility testing tools found');
      results.wcagCompliance = 'tools-available';
    } else {
      logWarning('No accessibility testing tools found');
      results.recommendations.push({
        type: 'accessibility',
        priority: 'high',
        issue: 'No accessibility testing tools',
        suggestion: 'Install @axe-core/react and axe-core for automated accessibility testing'
      });
    }

    // Check for common accessibility patterns in code
    const srcDirs = ['app', 'components', 'lib'];
    let ariaUsage = 0;
    let altTextUsage = 0;
    
    srcDirs.forEach(dir => {
      const dirPath = path.join(process.cwd(), dir);
      if (fs.existsSync(dirPath)) {
        const files = getAllFiles(dirPath, ['.tsx', '.jsx']);
        
        files.forEach(file => {
          const content = fs.readFileSync(file, 'utf8');
          
          // Count ARIA usage
          const ariaMatches = content.match(/aria-\w+/g);
          if (ariaMatches) ariaUsage += ariaMatches.length;
          
          // Count alt text usage
          const altMatches = content.match(/alt\s*=/g);
          if (altMatches) altTextUsage += altMatches.length;
        });
      }
    });

    log(`ARIA attributes found: ${ariaUsage}`, ariaUsage > 10 ? 'green' : 'yellow');
    log(`Alt text attributes found: ${altTextUsage}`, altTextUsage > 5 ? 'green' : 'yellow');

    if (ariaUsage < 10) {
      results.recommendations.push({
        type: 'accessibility',
        priority: 'medium',
        issue: 'Low ARIA attribute usage',
        suggestion: 'Add more ARIA labels, roles, and properties for better screen reader support'
      });
    }

    if (altTextUsage < 5) {
      results.recommendations.push({
        type: 'accessibility',
        priority: 'high',
        issue: 'Low alt text usage',
        suggestion: 'Add alt text to all images for screen reader accessibility'
      });
    }

  } catch (error) {
    logError(`Accessibility compliance check failed: ${error.message}`);
  }

  return results;
}

// Helper function to get all files recursively
function getAllFiles(dir, extensions) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    items.forEach(item => {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        traverse(fullPath);
      } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    });
  }
  
  traverse(dir);
  return files;
}

// Generate comprehensive report
function generateComprehensiveReport(bundleResults, depResults, a11yResults) {
  logHeader('Comprehensive Audit Report');
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      bundleAnalysis: bundleResults,
      dependencyAnalysis: depResults,
      accessibilityAnalysis: a11yResults
    },
    overallScore: 0,
    criticalIssues: [],
    recommendations: []
  };

  // Collect all recommendations
  const allRecommendations = [
    ...bundleResults.recommendations,
    ...depResults.recommendations,
    ...a11yResults.recommendations
  ];

  // Sort by priority
  const priorityOrder = { high: 3, medium: 2, low: 1 };
  allRecommendations.sort((a, b) => priorityOrder[b.priority] - priorityOrder[a.priority]);

  report.recommendations = allRecommendations;

  // Calculate overall score
  let score = 100;
  
  // Deduct points for budget violations
  bundleResults.budgetViolations.forEach(violation => {
    score -= Math.min(20, violation.excess / 10);
  });
  
  // Deduct points for high-priority recommendations
  allRecommendations.forEach(rec => {
    if (rec.priority === 'high') score -= 15;
    else if (rec.priority === 'medium') score -= 10;
    else score -= 5;
  });

  report.overallScore = Math.max(0, Math.round(score));

  // Display summary
  log(`\nOverall Audit Score: ${report.overallScore}/100`, 
      report.overallScore >= 80 ? 'green' : report.overallScore >= 60 ? 'yellow' : 'red');
  
  log(`Total Recommendations: ${allRecommendations.length}`, 
      allRecommendations.length === 0 ? 'green' : 'yellow');

  // Display top recommendations
  if (allRecommendations.length > 0) {
    log('\n🔧 Top Recommendations:', 'bright');
    allRecommendations.slice(0, 5).forEach((rec, index) => {
      const priorityColor = rec.priority === 'high' ? 'red' : rec.priority === 'medium' ? 'yellow' : 'blue';
      log(`\n${index + 1}. [${rec.priority.toUpperCase()}] ${rec.issue}`, priorityColor);
      log(`   💡 ${rec.suggestion}`, 'cyan');
    });
  }

  return report;
}

// Save report to file
function saveReport(report) {
  try {
    if (!fs.existsSync(AUDIT_CONFIG.reportDir)) {
      fs.mkdirSync(AUDIT_CONFIG.reportDir, { recursive: true });
    }

    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(AUDIT_CONFIG.reportDir, `comprehensive-audit-${timestamp}.json`);
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    logInfo(`Comprehensive report saved to: ${reportPath}`);
    
    return reportPath;
  } catch (error) {
    logError(`Failed to save report: ${error.message}`);
    return null;
  }
}

// Main execution
async function main() {
  logHeader('Solidity Learning Platform - Comprehensive Audit');
  
  try {
    // Load configuration
    loadPerformanceBudget();
    
    // Run all audits
    const bundleResults = analyzeBundleSizes();
    const depResults = analyzeDependencies();
    const a11yResults = checkAccessibilityCompliance();
    
    // Generate comprehensive report
    const report = generateComprehensiveReport(bundleResults, depResults, a11yResults);
    
    // Save report
    saveReport(report);
    
    // Exit with appropriate code
    const success = report.overallScore >= 70;
    
    log('\n' + '='.repeat(60), 'cyan');
    if (success) {
      logSuccess('🎉 Audit completed successfully!');
    } else {
      logWarning('⚠️  Audit completed with issues. See recommendations above.');
    }
    log('='.repeat(60), 'cyan');
    
    process.exit(success ? 0 : 1);
    
  } catch (error) {
    logError(`Comprehensive audit failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    logError(`Audit runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { analyzeBundleSizes, analyzeDependencies, checkAccessibilityCompliance };
