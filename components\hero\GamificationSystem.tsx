'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Trophy, 
  Star, 
  Zap, 
  Target, 
  Award, 
  Crown, 
  Flame,
  TrendingUp,
  Users,
  Clock,
  CheckCircle,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  xpReward: number;
  unlocked: boolean;
  unlockedAt?: Date;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  progress?: number;
  maxProgress?: number;
}

interface ProgressData {
  currentStep: number;
  completedSteps: Set<number>;
  totalSteps: number;
  totalXP: number;
  achievements: Achievement[];
  startTime: Date;
  lastActiveTime: Date;
  completionPercentage: number;
  estimatedTimeRemaining: number;
  streak: number;
}

interface LeaderboardEntry {
  rank: number;
  username: string;
  avatar?: string;
  xp: number;
  completionTime: number;
  achievements: number;
  isCurrentUser?: boolean;
}

interface GamificationSystemProps {
  progress: ProgressData;
  onAchievementUnlocked?: (achievement: Achievement) => void;
  onXPEarned?: (amount: number, reason: string) => void;
  showLeaderboard?: boolean;
  className?: string;
}

/**
 * Comprehensive gamification system with XP, achievements, and progress tracking
 */
export function GamificationSystem({
  progress,
  onAchievementUnlocked,
  onXPEarned,
  showLeaderboard = true,
  className = ''
}: GamificationSystemProps) {
  const [showAchievementModal, setShowAchievementModal] = useState(false);
  const [newAchievement, setNewAchievement] = useState<Achievement | null>(null);
  const [leaderboard, setLeaderboard] = useState<LeaderboardEntry[]>([]);

  // Mock leaderboard data
  useEffect(() => {
    const mockLeaderboard: LeaderboardEntry[] = [
      { rank: 1, username: 'CodeMaster', xp: 2450, completionTime: 145, achievements: 12 },
      { rank: 2, username: 'SolidityPro', xp: 2380, completionTime: 152, achievements: 11 },
      { rank: 3, username: 'BlockchainDev', xp: 2290, completionTime: 158, achievements: 10 },
      { rank: 4, username: 'You', xp: progress.totalXP, completionTime: 0, achievements: progress.achievements.filter(a => a.unlocked).length, isCurrentUser: true },
      { rank: 5, username: 'SmartContractGuru', xp: 2150, completionTime: 167, achievements: 9 }
    ];
    
    setLeaderboard(mockLeaderboard.sort((a, b) => b.xp - a.xp).map((entry, index) => ({ ...entry, rank: index + 1 })));
  }, [progress.totalXP, progress.achievements]);

  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary':
        return 'from-yellow-400 to-orange-500';
      case 'epic':
        return 'from-purple-400 to-pink-500';
      case 'rare':
        return 'from-blue-400 to-cyan-500';
      default:
        return 'from-gray-400 to-gray-500';
    }
  };

  const getRarityBorder = (rarity: string) => {
    switch (rarity) {
      case 'legendary':
        return 'border-yellow-400/50 shadow-yellow-400/20';
      case 'epic':
        return 'border-purple-400/50 shadow-purple-400/20';
      case 'rare':
        return 'border-blue-400/50 shadow-blue-400/20';
      default:
        return 'border-gray-400/50 shadow-gray-400/20';
    }
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getXPLevel = (xp: number) => {
    return Math.floor(xp / 100) + 1;
  };

  const getXPProgress = (xp: number) => {
    return (xp % 100) / 100;
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Progress Overview */}
      <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 border border-purple-500/20 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-bold text-white">Your Progress</h3>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Trophy className="w-5 h-5 text-yellow-400" />
              <span className="text-yellow-300 font-bold">{progress.totalXP} XP</span>
            </div>
            <div className="flex items-center space-x-2">
              <Crown className="w-5 h-5 text-purple-400" />
              <span className="text-purple-300 font-bold">Level {getXPLevel(progress.totalXP)}</span>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">
              Step {progress.currentStep + 1} of {progress.totalSteps}
            </span>
            <span className="text-sm text-gray-300">
              {Math.round(progress.completionPercentage)}% Complete
            </span>
          </div>
          <div className="w-full h-3 bg-gray-700 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full relative"
              initial={{ width: '0%' }}
              animate={{ width: `${progress.completionPercentage}%` }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-blue-400 rounded-full blur-sm opacity-50" />
            </motion.div>
          </div>
        </div>

        {/* XP Level Progress */}
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Level Progress</span>
            <span className="text-sm text-gray-300">
              {progress.totalXP % 100}/100 XP
            </span>
          </div>
          <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full"
              initial={{ width: '0%' }}
              animate={{ width: `${getXPProgress(progress.totalXP) * 100}%` }}
              transition={{ duration: 0.6, ease: "easeOut" }}
            />
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">
              {progress.achievements.filter(a => a.unlocked).length}
            </div>
            <div className="text-xs text-gray-400">Achievements</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">
              {progress.streak}
            </div>
            <div className="text-xs text-gray-400">Day Streak</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">
              {formatTime(progress.estimatedTimeRemaining)}
            </div>
            <div className="text-xs text-gray-400">Time Left</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">
              #{leaderboard.find(entry => entry.isCurrentUser)?.rank || 'N/A'}
            </div>
            <div className="text-xs text-gray-400">Rank</div>
          </div>
        </div>
      </div>

      {/* Achievements */}
      <div className="bg-gray-900/50 border border-gray-700 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-white flex items-center space-x-2">
            <Award className="w-5 h-5 text-yellow-400" />
            <span>Achievements</span>
          </h3>
          <span className="text-sm text-gray-400">
            {progress.achievements.filter(a => a.unlocked).length}/{progress.achievements.length}
          </span>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {progress.achievements.slice(0, 6).map((achievement) => (
            <motion.div
              key={achievement.id}
              className={cn(
                'p-4 rounded-lg border-2 transition-all duration-300',
                achievement.unlocked
                  ? `bg-gradient-to-br ${getRarityColor(achievement.rarity)}/10 ${getRarityBorder(achievement.rarity)} shadow-lg`
                  : 'bg-gray-800/50 border-gray-600 opacity-60'
              )}
              whileHover={achievement.unlocked ? { scale: 1.02 } : {}}
            >
              <div className="flex items-start space-x-3">
                <div className={cn(
                  'w-10 h-10 rounded-full flex items-center justify-center',
                  achievement.unlocked
                    ? `bg-gradient-to-br ${getRarityColor(achievement.rarity)}`
                    : 'bg-gray-600'
                )}>
                  <achievement.icon className="w-5 h-5 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className={cn(
                    'font-medium text-sm',
                    achievement.unlocked ? 'text-white' : 'text-gray-400'
                  )}>
                    {achievement.title}
                  </h4>
                  <p className="text-xs text-gray-400 mt-1">
                    {achievement.description}
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <span className={cn(
                      'text-xs font-bold',
                      achievement.unlocked ? 'text-yellow-400' : 'text-gray-500'
                    )}>
                      +{achievement.xpReward} XP
                    </span>
                    <span className={cn(
                      'text-xs px-2 py-1 rounded-full uppercase font-bold',
                      achievement.unlocked
                        ? `bg-gradient-to-r ${getRarityColor(achievement.rarity)} text-white`
                        : 'bg-gray-600 text-gray-300'
                    )}>
                      {achievement.rarity}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Leaderboard */}
      {showLeaderboard && (
        <div className="bg-gray-900/50 border border-gray-700 rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-white flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-green-400" />
              <span>Leaderboard</span>
            </h3>
            <span className="text-sm text-gray-400">Top Learners</span>
          </div>

          <div className="space-y-2">
            {leaderboard.slice(0, 5).map((entry) => (
              <motion.div
                key={entry.rank}
                className={cn(
                  'flex items-center justify-between p-3 rounded-lg transition-colors',
                  entry.isCurrentUser
                    ? 'bg-blue-500/20 border border-blue-500/30'
                    : 'bg-gray-800/50 hover:bg-gray-800/70'
                )}
                whileHover={{ scale: 1.01 }}
              >
                <div className="flex items-center space-x-3">
                  <div className={cn(
                    'w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm',
                    entry.rank === 1 ? 'bg-yellow-500 text-black' :
                    entry.rank === 2 ? 'bg-gray-400 text-black' :
                    entry.rank === 3 ? 'bg-orange-500 text-black' :
                    'bg-gray-600 text-white'
                  )}>
                    {entry.rank}
                  </div>
                  <div>
                    <div className={cn(
                      'font-medium',
                      entry.isCurrentUser ? 'text-blue-300' : 'text-white'
                    )}>
                      {entry.username}
                    </div>
                    <div className="text-xs text-gray-400">
                      {entry.achievements} achievements
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-yellow-400 font-bold">{entry.xp} XP</div>
                  {entry.completionTime > 0 && (
                    <div className="text-xs text-gray-400">
                      {formatTime(entry.completionTime)}
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Achievement Unlock Modal */}
      <AnimatePresence>
        {showAchievementModal && newAchievement && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowAchievementModal(false)}
          >
            <motion.div
              className="bg-gray-900 border border-yellow-400/50 rounded-lg p-6 max-w-md w-full shadow-2xl shadow-yellow-400/20"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="text-center">
                <motion.div
                  className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center"
                  animate={{ rotate: [0, 10, -10, 0] }}
                  transition={{ duration: 0.5, repeat: 2 }}
                >
                  <newAchievement.icon className="w-10 h-10 text-white" />
                </motion.div>
                <h3 className="text-xl font-bold text-white mb-2">Achievement Unlocked!</h3>
                <h4 className="text-lg font-semibold text-yellow-400 mb-2">{newAchievement.title}</h4>
                <p className="text-gray-300 mb-4">{newAchievement.description}</p>
                <div className="flex items-center justify-center space-x-4">
                  <div className="flex items-center space-x-1">
                    <Zap className="w-4 h-4 text-yellow-400" />
                    <span className="text-yellow-400 font-bold">+{newAchievement.xpReward} XP</span>
                  </div>
                  <span className={cn(
                    'px-3 py-1 rounded-full text-xs font-bold uppercase',
                    `bg-gradient-to-r ${getRarityColor(newAchievement.rarity)} text-white`
                  )}>
                    {newAchievement.rarity}
                  </span>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
