'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion';
import { 
  ArrowUp, 
  Play, 
  Share2, 
  BookOpen, 
  Target, 
  TrendingUp,
  ChevronRight,
  Zap,
  Award,
  Users,
  MessageCircle,
  ExternalLink,
  CheckCircle,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import { useProgressTracking } from '@/components/progress/ProgressTrackingSystem';

interface ScrollTrigger {
  id: string;
  depth: number; // Percentage (0-100)
  type: 'cta' | 'social_share' | 'progress_indicator' | 'content_unlock' | 'engagement_boost';
  content: {
    title: string;
    description?: string;
    ctaText: string;
    ctaAction: string;
    icon?: React.ComponentType<{ className?: string }>;
    urgency?: string;
    socialProof?: string;
  };
  conditions?: {
    minTimeOnPage?: number;
    maxTriggersPerSession?: number;
    userSegment?: string[];
    deviceType?: 'mobile' | 'desktop' | 'tablet';
  };
  styling: {
    position: 'floating' | 'inline' | 'sticky' | 'modal';
    animation: 'slide-in' | 'fade-in' | 'bounce' | 'pulse';
    color: 'primary' | 'secondary' | 'success' | 'warning' | 'info';
  };
}

interface ScrollAnalytics {
  scrollDepth: number;
  timeOnPage: number;
  triggersShown: string[];
  conversions: string[];
  scrollVelocity: number;
  engagementScore: number;
  exitPoints: number[];
  heatmapData: Array<{ depth: number; timeSpent: number; interactions: number }>;
}

interface ContextualCTA {
  id: string;
  sectionId: string;
  content: {
    title: string;
    description: string;
    ctaText: string;
    ctaAction: string;
    variant: 'primary' | 'secondary' | 'ghost';
  };
  triggers: {
    scrollDepth: number;
    timeInSection: number;
    userBehavior: 'engaged' | 'hesitant' | 'leaving';
  };
}

export function ScrollEngagementSystem({ 
  className,
  onConversion,
  onAnalyticsUpdate 
}: { 
  className?: string;
  onConversion?: (triggerId: string, action: string) => void;
  onAnalyticsUpdate?: (analytics: ScrollAnalytics) => void;
}) {
  const [scrollAnalytics, setScrollAnalytics] = useState<ScrollAnalytics>({
    scrollDepth: 0,
    timeOnPage: 0,
    triggersShown: [],
    conversions: [],
    scrollVelocity: 0,
    engagementScore: 0,
    exitPoints: [],
    heatmapData: []
  });

  const [activeTriggers, setActiveTriggers] = useState<Set<string>>(new Set());
  const [visibleCTAs, setVisibleCTAs] = useState<Map<string, boolean>>(new Map());
  const [showFloatingProgress, setShowFloatingProgress] = useState(false);
  const [showScrollToTop, setShowScrollToTop] = useState(false);
  const [currentSection, setCurrentSection] = useState<string>('');

  const { scrollY, scrollYProgress } = useScroll();
  const { userProgress } = useProgressTracking();
  
  const pageStartTime = useRef(Date.now());
  const lastScrollY = useRef(0);
  const scrollVelocityRef = useRef(0);
  const sectionRefs = useRef<Map<string, HTMLElement>>(new Map());

  // Predefined scroll triggers
  const scrollTriggers: ScrollTrigger[] = [
    {
      id: 'learning-outcomes-cta',
      depth: 25,
      type: 'cta',
      content: {
        title: 'Ready to Start Coding?',
        description: 'Begin your Solidity journey with hands-on projects',
        ctaText: 'Start Free Trial',
        ctaAction: 'start_trial',
        icon: Play,
        socialProof: '2,156+ developers started this week'
      },
      styling: {
        position: 'floating',
        animation: 'slide-in',
        color: 'primary'
      }
    },
    {
      id: 'comparison-advantage-cta',
      depth: 50,
      type: 'cta',
      content: {
        title: 'See Our Advantages',
        description: 'Compare features with other platforms',
        ctaText: 'View Comparison',
        ctaAction: 'view_comparison',
        icon: TrendingUp,
        urgency: 'Most popular section'
      },
      styling: {
        position: 'inline',
        animation: 'fade-in',
        color: 'secondary'
      }
    },
    {
      id: 'social-share-milestone',
      depth: 75,
      type: 'social_share',
      content: {
        title: 'Share Your Progress',
        description: 'Let others know about your blockchain learning journey',
        ctaText: 'Share Achievement',
        ctaAction: 'share_progress',
        icon: Share2
      },
      styling: {
        position: 'floating',
        animation: 'bounce',
        color: 'success'
      }
    },
    {
      id: 'final-conversion-cta',
      depth: 90,
      type: 'cta',
      content: {
        title: 'Don\'t Miss Out!',
        description: 'Join thousands of successful blockchain developers',
        ctaText: 'Get Started Now',
        ctaAction: 'final_conversion',
        icon: Zap,
        urgency: 'Limited time offer',
        socialProof: '87% job placement rate'
      },
      styling: {
        position: 'sticky',
        animation: 'pulse',
        color: 'warning'
      }
    }
  ];

  // Contextual CTAs based on content sections
  const contextualCTAs: ContextualCTA[] = [
    {
      id: 'learning-path-cta',
      sectionId: 'learning-path',
      content: {
        title: 'Start Your 30-Day Journey',
        description: 'Begin with personalized learning path',
        ctaText: 'Take Skill Assessment',
        ctaAction: 'start_assessment',
        variant: 'primary'
      },
      triggers: {
        scrollDepth: 30,
        timeInSection: 10000, // 10 seconds
        userBehavior: 'engaged'
      }
    },
    {
      id: 'faq-support-cta',
      sectionId: 'faq',
      content: {
        title: 'Still Have Questions?',
        description: 'Get instant help from our support team',
        ctaText: 'Chat with Expert',
        ctaAction: 'open_chat',
        variant: 'secondary'
      },
      triggers: {
        scrollDepth: 60,
        timeInSection: 15000, // 15 seconds
        userBehavior: 'hesitant'
      }
    }
  ];

  // Track scroll analytics
  useEffect(() => {
    const updateAnalytics = () => {
      const currentTime = Date.now();
      const timeOnPage = currentTime - pageStartTime.current;
      const scrollDepth = Math.round(scrollYProgress.get() * 100);
      
      // Calculate scroll velocity
      const currentScrollY = scrollY.get();
      const velocity = Math.abs(currentScrollY - lastScrollY.current);
      lastScrollY.current = currentScrollY;
      scrollVelocityRef.current = velocity;

      // Calculate engagement score based on multiple factors
      const engagementScore = Math.min(100, 
        (timeOnPage / 1000) * 0.1 + // Time factor
        scrollDepth * 0.3 + // Scroll depth factor
        (velocity > 0 ? 10 : 0) + // Activity factor
        activeTriggers.size * 5 // Interaction factor
      );

      const newAnalytics: ScrollAnalytics = {
        scrollDepth,
        timeOnPage,
        triggersShown: Array.from(activeTriggers),
        conversions: scrollAnalytics.conversions,
        scrollVelocity: velocity,
        engagementScore,
        exitPoints: scrollAnalytics.exitPoints,
        heatmapData: [
          ...scrollAnalytics.heatmapData,
          { depth: scrollDepth, timeSpent: timeOnPage, interactions: activeTriggers.size }
        ].slice(-100) // Keep last 100 data points
      };

      setScrollAnalytics(newAnalytics);
      onAnalyticsUpdate?.(newAnalytics);

      // Show/hide floating elements based on scroll
      setShowFloatingProgress(scrollDepth > 10 && scrollDepth < 95);
      setShowScrollToTop(scrollDepth > 50);
    };

    const unsubscribe = scrollY.onChange(updateAnalytics);
    const interval = setInterval(updateAnalytics, 1000);

    return () => {
      unsubscribe();
      clearInterval(interval);
    };
  }, [scrollY, scrollYProgress, activeTriggers, onAnalyticsUpdate, scrollAnalytics.conversions, scrollAnalytics.exitPoints, scrollAnalytics.heatmapData]);

  // Check and trigger scroll-based CTAs
  useEffect(() => {
    scrollTriggers.forEach(trigger => {
      if (scrollAnalytics.scrollDepth >= trigger.depth && !activeTriggers.has(trigger.id)) {
        // Check conditions
        if (trigger.conditions) {
          if (trigger.conditions.minTimeOnPage && scrollAnalytics.timeOnPage < trigger.conditions.minTimeOnPage) {
            return;
          }
          if (trigger.conditions.maxTriggersPerSession && activeTriggers.size >= trigger.conditions.maxTriggersPerSession) {
            return;
          }
        }

        setActiveTriggers(prev => new Set([...prev, trigger.id]));
        
        // Track trigger activation
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'scroll_trigger_activated', {
            trigger_id: trigger.id,
            scroll_depth: scrollAnalytics.scrollDepth,
            time_on_page: scrollAnalytics.timeOnPage,
            engagement_score: scrollAnalytics.engagementScore
          }

// Hook for scroll engagement analytics and optimization
export function useScrollEngagementAnalytics() {
  const [analytics, setAnalytics] = useState({
    totalPageViews: 0,
    averageScrollDepth: 0,
    averageTimeOnPage: 0,
    conversionsByDepth: {} as Record<string, number>,
    exitPointAnalysis: {} as Record<string, number>,
    ctaPerformance: {} as Record<string, { views: number; clicks: number; rate: number }>,
    heatmapData: [] as Array<{ depth: number; engagement: number; conversions: number }>,
    abTestResults: {} as Record<string, { variant: string; conversions: number; views: number }>
  });

  const [optimizationSettings, setOptimizationSettings] = useState({
    enableSmartTiming: true,
    adaptivePositioning: true,
    personalizedContent: true,
    respectUserPreferences: true,
    maxCTAsPerPage: 3,
    minTimeBetweenTriggers: 5000 // 5 seconds
  });

  useEffect(() => {
    // Load analytics from localStorage
    const savedAnalytics = localStorage.getItem('scroll_engagement_analytics');
    if (savedAnalytics) {
      try {
        setAnalytics(JSON.parse(savedAnalytics));
      } catch (error) {
        console.error('Failed to load scroll engagement analytics:', error);
      }
    }

    // Load optimization settings
    const savedSettings = localStorage.getItem('scroll_optimization_settings');
    if (savedSettings) {
      try {
        setOptimizationSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Failed to load optimization settings:', error);
      }
    }
  }, []);

  const trackPageView = useCallback(() => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        totalPageViews: prev.totalPageViews + 1
      };
      localStorage.setItem('scroll_engagement_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });
  }, []);

  const trackScrollSession = useCallback((sessionData: {
    maxScrollDepth: number;
    timeOnPage: number;
    conversions: string[];
    exitPoint: number;
  }) => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        averageScrollDepth: (prev.averageScrollDepth * prev.totalPageViews + sessionData.maxScrollDepth) / (prev.totalPageViews + 1),
        averageTimeOnPage: (prev.averageTimeOnPage * prev.totalPageViews + sessionData.timeOnPage) / (prev.totalPageViews + 1),
        exitPointAnalysis: {
          ...prev.exitPointAnalysis,
          [Math.round(sessionData.exitPoint / 10) * 10]: (prev.exitPointAnalysis[Math.round(sessionData.exitPoint / 10) * 10] || 0) + 1
        }
      };

      // Track conversions by depth
      sessionData.conversions.forEach(conversion => {
        const depthRange = Math.round(sessionData.maxScrollDepth / 25) * 25; // Group by 25% ranges
        newAnalytics.conversionsByDepth[depthRange] = (newAnalytics.conversionsByDepth[depthRange] || 0) + 1;
      });

      localStorage.setItem('scroll_engagement_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });
  }, []);

  const trackCTAPerformance = useCallback((ctaId: string, action: 'view' | 'click') => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        ctaPerformance: {
          ...prev.ctaPerformance,
          [ctaId]: {
            views: (prev.ctaPerformance[ctaId]?.views || 0) + (action === 'view' ? 1 : 0),
            clicks: (prev.ctaPerformance[ctaId]?.clicks || 0) + (action === 'click' ? 1 : 0),
            rate: 0 // Will be calculated
          }
        }
      };

      // Calculate click-through rate
      const cta = newAnalytics.ctaPerformance[ctaId];
      cta.rate = cta.views > 0 ? (cta.clicks / cta.views) * 100 : 0;

      localStorage.setItem('scroll_engagement_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });
  }, []);

  const getOptimalCTAPositions = useCallback(() => {
    const positions = [];

    // Analyze conversion data to find optimal positions
    const conversionDepths = Object.entries(analytics.conversionsByDepth)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3); // Top 3 performing depths

    conversionDepths.forEach(([depth, conversions]) => {
      if (conversions > 5) { // Minimum threshold
        positions.push({
          depth: parseInt(depth),
          score: conversions,
          recommended: true
        });
      }
    });

    // Add default positions if not enough data
    if (positions.length < 2) {
      positions.push(
        { depth: 25, score: 0, recommended: false },
        { depth: 75, score: 0, recommended: false }
      );
    }

    return positions;
  }, [analytics.conversionsByDepth]);

  const getPersonalizedTriggers = useCallback((userSegment: string, scrollBehavior: 'fast' | 'slow' | 'normal') => {
    const baseTriggers = [
      { depth: 25, type: 'engagement' },
      { depth: 50, type: 'conversion' },
      { depth: 75, type: 'social' },
      { depth: 90, type: 'final_push' }
    ];

    // Adjust based on scroll behavior
    if (scrollBehavior === 'fast') {
      // Show triggers earlier for fast scrollers
      return baseTriggers.map(trigger => ({
        ...trigger,
        depth: Math.max(10, trigger.depth - 15)
      }));
    } else if (scrollBehavior === 'slow') {
      // Show triggers later for slow scrollers
      return baseTriggers.map(trigger => ({
        ...trigger,
        depth: Math.min(95, trigger.depth + 10)
      }));
    }

    return baseTriggers;
  }, []);

  const generateHeatmapData = useCallback(() => {
    const heatmap = [];

    for (let depth = 0; depth <= 100; depth += 5) {
      const engagement = analytics.exitPointAnalysis[depth] || 0;
      const conversions = analytics.conversionsByDepth[depth] || 0;

      heatmap.push({
        depth,
        engagement,
        conversions,
        intensity: Math.min(100, (engagement + conversions * 5)) // Weight conversions more heavily
      });
    }

    return heatmap;
  }, [analytics.exitPointAnalysis, analytics.conversionsByDepth]);

  const getConversionInsights = useCallback(() => {
    const totalConversions = Object.values(analytics.conversionsByDepth).reduce((sum, count) => sum + count, 0);
    const bestPerformingDepth = Object.entries(analytics.conversionsByDepth)
      .sort(([,a], [,b]) => b - a)[0];

    const averageCTAPerformance = Object.values(analytics.ctaPerformance)
      .reduce((sum, cta) => sum + cta.rate, 0) / Object.keys(analytics.ctaPerformance).length || 0;

    return {
      totalConversions,
      bestPerformingDepth: bestPerformingDepth ? {
        depth: parseInt(bestPerformingDepth[0]),
        conversions: bestPerformingDepth[1]
      } : null,
      averageCTAPerformance: Math.round(averageCTAPerformance * 100) / 100,
      conversionRate: analytics.totalPageViews > 0 ? (totalConversions / analytics.totalPageViews) * 100 : 0,
      engagementScore: Math.min(100, analytics.averageScrollDepth + (analytics.averageTimeOnPage / 1000) * 0.1)
    };
  }, [analytics]);

  const updateOptimizationSettings = useCallback((newSettings: Partial<typeof optimizationSettings>) => {
    const updated = { ...optimizationSettings, ...newSettings };
    setOptimizationSettings(updated);
    localStorage.setItem('scroll_optimization_settings', JSON.stringify(updated));
  }, [optimizationSettings]);

  return {
    analytics,
    optimizationSettings,
    trackPageView,
    trackScrollSession,
    trackCTAPerformance,
    getOptimalCTAPositions,
    getPersonalizedTriggers,
    generateHeatmapData,
    getConversionInsights,
    updateOptimizationSettings
  };
}

// Scroll engagement provider for global state
export function ScrollEngagementProvider({ children }: { children: React.ReactNode }) {
  const analytics = useScrollEngagementAnalytics();
  const [globalSettings, setGlobalSettings] = useState({
    enabled: true,
    respectReducedMotion: true,
    mobileOptimized: true,
    debugMode: false
  });

  useEffect(() => {
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      setGlobalSettings(prev => ({ ...prev, respectReducedMotion: true }));
    }

    // Track initial page view
    analytics.trackPageView();
  }, [analytics]);

  const contextValue = {
    ...analytics,
    globalSettings,
    setGlobalSettings
  };

  return (
    <ScrollEngagementContext.Provider value={contextValue}>
      {children}
    </ScrollEngagementContext.Provider>
  );
}

// Context for scroll engagement
const ScrollEngagementContext = React.createContext<any>(null);

export function useScrollEngagementContext() {
  const context = React.useContext(ScrollEngagementContext);
  if (!context) {
    throw new Error('useScrollEngagementContext must be used within ScrollEngagementProvider');
  }
  return context;
});
        }
      }
    });
  }, [scrollAnalytics.scrollDepth, scrollAnalytics.timeOnPage, activeTriggers]);

  // Handle CTA interactions
  const handleCTAClick = useCallback((triggerId: string, action: string) => {
    setScrollAnalytics(prev => ({
      ...prev,
      conversions: [...prev.conversions, triggerId]
    }));

    onConversion?.(triggerId, action);

    // Track conversion
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'scroll_cta_conversion', {
        trigger_id: triggerId,
        action,
        scroll_depth: scrollAnalytics.scrollDepth,
        time_on_page: scrollAnalytics.timeOnPage,
        engagement_score: scrollAnalytics.engagementScore
      });
    }
  }, [scrollAnalytics.scrollDepth, scrollAnalytics.timeOnPage, scrollAnalytics.engagementScore, onConversion]);

  // Handle social sharing
  const handleSocialShare = useCallback((platform: string) => {
    const shareData = {
      title: 'Learning Solidity Development',
      text: `I'm learning blockchain development with this amazing platform! ${userProgress?.overallProgress || 0}% complete.`,
      url: window.location.href
    };

    if (platform === 'native' && navigator.share) {
      navigator.share(shareData);
    } else {
      // Fallback to platform-specific sharing
      const shareUrls = {
        twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareData.text)}&url=${encodeURIComponent(shareData.url)}`,
        linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareData.url)}`,
        facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareData.url)}`
      };

      if (shareUrls[platform as keyof typeof shareUrls]) {
        window.open(shareUrls[platform as keyof typeof shareUrls], '_blank', 'width=600,height=400');
      }
    }

    handleCTAClick('social-share', `share_${platform}`);
  }, [userProgress, handleCTAClick]);

  // Scroll to top functionality
  const scrollToTop = useCallback(() => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    handleCTAClick('scroll-to-top', 'scroll_top');
  }, [handleCTAClick]);

  // Get trigger styling
  const getTriggerStyling = (trigger: ScrollTrigger) => {
    const baseClasses = 'rounded-lg shadow-lg border backdrop-blur-sm transition-all duration-300';
    
    const colorClasses = {
      primary: 'bg-blue-600/90 border-blue-500 text-white',
      secondary: 'bg-purple-600/90 border-purple-500 text-white',
      success: 'bg-green-600/90 border-green-500 text-white',
      warning: 'bg-yellow-600/90 border-yellow-500 text-white',
      info: 'bg-gray-600/90 border-gray-500 text-white'
    };

    const positionClasses = {
      floating: 'fixed bottom-6 right-6 z-50 max-w-sm',
      inline: 'relative w-full',
      sticky: 'sticky top-4 z-40',
      modal: 'fixed inset-0 z-50 flex items-center justify-center p-4'
    };

    return cn(
      baseClasses,
      colorClasses[trigger.styling.color],
      positionClasses[trigger.styling.position]
    );
  };

  // Get animation variants
  const getAnimationVariants = (animation: string) => {
    const variants = {
      'slide-in': {
        initial: { x: 100, opacity: 0 },
        animate: { x: 0, opacity: 1 },
        exit: { x: 100, opacity: 0 }
      },
      'fade-in': {
        initial: { opacity: 0, scale: 0.9 },
        animate: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale: 0.9 }
      },
      'bounce': {
        initial: { y: 50, opacity: 0 },
        animate: { y: 0, opacity: 1 },
        exit: { y: 50, opacity: 0 }
      },
      'pulse': {
        initial: { scale: 0.8, opacity: 0 },
        animate: { scale: 1, opacity: 1 },
        exit: { scale: 0.8, opacity: 0 }
      }
    };

    return variants[animation as keyof typeof variants] || variants['fade-in'];
  };

  return (
    <div className={cn('relative', className)}>
      {/* Floating Progress Indicator */}
      <AnimatePresence>
        {showFloatingProgress && (
          <motion.div
            className="fixed top-6 left-6 z-50 bg-white/10 backdrop-blur-sm rounded-full p-3 border border-white/20"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex items-center space-x-3">
              <div className="relative w-12 h-12">
                <svg className="w-12 h-12 transform -rotate-90" viewBox="0 0 36 36">
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="rgba(255,255,255,0.2)"
                    strokeWidth="2"
                  />
                  <path
                    d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                    fill="none"
                    stroke="#3B82F6"
                    strokeWidth="2"
                    strokeDasharray={`${scrollAnalytics.scrollDepth}, 100`}
                    strokeLinecap="round"
                  />
                </svg>
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-white text-xs font-medium">
                    {Math.round(scrollAnalytics.scrollDepth)}%
                  </span>
                </div>
              </div>
              <div className="text-white text-sm">
                <div className="font-medium">Reading Progress</div>
                <div className="text-white/70 text-xs">
                  {Math.round(scrollAnalytics.timeOnPage / 1000)}s on page
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Scroll-Triggered CTAs */}
      <AnimatePresence>
        {scrollTriggers.map(trigger => {
          if (!activeTriggers.has(trigger.id)) return null;

          const Icon = trigger.content.icon || Target;
          const variants = getAnimationVariants(trigger.styling.animation);

          return (
            <motion.div
              key={trigger.id}
              className={getTriggerStyling(trigger)}
              variants={variants}
              initial="initial"
              animate="animate"
              exit="exit"
              transition={{ duration: 0.5, type: "spring", stiffness: 300, damping: 30 }}
            >
              <div className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="p-2 bg-white/20 rounded-lg">
                      <Icon className="w-6 h-6" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-semibold text-lg mb-2">{trigger.content.title}</h3>
                    {trigger.content.description && (
                      <p className="text-sm opacity-90 mb-3">{trigger.content.description}</p>
                    )}
                    
                    {/* Social Proof or Urgency */}
                    {(trigger.content.socialProof || trigger.content.urgency) && (
                      <div className="mb-4 text-xs opacity-75">
                        {trigger.content.socialProof && (
                          <div className="flex items-center space-x-1">
                            <Users className="w-3 h-3" />
                            <span>{trigger.content.socialProof}</span>
                          </div>
                        )}
                        {trigger.content.urgency && (
                          <div className="flex items-center space-x-1">
                            <Clock className="w-3 h-3" />
                            <span>{trigger.content.urgency}</span>
                          </div>
                        )}
                      </div>
                    )}

                    <div className="flex space-x-3">
                      <EnhancedButton
                        onClick={() => handleCTAClick(trigger.id, trigger.content.ctaAction)}
                        className="bg-white text-gray-900 hover:bg-gray-100 font-medium"
                        size="sm"
                        touchTarget
                      >
                        {trigger.content.ctaText}
                        <ChevronRight className="w-4 h-4 ml-1" />
                      </EnhancedButton>
                      
                      {trigger.type === 'social_share' && (
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleSocialShare('twitter')}
                            className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                            aria-label="Share on Twitter"
                          >
                            <Share2 className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleSocialShare('linkedin')}
                            className="p-2 bg-white/20 hover:bg-white/30 rounded-lg transition-colors"
                            aria-label="Share on LinkedIn"
                          >
                            <ExternalLink className="w-4 h-4" />
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {/* Close button */}
                  <button
                    onClick={() => setActiveTriggers(prev => {
                      const newSet = new Set(prev);
                      newSet.delete(trigger.id);
                      return newSet;
                    })}
                    className="p-1 hover:bg-white/20 rounded transition-colors"
                    aria-label="Close"
                  >
                    ×
                  </button>
                </div>
              </div>
            </motion.div>
          );
        })}
      </AnimatePresence>

      {/* Scroll to Top Button */}
      <AnimatePresence>
        {showScrollToTop && (
          <motion.button
            onClick={scrollToTop}
            className="fixed bottom-6 left-6 z-50 p-3 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            transition={{ duration: 0.3 }}
            aria-label="Scroll to top"
          >
            <ArrowUp className="w-5 h-5" />
          </motion.button>
        )}
      </AnimatePresence>

      {/* Engagement Score Indicator (Debug Mode) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-20 left-6 z-50 bg-black/80 text-white p-3 rounded-lg text-xs">
          <div>Scroll: {scrollAnalytics.scrollDepth}%</div>
          <div>Time: {Math.round(scrollAnalytics.timeOnPage / 1000)}s</div>
          <div>Engagement: {Math.round(scrollAnalytics.engagementScore)}</div>
          <div>Velocity: {Math.round(scrollAnalytics.scrollVelocity)}</div>
          <div>Triggers: {activeTriggers.size}</div>
        </div>
      )}
    </div>
  );
}
