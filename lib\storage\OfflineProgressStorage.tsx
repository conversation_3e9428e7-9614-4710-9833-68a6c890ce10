'use client';

import { useState, useEffect, useCallback } from 'react';

export interface OfflineProgressData {
  userId: string;
  totalXP: number;
  currentLevel: number;
  achievements: string[];
  streakData: {
    currentStreak: number;
    longestStreak: number;
    lastActivity: string; // ISO string
    streakHistory: string[]; // ISO strings
  };
  sessionData: {
    sessionXP: number;
    sessionStart: string; // ISO string
    actionsCount: number;
    completedLessons: string[];
  };
  preferences: {
    soundEnabled: boolean;
    animationsEnabled: boolean;
    notificationsEnabled: boolean;
    theme: string;
  };
  lastSyncTime: string; // ISO string
  version: number;
}

export interface SyncStatus {
  isOnline: boolean;
  lastSyncTime: Date | null;
  pendingChanges: number;
  syncInProgress: boolean;
  lastError: string | null;
}

class OfflineProgressStorage {
  private static instance: OfflineProgressStorage;
  private storageKey = 'solidity_learning_progress';
  private syncKey = 'solidity_learning_sync_queue';
  private currentVersion = 1;
  private syncListeners: ((status: SyncStatus) => void)[] = [];
  private syncStatus: SyncStatus = {
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    lastSyncTime: null,
    pendingChanges: 0,
    syncInProgress: false,
    lastError: null
  };

  private constructor() {
    this.initializeOnlineDetection();
    this.startPeriodicSync();
  }

  static getInstance(): OfflineProgressStorage {
    if (!OfflineProgressStorage.instance) {
      OfflineProgressStorage.instance = new OfflineProgressStorage();
    }
    return OfflineProgressStorage.instance;
  }

  private initializeOnlineDetection() {
    if (typeof window === 'undefined') return;

    window.addEventListener('online', () => {
      this.syncStatus.isOnline = true;
      this.notifySyncListeners();
      this.syncToServer();
    });

    window.addEventListener('offline', () => {
      this.syncStatus.isOnline = false;
      this.notifySyncListeners();
    });
  }

  private startPeriodicSync() {
    // Sync every 30 seconds when online
    setInterval(() => {
      if (this.syncStatus.isOnline && !this.syncStatus.syncInProgress) {
        this.syncToServer();
      }
    }, 30000);
  }

  // Save progress data locally
  saveProgress(data: Partial<OfflineProgressData>): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const existingData = this.loadProgress();
        const updatedData: OfflineProgressData = {
          ...existingData,
          ...data,
          lastSyncTime: new Date().toISOString(),
          version: this.currentVersion
        };

        localStorage.setItem(this.storageKey, JSON.stringify(updatedData));
        
        // Add to sync queue
        this.addToSyncQueue('update_progress', updatedData);
        
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  // Load progress data from local storage
  loadProgress(): OfflineProgressData {
    try {
      const stored = localStorage.getItem(this.storageKey);
      if (!stored) {
        return this.getDefaultProgressData();
      }

      const data = JSON.parse(stored) as OfflineProgressData;
      
      // Handle version migrations
      if (data.version < this.currentVersion) {
        return this.migrateData(data);
      }

      return data;
    } catch (error) {
      console.error('Failed to load progress data:', error);
      return this.getDefaultProgressData();
    }
  }

  private getDefaultProgressData(): OfflineProgressData {
    return {
      userId: 'anonymous',
      totalXP: 0,
      currentLevel: 1,
      achievements: [],
      streakData: {
        currentStreak: 0,
        longestStreak: 0,
        lastActivity: new Date().toISOString(),
        streakHistory: []
      },
      sessionData: {
        sessionXP: 0,
        sessionStart: new Date().toISOString(),
        actionsCount: 0,
        completedLessons: []
      },
      preferences: {
        soundEnabled: true,
        animationsEnabled: true,
        notificationsEnabled: true,
        theme: 'dark'
      },
      lastSyncTime: new Date().toISOString(),
      version: this.currentVersion
    };
  }

  private migrateData(data: any): OfflineProgressData {
    // Handle data migrations between versions
    const migrated = { ...data };
    
    if (!migrated.version || migrated.version < 1) {
      // Add new fields for version 1
      migrated.preferences = migrated.preferences || {
        soundEnabled: true,
        animationsEnabled: true,
        notificationsEnabled: true,
        theme: 'dark'
      };
      migrated.version = 1;
    }

    return migrated;
  }

  // Sync queue management
  private addToSyncQueue(action: string, data: any) {
    try {
      const queue = this.getSyncQueue();
      const syncItem = {
        id: `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        action,
        data,
        timestamp: new Date().toISOString(),
        retryCount: 0
      };

      queue.push(syncItem);
      localStorage.setItem(this.syncKey, JSON.stringify(queue));
      
      this.syncStatus.pendingChanges = queue.length;
      this.notifySyncListeners();
    } catch (error) {
      console.error('Failed to add to sync queue:', error);
    }
  }

  private getSyncQueue(): any[] {
    try {
      const stored = localStorage.getItem(this.syncKey);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to load sync queue:', error);
      return [];
    }
  }

  private clearSyncQueue() {
    localStorage.removeItem(this.syncKey);
    this.syncStatus.pendingChanges = 0;
    this.notifySyncListeners();
  }

  // Server synchronization
  private async syncToServer(): Promise<void> {
    if (this.syncStatus.syncInProgress || !this.syncStatus.isOnline) {
      return;
    }

    this.syncStatus.syncInProgress = true;
    this.syncStatus.lastError = null;
    this.notifySyncListeners();

    try {
      const queue = this.getSyncQueue();
      if (queue.length === 0) {
        this.syncStatus.syncInProgress = false;
        this.notifySyncListeners();
        return;
      }

      // Process sync queue
      for (const item of queue) {
        await this.processSyncItem(item);
      }

      // Clear queue on successful sync
      this.clearSyncQueue();
      this.syncStatus.lastSyncTime = new Date();
      
    } catch (error) {
      console.error('Sync failed:', error);
      this.syncStatus.lastError = error instanceof Error ? error.message : 'Sync failed';
    } finally {
      this.syncStatus.syncInProgress = false;
      this.notifySyncListeners();
    }
  }

  private async processSyncItem(item: any): Promise<void> {
    // Simulate API call - in real implementation, this would call actual API
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (Math.random() > 0.1) { // 90% success rate
          console.log('Synced item:', item);
          resolve();
        } else {
          reject(new Error('Network error'));
        }
      }, 100);
    });
  }

  // XP and Achievement tracking
  addXP(amount: number, source: string): Promise<void> {
    const progress = this.loadProgress();
    const newTotalXP = progress.totalXP + amount;
    const newLevel = Math.floor(newTotalXP / 1000) + 1;
    
    return this.saveProgress({
      totalXP: newTotalXP,
      currentLevel: Math.max(progress.currentLevel, newLevel),
      sessionData: {
        ...progress.sessionData,
        sessionXP: progress.sessionData.sessionXP + amount,
        actionsCount: progress.sessionData.actionsCount + 1
      }
    });
  }

  unlockAchievement(achievementId: string): Promise<void> {
    const progress = this.loadProgress();
    if (progress.achievements.includes(achievementId)) {
      return Promise.resolve(); // Already unlocked
    }

    return this.saveProgress({
      achievements: [...progress.achievements, achievementId]
    });
  }

  updateStreak(newStreak: number): Promise<void> {
    const progress = this.loadProgress();
    const now = new Date().toISOString();
    
    return this.saveProgress({
      streakData: {
        currentStreak: newStreak,
        longestStreak: Math.max(progress.streakData.longestStreak, newStreak),
        lastActivity: now,
        streakHistory: [...progress.streakData.streakHistory, now].slice(-30) // Keep last 30 days
      }
    });
  }

  completeLesson(lessonId: string): Promise<void> {
    const progress = this.loadProgress();
    if (progress.sessionData.completedLessons.includes(lessonId)) {
      return Promise.resolve(); // Already completed this session
    }

    return this.saveProgress({
      sessionData: {
        ...progress.sessionData,
        completedLessons: [...progress.sessionData.completedLessons, lessonId]
      }
    });
  }

  updatePreferences(preferences: Partial<OfflineProgressData['preferences']>): Promise<void> {
    const progress = this.loadProgress();
    return this.saveProgress({
      preferences: {
        ...progress.preferences,
        ...preferences
      }
    });
  }

  // Session management
  startNewSession(): Promise<void> {
    const progress = this.loadProgress();
    return this.saveProgress({
      sessionData: {
        sessionXP: 0,
        sessionStart: new Date().toISOString(),
        actionsCount: 0,
        completedLessons: []
      }
    });
  }

  getSessionStats() {
    const progress = this.loadProgress();
    const sessionStart = new Date(progress.sessionData.sessionStart);
    const sessionDuration = Date.now() - sessionStart.getTime();
    
    return {
      sessionXP: progress.sessionData.sessionXP,
      sessionDuration,
      actionsCount: progress.sessionData.actionsCount,
      completedLessons: progress.sessionData.completedLessons.length,
      xpPerMinute: progress.sessionData.sessionXP / (sessionDuration / 60000) || 0
    };
  }

  // Sync status management
  onSyncStatusChange(callback: (status: SyncStatus) => void) {
    this.syncListeners.push(callback);
    
    // Return unsubscribe function
    return () => {
      this.syncListeners = this.syncListeners.filter(listener => listener !== callback);
    };
  }

  private notifySyncListeners() {
    this.syncListeners.forEach(listener => {
      try {
        listener(this.syncStatus);
      } catch (error) {
        console.error('Sync listener error:', error);
      }
    });
  }

  getSyncStatus(): SyncStatus {
    return { ...this.syncStatus };
  }

  // Force sync
  forcSync(): Promise<void> {
    return this.syncToServer();
  }

  // Data export/import
  exportData(): string {
    const progress = this.loadProgress();
    const queue = this.getSyncQueue();
    
    return JSON.stringify({
      progress,
      syncQueue: queue,
      exportTime: new Date().toISOString()
    }, null, 2);
  }

  importData(jsonData: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const data = JSON.parse(jsonData);
        
        if (data.progress) {
          localStorage.setItem(this.storageKey, JSON.stringify(data.progress));
        }
        
        if (data.syncQueue) {
          localStorage.setItem(this.syncKey, JSON.stringify(data.syncQueue));
          this.syncStatus.pendingChanges = data.syncQueue.length;
        }
        
        this.notifySyncListeners();
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  }

  // Clear all data
  clearAllData(): Promise<void> {
    return new Promise((resolve) => {
      localStorage.removeItem(this.storageKey);
      localStorage.removeItem(this.syncKey);
      this.syncStatus.pendingChanges = 0;
      this.notifySyncListeners();
      resolve();
    });
  }
}

export default OfflineProgressStorage;

// React hook for using offline storage
export function useOfflineProgress() {
  const [storage] = useState(() => OfflineProgressStorage.getInstance());
  const [progress, setProgress] = useState<OfflineProgressData | null>(null);
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);

  useEffect(() => {
    // Load initial progress
    setProgress(storage.loadProgress());
    setSyncStatus(storage.getSyncStatus());

    // Subscribe to sync status changes
    const unsubscribe = storage.onSyncStatusChange(setSyncStatus);
    
    return unsubscribe;
  }, [storage]);

  const updateProgress = useCallback(async (data: Partial<OfflineProgressData>) => {
    await storage.saveProgress(data);
    setProgress(storage.loadProgress());
  }, [storage]);

  const addXP = useCallback(async (amount: number, source: string) => {
    await storage.addXP(amount, source);
    setProgress(storage.loadProgress());
  }, [storage]);

  const unlockAchievement = useCallback(async (achievementId: string) => {
    await storage.unlockAchievement(achievementId);
    setProgress(storage.loadProgress());
  }, [storage]);

  return {
    progress,
    syncStatus,
    updateProgress,
    addXP,
    unlockAchievement,
    updateStreak: storage.updateStreak.bind(storage),
    completeLesson: storage.completeLesson.bind(storage),
    updatePreferences: storage.updatePreferences.bind(storage),
    getSessionStats: storage.getSessionStats.bind(storage),
    forceSync: storage.forcSync.bind(storage),
    exportData: storage.exportData.bind(storage),
    importData: storage.importData.bind(storage),
    clearAllData: storage.clearAllData.bind(storage)
  };
}
