'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Star, 
  Users, 
  TrendingUp, 
  Award, 
  CheckCircle, 
  Quote,
  ChevronLeft,
  ChevronRight,
  MapPin,
  Briefcase,
  ExternalLink,
  Shield,
  Zap,
  Target
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Review {
  id: string;
  name: string;
  role: string;
  company: string;
  avatar: string;
  rating: number;
  review: string;
  date: Date;
  verified: boolean;
  location?: string;
  achievements?: string[];
}

interface SuccessStory {
  id: string;
  name: string;
  role: string;
  company: string;
  avatar: string;
  story: string;
  beforeRole: string;
  afterRole: string;
  timeToSuccess: string;
  skills: string[];
  image?: string;
}

interface PlatformStats {
  totalUsers: number;
  contractsDeployed: number;
  developersHired: number;
  averageRating: number;
  totalReviews: number;
  successRate: number;
  companiesUsing: number;
}

// Mock data
const mockReviews: Review[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Blockchain Developer',
    company: 'ConsenSys',
    avatar: '/avatars/sarah.jpg',
    rating: 5,
    review: 'This platform transformed my career! The interactive coding environment and real-time feedback made learning Solidity incredibly engaging. I went from zero blockchain knowledge to landing my dream job in just 3 months.',
    date: new Date('2024-01-15'),
    verified: true,
    location: 'San Francisco, CA',
    achievements: ['Smart Contract Expert', 'DeFi Specialist']
  },
  {
    id: '2',
    name: 'Marcus Rodriguez',
    role: 'Senior Smart Contract Engineer',
    company: 'Chainlink',
    avatar: '/avatars/marcus.jpg',
    rating: 5,
    review: 'The gamification system kept me motivated throughout my learning journey. The achievement system and progress tracking made complex concepts feel achievable. Highly recommend for anyone serious about blockchain development.',
    date: new Date('2024-01-10'),
    verified: true,
    location: 'Austin, TX',
    achievements: ['Streak Master', 'Code Ninja']
  },
  {
    id: '3',
    name: 'Priya Patel',
    role: 'DeFi Protocol Developer',
    company: 'Uniswap',
    avatar: '/avatars/priya.jpg',
    rating: 5,
    review: 'The hands-on approach with real testnet deployments gave me confidence to work on production systems. The community support and guided tours made the learning curve much smoother.',
    date: new Date('2024-01-05'),
    verified: true,
    location: 'London, UK',
    achievements: ['DeFi Pioneer', 'Community Helper']
  }
];

const mockSuccessStories: SuccessStory[] = [
  {
    id: '1',
    name: 'Alex Thompson',
    role: 'Lead Blockchain Architect',
    company: 'Polygon',
    avatar: '/avatars/alex.jpg',
    story: 'Started as a traditional web developer with no blockchain experience. The platform\'s structured learning path and practical projects helped me transition into blockchain development. Now I\'m leading a team of 15 developers building the next generation of DeFi protocols.',
    beforeRole: 'Frontend Developer',
    afterRole: 'Lead Blockchain Architect',
    timeToSuccess: '6 months',
    skills: ['Solidity', 'DeFi', 'Layer 2', 'Smart Contract Security'],
    image: '/success-stories/alex-story.jpg'
  },
  {
    id: '2',
    name: 'Emily Zhang',
    role: 'Smart Contract Auditor',
    company: 'OpenZeppelin',
    avatar: '/avatars/emily.jpg',
    story: 'The security-focused modules and real-world vulnerability examples prepared me for a career in smart contract auditing. The platform\'s emphasis on best practices and security patterns was invaluable.',
    beforeRole: 'Cybersecurity Analyst',
    afterRole: 'Smart Contract Auditor',
    timeToSuccess: '4 months',
    skills: ['Security Auditing', 'Solidity', 'Formal Verification', 'DeFi'],
    image: '/success-stories/emily-story.jpg'
  }
];

const mockStats: PlatformStats = {
  totalUsers: 12547,
  contractsDeployed: 45623,
  developersHired: 1834,
  averageRating: 4.9,
  totalReviews: 2156,
  successRate: 94,
  companiesUsing: 150
};

// Star rating component
function StarRating({ rating, size = 'sm' }: { rating: number; size?: 'sm' | 'md' | 'lg' }) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  return (
    <div className="flex items-center space-x-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          className={cn(
            sizeClasses[size],
            star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-400'
          )}
        />
      ))}
    </div>
  );
}

// Review card component
function ReviewCard({ review }: { review: Review }) {
  return (
    <motion.div
      className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 h-full"
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      {/* Header */}
      <div className="flex items-start space-x-4 mb-4">
        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
          <span className="text-white font-semibold text-lg">
            {review.name.split(' ').map(n => n[0]).join('')}
          </span>
        </div>
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className="font-semibold text-white">{review.name}</h4>
            {review.verified && (
              <CheckCircle className="w-4 h-4 text-green-400" title="Verified user" />
            )}
          </div>
          <p className="text-gray-400 text-sm">{review.role} at {review.company}</p>
          {review.location && (
            <div className="flex items-center space-x-1 mt-1">
              <MapPin className="w-3 h-3 text-gray-500" />
              <span className="text-gray-500 text-xs">{review.location}</span>
            </div>
          )}
        </div>
      </div>

      {/* Rating */}
      <div className="flex items-center space-x-2 mb-4">
        <StarRating rating={review.rating} />
        <span className="text-gray-400 text-sm">
          {new Date(review.date).toLocaleDateString()}
        </span>
      </div>

      {/* Review text */}
      <div className="relative mb-4">
        <Quote className="w-6 h-6 text-blue-400/30 absolute -top-2 -left-2" />
        <p className="text-gray-300 leading-relaxed pl-4">{review.review}</p>
      </div>

      {/* Achievements */}
      {review.achievements && review.achievements.length > 0 && (
        <div className="flex flex-wrap gap-2">
          {review.achievements.map((achievement) => (
            <span
              key={achievement}
              className="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full border border-blue-500/30"
            >
              {achievement}
            </span>
          ))}
        </div>
      )}
    </motion.div>
  );
}

// Testimonial carousel
export function TestimonialCarousel({ className }: { className?: string }) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-advance carousel
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % mockReviews.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const nextReview = () => {
    setCurrentIndex((prev) => (prev + 1) % mockReviews.length);
    setIsAutoPlaying(false);
  };

  const prevReview = () => {
    setCurrentIndex((prev) => (prev - 1 + mockReviews.length) % mockReviews.length);
    setIsAutoPlaying(false);
  };

  return (
    <div className={cn('relative', className)}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-2xl font-bold text-white">What Developers Say</h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={prevReview}
            className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
          >
            <ChevronLeft className="w-5 h-5 text-white" />
          </button>
          <button
            onClick={nextReview}
            className="p-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors"
          >
            <ChevronRight className="w-5 h-5 text-white" />
          </button>
        </div>
      </div>

      <div className="relative overflow-hidden">
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -100 }}
            transition={{ duration: 0.3 }}
          >
            <ReviewCard review={mockReviews[currentIndex]} />
          </motion.div>
        </AnimatePresence>
      </div>

      {/* Indicators */}
      <div className="flex justify-center space-x-2 mt-6">
        {mockReviews.map((_, index) => (
          <button
            key={index}
            onClick={() => {
              setCurrentIndex(index);
              setIsAutoPlaying(false);
            }}
            className={cn(
              'w-2 h-2 rounded-full transition-colors',
              index === currentIndex ? 'bg-blue-400' : 'bg-gray-600'
            )}
          />
        ))}
      </div>
    </div>
  );
}

// Platform statistics display
export function PlatformStatistics({ className, variant = 'full' }: { 
  className?: string; 
  variant?: 'full' | 'compact' | 'inline';
}) {
  const stats = [
    {
      icon: Users,
      label: 'Active Developers',
      value: mockStats.totalUsers.toLocaleString(),
      color: 'text-blue-400'
    },
    {
      icon: Zap,
      label: 'Contracts Deployed',
      value: mockStats.contractsDeployed.toLocaleString(),
      color: 'text-green-400'
    },
    {
      icon: Briefcase,
      label: 'Developers Hired',
      value: mockStats.developersHired.toLocaleString(),
      color: 'text-purple-400'
    },
    {
      icon: Star,
      label: 'Average Rating',
      value: `${mockStats.averageRating}/5`,
      color: 'text-yellow-400'
    }
  ];

  if (variant === 'inline') {
    return (
      <div className={cn('flex items-center space-x-6 text-sm', className)}>
        <div className="flex items-center space-x-1">
          <Star className="w-4 h-4 text-yellow-400 fill-current" />
          <span className="text-white font-medium">{mockStats.averageRating}</span>
          <span className="text-gray-400">({mockStats.totalReviews.toLocaleString()} reviews)</span>
        </div>
        <div className="flex items-center space-x-1">
          <Users className="w-4 h-4 text-blue-400" />
          <span className="text-white">{mockStats.totalUsers.toLocaleString()}+ developers</span>
        </div>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn('grid grid-cols-2 gap-4', className)}>
        {stats.slice(0, 4).map((stat) => (
          <div key={stat.label} className="text-center">
            <div className={cn('text-2xl font-bold', stat.color)}>{stat.value}</div>
            <div className="text-gray-400 text-sm">{stat.label}</div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={cn('grid grid-cols-2 md:grid-cols-4 gap-6', className)}>
      {stats.map((stat) => (
        <motion.div
          key={stat.label}
          className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 text-center"
          whileHover={{ scale: 1.05 }}
          transition={{ duration: 0.2 }}
        >
          <stat.icon className={cn('w-8 h-8 mx-auto mb-3', stat.color)} />
          <div className={cn('text-3xl font-bold mb-2', stat.color)}>{stat.value}</div>
          <div className="text-gray-400 text-sm">{stat.label}</div>
        </motion.div>
      ))}
    </div>
  );
}

// Trust indicators
export function TrustIndicators({ className }: { className?: string }) {
  const indicators = [
    {
      icon: Shield,
      label: 'Enterprise Security',
      description: 'SOC 2 Type II Certified'
    },
    {
      icon: Award,
      label: '94% Success Rate',
      description: 'Students land blockchain jobs'
    },
    {
      icon: Users,
      label: '150+ Companies',
      description: 'Trust our graduates'
    },
    {
      icon: CheckCircle,
      label: 'Verified Reviews',
      description: 'All testimonials verified'
    }
  ];

  return (
    <div className={cn('grid grid-cols-2 md:grid-cols-4 gap-4', className)}>
      {indicators.map((indicator) => (
        <div
          key={indicator.label}
          className="flex items-center space-x-3 p-4 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10"
        >
          <indicator.icon className="w-6 h-6 text-green-400 flex-shrink-0" />
          <div>
            <div className="font-medium text-white text-sm">{indicator.label}</div>
            <div className="text-gray-400 text-xs">{indicator.description}</div>
          </div>
        </div>
      ))}
    </div>
  );
}

// Success stories section
export function SuccessStories({ className }: { className?: string }) {
  const [selectedStory, setSelectedStory] = useState<SuccessStory | null>(null);

  return (
    <div className={className}>
      <h3 className="text-2xl font-bold text-white mb-6">Developer Success Stories</h3>
      
      <div className="grid md:grid-cols-2 gap-6">
        {mockSuccessStories.map((story) => (
          <motion.div
            key={story.id}
            className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 cursor-pointer"
            whileHover={{ scale: 1.02 }}
            onClick={() => setSelectedStory(story)}
          >
            <div className="flex items-start space-x-4 mb-4">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold text-xl">
                  {story.name.split(' ').map(n => n[0]).join('')}
                </span>
              </div>
              <div>
                <h4 className="font-semibold text-white">{story.name}</h4>
                <p className="text-green-400 text-sm">{story.role} at {story.company}</p>
                <div className="text-gray-400 text-xs mt-1">
                  {story.beforeRole} → {story.afterRole} in {story.timeToSuccess}
                </div>
              </div>
            </div>
            
            <p className="text-gray-300 text-sm leading-relaxed mb-4 line-clamp-3">
              {story.story}
            </p>
            
            <div className="flex flex-wrap gap-2">
              {story.skills.slice(0, 3).map((skill) => (
                <span
                  key={skill}
                  className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full"
                >
                  {skill}
                </span>
              ))}
              {story.skills.length > 3 && (
                <span className="px-2 py-1 bg-gray-500/20 text-gray-400 text-xs rounded-full">
                  +{story.skills.length - 3} more
                </span>
              )}
            </div>
          </motion.div>
        ))}
      </div>

      {/* Success story modal */}
      <AnimatePresence>
        {selectedStory && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setSelectedStory(null)}
          >
            <motion.div
              className="bg-gray-900 rounded-lg shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-y-auto border border-gray-700"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <div className="flex items-start space-x-4 mb-6">
                  <div className="w-20 h-20 bg-gradient-to-br from-green-500 to-blue-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold text-2xl">
                      {selectedStory.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-white">{selectedStory.name}</h3>
                    <p className="text-green-400">{selectedStory.role} at {selectedStory.company}</p>
                    <div className="text-gray-400 text-sm mt-1">
                      Career transition: {selectedStory.beforeRole} → {selectedStory.afterRole}
                    </div>
                    <div className="text-blue-400 text-sm">
                      Time to success: {selectedStory.timeToSuccess}
                    </div>
                  </div>
                </div>

                <p className="text-gray-300 leading-relaxed mb-6">
                  {selectedStory.story}
                </p>

                <div className="mb-6">
                  <h4 className="font-semibold text-white mb-3">Skills Acquired</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedStory.skills.map((skill) => (
                      <span
                        key={skill}
                        className="px-3 py-1 bg-green-500/20 text-green-300 text-sm rounded-full border border-green-500/30"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>

                <div className="flex justify-end">
                  <button
                    onClick={() => setSelectedStory(null)}
                    className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Main social proof component
export function SocialProofSystem({ 
  className,
  showReviews = true,
  showStats = true,
  showTrust = true,
  showStories = true,
  variant = 'full'
}: {
  className?: string;
  showReviews?: boolean;
  showStats?: boolean;
  showTrust?: boolean;
  showStories?: boolean;
  variant?: 'full' | 'compact' | 'hero';
}) {
  if (variant === 'hero') {
    return (
      <div className={cn('space-y-8', className)}>
        <PlatformStatistics variant="inline" />
        {showTrust && <TrustIndicators />}
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn('space-y-6', className)}>
        {showStats && <PlatformStatistics variant="compact" />}
        {showReviews && <TestimonialCarousel />}
      </div>
    );
  }

  return (
    <div className={cn('space-y-12', className)}>
      {showStats && <PlatformStatistics />}
      {showTrust && <TrustIndicators />}
      {showReviews && <TestimonialCarousel />}
      {showStories && <SuccessStories />}
    </div>
  );
}
