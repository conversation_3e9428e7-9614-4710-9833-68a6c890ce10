// Gamification-specific test setup
import '@testing-library/jest-dom';

// Mock framer-motion for consistent testing
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => React.createElement('div', props, children),
    span: ({ children, ...props }) => React.createElement('span', props, children),
    button: ({ children, ...props }) => React.createElement('button', props, children),
  },
  AnimatePresence: ({ children }) => children,
}));

// Mock performance API
global.performance = {
  now: jest.fn(() => Date.now()),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByType: jest.fn(() => []),
  getEntriesByName: jest.fn(() => []),
  clearMarks: jest.fn(),
  clearMeasures: jest.fn(),
  memory: {
    usedJSHeapSize: 50 * 1024 * 1024, // 50MB
    totalJSHeapSize: 100 * 1024 * 1024, // 100MB
    jsHeapSizeLimit: 2 * 1024 * 1024 * 1024 // 2GB
  }
};

// Mock PerformanceObserver
global.PerformanceObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  takeRecords: jest.fn(() => [])
}));

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn((callback) => {
  return setTimeout(callback, 16); // ~60fps
});

global.cancelAnimationFrame = jest.fn((id) => {
  clearTimeout(id);
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn()
};
global.localStorage = localStorageMock;

// Mock sessionStorage
global.sessionStorage = localStorageMock;

// Mock navigator
global.navigator = {
  ...global.navigator,
  onLine: true,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
};

// Mock window.matchMedia
global.matchMedia = jest.fn().mockImplementation(query => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn(),
  removeListener: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock Web Audio API
global.AudioContext = jest.fn().mockImplementation(() => ({
  createOscillator: jest.fn(() => ({
    connect: jest.fn(),
    start: jest.fn(),
    stop: jest.fn(),
    frequency: { setValueAtTime: jest.fn(), linearRampToValueAtTime: jest.fn() },
    type: 'sine'
  })),
  createGain: jest.fn(() => ({
    connect: jest.fn(),
    gain: { 
      setValueAtTime: jest.fn(), 
      linearRampToValueAtTime: jest.fn(),
      exponentialRampToValueAtTime: jest.fn()
    }
  })),
  destination: {},
  currentTime: 0
}));

// Mock console methods for cleaner test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  // Reset all mocks before each test
  jest.clearAllMocks();
  
  // Reset localStorage
  localStorageMock.getItem.mockReturnValue(null);
  localStorageMock.setItem.mockClear();
  localStorageMock.removeItem.mockClear();
  localStorageMock.clear.mockClear();
  
  // Reset performance mocks
  global.performance.now.mockReturnValue(Date.now());
  
  // Reset navigator online status
  Object.defineProperty(global.navigator, 'onLine', {
    writable: true,
    value: true
  });
  
  // Suppress console errors/warnings for expected test scenarios
  console.error = jest.fn();
  console.warn = jest.fn();
});

afterEach(() => {
  // Restore console methods
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
  
  // Clean up any global state
  delete window.addXPNotification;
  delete window.checkMicroAchievement;
  delete window.addAchievementNotification;
});

// Custom matchers for gamification testing
expect.extend({
  toHaveXPAmount(received, expected) {
    const xpElement = received.querySelector('[data-testid="xp-amount"]') || 
                     received.querySelector('*:contains("+' + expected + '")');
    
    const pass = xpElement !== null;
    
    if (pass) {
      return {
        message: () => `expected element not to have XP amount ${expected}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected element to have XP amount ${expected}`,
        pass: false,
      };
    }
  },
  
  toHaveAchievementTitle(received, expected) {
    const titleElement = received.querySelector('[data-testid="achievement-title"]') ||
                        received.querySelector('*:contains("' + expected + '")');
    
    const pass = titleElement !== null;
    
    if (pass) {
      return {
        message: () => `expected element not to have achievement title ${expected}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected element to have achievement title ${expected}`,
        pass: false,
      };
    }
  },
  
  toHaveStreakCount(received, expected) {
    const streakElement = received.querySelector('[data-testid="streak-count"]') ||
                         received.querySelector('*:contains("' + expected + ' days")');
    
    const pass = streakElement !== null;
    
    if (pass) {
      return {
        message: () => `expected element not to have streak count ${expected}`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected element to have streak count ${expected}`,
        pass: false,
      };
    }
  }
});

// Performance testing utilities
global.testUtils = {
  measureRenderTime: (renderFunction) => {
    const start = performance.now();
    const result = renderFunction();
    const end = performance.now();
    return {
      result,
      renderTime: end - start
    };
  },
  
  simulateSlowNetwork: () => {
    // Mock slow network conditions
    global.fetch = jest.fn(() => 
      new Promise(resolve => 
        setTimeout(() => resolve({
          ok: true,
          json: () => Promise.resolve({})
        }), 2000)
      )
    );
  },
  
  simulateOffline: () => {
    Object.defineProperty(global.navigator, 'onLine', {
      writable: true,
      value: false
    });
    
    // Dispatch offline event
    const offlineEvent = new Event('offline');
    window.dispatchEvent(offlineEvent);
  },
  
  simulateOnline: () => {
    Object.defineProperty(global.navigator, 'onLine', {
      writable: true,
      value: true
    });
    
    // Dispatch online event
    const onlineEvent = new Event('online');
    window.dispatchEvent(onlineEvent);
  }
};

// Accessibility testing utilities
global.a11yUtils = {
  checkColorContrast: (element) => {
    const style = window.getComputedStyle(element);
    const color = style.color;
    const backgroundColor = style.backgroundColor;
    
    // Basic contrast check (simplified)
    return color !== backgroundColor;
  },
  
  checkFocusable: (element) => {
    const tabIndex = element.getAttribute('tabindex');
    const isInteractive = ['button', 'input', 'select', 'textarea', 'a'].includes(
      element.tagName.toLowerCase()
    );
    
    return isInteractive || (tabIndex !== null && tabIndex !== '-1');
  },
  
  checkAriaLabels: (element) => {
    return element.getAttribute('aria-label') || 
           element.getAttribute('aria-labelledby') ||
           element.getAttribute('aria-describedby');
  }
};

// Mock data generators
global.mockData = {
  generateXPGain: (overrides = {}) => ({
    id: 'test-xp-' + Math.random().toString(36).substr(2, 9),
    amount: 100,
    source: 'lesson',
    description: 'Test XP gain',
    timestamp: new Date(),
    ...overrides
  }),
  
  generateAchievement: (overrides = {}) => ({
    id: 'test-achievement-' + Math.random().toString(36).substr(2, 9),
    title: 'Test Achievement',
    description: 'A test achievement',
    rarity: 'common',
    category: 'learning',
    requirements: {
      type: 'lesson_completion',
      target: 1,
      current: 1
    },
    rewards: {
      xp: 100
    },
    unlockedAt: new Date(),
    progress: 100,
    ...overrides
  }),
  
  generateStreakData: (overrides = {}) => ({
    currentStreak: 5,
    longestStreak: 10,
    lastActivity: new Date(),
    streakGoal: 30,
    todayCompleted: false,
    streakHistory: [new Date()],
    milestones: [],
    ...overrides
  })
};
