'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Zap, 
  TrendingUp,
  Monitor,
  Wifi,
  HardDrive,
  Cpu
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte
  
  // Custom metrics
  pageLoadTime: number;
  domContentLoaded: number;
  resourceLoadTime: number;
  jsExecutionTime: number;
  
  // User experience metrics
  interactionDelay: number;
  scrollResponsiveness: number;
  animationFrameRate: number;
  
  // Network metrics
  connectionType: string;
  effectiveType: string;
  downlink: number;
  rtt: number;
  
  timestamp: Date;
}

interface PerformanceAlert {
  id: string;
  type: 'warning' | 'error' | 'critical';
  metric: string;
  value: number;
  threshold: number;
  message: string;
  timestamp: Date;
  resolved: boolean;
}

interface BundleAnalysis {
  totalSize: number;
  gzippedSize: number;
  chunks: Array<{
    name: string;
    size: number;
    modules: number;
    isAsync: boolean;
  }>;
  duplicateModules: string[];
  unusedCode: number;
  recommendations: Array<{
    type: 'split' | 'lazy' | 'remove' | 'optimize';
    description: string;
    impact: number;
  }>;
}

// Performance monitoring hook
export function usePerformanceMonitoring() {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  // Performance thresholds
  const thresholds = {
    lcp: 2500, // Good: < 2.5s
    fid: 100,  // Good: < 100ms
    cls: 0.1,  // Good: < 0.1
    fcp: 1800, // Good: < 1.8s
    ttfb: 600, // Good: < 600ms
    pageLoadTime: 3000,
    interactionDelay: 50
  };

  const collectPerformanceMetrics = useCallback(() => {
    if (typeof window === 'undefined') return;

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    
    // Get network information
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    
    const newMetrics: PerformanceMetrics = {
      // Core Web Vitals (will be updated by observer)
      lcp: 0,
      fid: 0,
      cls: 0,
      fcp: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
      ttfb: navigation?.responseStart - navigation?.requestStart || 0,
      
      // Page timing metrics
      pageLoadTime: navigation?.loadEventEnd - navigation?.navigationStart || 0,
      domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.navigationStart || 0,
      resourceLoadTime: navigation?.loadEventEnd - navigation?.responseEnd || 0,
      jsExecutionTime: performance.now(), // Simplified
      
      // User experience metrics
      interactionDelay: 0, // Will be measured on interactions
      scrollResponsiveness: 60, // Assume 60fps initially
      animationFrameRate: 60,
      
      // Network metrics
      connectionType: connection?.type || 'unknown',
      effectiveType: connection?.effectiveType || 'unknown',
      downlink: connection?.downlink || 0,
      rtt: connection?.rtt || 0,
      
      timestamp: new Date()
    };

    setMetrics(newMetrics);
    checkPerformanceThresholds(newMetrics);
  }, []);

  const processPerformanceEntry = useCallback((entry: PerformanceEntry) => {
    setMetrics(prev => {
      if (!prev) return prev;
      
      const updated = { ...prev };
      
      switch (entry.entryType) {
        case 'largest-contentful-paint':
          updated.lcp = entry.startTime;
          break;
        case 'first-input':
          updated.fid = (entry as any).processingStart - entry.startTime;
          break;
        case 'layout-shift':
          if (!(entry as any).hadRecentInput) {
            updated.cls += (entry as any).value;
          }
          break;
      }
      
      return updated;
    });
  }, []);

  // Initialize performance monitoring
  useEffect(() => {
    if (typeof window === 'undefined') return;

    setIsMonitoring(true);

    // Collect initial metrics
    collectPerformanceMetrics();

    // Set up periodic monitoring
    const interval = setInterval(collectPerformanceMetrics, 30000); // Every 30 seconds

    // Monitor for performance issues
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        processPerformanceEntry(entry);
      }
    });

    observer.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });

    return () => {
      clearInterval(interval);
      observer.disconnect();
      setIsMonitoring(false);
    };
  }, [collectPerformanceMetrics, processPerformanceEntry]);

  const checkPerformanceThresholds = useCallback((metrics: PerformanceMetrics) => {
    const newAlerts: PerformanceAlert[] = [];
    
    Object.entries(thresholds).forEach(([metric, threshold]) => {
      const value = metrics[metric as keyof PerformanceMetrics] as number;
      
      if (value > threshold) {
        const severity = value > threshold * 2 ? 'critical' : value > threshold * 1.5 ? 'error' : 'warning';
        
        newAlerts.push({
          id: `${metric}-${Date.now()}`,
          type: severity,
          metric,
          value,
          threshold,
          message: `${metric.toUpperCase()} (${value.toFixed(2)}) exceeds threshold (${threshold})`,
          timestamp: new Date(),
          resolved: false
        });
      }
    });
    
    if (newAlerts.length > 0) {
      setAlerts(prev => [...prev, ...newAlerts]);
    }
  }, [thresholds]);

  const resolveAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, resolved: true } : alert
    ));
  }, []);

  const clearAlerts = useCallback(() => {
    setAlerts([]);
  }, []);

  return {
    metrics,
    alerts,
    isMonitoring,
    resolveAlert,
    clearAlerts,
    thresholds
  };
}

// Sentry error tracking hook
export function useSentryIntegration() {
  const [errorCount, setErrorCount] = useState(0);
  const [lastError, setLastError] = useState<Error | null>(null);

  useEffect(() => {
    // Initialize Sentry (would be done in app initialization)
    // This is a simplified version for demonstration
    
    const handleError = (event: ErrorEvent) => {
      setErrorCount(prev => prev + 1);
      setLastError(new Error(event.message));
      
      // Track with analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'exception', {
          description: event.message,
          fatal: false,
          filename: event.filename,
          lineno: event.lineno
        });
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      setErrorCount(prev => prev + 1);
      setLastError(new Error(String(event.reason)));
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  const captureException = useCallback((error: Error, context?: any) => {
    setErrorCount(prev => prev + 1);
    setLastError(error);
    
    // In a real implementation, this would send to Sentry
    console.error('Captured exception:', error, context);
  }, []);

  return {
    errorCount,
    lastError,
    captureException
  };
}

// Core Web Vitals component
export function CoreWebVitals({ className }: { className?: string }) {
  const { metrics, alerts } = usePerformanceMonitoring();

  if (!metrics) {
    return (
      <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6', className)}>
        <div className="flex items-center space-x-2 mb-4">
          <Activity className="w-5 h-5 text-blue-400" />
          <h3 className="text-lg font-semibold text-white">Core Web Vitals</h3>
        </div>
        <div className="text-gray-400">Collecting performance data...</div>
      </div>
    );
  }

  // This should be the main return for CoreWebVitals when metrics exist
  return (
    <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6', className)}>
      <div className="flex items-center space-x-2 mb-4">
        <Activity className="w-5 h-5 text-blue-400" />
        <h3 className="text-lg font-semibold text-white">Core Web Vitals</h3>
      </div>
      <div className="text-gray-400">Performance metrics loaded successfully.</div>
    </div>
  );
}

// Bundle analysis hook
export function useBundleAnalysis() {
  const [bundleData, setBundleData] = useState<BundleAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const analyzeBundles = useCallback(async () => {
    setIsAnalyzing(true);

    try {
      // Simulate bundle analysis (in real implementation, this would call webpack-bundle-analyzer API)
      await new Promise(resolve => setTimeout(resolve, 2000));

      const mockBundleData: BundleAnalysis = {
        totalSize: 2.4 * 1024 * 1024, // 2.4MB
        gzippedSize: 0.8 * 1024 * 1024, // 800KB
        chunks: [
          {
            name: 'main',
            size: 1.2 * 1024 * 1024,
            modules: 45,
            isAsync: false
          },
          {
            name: 'vendor',
            size: 0.8 * 1024 * 1024,
            modules: 23,
            isAsync: false
          },
          {
            name: 'editor',
            size: 0.4 * 1024 * 1024,
            modules: 12,
            isAsync: true
          }
        ],
        duplicateModules: ['lodash', 'moment', 'react-dom'],
        unusedCode: 0.15 * 1024 * 1024, // 150KB
        recommendations: [
          {
            type: 'lazy',
            description: 'Lazy load the code editor component',
            impact: 15
          },
          {
            type: 'remove',
            description: 'Remove unused lodash functions',
            impact: 8
          },
          {
            type: 'split',
            description: 'Split vendor chunk into smaller chunks',
            impact: 12
          }
        ]
      };

      setBundleData(mockBundleData);
    } catch (error) {
      console.error('Bundle analysis failed:', error);
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  useEffect(() => {
    // Auto-analyze on component mount
    analyzeBundles();
  }, [analyzeBundles]);

  return {
    bundleData,
    isAnalyzing,
    analyzeBundles
  };
}

// Lighthouse CI integration hook
export function useLighthouseCI() {
  const [lighthouseScores, setLighthouseScores] = useState<{
    performance: number;
    accessibility: number;
    bestPractices: number;
    seo: number;
    pwa: number;
    lastRun: Date;
  } | null>(null);

  const [isRunning, setIsRunning] = useState(false);

  const runLighthouseAudit = useCallback(async () => {
    setIsRunning(true);

    try {
      // Simulate Lighthouse audit (in real implementation, this would trigger Lighthouse CI)
      await new Promise(resolve => setTimeout(resolve, 10000));

      const mockScores = {
        performance: 85 + Math.random() * 10,
        accessibility: 92 + Math.random() * 5,
        bestPractices: 88 + Math.random() * 8,
        seo: 95 + Math.random() * 5,
        pwa: 78 + Math.random() * 15,
        lastRun: new Date()
      };

      setLighthouseScores(mockScores);

      // Track with analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'lighthouse_audit', {
          performance_score: mockScores.performance,
          accessibility_score: mockScores.accessibility,
          best_practices_score: mockScores.bestPractices,
          seo_score: mockScores.seo,
          pwa_score: mockScores.pwa
        });
      }
    } catch (error) {
      console.error('Lighthouse audit failed:', error);
    } finally {
      setIsRunning(false);
    }
  }, []);

  return {
    lighthouseScores,
    isRunning,
    runLighthouseAudit
  };
}

// Bundle Analysis Component
export function BundleAnalysisView({ className }: { className?: string }) {
  const { bundleData, isAnalyzing, analyzeBundles } = useBundleAnalysis();

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6', className)}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
          <HardDrive className="w-5 h-5" />
          <span>Bundle Analysis</span>
        </h3>
        <button
          onClick={analyzeBundles}
          disabled={isAnalyzing}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg text-sm transition-colors"
        >
          {isAnalyzing ? 'Analyzing...' : 'Re-analyze'}
        </button>
      </div>

      {isAnalyzing ? (
        <div className="text-center py-8">
          <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <div className="text-gray-400">Analyzing bundle size and composition...</div>
        </div>
      ) : bundleData ? (
        <div className="space-y-6">
          {/* Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white/5 rounded-lg p-4">
              <div className="text-2xl font-bold text-white">{formatFileSize(bundleData.totalSize)}</div>
              <div className="text-gray-400 text-sm">Total Bundle Size</div>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-400">{formatFileSize(bundleData.gzippedSize)}</div>
              <div className="text-gray-400 text-sm">Gzipped Size</div>
            </div>
            <div className="bg-white/5 rounded-lg p-4">
              <div className="text-2xl font-bold text-yellow-400">{formatFileSize(bundleData.unusedCode)}</div>
              <div className="text-gray-400 text-sm">Unused Code</div>
            </div>
          </div>

          {/* Chunks */}
          <div>
            <h4 className="text-white font-medium mb-3">Bundle Chunks</h4>
            <div className="space-y-2">
              {bundleData.chunks.map(chunk => (
                <div key={chunk.name} className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-white font-medium">{chunk.name}</span>
                      {chunk.isAsync && (
                        <span className="ml-2 px-2 py-1 bg-blue-500/20 text-blue-400 text-xs rounded">
                          Async
                        </span>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="text-white">{formatFileSize(chunk.size)}</div>
                      <div className="text-gray-400 text-xs">{chunk.modules} modules</div>
                    </div>
                  </div>
                  <div className="mt-2 w-full bg-gray-700 rounded-full h-1">
                    <div
                      className="bg-blue-500 h-1 rounded-full"
                      style={{ width: `${(chunk.size / bundleData.totalSize) * 100}%` }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recommendations */}
          <div>
            <h4 className="text-white font-medium mb-3">Optimization Recommendations</h4>
            <div className="space-y-2">
              {bundleData.recommendations.map((rec, index) => (
                <div key={index} className="bg-white/5 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-white">{rec.description}</span>
                      <div className="text-gray-400 text-xs mt-1">
                        Type: {rec.type}
                      </div>
                    </div>
                    <div className="text-green-400 font-medium">
                      -{rec.impact}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Duplicate Modules */}
          {bundleData.duplicateModules.length > 0 && (
            <div>
              <h4 className="text-white font-medium mb-3">Duplicate Modules</h4>
              <div className="bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-3">
                <div className="text-yellow-400 text-sm">
                  Found {bundleData.duplicateModules.length} duplicate modules:
                </div>
                <div className="text-yellow-300 text-xs mt-1">
                  {bundleData.duplicateModules.join(', ')}
                </div>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="text-gray-400 text-center py-8">
          No bundle analysis data available
        </div>
      )}
    </div>
  );
}

// Lighthouse Scores Component
export function LighthouseScores({ className }: { className?: string }) {
  const { lighthouseScores, isRunning, runLighthouseAudit } = useLighthouseCI();

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreBackground = (score: number) => {
    if (score >= 90) return 'bg-green-500/20';
    if (score >= 70) return 'bg-yellow-500/20';
    return 'bg-red-500/20';
  };

  const scores = lighthouseScores ? [
    { name: 'Performance', value: lighthouseScores.performance, icon: Zap },
    { name: 'Accessibility', value: lighthouseScores.accessibility, icon: CheckCircle },
    { name: 'Best Practices', value: lighthouseScores.bestPractices, icon: CheckCircle },
    { name: 'SEO', value: lighthouseScores.seo, icon: TrendingUp },
    { name: 'PWA', value: lighthouseScores.pwa, icon: Monitor }
  ] : [];

  return (
    <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6', className)}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white flex items-center space-x-2">
          <Activity className="w-5 h-5" />
          <span>Lighthouse Scores</span>
        </h3>
        <button
          onClick={runLighthouseAudit}
          disabled={isRunning}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg text-sm transition-colors"
        >
          {isRunning ? 'Running Audit...' : 'Run Audit'}
        </button>
      </div>

      {isRunning ? (
        <div className="text-center py-8">
          <div className="animate-spin w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <div className="text-gray-400">Running Lighthouse audit...</div>
          <div className="text-gray-500 text-sm mt-2">This may take up to 60 seconds</div>
        </div>
      ) : lighthouseScores ? (
        <div className="space-y-4">
          <div className="text-sm text-gray-400 mb-4">
            Last run: {lighthouseScores.lastRun.toLocaleString()}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {scores.map(score => {
              const Icon = score.icon;
              return (
                <div key={score.name} className={cn('rounded-lg p-4', getScoreBackground(score.value))}>
                  <div className="flex items-center space-x-2 mb-2">
                    <Icon className={cn('w-4 h-4', getScoreColor(score.value))} />
                    <span className="text-white text-sm font-medium">{score.name}</span>
                  </div>
                  <div className={cn('text-2xl font-bold', getScoreColor(score.value))}>
                    {Math.round(score.value)}
                  </div>
                  <div className="mt-2 w-full bg-gray-700 rounded-full h-1">
                    <div
                      className={cn(
                        'h-1 rounded-full transition-all duration-500',
                        score.value >= 90 ? 'bg-green-500' :
                        score.value >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                      )}
                      style={{ width: `${score.value}%` }}
                    />
                  </div>
                </div>
              );
            })}
          </div>

          {/* Overall Score */}
          <div className="bg-white/5 rounded-lg p-4 text-center">
            <div className="text-gray-400 text-sm mb-1">Overall Score</div>
            <div className={cn(
              'text-3xl font-bold',
              getScoreColor((lighthouseScores.performance + lighthouseScores.accessibility + lighthouseScores.bestPractices + lighthouseScores.seo) / 4)
            )}>
              {Math.round((lighthouseScores.performance + lighthouseScores.accessibility + lighthouseScores.bestPractices + lighthouseScores.seo) / 4)}
            </div>
          </div>
        </div>
      ) : (
        <div className="text-gray-400 text-center py-8">
          No Lighthouse audit data available. Run an audit to see scores.
        </div>
      )}
    </div>
  );
}

  const getScoreColor = (value: number, threshold: number) => {
    if (value <= threshold) return 'text-green-400';
    if (value <= threshold * 1.5) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getScoreStatus = (value: number, threshold: number) => {
    if (value <= threshold) return 'Good';
    if (value <= threshold * 1.5) return 'Needs Improvement';
    return 'Poor';
  };

  const vitals = [
    {
      name: 'LCP',
      label: 'Largest Contentful Paint',
      value: metrics.lcp,
      threshold: 2500,
      unit: 'ms',
      description: 'Time to render the largest content element'
    },
    {
      name: 'FID',
      label: 'First Input Delay',
      value: metrics.fid,
      threshold: 100,
      unit: 'ms',
      description: 'Time from first user interaction to browser response'
    },
    {
      name: 'CLS',
      label: 'Cumulative Layout Shift',
      value: metrics.cls,
      threshold: 0.1,
      unit: '',
      description: 'Visual stability of the page'
    },
    {
      name: 'FCP',
      label: 'First Contentful Paint',
      value: metrics.fcp,
      threshold: 1800,
      unit: 'ms',
      description: 'Time to render the first content element'
    }
  ];

  return (
    <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6', className)}>
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Activity className="w-5 h-5 text-blue-400" />
          <h3 className="text-lg font-semibold text-white">Core Web Vitals</h3>
        </div>
        <div className="text-sm text-gray-400">
          Last updated: {metrics.timestamp.toLocaleTimeString()}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {vitals.map(vital => (
          <motion.div
            key={vital.name}
            className="bg-white/5 rounded-lg p-4"
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-white font-medium">{vital.name}</h4>
              <div className={cn(
                'text-xs px-2 py-1 rounded-full',
                getScoreColor(vital.value, vital.threshold) === 'text-green-400' ? 'bg-green-500/20 text-green-400' :
                getScoreColor(vital.value, vital.threshold) === 'text-yellow-400' ? 'bg-yellow-500/20 text-yellow-400' :
                'bg-red-500/20 text-red-400'
              )}>
                {getScoreStatus(vital.value, vital.threshold)}
              </div>
            </div>
            
            <div className={cn('text-2xl font-bold mb-1', getScoreColor(vital.value, vital.threshold))}>
              {vital.value.toFixed(vital.unit === 'ms' ? 0 : 3)}{vital.unit}
            </div>
            
            <div className="text-gray-400 text-xs mb-2">{vital.label}</div>
            <div className="text-gray-500 text-xs">{vital.description}</div>
            
            {/* Progress bar */}
            <div className="mt-3 w-full bg-gray-700 rounded-full h-1">
              <div 
                className={cn(
                  'h-1 rounded-full transition-all duration-500',
                  getScoreColor(vital.value, vital.threshold) === 'text-green-400' ? 'bg-green-500' :
                  getScoreColor(vital.value, vital.threshold) === 'text-yellow-400' ? 'bg-yellow-500' :
                  'bg-red-500'
                )}
                style={{ 
                  width: `${Math.min(100, (vital.value / (vital.threshold * 2)) * 100)}%` 
                }}
              />
            </div>
          </motion.div>
        ))}
      </div>

      {/* Active alerts */}
      {alerts.filter(alert => !alert.resolved).length > 0 && (
        <div className="mt-6 space-y-2">
          <h4 className="text-white font-medium flex items-center space-x-2">
            <AlertTriangle className="w-4 h-4 text-yellow-400" />
            <span>Performance Alerts</span>
          </h4>
          {alerts.filter(alert => !alert.resolved).slice(0, 3).map(alert => (
            <div
              key={alert.id}
              className={cn(
                'p-3 rounded-lg border text-sm',
                alert.type === 'critical' ? 'bg-red-500/20 border-red-500/50 text-red-400' :
                alert.type === 'error' ? 'bg-orange-500/20 border-orange-500/50 text-orange-400' :
                'bg-yellow-500/20 border-yellow-500/50 text-yellow-400'
              )}
            >
              {alert.message}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// Performance dashboard component
export function PerformanceDashboard({ className }: { className?: string }) {
  const { metrics, alerts, isMonitoring } = usePerformanceMonitoring();
  const { errorCount, lastError } = useSentryIntegration();

  const systemMetrics = [
    {
      name: 'Page Load Time',
      value: metrics?.pageLoadTime || 0,
      unit: 'ms',
      icon: Clock,
      threshold: 3000
    },
    {
      name: 'DOM Content Loaded',
      value: metrics?.domContentLoaded || 0,
      unit: 'ms',
      icon: Monitor,
      threshold: 2000
    },
    {
      name: 'Network RTT',
      value: metrics?.rtt || 0,
      unit: 'ms',
      icon: Wifi,
      threshold: 100
    },
    {
      name: 'JS Errors',
      value: errorCount,
      unit: '',
      icon: AlertTriangle,
      threshold: 0
    }
  ];

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Performance Monitoring</h2>
        <div className="flex items-center space-x-2">
          <div className={cn(
            'w-2 h-2 rounded-full',
            isMonitoring ? 'bg-green-400' : 'bg-red-400'
          )} />
          <span className="text-gray-400 text-sm">
            {isMonitoring ? 'Monitoring Active' : 'Monitoring Inactive'}
          </span>
        </div>
      </div>

      {/* Core Web Vitals */}
      <CoreWebVitals />

      {/* System Metrics */}
      <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
        <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
          <Cpu className="w-5 h-5" />
          <span>System Performance</span>
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {systemMetrics.map(metric => {
            const Icon = metric.icon;
            const isGood = metric.value <= metric.threshold;
            
            return (
              <div key={metric.name} className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <div className={cn(
                    'p-2 rounded-lg',
                    isGood ? 'bg-green-500/20' : 'bg-red-500/20'
                  )}>
                    <Icon className={cn(
                      'w-4 h-4',
                      isGood ? 'text-green-400' : 'text-red-400'
                    )} />
                  </div>
                  <div>
                    <div className={cn(
                      'text-lg font-bold',
                      isGood ? 'text-green-400' : 'text-red-400'
                    )}>
                      {metric.value.toLocaleString()}{metric.unit}
                    </div>
                    <div className="text-gray-400 text-sm">{metric.name}</div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Recent Errors */}
      {lastError && (
        <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-red-400 mb-4 flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5" />
            <span>Recent Error</span>
          </h3>
          <div className="bg-red-500/10 rounded-lg p-4">
            <div className="text-red-400 font-mono text-sm">{lastError.message}</div>
            <div className="text-red-300 text-xs mt-2">
              Total errors in session: {errorCount}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
