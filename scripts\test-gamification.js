#!/usr/bin/env node

/**
 * Comprehensive Gamification Testing Script
 * 
 * This script runs all gamification-related tests with performance monitoring,
 * accessibility checks, and detailed reporting.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Test configuration
const testConfig = {
  suites: [
    {
      name: 'XP System Tests',
      pattern: '__tests__/gamification/XPNotification.test.tsx',
      timeout: 30000,
      coverage: 90
    },
    {
      name: 'Achievement System Tests',
      pattern: '__tests__/gamification/AchievementSystem.test.tsx',
      timeout: 30000,
      coverage: 85
    },
    {
      name: 'Streak System Tests',
      pattern: '__tests__/gamification/StreakSystem.test.tsx',
      timeout: 30000,
      coverage: 85
    },
    {
      name: 'Performance Tests',
      pattern: '__tests__/analytics/PerformanceTests.test.tsx',
      timeout: 60000,
      coverage: 85
    },
    {
      name: 'Accessibility Tests',
      pattern: '__tests__/accessibility/GamificationAccessibility.test.tsx',
      timeout: 45000,
      coverage: 80
    }
  ],
  performance: {
    maxRenderTime: 100, // ms
    maxResponseTime: 200, // ms
    minFPS: 55,
    maxMemoryUsage: 100 // MB
  },
  accessibility: {
    wcagLevel: 'AA',
    colorContrastRatio: 4.5,
    minTouchTarget: 44 // px
  }
};

// Utility functions
function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${title}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSubsection(title) {
  log(`\n${colors.yellow}▶ ${title}${colors.reset}`);
}

function execCommand(command, options = {}) {
  try {
    const result = execSync(command, {
      encoding: 'utf8',
      stdio: 'pipe',
      ...options
    });
    return { success: true, output: result };
  } catch (error) {
    return { 
      success: false, 
      output: error.stdout || error.message,
      error: error.stderr || error.message
    };
  }
}

function checkPrerequisites() {
  logSubsection('Checking Prerequisites');
  
  // Check if Jest is installed
  const jestCheck = execCommand('npx jest --version');
  if (!jestCheck.success) {
    log('❌ Jest is not installed or not accessible', 'red');
    process.exit(1);
  }
  log(`✅ Jest version: ${jestCheck.output.trim()}`, 'green');
  
  // Check if test files exist
  const testFiles = testConfig.suites.map(suite => suite.pattern);
  const missingFiles = testFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    log('❌ Missing test files:', 'red');
    missingFiles.forEach(file => log(`   - ${file}`, 'red'));
    process.exit(1);
  }
  log('✅ All test files found', 'green');
  
  // Check if coverage directory exists, create if not
  const coverageDir = path.join(process.cwd(), 'coverage');
  if (!fs.existsSync(coverageDir)) {
    fs.mkdirSync(coverageDir, { recursive: true });
    log('✅ Created coverage directory', 'green');
  }
}

function runTestSuite(suite) {
  logSubsection(`Running ${suite.name}`);
  
  const startTime = Date.now();
  
  const command = [
    'npx jest',
    `--testPathPattern="${suite.pattern}"`,
    '--coverage',
    '--verbose',
    `--testTimeout=${suite.timeout}`,
    '--detectOpenHandles',
    '--forceExit'
  ].join(' ');
  
  const result = execCommand(command);
  const duration = Date.now() - startTime;
  
  if (result.success) {
    log(`✅ ${suite.name} passed (${duration}ms)`, 'green');
    return { success: true, duration, coverage: extractCoverage(result.output) };
  } else {
    log(`❌ ${suite.name} failed (${duration}ms)`, 'red');
    log('Error output:', 'red');
    log(result.error || result.output, 'red');
    return { success: false, duration, error: result.error };
  }
}

function extractCoverage(output) {
  // Extract coverage percentage from Jest output
  const coverageMatch = output.match(/All files\s+\|\s+(\d+\.?\d*)/);
  return coverageMatch ? parseFloat(coverageMatch[1]) : 0;
}

function runPerformanceTests() {
  logSubsection('Running Performance Benchmarks');
  
  const performanceTests = [
    {
      name: 'XP Notification Render Time',
      command: 'npx jest --testNamePattern="renders within performance threshold"',
      threshold: testConfig.performance.maxRenderTime
    },
    {
      name: 'Achievement System Response Time',
      command: 'npx jest --testNamePattern="achievement notification performance"',
      threshold: testConfig.performance.maxResponseTime
    },
    {
      name: 'Memory Usage Test',
      command: 'npx jest --testNamePattern="memory usage"',
      threshold: testConfig.performance.maxMemoryUsage
    }
  ];
  
  const results = [];
  
  for (const test of performanceTests) {
    const result = execCommand(test.command);
    const passed = result.success;
    
    results.push({
      name: test.name,
      passed,
      threshold: test.threshold
    });
    
    if (passed) {
      log(`✅ ${test.name}`, 'green');
    } else {
      log(`❌ ${test.name}`, 'red');
    }
  }
  
  return results;
}

function runAccessibilityTests() {
  logSubsection('Running Accessibility Tests');
  
  const a11yCommand = [
    'npx jest',
    '--testPathPattern="accessibility"',
    '--verbose'
  ].join(' ');
  
  const result = execCommand(a11yCommand);
  
  if (result.success) {
    log('✅ All accessibility tests passed', 'green');
    return { success: true };
  } else {
    log('❌ Accessibility tests failed', 'red');
    log(result.error || result.output, 'red');
    return { success: false, error: result.error };
  }
}

function generateReport(results) {
  logSection('Test Report Summary');
  
  const totalTests = results.suites.length;
  const passedTests = results.suites.filter(s => s.success).length;
  const failedTests = totalTests - passedTests;
  
  // Overall status
  log(`\nOverall Status: ${passedTests}/${totalTests} test suites passed`, 
      passedTests === totalTests ? 'green' : 'red');
  
  // Individual suite results
  log('\nTest Suite Results:', 'bright');
  results.suites.forEach(suite => {
    const status = suite.success ? '✅' : '❌';
    const coverage = suite.coverage ? ` (${suite.coverage.toFixed(1)}% coverage)` : '';
    log(`  ${status} ${suite.name} - ${suite.duration}ms${coverage}`);
  });
  
  // Performance results
  if (results.performance) {
    log('\nPerformance Results:', 'bright');
    results.performance.forEach(perf => {
      const status = perf.passed ? '✅' : '❌';
      log(`  ${status} ${perf.name}`);
    });
  }
  
  // Accessibility results
  if (results.accessibility) {
    const status = results.accessibility.success ? '✅' : '❌';
    log(`\nAccessibility: ${status} WCAG ${testConfig.accessibility.wcagLevel} compliance`, 
        results.accessibility.success ? 'green' : 'red');
  }
  
  // Coverage summary
  const avgCoverage = results.suites
    .filter(s => s.coverage)
    .reduce((sum, s) => sum + s.coverage, 0) / 
    results.suites.filter(s => s.coverage).length;
  
  if (avgCoverage) {
    log(`\nAverage Coverage: ${avgCoverage.toFixed(1)}%`, 
        avgCoverage >= 85 ? 'green' : 'yellow');
  }
  
  // Recommendations
  log('\nRecommendations:', 'bright');
  if (failedTests > 0) {
    log('  • Fix failing tests before deployment', 'yellow');
  }
  if (avgCoverage < 85) {
    log('  • Increase test coverage to meet 85% threshold', 'yellow');
  }
  if (results.performance && results.performance.some(p => !p.passed)) {
    log('  • Optimize performance for failing benchmarks', 'yellow');
  }
  if (results.accessibility && !results.accessibility.success) {
    log('  • Address accessibility violations', 'yellow');
  }
  
  // Generate HTML report
  generateHTMLReport(results);
}

function generateHTMLReport(results) {
  const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gamification Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .section { margin: 20px 0; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .test-card { border: 1px solid #ddd; border-radius: 8px; padding: 15px; }
        .coverage-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; }
        .coverage-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 Gamification System Test Report</h1>
            <p>Generated on ${new Date().toLocaleString()}</p>
        </div>
        
        <div class="section">
            <h2>Test Results</h2>
            <div class="test-grid">
                ${results.suites.map(suite => `
                    <div class="test-card">
                        <h3 class="${suite.success ? 'status-pass' : 'status-fail'}">
                            ${suite.success ? '✅' : '❌'} ${suite.name}
                        </h3>
                        <p>Duration: ${suite.duration}ms</p>
                        ${suite.coverage ? `
                            <div class="coverage-bar">
                                <div class="coverage-fill" style="width: ${suite.coverage}%"></div>
                            </div>
                            <p>Coverage: ${suite.coverage.toFixed(1)}%</p>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
        </div>
    </div>
</body>
</html>`;

  fs.writeFileSync(path.join('coverage', 'gamification-report.html'), htmlContent);
  log('\n📊 HTML report generated: coverage/gamification-report.html', 'cyan');
}

// Main execution
async function main() {
  logSection('🎮 Gamification System Test Suite');
  
  try {
    // Check prerequisites
    checkPrerequisites();
    
    // Run test suites
    logSection('Running Test Suites');
    const suiteResults = [];
    
    for (const suite of testConfig.suites) {
      const result = runTestSuite(suite);
      suiteResults.push({ ...suite, ...result });
    }
    
    // Run performance tests
    const performanceResults = runPerformanceTests();
    
    // Run accessibility tests
    const accessibilityResults = runAccessibilityTests();
    
    // Generate comprehensive report
    const finalResults = {
      suites: suiteResults,
      performance: performanceResults,
      accessibility: accessibilityResults,
      timestamp: new Date().toISOString()
    };
    
    generateReport(finalResults);
    
    // Exit with appropriate code
    const allPassed = suiteResults.every(s => s.success) && 
                     performanceResults.every(p => p.passed) && 
                     accessibilityResults.success;
    
    if (allPassed) {
      log('\n🎉 All tests passed! Gamification system is ready for deployment.', 'green');
      process.exit(0);
    } else {
      log('\n⚠️  Some tests failed. Please review and fix before deployment.', 'yellow');
      process.exit(1);
    }
    
  } catch (error) {
    log(`\n💥 Test execution failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main, testConfig };
