#!/usr/bin/env node

/**
 * Comprehensive Dead Code and Organization Analyzer
 * Analyzes the Solidity Learning Platform codebase for unused files, exports, and organizational issues
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Analysis configuration
const ANALYSIS_CONFIG = {
  sourceDirectories: ['app', 'components', 'lib', 'hooks', 'utils', 'types', 'services', 'stores'],
  assetDirectories: ['public'],
  testDirectories: ['__tests__', 'tests', 'e2e'],
  excludePatterns: [
    'node_modules',
    '.next',
    '.git',
    'dist',
    'build',
    'coverage',
    'test-results',
    'reports',
    'logs'
  ],
  fileExtensions: ['.ts', '.tsx', '.js', '.jsx'],
  assetExtensions: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot'],
  configFiles: [
    'next.config.js',
    'tailwind.config.js',
    'tsconfig.json',
    'package.json',
    'middleware.ts',
    'lighthouserc.js',
    'jest.config.js',
    'playwright.config.ts'
  ]
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Get all files recursively
function getAllFiles(dir, extensions = [], excludePatterns = []) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item);
        const relativePath = path.relative(process.cwd(), fullPath);
        
        // Skip excluded patterns
        if (excludePatterns.some(pattern => relativePath.includes(pattern))) {
          return;
        }
        
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          traverse(fullPath);
        } else if (stat.isFile()) {
          if (extensions.length === 0 || extensions.some(ext => item.endsWith(ext))) {
            files.push({
              path: fullPath,
              relativePath,
              name: item,
              size: stat.size,
              lastModified: stat.mtime,
              extension: path.extname(item)
            });
          }
        }
      });
    } catch (error) {
      // Skip directories that can't be read
    }
  }
  
  if (fs.existsSync(dir)) {
    traverse(dir);
  }
  
  return files;
}

// Extract imports and exports from a file
function analyzeFileImportsExports(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    const imports = [];
    const exports = [];
    const reExports = [];
    
    // Extract imports
    const importRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"`]([^'"`]+)['"`]/g;
    let importMatch;
    while ((importMatch = importRegex.exec(content)) !== null) {
      imports.push({
        module: importMatch[1],
        line: content.substring(0, importMatch.index).split('\n').length
      });
    }
    
    // Extract named exports
    const namedExportRegex = /export\s+(?:const|let|var|function|class|interface|type|enum)\s+(\w+)/g;
    let namedExportMatch;
    while ((namedExportMatch = namedExportRegex.exec(content)) !== null) {
      exports.push({
        name: namedExportMatch[1],
        type: 'named',
        line: content.substring(0, namedExportMatch.index).split('\n').length
      });
    }
    
    // Extract default exports
    const defaultExportRegex = /export\s+default\s+(?:(?:function|class)\s+(\w+)|(\w+))/g;
    let defaultExportMatch;
    while ((defaultExportMatch = defaultExportRegex.exec(content)) !== null) {
      exports.push({
        name: defaultExportMatch[1] || defaultExportMatch[2] || 'default',
        type: 'default',
        line: content.substring(0, defaultExportMatch.index).split('\n').length
      });
    }
    
    // Extract re-exports
    const reExportRegex = /export\s+(?:\{[^}]*\}|\*)\s+from\s+['"`]([^'"`]+)['"`]/g;
    let reExportMatch;
    while ((reExportMatch = reExportRegex.exec(content)) !== null) {
      reExports.push({
        module: reExportMatch[1],
        line: content.substring(0, reExportMatch.index).split('\n').length
      });
    }
    
    return { imports, exports, reExports };
  } catch (error) {
    return { imports: [], exports: [], reExports: [] };
  }
}

// Find unused files
function findUnusedFiles() {
  logHeader('Analyzing Unused Files');
  
  const allSourceFiles = [];
  const allAssetFiles = [];
  
  // Get all source files
  ANALYSIS_CONFIG.sourceDirectories.forEach(dir => {
    const files = getAllFiles(dir, ANALYSIS_CONFIG.fileExtensions, ANALYSIS_CONFIG.excludePatterns);
    allSourceFiles.push(...files);
  });
  
  // Get all asset files
  ANALYSIS_CONFIG.assetDirectories.forEach(dir => {
    const files = getAllFiles(dir, ANALYSIS_CONFIG.assetExtensions, ANALYSIS_CONFIG.excludePatterns);
    allAssetFiles.push(...files);
  });
  
  log(`Found ${allSourceFiles.length} source files`, 'blue');
  log(`Found ${allAssetFiles.length} asset files`, 'blue');
  
  // Build dependency graph
  const dependencyGraph = new Map();
  const allImports = new Set();
  
  allSourceFiles.forEach(file => {
    const analysis = analyzeFileImportsExports(file.path);
    dependencyGraph.set(file.relativePath, analysis);
    
    analysis.imports.forEach(imp => {
      // Resolve relative imports
      let resolvedPath = imp.module;
      if (imp.module.startsWith('.')) {
        resolvedPath = path.resolve(path.dirname(file.path), imp.module);
        resolvedPath = path.relative(process.cwd(), resolvedPath);
      }
      allImports.add(resolvedPath);
    });
  });
  
  // Find unused source files
  const unusedSourceFiles = allSourceFiles.filter(file => {
    const relativePath = file.relativePath.replace(/\\/g, '/');
    const withoutExt = relativePath.replace(/\.(ts|tsx|js|jsx)$/, '');
    
    // Check if file is imported
    const isImported = Array.from(allImports).some(imp => {
      const normalizedImp = imp.replace(/\\/g, '/');
      return normalizedImp === relativePath || 
             normalizedImp === withoutExt ||
             normalizedImp.endsWith('/' + file.name) ||
             normalizedImp.endsWith('/' + file.name.replace(/\.(ts|tsx|js|jsx)$/, ''));
    });
    
    // Check if it's a Next.js special file
    const isNextJSFile = file.name === 'page.tsx' || 
                        file.name === 'layout.tsx' || 
                        file.name === 'loading.tsx' || 
                        file.name === 'error.tsx' ||
                        file.name === 'not-found.tsx' ||
                        file.name === 'middleware.ts' ||
                        file.relativePath.includes('api/');
    
    // Check if it's a config file
    const isConfigFile = ANALYSIS_CONFIG.configFiles.some(config => 
      file.relativePath.endsWith(config)
    );
    
    return !isImported && !isNextJSFile && !isConfigFile;
  });
  
  // Find unused asset files
  const unusedAssetFiles = allAssetFiles.filter(file => {
    const fileName = file.name;
    const fileNameWithoutExt = fileName.replace(/\.[^.]+$/, '');
    
    // Check if asset is referenced in any source file
    const isReferenced = allSourceFiles.some(sourceFile => {
      try {
        const content = fs.readFileSync(sourceFile.path, 'utf8');
        return content.includes(fileName) || 
               content.includes(fileNameWithoutExt) ||
               content.includes(file.relativePath.replace(/\\/g, '/'));
      } catch {
        return false;
      }
    });
    
    return !isReferenced;
  });
  
  // Display results
  if (unusedSourceFiles.length > 0) {
    logWarning(`Found ${unusedSourceFiles.length} potentially unused source files:`);
    unusedSourceFiles.forEach(file => {
      log(`  ${file.relativePath} (${(file.size / 1024).toFixed(1)}KB, modified: ${file.lastModified.toDateString()})`, 'yellow');
    });
  } else {
    logSuccess('No unused source files found');
  }
  
  if (unusedAssetFiles.length > 0) {
    logWarning(`Found ${unusedAssetFiles.length} potentially unused asset files:`);
    unusedAssetFiles.forEach(file => {
      log(`  ${file.relativePath} (${(file.size / 1024).toFixed(1)}KB)`, 'yellow');
    });
  } else {
    logSuccess('No unused asset files found');
  }
  
  return {
    unusedSourceFiles,
    unusedAssetFiles,
    totalSourceFiles: allSourceFiles.length,
    totalAssetFiles: allAssetFiles.length,
    dependencyGraph
  };
}

// Find unused exports
function findUnusedExports(dependencyGraph) {
  logHeader('Analyzing Unused Exports');
  
  const allExports = new Map();
  const allImports = new Set();
  
  // Collect all exports and imports
  for (const [filePath, analysis] of dependencyGraph) {
    analysis.exports.forEach(exp => {
      const key = `${filePath}:${exp.name}`;
      allExports.set(key, {
        file: filePath,
        export: exp,
        used: false
      });
    });
    
    analysis.imports.forEach(imp => {
      allImports.add(imp.module);
    });
  }
  
  // Mark exports as used based on imports
  for (const [filePath, analysis] of dependencyGraph) {
    analysis.imports.forEach(imp => {
      // This is a simplified check - in a real implementation,
      // you'd need to parse the import statements more carefully
      const importedFile = imp.module;
      
      // Mark all exports from imported files as potentially used
      for (const [exportKey, exportInfo] of allExports) {
        if (exportInfo.file.includes(importedFile) || importedFile.includes(exportInfo.file)) {
          exportInfo.used = true;
        }
      }
    });
  }
  
  // Find unused exports
  const unusedExports = Array.from(allExports.values()).filter(exp => !exp.used);
  
  if (unusedExports.length > 0) {
    logWarning(`Found ${unusedExports.length} potentially unused exports:`);
    unusedExports.forEach(exp => {
      log(`  ${exp.file}: ${exp.export.name} (${exp.export.type}, line ${exp.export.line})`, 'yellow');
    });
  } else {
    logSuccess('No unused exports found');
  }
  
  return {
    totalExports: allExports.size,
    unusedExports,
    usedExports: allExports.size - unusedExports.length
  };
}

// Analyze directory structure consistency
function analyzeDirectoryStructure() {
  logHeader('Analyzing Directory Structure Consistency');
  
  const issues = [];
  const recommendations = [];
  
  // Check app directory structure
  const appDir = path.join(process.cwd(), 'app');
  if (fs.existsSync(appDir)) {
    const appRoutes = fs.readdirSync(appDir).filter(item => {
      const itemPath = path.join(appDir, item);
      return fs.statSync(itemPath).isDirectory();
    });
    
    appRoutes.forEach(route => {
      const routePath = path.join(appDir, route);
      const files = fs.readdirSync(routePath);
      
      const hasPage = files.includes('page.tsx');
      const hasLayout = files.includes('layout.tsx');
      const hasLoading = files.includes('loading.tsx');
      const hasError = files.includes('error.tsx');
      
      if (!hasPage && !files.some(f => f.startsWith('page.'))) {
        issues.push(`Route ${route} missing page.tsx`);
      }
      
      if (route !== 'api' && !hasLayout && route !== 'globals.css') {
        recommendations.push(`Consider adding layout.tsx to ${route} for consistent structure`);
      }
    });
  }
  
  // Check components directory structure
  const componentsDir = path.join(process.cwd(), 'components');
  if (fs.existsSync(componentsDir)) {
    const componentDirs = fs.readdirSync(componentsDir).filter(item => {
      const itemPath = path.join(componentsDir, item);
      return fs.statSync(itemPath).isDirectory();
    });
    
    componentDirs.forEach(dir => {
      // Check naming convention (should be PascalCase or kebab-case)
      if (!/^[A-Z][a-zA-Z]*$/.test(dir) && !/^[a-z]+(-[a-z]+)*$/.test(dir)) {
        issues.push(`Component directory ${dir} doesn't follow naming convention`);
      }
    });
  }
  
  // Check for index files
  const dirsToCheck = ['components', 'lib', 'hooks', 'utils', 'types'];
  dirsToCheck.forEach(dir => {
    const dirPath = path.join(process.cwd(), dir);
    if (fs.existsSync(dirPath)) {
      const hasIndex = fs.existsSync(path.join(dirPath, 'index.ts')) || 
                     fs.existsSync(path.join(dirPath, 'index.tsx'));
      
      if (!hasIndex) {
        recommendations.push(`Consider adding index.ts to ${dir} for cleaner imports`);
      }
    }
  });
  
  if (issues.length > 0) {
    logWarning('Directory structure issues found:');
    issues.forEach(issue => log(`  ${issue}`, 'yellow'));
  }
  
  if (recommendations.length > 0) {
    logInfo('Directory structure recommendations:');
    recommendations.forEach(rec => log(`  ${rec}`, 'blue'));
  }
  
  if (issues.length === 0 && recommendations.length === 0) {
    logSuccess('Directory structure looks good');
  }
  
  return { issues, recommendations };
}

// Generate comprehensive report
function generateReport(unusedFilesResult, unusedExportsResult, structureResult) {
  logHeader('Generating Comprehensive Report');
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalSourceFiles: unusedFilesResult.totalSourceFiles,
      totalAssetFiles: unusedFilesResult.totalAssetFiles,
      unusedSourceFiles: unusedFilesResult.unusedSourceFiles.length,
      unusedAssetFiles: unusedFilesResult.unusedAssetFiles.length,
      totalExports: unusedExportsResult.totalExports,
      unusedExports: unusedExportsResult.unusedExports.length,
      structureIssues: structureResult.issues.length,
      structureRecommendations: structureResult.recommendations.length
    },
    deadCode: {
      unusedFiles: {
        source: unusedFilesResult.unusedSourceFiles.map(f => ({
          path: f.relativePath,
          size: f.size,
          lastModified: f.lastModified,
          confidence: 'medium' // Would need more sophisticated analysis for high confidence
        })),
        assets: unusedFilesResult.unusedAssetFiles.map(f => ({
          path: f.relativePath,
          size: f.size,
          confidence: 'medium'
        }))
      },
      unusedExports: unusedExportsResult.unusedExports.map(exp => ({
        file: exp.file,
        name: exp.export.name,
        type: exp.export.type,
        line: exp.export.line,
        confidence: 'low' // Export analysis needs more sophisticated dependency tracking
      }))
    },
    organization: {
      issues: structureResult.issues,
      recommendations: structureResult.recommendations
    },
    potentialSavings: {
      files: unusedFilesResult.unusedSourceFiles.length + unusedFilesResult.unusedAssetFiles.length,
      estimatedSizeKB: Math.round(
        (unusedFilesResult.unusedSourceFiles.reduce((sum, f) => sum + f.size, 0) +
         unusedFilesResult.unusedAssetFiles.reduce((sum, f) => sum + f.size, 0)) / 1024
      )
    }
  };
  
  // Save report
  const reportDir = path.join(process.cwd(), 'reports');
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(reportDir, `dead-code-analysis-${timestamp}.json`);
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  logSuccess(`Report saved to: ${reportPath}`);
  
  // Display summary
  log('\n📊 Analysis Summary:', 'bright');
  log(`Total files analyzed: ${report.summary.totalSourceFiles + report.summary.totalAssetFiles}`, 'blue');
  log(`Potentially unused files: ${report.summary.unusedSourceFiles + report.summary.unusedAssetFiles}`, 'yellow');
  log(`Potentially unused exports: ${report.summary.unusedExports}`, 'yellow');
  log(`Potential size savings: ${report.potentialSavings.estimatedSizeKB}KB`, 'green');
  
  return report;
}

// Main execution
async function main() {
  logHeader('Solidity Learning Platform - Dead Code Analysis');
  
  try {
    const unusedFilesResult = findUnusedFiles();
    const unusedExportsResult = findUnusedExports(unusedFilesResult.dependencyGraph);
    const structureResult = analyzeDirectoryStructure();
    
    const report = generateReport(unusedFilesResult, unusedExportsResult, structureResult);
    
    const hasIssues = report.summary.unusedSourceFiles > 0 || 
                     report.summary.unusedAssetFiles > 0 || 
                     report.summary.structureIssues > 0;
    
    log('\n' + '='.repeat(60), 'cyan');
    if (hasIssues) {
      logWarning('⚠️  Dead code and organization issues found. See report for details.');
    } else {
      logSuccess('🎉 No significant dead code or organization issues found!');
    }
    log('='.repeat(60), 'cyan');
    
    process.exit(hasIssues ? 1 : 0);
    
  } catch (error) {
    logError(`Analysis failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    logError(`Dead code analyzer failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { findUnusedFiles, findUnusedExports, analyzeDirectoryStructure };
