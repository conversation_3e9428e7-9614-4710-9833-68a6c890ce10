@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Menlo&family=Monaco&family=Consolas&family=Liberation+Mono&family=Courier+New&display=swap');

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
    scroll-behavior: smooth;
  }

  body {
    @apply bg-brand-bg-dark text-brand-text-primary antialiased;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  code, pre {
    font-family: '<PERSON>lo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  }

  /* Focus styles for accessibility */
  *:focus {
    @apply outline-none ring-2 ring-brand-primary-500 ring-offset-2 ring-offset-brand-bg-dark;
  }

  /* Custom scrollbar for webkit browsers */
  * {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.brand.primary.500') theme('colors.brand.bg.dark');
  }

  *::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  *::-webkit-scrollbar-track {
    @apply bg-brand-bg-dark rounded;
  }

  *::-webkit-scrollbar-thumb {
    @apply bg-brand-primary-500 rounded;
  }

  *::-webkit-scrollbar-thumb:hover {
    @apply bg-brand-primary-600;
  }
}

/* Custom component styles */
@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply btn bg-brand-primary-600 hover:bg-brand-primary-700 text-white shadow-md hover:shadow-lg focus:ring-brand-primary-500;
  }

  .btn-secondary {
    @apply btn bg-brand-bg-medium hover:bg-brand-bg-light text-brand-text-primary border border-brand-bg-light focus:ring-brand-primary-500;
  }

  .btn-accent {
    @apply btn bg-brand-accent-600 hover:bg-brand-accent-700 text-white shadow-md hover:shadow-lg focus:ring-brand-accent-500;
  }

  .btn-success {
    @apply btn bg-brand-success-600 hover:bg-brand-success-700 text-white shadow-md hover:shadow-lg focus:ring-brand-success-500;
  }

  .btn-warning {
    @apply btn bg-brand-warning-600 hover:bg-brand-warning-700 text-white shadow-md hover:shadow-lg focus:ring-brand-warning-500;
  }

  .btn-error {
    @apply btn bg-brand-error-600 hover:bg-brand-error-700 text-white shadow-md hover:shadow-lg focus:ring-brand-error-500;
  }

  .btn-ghost {
    @apply btn bg-transparent hover:bg-brand-bg-medium text-brand-text-primary border border-transparent hover:border-brand-bg-light focus:ring-brand-primary-500;
  }

  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }

  .btn-lg {
    @apply px-6 py-3 text-base;
  }

  /* Card Components */
  .card {
    @apply bg-brand-bg-medium rounded-xl shadow-lg border border-brand-bg-light/20 backdrop-blur-sm;
  }

  .card-body {
    @apply p-6;
  }

  .card-header {
    @apply px-6 py-4 border-b border-brand-bg-light/20;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-brand-bg-light/20;
  }

  /* Input Components */
  .input {
    @apply w-full px-3 py-2 bg-brand-bg-dark border border-brand-bg-light rounded-lg text-brand-text-primary placeholder-brand-text-muted focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:border-transparent transition-colors duration-200;
  }

  .input-error {
    @apply border-brand-error-500 focus:ring-brand-error-500;
  }

  .input-success {
    @apply border-brand-success-500 focus:ring-brand-success-500;
  }

  /* Loading Components */
  .skeleton {
    @apply animate-pulse bg-brand-bg-light rounded;
  }

  .skeleton-text {
    @apply skeleton h-4 w-full;
  }

  .skeleton-title {
    @apply skeleton h-6 w-3/4;
  }

  .skeleton-avatar {
    @apply skeleton h-10 w-10 rounded-full;
  }

  /* Alert Components */
  .alert {
    @apply p-4 rounded-lg border flex items-start gap-3;
  }

  .alert-info {
    @apply alert bg-brand-info-50/10 border-brand-info-500/20 text-brand-info-300;
  }

  .alert-success {
    @apply alert bg-brand-success-50/10 border-brand-success-500/20 text-brand-success-300;
  }

  .alert-warning {
    @apply alert bg-brand-warning-50/10 border-brand-warning-500/20 text-brand-warning-300;
  }

  .alert-error {
    @apply alert bg-brand-error-50/10 border-brand-error-500/20 text-brand-error-300;
  }

  /* Navigation Components */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-brand-bg-light hover:text-brand-text-primary;
  }

  .nav-link-active {
    @apply nav-link bg-brand-primary-600 text-white shadow-md;
  }

  /* Modal Components */
  .modal-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4;
  }

  .modal-content {
    @apply bg-brand-bg-medium rounded-xl shadow-2xl border border-brand-bg-light/20 max-w-md w-full max-h-[90vh] overflow-y-auto;
  }
}

/* Custom utility styles */
@layer utilities {
  /* Text utilities */
  .text-gradient {
    @apply bg-gradient-to-r from-brand-primary-500 to-brand-accent-500 bg-clip-text text-transparent;
  }

  .text-gradient-purple {
    @apply bg-gradient-to-r from-brand-primary-600 to-brand-primary-400 bg-clip-text text-transparent;
  }

  /* Shadow utilities */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 40px rgba(139, 92, 246, 0.4);
  }

  .shadow-inner-glow {
    box-shadow: inset 0 0 10px rgba(139, 92, 246, 0.2);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.3s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-bounce-subtle {
    animation: bounceSubtle 0.6s ease-in-out;
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }

  /* Layout utilities */
  .glass-effect {
    @apply bg-brand-bg-medium/80 backdrop-blur-md border border-brand-bg-light/20;
  }

  .glass-effect-strong {
    @apply bg-brand-bg-medium/90 backdrop-blur-lg border border-brand-bg-light/30;
  }

  /* Interactive utilities */
  .hover-lift {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-glow;
  }

  /* Responsive utilities */
  .mobile-only {
    @apply block md:hidden;
  }

  .desktop-only {
    @apply hidden md:block;
  }

  .tablet-up {
    @apply hidden sm:block;
  }

  /* Focus utilities */
  .focus-brand {
    @apply focus:outline-none focus:ring-2 focus:ring-brand-primary-500 focus:ring-offset-2 focus:ring-offset-brand-bg-dark;
  }

  /* Loading utilities */
  .loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
  }

  @keyframes loadingDots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
  }

  /* Scrollbar utilities */
  .scrollbar-brand {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.brand.primary.500') theme('colors.brand.bg.dark');
  }

  .scrollbar-brand::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-brand::-webkit-scrollbar-track {
    @apply bg-brand-bg-dark rounded;
  }

  .scrollbar-brand::-webkit-scrollbar-thumb {
    @apply bg-brand-primary-500 rounded;
  }

  .scrollbar-brand::-webkit-scrollbar-thumb:hover {
    @apply bg-brand-primary-600;
  }
}
