#!/usr/bin/env node

/**
 * Tree Shaking Optimizer
 * Automatically fixes import patterns and optimizes for better tree shaking
 */

const fs = require('fs');
const path = require('path');

class TreeShakingOptimizer {
  constructor() {
    this.optimizationsApplied = 0;
    this.filesProcessed = 0;
    this.backupDir = 'tree-shaking-backup';
    this.dryRun = process.argv.includes('--dry-run');
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
  }

  createBackup() {
    if (this.dryRun) {
      this.log('Dry run mode - skipping backup');
      return;
    }

    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }

    this.log('Creating backup of source files...');
    
    const sourceDirectories = ['components', 'app', 'lib', 'hooks', 'utils'];
    sourceDirectories.forEach(dir => {
      if (fs.existsSync(dir)) {
        const { execSync } = require('child_process');
        execSync(`cp -r ${dir} ${this.backupDir}/`, { stdio: 'inherit' });
      }
    });
  }

  getOptimizationRules() {
    return {
      // Lodash optimizations
      lodash: {
        pattern: /import\s+_\s+from\s+['"]lodash['"];?/g,
        replacement: (match, usage) => {
          // This would need more sophisticated analysis to determine actual usage
          return '// TODO: Replace with specific lodash imports';
        }
      },

      // React optimizations
      react: {
        // Optimize React imports
        patterns: [
          {
            pattern: /import\s+React,\s*{\s*([^}]+)\s*}\s+from\s+['"]react['"];?/g,
            replacement: (match, namedImports) => {
              return `import React from 'react';\nimport { ${namedImports} } from 'react';`;
            }
          }
        ]
      },

      // Material-UI / MUI optimizations
      mui: {
        pattern: /import\s+{\s*([^}]+)\s*}\s+from\s+['"]@mui\/material['"];?/g,
        replacement: (match, imports) => {
          const importList = imports.split(',').map(imp => imp.trim());
          return importList.map(imp => 
            `import ${imp} from '@mui/material/${imp}';`
          ).join('\n');
        }
      },

      // Antd optimizations
      antd: {
        pattern: /import\s+{\s*([^}]+)\s*}\s+from\s+['"]antd['"];?/g,
        replacement: (match, imports) => {
          const importList = imports.split(',').map(imp => imp.trim());
          return importList.map(imp => 
            `import ${imp} from 'antd/es/${imp.toLowerCase()}';`
          ).join('\n');
        }
      },

      // Date-fns optimizations
      dateFns: {
        pattern: /import\s+{\s*([^}]+)\s*}\s+from\s+['"]date-fns['"];?/g,
        replacement: (match, imports) => {
          const importList = imports.split(',').map(imp => imp.trim());
          return importList.map(imp => 
            `import ${imp} from 'date-fns/${imp}';`
          ).join('\n');
        }
      },

      // RxJS optimizations
      rxjs: {
        patterns: [
          {
            pattern: /import\s+{\s*([^}]+)\s*}\s+from\s+['"]rxjs\/operators['"];?/g,
            replacement: (match, imports) => {
              const importList = imports.split(',').map(imp => imp.trim());
              return importList.map(imp => 
                `import { ${imp} } from 'rxjs/operators/${imp}';`
              ).join('\n');
            }
          }
        ]
      }
    };
  }

  analyzeFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const optimizations = [];
    const rules = this.getOptimizationRules();

    // Check for lodash imports
    const lodashMatches = content.match(/import.*from\s+['"]lodash['"];?/g);
    if (lodashMatches) {
      optimizations.push({
        type: 'lodash',
        original: lodashMatches,
        suggestion: 'Use specific lodash imports for better tree shaking'
      });
    }

    // Check for barrel imports that might cause issues
    const barrelImports = content.match(/import.*from\s+['"][^'"]*\/index['"];?/g);
    if (barrelImports) {
      optimizations.push({
        type: 'barrel',
        original: barrelImports,
        suggestion: 'Consider direct imports to avoid potential circular dependencies'
      });
    }

    // Check for wildcard imports
    const wildcardImports = content.match(/import\s+\*\s+as\s+\w+\s+from/g);
    if (wildcardImports) {
      optimizations.push({
        type: 'wildcard',
        original: wildcardImports,
        suggestion: 'Use named imports instead of wildcard imports'
      });
    }

    // Check for large library imports
    const largeLibraryPatterns = [
      /import.*from\s+['"]@mui\/material['"];?/g,
      /import.*from\s+['"]antd['"];?/g,
      /import.*from\s+['"]@ant-design\/icons['"];?/g
    ];

    largeLibraryPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        optimizations.push({
          type: 'large-library',
          original: matches,
          suggestion: 'Use specific component imports for better tree shaking'
        });
      }
    });

    return optimizations;
  }

  optimizeFile(filePath) {
    let content = fs.readFileSync(filePath, 'utf8');
    let optimizationsCount = 0;
    const originalContent = content;

    // Apply specific optimizations
    const rules = this.getOptimizationRules();

    // Optimize lodash imports (basic example)
    const lodashPattern = /import\s+_\s+from\s+['"]lodash['"];?/g;
    if (lodashPattern.test(content)) {
      // This is a simplified example - in practice, you'd need to analyze usage
      content = content.replace(lodashPattern, 
        '// TODO: Replace with specific lodash imports like:\n// import { debounce, throttle } from \'lodash\';'
      );
      optimizationsCount++;
    }

    // Optimize Material-UI imports
    const muiPattern = /import\s+{\s*([^}]+)\s*}\s+from\s+['"]@mui\/material['"];?/g;
    content = content.replace(muiPattern, (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      optimizationsCount++;
      return importList.map(imp => 
        `import ${imp} from '@mui/material/${imp}';`
      ).join('\n');
    });

    // Optimize Ant Design imports
    const antdPattern = /import\s+{\s*([^}]+)\s*}\s+from\s+['"]antd['"];?/g;
    content = content.replace(antdPattern, (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      optimizationsCount++;
      return importList.map(imp => 
        `import ${imp} from 'antd/es/${imp.toLowerCase()}';`
      ).join('\n');
    });

    // Optimize date-fns imports
    const dateFnsPattern = /import\s+{\s*([^}]+)\s*}\s+from\s+['"]date-fns['"];?/g;
    content = content.replace(dateFnsPattern, (match, imports) => {
      const importList = imports.split(',').map(imp => imp.trim());
      optimizationsCount++;
      return importList.map(imp => 
        `import ${imp} from 'date-fns/${imp}';`
      ).join('\n');
    });

    // Clean up multiple consecutive newlines
    content = content.replace(/\n\s*\n\s*\n+/g, '\n\n');

    // Write optimized content if changes were made
    if (content !== originalContent && !this.dryRun) {
      fs.writeFileSync(filePath, content, 'utf8');
    }

    if (optimizationsCount > 0) {
      this.log(`Optimized ${filePath}: ${optimizationsCount} improvements`);
    }

    return optimizationsCount;
  }

  processDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      this.log(`Directory ${dirPath} does not exist, skipping`);
      return;
    }

    const items = fs.readdirSync(dirPath);

    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        this.processDirectory(itemPath);
      } else if (stat.isFile() && /\.(ts|tsx|js|jsx)$/.test(item)) {
        try {
          const optimizations = this.optimizeFile(itemPath);
          this.optimizationsApplied += optimizations;
          this.filesProcessed++;
        } catch (error) {
          this.log(`Error processing ${itemPath}: ${error.message}`, 'error');
        }
      }
    }
  }

  createPackageJsonOptimizations() {
    this.log('Creating package.json optimizations...');
    
    const packageJsonPath = 'package.json';
    if (!fs.existsSync(packageJsonPath)) {
      this.log('package.json not found', 'warn');
      return;
    }

    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    // Add sideEffects field for better tree shaking
    if (!packageJson.sideEffects) {
      packageJson.sideEffects = false;
    }

    // Add module field if not present
    if (!packageJson.module && packageJson.main) {
      packageJson.module = packageJson.main.replace('.js', '.esm.js');
    }

    // Optimize dependencies for tree shaking
    const optimizedDependencies = {
      'lodash': 'lodash-es', // Use ES modules version
      'moment': 'date-fns', // Suggest replacement
    };

    let hasChanges = false;
    Object.entries(optimizedDependencies).forEach(([oldDep, newDep]) => {
      if (packageJson.dependencies && packageJson.dependencies[oldDep]) {
        this.log(`Recommendation: Replace ${oldDep} with ${newDep} for better tree shaking`);
        // Don't automatically replace, just recommend
      }
    });

    // Add tree shaking friendly scripts
    if (!packageJson.scripts['analyze']) {
      packageJson.scripts['analyze'] = 'ANALYZE=true npm run build';
      hasChanges = true;
    }

    if (!packageJson.scripts['bundle-size']) {
      packageJson.scripts['bundle-size'] = 'npm run build && bundlesize';
      hasChanges = true;
    }

    if (hasChanges && !this.dryRun) {
      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      this.log('Updated package.json with tree shaking optimizations');
    }
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      mode: this.dryRun ? 'dry-run' : 'applied',
      summary: {
        filesProcessed: this.filesProcessed,
        optimizationsApplied: this.optimizationsApplied
      },
      recommendations: [
        'Use ES modules instead of CommonJS where possible',
        'Import only what you need from large libraries',
        'Avoid importing entire libraries with wildcard imports',
        'Use babel-plugin-import for automatic optimization',
        'Configure webpack to mark packages as side-effect free',
        'Consider using lighter alternatives to heavy libraries'
      ],
      nextSteps: [
        'Run bundle analysis to measure improvements',
        'Set up bundle size monitoring in CI/CD',
        'Review and test all optimized imports',
        'Consider implementing automated import optimization in build process'
      ]
    };

    const reportPath = `tree-shaking-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    return { report, reportPath };
  }

  run() {
    this.log('Starting tree shaking optimization...');
    this.log(`Mode: ${this.dryRun ? 'DRY RUN' : 'APPLY CHANGES'}`);

    // Create backup
    this.createBackup();

    // Process source directories
    const directories = ['components', 'app', 'lib', 'hooks', 'utils'];
    directories.forEach(dir => {
      this.log(`Processing directory: ${dir}`);
      this.processDirectory(dir);
    });

    // Optimize package.json
    this.createPackageJsonOptimizations();

    // Generate report
    const { report, reportPath } = this.generateReport();

    // Display summary
    console.log('\n=== TREE SHAKING OPTIMIZATION REPORT ===');
    console.log(`Files processed: ${report.summary.filesProcessed}`);
    console.log(`Optimizations applied: ${report.summary.optimizationsApplied}`);
    
    console.log('\nRecommendations:');
    report.recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });

    if (this.dryRun) {
      console.log('\nThis was a dry run. No files were modified.');
      console.log('Run without --dry-run to apply optimizations.');
    } else {
      console.log(`\nBackup created in: ${this.backupDir}`);
      console.log('Tree shaking optimizations applied successfully!');
    }

    console.log(`\nDetailed report saved to: ${reportPath}`);

    this.log('Tree shaking optimization completed');
  }
}

// Run the optimizer
if (require.main === module) {
  const optimizer = new TreeShakingOptimizer();
  optimizer.run();
}

module.exports = TreeShakingOptimizer;
