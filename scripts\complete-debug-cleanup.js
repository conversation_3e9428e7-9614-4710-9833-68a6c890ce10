#!/usr/bin/env node

/**
 * Complete Debug Statement Cleanup
 * Removes remaining console.log, console.debug, TODO, FIXME comments
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  sourceDirectories: ['app', 'components', 'lib', 'hooks'],
  excludePatterns: ['node_modules', '.next', '.git', 'dist', 'build', 'scripts'],
  fileExtensions: ['.ts', '.tsx', '.js', '.jsx'],
  debugPatterns: [
    /console\.(log|debug|info|warn)\s*\([^)]*\)\s*;?/g,
    /\/\/\s*(TODO|FIXME|HACK|XXX).*$/gm,
    /\/\*\s*(TODO|FIXME|HACK|XXX).*?\*\//gs,
    /debugger\s*;?/g
  ]
};

// Colors for output
const colors = {
  reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m',
  yellow: '\x1b[33m', blue: '\x1b[34m', cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(message, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

// Get all files recursively
function getAllFiles(dir, extensions, excludePatterns) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      for (const item of items) {
        const fullPath = path.join(currentDir, item);
        const relativePath = path.relative(process.cwd(), fullPath);
        
        // Skip excluded patterns
        if (excludePatterns.some(pattern => relativePath.includes(pattern))) {
          continue;
        }
        
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          traverse(fullPath);
        } else if (stat.isFile() && extensions.some(ext => item.endsWith(ext))) {
          files.push({
            path: fullPath,
            relativePath,
            name: item,
            size: stat.size
          });
        }
      }
    } catch (error) {
      logWarning(`Could not read directory ${currentDir}: ${error.message}`);
    }
  }
  
  traverse(dir);
  return files;
}

// Find debug statements in a file
function findDebugStatements(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const findings = [];
    
    CONFIG.debugPatterns.forEach((pattern, index) => {
      const matches = [...content.matchAll(pattern)];
      matches.forEach(match => {
        const lines = content.substring(0, match.index).split('\n');
        findings.push({
          type: ['console', 'comment', 'comment', 'debugger'][index],
          match: match[0],
          line: lines.length,
          column: lines[lines.length - 1].length + 1
        });
      });
    });
    
    return findings;
  } catch (error) {
    logWarning(`Could not read file ${filePath}: ${error.message}`);
    return [];
  }
}

// Clean debug statements from a file
function cleanDebugStatements(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let cleaned = false;
    let removedCount = 0;
    
    CONFIG.debugPatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        content = content.replace(pattern, '');
        removedCount += matches.length;
        cleaned = true;
      }
    });
    
    if (cleaned) {
      // Clean up empty lines and extra whitespace
      content = content
        .replace(/\n\s*\n\s*\n/g, '\n\n') // Remove multiple empty lines
        .replace(/^\s*\n/gm, '') // Remove empty lines at start of blocks
        .trim() + '\n'; // Ensure file ends with newline
      
      fs.writeFileSync(filePath, content, 'utf8');
      return removedCount;
    }
    
    return 0;
  } catch (error) {
    logError(`Could not clean file ${filePath}: ${error.message}`);
    return 0;
  }
}

// Main cleanup function
function performDebugCleanup() {
  logHeader('Complete Debug Statement Cleanup');
  
  let totalFiles = 0;
  let cleanedFiles = 0;
  let totalRemoved = 0;
  const results = [];
  
  CONFIG.sourceDirectories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      logWarning(`Directory ${dir} does not exist, skipping...`);
      return;
    }
    
    log(`\nScanning directory: ${dir}`, 'blue');
    const files = getAllFiles(dir, CONFIG.fileExtensions, CONFIG.excludePatterns);
    totalFiles += files.length;
    
    files.forEach(file => {
      const findings = findDebugStatements(file.path);
      
      if (findings.length > 0) {
        log(`\nFound ${findings.length} debug statements in ${file.relativePath}:`, 'yellow');
        findings.forEach(finding => {
          log(`  Line ${finding.line}: ${finding.type} - ${finding.match.substring(0, 60)}...`, 'yellow');
        });
        
        const removed = cleanDebugStatements(file.path);
        if (removed > 0) {
          cleanedFiles++;
          totalRemoved += removed;
          logSuccess(`Cleaned ${removed} debug statements from ${file.relativePath}`);
          
          results.push({
            file: file.relativePath,
            removed,
            size: file.size
          });
        }
      }
    });
  });
  
  // Generate summary
  logHeader('Cleanup Summary');
  logSuccess(`Total files scanned: ${totalFiles}`);
  logSuccess(`Files cleaned: ${cleanedFiles}`);
  logSuccess(`Debug statements removed: ${totalRemoved}`);
  
  if (results.length > 0) {
    log('\nCleaned files:', 'blue');
    results.forEach(result => {
      log(`  ${result.file}: ${result.removed} statements removed`, 'green');
    });
    
    const totalSizeReduction = results.reduce((sum, result) => sum + (result.removed * 20), 0); // Estimate 20 bytes per statement
    log(`\nEstimated size reduction: ${totalSizeReduction} bytes`, 'green');
  }
  
  return {
    totalFiles,
    cleanedFiles,
    totalRemoved,
    results
  };
}

// Specific file cleanup (for manual targeting)
function cleanSpecificFiles(filePaths) {
  logHeader('Cleaning Specific Files');
  
  let totalRemoved = 0;
  
  filePaths.forEach(filePath => {
    if (fs.existsSync(filePath)) {
      const findings = findDebugStatements(filePath);
      if (findings.length > 0) {
        log(`\nCleaning ${filePath}...`, 'blue');
        const removed = cleanDebugStatements(filePath);
        totalRemoved += removed;
        logSuccess(`Removed ${removed} debug statements`);
      } else {
        log(`No debug statements found in ${filePath}`, 'green');
      }
    } else {
      logWarning(`File not found: ${filePath}`);
    }
  });
  
  return totalRemoved;
}

// Main execution
if (require.main === module) {
  try {
    const args = process.argv.slice(2);
    
    if (args.length > 0) {
      // Clean specific files
      cleanSpecificFiles(args);
    } else {
      // Clean all files
      const results = performDebugCleanup();
      
      if (results.totalRemoved === 0) {
        logSuccess('🎉 No debug statements found! Codebase is clean.');
      } else {
        logSuccess(`🧹 Cleanup complete! Removed ${results.totalRemoved} debug statements from ${results.cleanedFiles} files.`);
      }
    }
    
    process.exit(0);
  } catch (error) {
    logError(`Cleanup failed: ${error.message}`);
    process.exit(1);
  }
}
