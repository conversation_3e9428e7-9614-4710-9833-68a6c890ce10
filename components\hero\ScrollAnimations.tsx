'use client';

import React, { useEffect, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { cn } from '@/lib/utils';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface ScrollRevealProps {
  children: React.ReactNode;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right';
  delay?: number;
  duration?: number;
  distance?: number;
  stagger?: number;
}

interface ParallaxElementProps {
  children: React.ReactNode;
  className?: string;
  speed?: number;
  direction?: 'vertical' | 'horizontal';
}

interface StaggeredRevealProps {
  children: React.ReactNode[];
  className?: string;
  staggerDelay?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
}

/**
 * Scroll-triggered reveal animation with GSAP
 */
export function ScrollReveal({
  children,
  className = '',
  direction = 'up',
  delay = 0,
  duration = 0.8,
  distance = 50,
  stagger = 0
}: ScrollRevealProps) {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (prefersReducedMotion) {
      // Skip animations for users who prefer reduced motion
      gsap.set(element, { opacity: 1 });
      return;
    }

    const getInitialTransform = () => {
      switch (direction) {
        case 'up':
          return { y: distance, opacity: 0 };
        case 'down':
          return { y: -distance, opacity: 0 };
        case 'left':
          return { x: distance, opacity: 0 };
        case 'right':
          return { x: -distance, opacity: 0 };
        default:
          return { y: distance, opacity: 0 };
      }
    };

    const getFinalTransform = () => {
      switch (direction) {
        case 'up':
        case 'down':
          return { y: 0, opacity: 1 };
        case 'left':
        case 'right':
          return { x: 0, opacity: 1 };
        default:
          return { y: 0, opacity: 1 };
      }
    };

    // Set initial state
    gsap.set(element, getInitialTransform());

    // Create scroll trigger animation
    const animation = gsap.to(element, {
      ...getFinalTransform(),
      duration,
      delay,
      ease: "power3.out",
      scrollTrigger: {
        trigger: element,
        start: "top 85%",
        end: "bottom 15%",
        toggleActions: "play none none reverse",
        once: false
      }
    });

    // Handle staggered children if specified
    if (stagger > 0) {
      const children = element.children;
      if (children.length > 0) {
        gsap.set(children, getInitialTransform());
        gsap.to(children, {
          ...getFinalTransform(),
          duration,
          stagger,
          ease: "power3.out",
          scrollTrigger: {
            trigger: element,
            start: "top 85%",
            toggleActions: "play none none reverse"
          }
        });
      }
    }

    return () => {
      animation.kill();
      ScrollTrigger.getAll().forEach(trigger => {
        if (trigger.trigger === element) {
          trigger.kill();
        }
      });
    };
  }, [direction, delay, duration, distance, stagger]);

  return (
    <div ref={elementRef} className={className}>
      {children}
    </div>
  );
}

/**
 * Parallax effect component
 */
export function ParallaxElement({
  children,
  className = '',
  speed = 0.5,
  direction = 'vertical'
}: ParallaxElementProps) {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (prefersReducedMotion) {
      return;
    }

    const animation = gsap.to(element, {
      [direction === 'vertical' ? 'y' : 'x']: () => -ScrollTrigger.maxScroll(window) * speed,
      ease: "none",
      scrollTrigger: {
        trigger: element,
        start: "top bottom",
        end: "bottom top",
        scrub: true,
        invalidateOnRefresh: true
      }
    });

    return () => {
      animation.kill();
    };
  }, [speed, direction]);

  return (
    <div ref={elementRef} className={className}>
      {children}
    </div>
  );
}

/**
 * Staggered reveal animation for multiple elements
 */
export function StaggeredReveal({
  children,
  className = '',
  staggerDelay = 0.1,
  direction = 'up'
}: StaggeredRevealProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const elements = container.children;
    if (elements.length === 0) return;

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (prefersReducedMotion) {
      gsap.set(elements, { opacity: 1 });
      return;
    }

    const getInitialTransform = () => {
      switch (direction) {
        case 'up':
          return { y: 30, opacity: 0 };
        case 'down':
          return { y: -30, opacity: 0 };
        case 'left':
          return { x: 30, opacity: 0 };
        case 'right':
          return { x: -30, opacity: 0 };
        default:
          return { y: 30, opacity: 0 };
      }
    };

    const getFinalTransform = () => {
      switch (direction) {
        case 'up':
        case 'down':
          return { y: 0, opacity: 1 };
        case 'left':
        case 'right':
          return { x: 0, opacity: 1 };
        default:
          return { y: 0, opacity: 1 };
      }
    };

    // Set initial state
    gsap.set(elements, getInitialTransform());

    // Create staggered animation
    const timeline = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: "top 85%",
        toggleActions: "play none none reverse"
      }
    });

    timeline.to(elements, {
      ...getFinalTransform(),
      duration: 0.6,
      stagger: staggerDelay,
      ease: "power3.out"
    });

    return () => {
      timeline.kill();
    };
  }, [staggerDelay, direction]);

  return (
    <div ref={containerRef} className={className}>
      {children.map((child, index) => (
        <div key={index}>{child}</div>
      ))}
    </div>
  );
}

/**
 * Progressive content reveal with custom timing
 */
export function ProgressiveReveal({
  children,
  className = ''
}: {
  children: React.ReactNode;
  className?: string;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  return (
    <motion.div
      ref={ref}
      className={className}
      initial={{ opacity: 0 }}
      animate={isInView ? { opacity: 1 } : { opacity: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
    >
      {children}
    </motion.div>
  );
}

/**
 * Text reveal animation with character stagger
 */
export function TextReveal({
  text,
  className = '',
  delay = 0,
  staggerDelay = 0.03
}: {
  text: string;
  className?: string;
  delay?: number;
  staggerDelay?: number;
}) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (prefersReducedMotion) {
      container.style.opacity = '1';
      return;
    }

    const chars = container.querySelectorAll('.char');
    
    gsap.set(chars, { y: 20, opacity: 0 });

    const animation = gsap.to(chars, {
      y: 0,
      opacity: 1,
      duration: 0.6,
      stagger: staggerDelay,
      delay,
      ease: "power3.out",
      scrollTrigger: {
        trigger: container,
        start: "top 85%",
        toggleActions: "play none none reverse"
      }
    });

    return () => {
      animation.kill();
    };
  }, [delay, staggerDelay]);

  return (
    <div ref={containerRef} className={className}>
      {text.split('').map((char, index) => (
        <span key={index} className="char inline-block">
          {char === ' ' ? '\u00A0' : char}
        </span>
      ))}
    </div>
  );
}

/**
 * Scroll-triggered counter animation
 */
export function AnimatedCounter({
  from = 0,
  to,
  duration = 2,
  className = '',
  suffix = '',
  prefix = ''
}: {
  from?: number;
  to: number;
  duration?: number;
  className?: string;
  suffix?: string;
  prefix?: string;
}) {
  const counterRef = useRef<HTMLSpanElement>(null);
  const [count, setCount] = React.useState(from);

  useEffect(() => {
    const element = counterRef.current;
    if (!element) return;

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (prefersReducedMotion) {
      setCount(to);
      return;
    }

    const animation = gsap.to({ value: from }, {
      value: to,
      duration,
      ease: "power2.out",
      onUpdate: function() {
        setCount(Math.round(this.targets()[0].value));
      },
      scrollTrigger: {
        trigger: element,
        start: "top 85%",
        toggleActions: "play none none reverse"
      }
    });

    return () => {
      animation.kill();
    };
  }, [from, to, duration]);

  return (
    <span ref={counterRef} className={className}>
      {prefix}{count.toLocaleString()}{suffix}
    </span>
  );
}

/**
 * Floating animation for background elements
 */
export function FloatingElement({
  children,
  className = '',
  amplitude = 10,
  duration = 3,
  delay = 0
}: {
  children: React.ReactNode;
  className?: string;
  amplitude?: number;
  duration?: number;
  delay?: number;
}) {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    
    if (prefersReducedMotion) {
      return;
    }

    const animation = gsap.to(element, {
      y: -amplitude,
      duration,
      delay,
      ease: "power2.inOut",
      yoyo: true,
      repeat: -1
    });

    return () => {
      animation.kill();
    };
  }, [amplitude, duration, delay]);

  return (
    <div ref={elementRef} className={className}>
      {children}
    </div>
  );
}
