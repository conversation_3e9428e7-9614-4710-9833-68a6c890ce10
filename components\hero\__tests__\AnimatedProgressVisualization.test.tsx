/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { AnimatedProgressVisualization } from '../AnimatedProgressVisualization';

// Mock GSAP
jest.mock('gsap', () => ({
  gsap: {
    set: jest.fn(),
    to: jest.fn(() => ({ kill: jest.fn() })),
    fromTo: jest.fn(() => ({ kill: jest.fn() })),
    registerPlugin: jest.fn(),
  },
  ScrollTrigger: {
    maxScroll: jest.fn(() => 1000),
    getAll: jest.fn(() => []),
  },
}));

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    circle: ({ children, ...props }: any) => <circle {...props}>{children}</circle>,
  },
}));

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});
window.IntersectionObserver = mockIntersectionObserver;

// Mock matchMedia for reduced motion
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: query === '(prefers-reduced-motion: reduce)' ? false : true,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

describe('AnimatedProgressVisualization', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders with default props', () => {
    render(<AnimatedProgressVisualization />);
    
    const container = screen.getByRole('region', { name: 'Live platform metrics' });
    expect(container).toBeInTheDocument();
    
    expect(screen.getByText('Live Activity')).toBeInTheDocument();
    expect(screen.getByText('Updates every few seconds')).toBeInTheDocument();
  });

  it('displays all metric categories', async () => {
    render(<AnimatedProgressVisualization />);
    
    await waitFor(() => {
      expect(screen.getByText('Learners Online')).toBeInTheDocument();
      expect(screen.getByText('Courses Completed Today')).toBeInTheDocument();
      expect(screen.getByText('Smart Contracts This Week')).toBeInTheDocument();
      expect(screen.getByText('Success Rate')).toBeInTheDocument();
    });
  });

  it('shows metric values when showValues is true', async () => {
    render(<AnimatedProgressVisualization showValues={true} />);
    
    await waitFor(() => {
      // Should show numeric values
      expect(screen.getByText('127')).toBeInTheDocument();
      expect(screen.getByText('34')).toBeInTheDocument();
      expect(screen.getByText('156')).toBeInTheDocument();
      expect(screen.getByText('94%')).toBeInTheDocument();
    });
  });

  it('hides metric values when showValues is false', () => {
    render(<AnimatedProgressVisualization showValues={false} />);
    
    // Should not show specific numeric values
    expect(screen.queryByText('127')).not.toBeInTheDocument();
    expect(screen.queryByText('34')).not.toBeInTheDocument();
  });

  it('shows labels when showLabels is true', () => {
    render(<AnimatedProgressVisualization showLabels={true} />);
    
    expect(screen.getByText('Learners Online')).toBeInTheDocument();
    expect(screen.getByText('Courses Completed Today')).toBeInTheDocument();
  });

  it('hides labels when showLabels is false', () => {
    render(<AnimatedProgressVisualization showLabels={false} />);
    
    expect(screen.queryByText('Learners Online')).not.toBeInTheDocument();
    expect(screen.queryByText('Courses Completed Today')).not.toBeInTheDocument();
  });

  it('applies horizontal variant styling', () => {
    render(<AnimatedProgressVisualization variant="horizontal" />);
    
    const container = screen.getByRole('region');
    expect(container).toBeInTheDocument();
  });

  it('applies vertical variant styling', () => {
    render(<AnimatedProgressVisualization variant="vertical" />);
    
    const container = screen.getByRole('region');
    expect(container).toBeInTheDocument();
  });

  it('applies circular variant styling', () => {
    render(<AnimatedProgressVisualization variant="circular" />);
    
    const container = screen.getByRole('region');
    expect(container).toBeInTheDocument();
    
    // Should render SVG circles for circular variant
    const circles = container.querySelectorAll('circle');
    expect(circles.length).toBeGreaterThan(0);
  });

  it('sets up intersection observer for animation trigger', () => {
    render(<AnimatedProgressVisualization />);
    
    expect(mockIntersectionObserver).toHaveBeenCalledWith(
      expect.any(Function),
      { threshold: 0.3 }
    );
  });

  it('respects reduced motion preferences', () => {
    // Mock reduced motion preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    render(<AnimatedProgressVisualization respectReducedMotion={true} />);
    
    // Should still render but skip animations
    expect(screen.getByText('Live Activity')).toBeInTheDocument();
  });

  it('updates metrics over time', async () => {
    render(<AnimatedProgressVisualization updateInterval={1000} />);
    
    // Fast-forward time to trigger updates
    jest.advanceTimersByTime(5000);
    
    // Should still be rendering metrics
    expect(screen.getByText('Live Activity')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<AnimatedProgressVisualization className="custom-progress" />);
    
    const container = screen.getByRole('region');
    expect(container).toHaveClass('custom-progress');
  });

  it('displays metric descriptions', async () => {
    render(<AnimatedProgressVisualization showLabels={true} />);
    
    await waitFor(() => {
      expect(screen.getByText('Developers currently learning')).toBeInTheDocument();
      expect(screen.getByText('Courses finished today')).toBeInTheDocument();
      expect(screen.getByText('Contracts deployed this week')).toBeInTheDocument();
      expect(screen.getByText('Course completion rate')).toBeInTheDocument();
    });
  });

  it('renders progress bars for horizontal variant', () => {
    const { container } = render(<AnimatedProgressVisualization variant="horizontal" />);
    
    // Should have progress bar elements
    const progressBars = container.querySelectorAll('[class*="bg-gradient-to-r"]');
    expect(progressBars.length).toBeGreaterThan(0);
  });

  it('renders circular progress for circular variant', () => {
    const { container } = render(<AnimatedProgressVisualization variant="circular" />);
    
    // Should have SVG elements for circular progress
    const svgElements = container.querySelectorAll('svg');
    expect(svgElements.length).toBeGreaterThan(0);
  });

  it('displays live activity indicator', () => {
    render(<AnimatedProgressVisualization />);
    
    // Should show pulsing live indicator
    const liveIndicator = screen.getByText('Live Activity');
    expect(liveIndicator).toBeInTheDocument();
  });

  it('shows update frequency information', () => {
    render(<AnimatedProgressVisualization />);
    
    expect(screen.getByText('Updates every few seconds')).toBeInTheDocument();
  });

  it('handles animation duration prop', () => {
    render(<AnimatedProgressVisualization animationDuration={5000} />);
    
    // Should render without errors
    expect(screen.getByText('Live Activity')).toBeInTheDocument();
  });

  it('cleans up intervals on unmount', () => {
    const { unmount } = render(<AnimatedProgressVisualization />);
    
    // Should not throw errors when unmounting
    expect(() => unmount()).not.toThrow();
  });

  it('has proper accessibility attributes', () => {
    render(<AnimatedProgressVisualization />);
    
    const container = screen.getByRole('region', { name: 'Live platform metrics' });
    expect(container).toBeInTheDocument();
  });

  it('displays icons for each metric', () => {
    const { container } = render(<AnimatedProgressVisualization />);
    
    // Should have icon elements (SVGs from Lucide)
    const icons = container.querySelectorAll('svg');
    expect(icons.length).toBeGreaterThan(0);
  });

  it('applies gradient styling to progress elements', () => {
    const { container } = render(<AnimatedProgressVisualization variant="horizontal" />);
    
    // Should have gradient classes
    const gradientElements = container.querySelectorAll('[class*="gradient"]');
    expect(gradientElements.length).toBeGreaterThan(0);
  });

  it('handles zero values gracefully', async () => {
    render(<AnimatedProgressVisualization />);
    
    // Should render even with zero initial values
    expect(screen.getByText('Live Activity')).toBeInTheDocument();
  });

  it('maintains consistent metric ordering', async () => {
    render(<AnimatedProgressVisualization />);
    
    await waitFor(() => {
      const labels = screen.getAllByText(/Learners Online|Courses Completed|Smart Contracts|Success Rate/);
      expect(labels).toHaveLength(4);
    });
  });

  it('displays proper units for each metric', async () => {
    render(<AnimatedProgressVisualization showValues={true} />);
    
    await waitFor(() => {
      // Success rate should show percentage
      expect(screen.getByText('94%')).toBeInTheDocument();
      // Others should show numbers without units or with +
      expect(screen.getByText('127')).toBeInTheDocument();
    });
  });
});
