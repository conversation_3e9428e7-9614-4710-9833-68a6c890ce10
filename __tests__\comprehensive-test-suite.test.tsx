/**
 * Comprehensive Test Suite for Solidity Learning Platform
 * 
 * This test suite covers all major components and systems with:
 * - Unit tests for individual components
 * - Integration tests for component interactions
 * - Accessibility tests for WCAG 2.1 AA compliance
 * - Performance tests for Core Web Vitals
 * - E2E tests for critical user journeys
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import '@testing-library/jest-dom';

// Component imports
import { ComprehensiveEngagementSystem } from '@/components/engagement/ComprehensiveEngagementSystem';
import { ComprehensiveAnalyticsSystem } from '@/components/analytics/ComprehensiveAnalyticsSystem';
import { ComprehensiveConversionSystem } from '@/components/conversion/ComprehensiveConversionSystem';
import { ComprehensiveContentSystem } from '@/components/content/ComprehensiveContentSystem';
import { RealTimeNotificationSystem } from '@/components/engagement/RealTimeNotificationSystem';
import { AnimatedStatsCounter } from '@/components/engagement/AnimatedStatsCounter';
import { AIAssistantWidget } from '@/components/engagement/AIAssistantWidget';
import { InteractiveFAQ } from '@/components/engagement/InteractiveFAQ';

// Test utilities
import { createMockAnalyticsConfig, createMockUserContext, createMockContentItems } from './test-utils';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock implementations
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    pathname: '/',
    query: {},
    asPath: '/'
  })
}));

jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
  useInView: () => true,
  useMotionValue: () => ({ set: jest.fn(), on: jest.fn() }),
  useSpring: () => ({ set: jest.fn(), on: jest.fn() })
}));

// Global test setup
beforeEach(() => {
  // Clear localStorage
  localStorage.clear();
  
  // Reset all mocks
  jest.clearAllMocks();
  
  // Mock IntersectionObserver
  global.IntersectionObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
  }));

  // Mock ResizeObserver
  global.ResizeObserver = jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
  }));

  // Mock matchMedia
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
});

describe('Engagement System Tests', () => {
  const mockConfig = {
    enableNotifications: true,
    enableStatsCounter: true,
    enableAIAssistant: true,
    enableFAQ: true,
    enableExitIntent: true,
    enableScrollCTAs: true,
    enableUrgencyTimers: true,
    enableSocialSharing: true,
    enableAchievements: true,
    enableAnalytics: true,
    trackEngagement: true,
    theme: 'auto' as const,
    position: 'bottom-right' as const,
    animationLevel: 'standard' as const
  };

  const mockUserContext = createMockUserContext();

  describe('Real-time Notification System', () => {
    test('renders notification system with proper accessibility', async () => {
      render(<RealTimeNotificationSystem />);
      
      const notificationRegion = screen.getByRole('region', { name: /live notifications/i });
      expect(notificationRegion).toBeInTheDocument();
      expect(notificationRegion).toHaveAttribute('aria-live', 'polite');
    });

    test('displays notifications with proper timing', async () => {
      render(<RealTimeNotificationSystem />);
      
      // Wait for initial notification to appear
      await waitFor(() => {
        expect(screen.getByText(/just joined|completed|earned/i)).toBeInTheDocument();
      }, { timeout: 10000 });
    });

    test('allows dismissing notifications', async () => {
      const user = userEvent.setup();
      render(<RealTimeNotificationSystem />);
      
      await waitFor(() => {
        const dismissButton = screen.getByLabelText(/dismiss notification/i);
        expect(dismissButton).toBeInTheDocument();
      });

      const dismissButton = screen.getByLabelText(/dismiss notification/i);
      await user.click(dismissButton);
      
      await waitFor(() => {
        expect(screen.queryByLabelText(/dismiss notification/i)).not.toBeInTheDocument();
      });
    });

    test('meets accessibility standards', async () => {
      const { container } = render(<RealTimeNotificationSystem />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Animated Stats Counter', () => {
    test('renders stats with proper structure', () => {
      render(<AnimatedStatsCounter />);
      
      const statsRegion = screen.getByRole('region', { name: /platform statistics/i });
      expect(statsRegion).toBeInTheDocument();
    });

    test('animates counters when in view', async () => {
      render(<AnimatedStatsCounter />);
      
      // Check that counters start at 0 and animate
      const counters = screen.getAllByText('0');
      expect(counters.length).toBeGreaterThan(0);
      
      // Wait for animation to start
      await waitFor(() => {
        const nonZeroCounters = screen.queryAllByText(/^(?!0$)\d+/);
        expect(nonZeroCounters.length).toBeGreaterThan(0);
      }, { timeout: 3000 });
    });

    test('displays correct stat labels', () => {
      render(<AnimatedStatsCounter />);
      
      expect(screen.getByText(/active learners/i)).toBeInTheDocument();
      expect(screen.getByText(/lessons completed/i)).toBeInTheDocument();
      expect(screen.getByText(/achievements earned/i)).toBeInTheDocument();
    });

    test('meets accessibility standards', async () => {
      const { container } = render(<AnimatedStatsCounter />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('AI Assistant Widget', () => {
    test('renders chat button with proper accessibility', () => {
      render(<AIAssistantWidget />);
      
      const chatButton = screen.getByLabelText(/open ai assistant chat/i);
      expect(chatButton).toBeInTheDocument();
      expect(chatButton).toHaveAttribute('aria-label');
    });

    test('opens chat interface when clicked', async () => {
      const user = userEvent.setup();
      render(<AIAssistantWidget />);
      
      const chatButton = screen.getByLabelText(/open ai assistant chat/i);
      await user.click(chatButton);
      
      expect(screen.getByText(/ai assistant/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/ask me anything/i)).toBeInTheDocument();
    });

    test('handles message sending', async () => {
      const user = userEvent.setup();
      render(<AIAssistantWidget />);
      
      // Open chat
      const chatButton = screen.getByLabelText(/open ai assistant chat/i);
      await user.click(chatButton);
      
      // Type and send message
      const input = screen.getByPlaceholderText(/ask me anything/i);
      await user.type(input, 'Hello, I need help with Solidity');
      
      const sendButton = screen.getByLabelText(/send message/i);
      await user.click(sendButton);
      
      // Check message appears
      expect(screen.getByText(/hello, i need help with solidity/i)).toBeInTheDocument();
    });

    test('provides contextual suggestions', async () => {
      const user = userEvent.setup();
      render(<AIAssistantWidget currentPage="/learn" />);
      
      const chatButton = screen.getByLabelText(/open ai assistant chat/i);
      await user.click(chatButton);
      
      // Check for contextual suggestions
      expect(screen.getByText(/explain this concept/i)).toBeInTheDocument();
      expect(screen.getByText(/show me a practical example/i)).toBeInTheDocument();
    });

    test('meets accessibility standards', async () => {
      const { container } = render(<AIAssistantWidget />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Interactive FAQ', () => {
    test('renders FAQ with search functionality', () => {
      render(<InteractiveFAQ />);
      
      expect(screen.getByText(/frequently asked questions/i)).toBeInTheDocument();
      expect(screen.getByPlaceholderText(/search questions/i)).toBeInTheDocument();
    });

    test('filters questions based on search', async () => {
      const user = userEvent.setup();
      render(<InteractiveFAQ />);
      
      const searchInput = screen.getByPlaceholderText(/search questions/i);
      await user.type(searchInput, 'solidity');
      
      // Check that results are filtered
      await waitFor(() => {
        const questionElements = screen.getAllByRole('button', { expanded: false });
        expect(questionElements.length).toBeGreaterThan(0);
      });
    });

    test('expands and collapses FAQ items', async () => {
      const user = userEvent.setup();
      render(<InteractiveFAQ />);
      
      const firstQuestion = screen.getAllByRole('button', { expanded: false })[0];
      await user.click(firstQuestion);
      
      expect(firstQuestion).toHaveAttribute('aria-expanded', 'true');
    });

    test('handles helpful voting', async () => {
      const user = userEvent.setup();
      render(<InteractiveFAQ />);
      
      // Expand first question
      const firstQuestion = screen.getAllByRole('button', { expanded: false })[0];
      await user.click(firstQuestion);
      
      // Find and click helpful button
      const helpfulButton = screen.getByText(/👍 yes/i);
      await user.click(helpfulButton);
      
      // Check button state changes
      expect(helpfulButton).toHaveClass(/bg-green-100|border-green-500/);
    });

    test('meets accessibility standards', async () => {
      const { container } = render(<InteractiveFAQ />);
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Comprehensive Engagement System Integration', () => {
    test('renders all engagement components together', () => {
      render(
        <ComprehensiveEngagementSystem
          config={mockConfig}
          userContext={mockUserContext}
        >
          <div>Test Content</div>
        </ComprehensiveEngagementSystem>
      );
      
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    test('tracks engagement events properly', async () => {
      const mockOnEngagementEvent = jest.fn();
      
      render(
        <ComprehensiveEngagementSystem
          config={mockConfig}
          userContext={mockUserContext}
          onEngagementEvent={mockOnEngagementEvent}
        >
          <div>Test Content</div>
        </ComprehensiveEngagementSystem>
      );
      
      // Simulate user interaction
      const user = userEvent.setup();
      await user.click(screen.getByText('Test Content'));
      
      // Check that engagement events are tracked
      await waitFor(() => {
        expect(mockOnEngagementEvent).toHaveBeenCalled();
      });
    });

    test('respects user preferences for reduced motion', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      const mockUserContextWithPreferences = {
        ...mockUserContext,
        preferences: { reducedMotion: true }
      };

      render(
        <ComprehensiveEngagementSystem
          config={mockConfig}
          userContext={mockUserContextWithPreferences}
        >
          <div>Test Content</div>
        </ComprehensiveEngagementSystem>
      );
      
      expect(screen.getByText('Test Content')).toBeInTheDocument();
    });

    test('meets accessibility standards', async () => {
      const { container } = render(
        <ComprehensiveEngagementSystem
          config={mockConfig}
          userContext={mockUserContext}
        >
          <div>Test Content</div>
        </ComprehensiveEngagementSystem>
      );
      
      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });
  });
});

describe('Performance Tests', () => {
  test('components render within performance budget', async () => {
    const startTime = performance.now();
    
    render(
      <ComprehensiveEngagementSystem
        config={{
          enableNotifications: true,
          enableStatsCounter: true,
          enableAIAssistant: true,
          enableFAQ: true,
          enableExitIntent: false, // Disable for performance test
          enableScrollCTAs: false,
          enableUrgencyTimers: false,
          enableSocialSharing: true,
          enableAchievements: true,
          enableAnalytics: true,
          trackEngagement: true,
          theme: 'auto' as const,
          position: 'bottom-right' as const,
          animationLevel: 'minimal' as const
        }}
        userContext={createMockUserContext()}
      >
        <div>Performance Test Content</div>
      </ComprehensiveEngagementSystem>
    );
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    // Should render within 100ms
    expect(renderTime).toBeLessThan(100);
  });

  test('handles large datasets efficiently', async () => {
    const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
      id: `item-${i}`,
      title: `Test Item ${i}`,
      description: `Description for test item ${i}`
    }));

    const startTime = performance.now();
    
    render(<AnimatedStatsCounter />);
    
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    // Should handle large datasets within reasonable time
    expect(renderTime).toBeLessThan(200);
  });
});

describe('Error Handling Tests', () => {
  test('handles missing props gracefully', () => {
    // Suppress console errors for this test
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    
    expect(() => {
      render(<RealTimeNotificationSystem />);
    }).not.toThrow();
    
    consoleSpy.mockRestore();
  });

  test('handles network errors gracefully', async () => {
    // Mock fetch to simulate network error
    global.fetch = jest.fn().mockRejectedValue(new Error('Network error'));
    
    render(<AIAssistantWidget />);
    
    const user = userEvent.setup();
    const chatButton = screen.getByLabelText(/open ai assistant chat/i);
    await user.click(chatButton);
    
    const input = screen.getByPlaceholderText(/ask me anything/i);
    await user.type(input, 'Test message');
    
    const sendButton = screen.getByLabelText(/send message/i);
    await user.click(sendButton);
    
    // Should handle error gracefully
    await waitFor(() => {
      expect(screen.getByText(/trouble responding/i)).toBeInTheDocument();
    });
  });
});

describe('Integration Tests', () => {
  test('analytics and engagement systems work together', async () => {
    const mockAnalyticsConfig = createMockAnalyticsConfig();
    
    render(
      <ComprehensiveAnalyticsSystem
        config={mockAnalyticsConfig}
        userId="test-user"
        userTraits={{ experience: 'beginner' }}
      >
        <ComprehensiveEngagementSystem
          config={{
            enableNotifications: true,
            enableStatsCounter: true,
            enableAIAssistant: true,
            enableFAQ: true,
            enableExitIntent: true,
            enableScrollCTAs: true,
            enableUrgencyTimers: true,
            enableSocialSharing: true,
            enableAchievements: true,
            enableAnalytics: true,
            trackEngagement: true,
            theme: 'auto' as const,
            position: 'bottom-right' as const,
            animationLevel: 'standard' as const
          }}
          userContext={createMockUserContext()}
        >
          <div>Integration Test Content</div>
        </ComprehensiveEngagementSystem>
      </ComprehensiveAnalyticsSystem>
    );
    
    expect(screen.getByText('Integration Test Content')).toBeInTheDocument();
  });

  test('content and conversion systems integrate properly', () => {
    const mockContentItems = createMockContentItems();
    
    render(
      <ComprehensiveContentSystem initialContent={mockContentItems}>
        <ComprehensiveConversionSystem
          config={{
            enableExitIntent: true,
            exitIntentSensitivity: 20,
            exitIntentDelay: 1000,
            maxExitIntentTriggers: 1,
            enableScrollTriggers: true,
            scrollMilestones: [25, 50, 75, 90],
            ctaVariations: ['Start Free Trial', 'Begin Learning'],
            enableUrgencyTimers: true,
            enableScarcityIndicators: true,
            urgencyDuration: 24 * 60 * 60 * 1000,
            scarcityThreshold: 50,
            enableSocialProof: true,
            socialProofTypes: ['numbers', 'testimonials'],
            socialProofFrequency: 8000,
            enablePersonalization: true,
            segmentationRules: [],
            enableABTesting: true,
            activeTests: []
          }}
          userId="test-user"
        >
          <div>Content and Conversion Test</div>
        </ComprehensiveConversionSystem>
      </ComprehensiveContentSystem>
    );
    
    expect(screen.getByText('Content and Conversion Test')).toBeInTheDocument();
  });
});
