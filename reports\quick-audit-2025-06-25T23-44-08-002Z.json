{"timestamp": "2025-06-25T23:44:08.002Z", "score": 70, "bundleAnalysis": {"javascript": 17315, "css": 0, "chunks": [{"name": "node_modules_axe-core_axe_bc1a5162.js", "size": 1916, "type": "javascript"}, {"name": "node_modules_5dad3b9f._.js", "size": 1096, "type": "javascript"}, {"name": "node_modules_next_dist_compiled_2ce9398a._.js", "size": 1084, "type": "javascript"}, {"name": "[root-of-the-server]__8df7605f._.js", "size": 1039, "type": "javascript"}, {"name": "[root-of-the-server]__e2c08166._.js", "size": 1019, "type": "javascript"}, {"name": "node_modules_next_dist_client_8f19e6fb._.js", "size": 972, "type": "javascript"}, {"name": "node_modules_react-dom_82bb97c6._.js", "size": 851, "type": "javascript"}, {"name": "node_modules_@tanstack_query-devtools_build_894e336e._.js", "size": 713, "type": "javascript"}, {"name": "node_modules_@tanstack_query-devtools_build_4f9de65b._.js", "size": 712, "type": "javascript"}, {"name": "components_dd8b7b2a._.js", "size": 604, "type": "javascript"}, {"name": "node_modules_ethers_lib_esm_4aa433fe._.js", "size": 601, "type": "javascript"}, {"name": "node_modules_framer-motion_dist_es_1315c24f._.js", "size": 508, "type": "javascript"}, {"name": "node_modules_d4df0f89._.js", "size": 460, "type": "javascript"}, {"name": "node_modules_ethers_lib_esm_providers_c516f8ef._.js", "size": 433, "type": "javascript"}, {"name": "components_3872a310._.js", "size": 426, "type": "javascript"}, {"name": "_6b4dc5b0._.js", "size": 381, "type": "javascript"}, {"name": "components_5eb8c8a1._.js", "size": 322, "type": "javascript"}, {"name": "lib_da9bce2f._.js", "size": 310, "type": "javascript"}, {"name": "_c7d1cac2._.js", "size": 298, "type": "javascript"}, {"name": "components_ui_fc742543._.js", "size": 286, "type": "javascript"}, {"name": "node_modules_zod_dist_esm_fc28aa98._.js", "size": 279, "type": "javascript"}, {"name": "node_modules_motion-dom_dist_es_fa3ea29e._.js", "size": 277, "type": "javascript"}, {"name": "node_modules_244dad55._.js", "size": 260, "type": "javascript"}, {"name": "node_modules_@radix-ui_69c59712._.js", "size": 228, "type": "javascript"}, {"name": "components_cb681af9._.js", "size": 201, "type": "javascript"}, {"name": "node_modules_ethers_lib_esm_abi_b5bc55b4._.js", "size": 195, "type": "javascript"}, {"name": "node_modules_next_fa761ad6._.js", "size": 166, "type": "javascript"}, {"name": "components_collaboration_df3e8073._.js", "size": 158, "type": "javascript"}, {"name": "_d8555adf._.js", "size": 154, "type": "javascript"}, {"name": "components_xp_0302e268._.js", "size": 153, "type": "javascript"}, {"name": "node_modules_@floating-ui_9ec1fa39._.js", "size": 136, "type": "javascript"}, {"name": "node_modules_@noble_curves_esm_cf8dbd06._.js", "size": 114, "type": "javascript"}, {"name": "node_modules_@tanstack_query-devtools_build_c02dd60e._.js", "size": 110, "type": "javascript"}, {"name": "node_modules_next_dist_build_polyfills_polyfill-nomodule.js", "size": 110, "type": "javascript"}, {"name": "node_modules_250f142e._.js", "size": 100, "type": "javascript"}, {"name": "node_modules_next_dist_2ecbf5fa._.js", "size": 94, "type": "javascript"}, {"name": "node_modules_03bc88a1._.js", "size": 70, "type": "javascript"}, {"name": "pages__app_2befdc56._.js", "size": 65, "type": "javascript"}, {"name": "pages__error_f498bf22._.js", "size": 65, "type": "javascript"}, {"name": "_93808211._.js", "size": 65, "type": "javascript"}, {"name": "_91fa056c._.js", "size": 51, "type": "javascript"}, {"name": "node_modules_b5ed7746._.js", "size": 43, "type": "javascript"}, {"name": "app_offline_page_tsx_09e82441._.js", "size": 38, "type": "javascript"}, {"name": "_119d267c._.js", "size": 35, "type": "javascript"}, {"name": "_15c77cfe._.js", "size": 35, "type": "javascript"}, {"name": "node_modules_lucide-react_dist_esm_icons_80e55afe._.js", "size": 18, "type": "javascript"}, {"name": "[root-of-the-server]__49fd8634._.js", "size": 15, "type": "javascript"}, {"name": "[root-of-the-server]__923cb372._.js", "size": 15, "type": "javascript"}, {"name": "[turbopack]_browser_dev_hmr-client_hmr-client_ts_61dcf9ba._.js", "size": 15, "type": "javascript"}, {"name": "node_modules_lucide-react_dist_esm_icons_12a585ed._.js", "size": 10, "type": "javascript"}, {"name": "node_modules_@swc_helpers_cjs_00636ac3._.js", "size": 3, "type": "javascript"}, {"name": "app_layout_tsx_c0237562._.js", "size": 1, "type": "javascript"}, {"name": "components_code_CodeLab_tsx_eb58f9be._.js", "size": 1, "type": "javascript"}, {"name": "components_learning_LearningDashboard_tsx_a0bf7ac2._.js", "size": 1, "type": "javascript"}, {"name": "node_modules_@tanstack_query-devtools_build_6b2d4729._.js", "size": 1, "type": "javascript"}, {"name": "[turbopack]_browser_dev_hmr-client_hmr-client_ts_fd44f5a4._.js", "size": 1, "type": "javascript"}, {"name": "_e69f0d32._.js", "size": 1, "type": "javascript"}, {"name": "app_achievements_page_tsx_4634e90f._.js", "size": 0, "type": "javascript"}, {"name": "app_code_page_tsx_4634e90f._.js", "size": 0, "type": "javascript"}, {"name": "app_collaborate_page_tsx_4634e90f._.js", "size": 0, "type": "javascript"}, {"name": "app_learn_page_tsx_4634e90f._.js", "size": 0, "type": "javascript"}, {"name": "app_not-found_tsx_4634e90f._.js", "size": 0, "type": "javascript"}, {"name": "app_offline_page_tsx_4634e90f._.js", "size": 0, "type": "javascript"}, {"name": "app_page_tsx_4634e90f._.js", "size": 0, "type": "javascript"}, {"name": "components_code_CodeLab_tsx_d116ad8c._.js", "size": 0, "type": "javascript"}, {"name": "components_learning_LearningDashboard_tsx_62460912._.js", "size": 0, "type": "javascript"}, {"name": "dd92d_modules_@tanstack_query-devtools_build_DevtoolsPanelComponent_JZI2RDCT_06dd0b32.js", "size": 0, "type": "javascript"}, {"name": "node_modules_@tanstack_query-devtools_build_DevtoolsComponent_HH7B3BHX_06dd0b32.js", "size": 0, "type": "javascript"}, {"name": "pages__app_5771e187._.js", "size": 0, "type": "javascript"}, {"name": "pages__error_5771e187._.js", "size": 0, "type": "javascript"}, {"name": "[turbopack]_browser_dev_hmr-client_hmr-client_ts_66796270._.js", "size": 0, "type": "javascript"}], "violations": [{"type": "javascript", "actual": 17315, "budget": 400, "severity": "high"}], "recommendations": ["Implement code splitting and remove unused JavaScript"]}, "dependencyAnalysis": {"production": 76, "development": 23, "heavy": [{"package": "monaco-editor", "description": "Code editor (heavy)"}, {"package": "three", "description": "3D graphics library (heavy)"}, {"package": "@google/genai", "description": "AI integration (heavy)"}, {"package": "framer-motion", "description": "Animation library (heavy)"}, {"package": "react-syntax-highlighter", "description": "Syntax highlighting (heavy)"}], "security": [], "recommendations": ["Consider reducing the number of dependencies", "Multiple heavy dependencies detected - consider lazy loading"]}, "accessibilityAnalysis": {"toolsInstalled": true, "ariaUsage": 238, "altTextUsage": 23, "semanticElements": 50, "recommendations": []}, "recommendations": ["Implement code splitting and remove unused JavaScript", "Consider reducing the number of dependencies", "Multiple heavy dependencies detected - consider lazy loading"]}