'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { motion, useMotionValue, useSpring, useTransform, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';
import { Code, Play, BookOpen, Users, Sparkles, Eye, Zap, Star, Trophy, Edit3, Copy, Download } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useXPNotifications } from '@/components/xp/XPNotification';
import { useMicroAchievements } from '@/components/gamification/MicroAchievements';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface InteractiveCardProps {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  previewContent?: React.ReactNode | (() => React.ReactNode);
  onClick?: () => void;
  className?: string;
  codeSnippet?: string;
  difficulty?: 'beginner' | 'intermediate' | 'advanced';
  xpReward?: number;
  achievementId?: string;
  enableGamification?: boolean;
  interactive?: boolean;
  category?: 'tutorial' | 'example' | 'challenge' | 'tool';
}

interface ParticleEffectProps {
  className?: string;
  particleCount?: number;
  color?: string;
}

/**
 * Interactive Card with Hover Effects and Preview Overlays
 */
export function InteractiveCard({
  title,
  description,
  icon: Icon,
  color,
  previewContent,
  onClick,
  className = '',
  codeSnippet,
  difficulty = 'beginner',
  xpReward = 10,
  achievementId,
  enableGamification = true,
  interactive = true,
  category = 'tutorial'
}: InteractiveCardProps) {
  const cardRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [isInteracted, setIsInteracted] = useState(false);
  const [showCodeEditor, setShowCodeEditor] = useState(false);
  const [editableCode, setEditableCode] = useState(codeSnippet || '');

  // Gamification hooks
  const { triggerXPGain } = useXPNotifications();
  const { triggerMicroAchievement } = useMicroAchievements();

  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const rotateX = useSpring(useTransform(y, [-100, 100], [30, -30]));
  const rotateY = useSpring(useTransform(x, [-100, 100], [-30, 30]));

  useEffect(() => {
    const card = cardRef.current;
    if (!card) return;

    const handleMouseMove = (e: MouseEvent) => {
      const rect = card.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      
      x.set((e.clientX - centerX) / 5);
      y.set((e.clientY - centerY) / 5);
    };

    const handleMouseLeave = () => {
      x.set(0);
      y.set(0);
    };

    card.addEventListener('mousemove', handleMouseMove);
    card.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      card.removeEventListener('mousemove', handleMouseMove);
      card.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [x, y]);

  // Gamification interaction handlers
  const handleCardInteraction = useCallback((interactionType: string) => {
    if (!enableGamification || isInteracted) return;

    setIsInteracted(true);

    // Award XP for interaction
    triggerXPGain(xpReward, 'exploration', `Explored ${title}`);

    // Trigger micro-achievement
    if (achievementId) {
      triggerMicroAchievement(achievementId);
    }

    // Category-specific achievements
    switch (category) {
      case 'tutorial':
        triggerMicroAchievement('tutorial-explorer');
        break;
      case 'example':
        triggerMicroAchievement('example-viewer');
        break;
      case 'challenge':
        triggerMicroAchievement('challenge-seeker');
        break;
      case 'tool':
        triggerMicroAchievement('tool-user');
        break;
    }
  }, [enableGamification, isInteracted, xpReward, title, achievementId, category, triggerXPGain, triggerMicroAchievement]);

  const handleCodeEdit = useCallback((newCode: string) => {
    setEditableCode(newCode);

    if (enableGamification && newCode !== codeSnippet) {
      triggerXPGain(5, 'coding', 'Modified code example');
      triggerMicroAchievement('code-modifier');
    }
  }, [enableGamification, codeSnippet, triggerXPGain, triggerMicroAchievement]);

  const handleCodeRun = useCallback(() => {
    if (enableGamification) {
      triggerXPGain(15, 'coding', 'Executed code example');
      triggerMicroAchievement('code-runner');
    }
  }, [enableGamification, triggerXPGain, triggerMicroAchievement]);

  const getDifficultyColor = () => {
    switch (difficulty) {
      case 'beginner':
        return 'text-green-400 bg-green-500/20';
      case 'intermediate':
        return 'text-yellow-400 bg-yellow-500/20';
      case 'advanced':
        return 'text-red-400 bg-red-500/20';
      default:
        return 'text-blue-400 bg-blue-500/20';
    }
  };

  return (
    <motion.div
      ref={cardRef}
      className={cn(
        'relative group cursor-pointer perspective-1000',
        className
      )}
      style={{
        rotateX,
        rotateY,
        transformStyle: 'preserve-3d'
      }}
      whileHover={{ scale: 1.05, z: 50 }}
      whileTap={{ scale: 0.95 }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      onClick={onClick}
    >
      {/* Main Card */}
      <motion.div
        className={cn(
          'glass p-6 rounded-xl border border-white/10 backdrop-blur-md bg-white/5',
          'hover:border-white/20 transition-all duration-300 relative overflow-hidden',
          'shadow-lg hover:shadow-2xl'
        )}
        animate={{
          boxShadow: isHovered 
            ? '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 30px rgba(59, 130, 246, 0.3)'
            : '0 10px 25px -5px rgba(0, 0, 0, 0.3)'
        }}
        transition={{ duration: 0.3 }}
      >
        {/* Animated background gradient */}
        <motion.div
          className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-500"
          animate={{
            background: isHovered
              ? `linear-gradient(45deg, ${color}20, transparent, ${color}20)`
              : 'transparent'
          }}
        />

        {/* Floating particles effect */}
        {isHovered && (
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className={cn('absolute w-1 h-1 rounded-full', color.replace('text-', 'bg-'))}
                initial={{ 
                  x: Math.random() * 100 + '%',
                  y: Math.random() * 100 + '%',
                  opacity: 0,
                  scale: 0
                }}
                animate={{
                  y: [null, '-20px'],
                  opacity: [0, 1, 0],
                  scale: [0, 1, 0]
                }}
                transition={{
                  duration: 2,
                  delay: i * 0.2,
                  repeat: Infinity,
                  repeatDelay: 1
                }}
              />
            ))}
          </div>
        )}

        {/* Difficulty Badge */}
        {enableGamification && (
          <div className="absolute top-3 left-3">
            <motion.div
              className={cn(
                'px-2 py-1 rounded-full text-xs font-medium border',
                getDifficultyColor()
              )}
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              {difficulty}
            </motion.div>
          </div>
        )}

        {/* XP Reward Badge */}
        {enableGamification && xpReward > 0 && (
          <div className="absolute top-3 right-3">
            <motion.div
              className="flex items-center space-x-1 px-2 py-1 rounded-full bg-yellow-500/20 border border-yellow-500/30 text-yellow-400 text-xs font-medium"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
              whileHover={{ scale: 1.1 }}
            >
              <Star className="w-3 h-3" />
              <span>+{xpReward} XP</span>
            </motion.div>
          </div>
        )}

        {/* Card Content */}
        <div className="relative z-10 flex flex-col items-center text-center space-y-4">
          <motion.div
            className={cn(
              'w-12 h-12 rounded-full flex items-center justify-center',
              'bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm'
            )}
            animate={{
              scale: isHovered ? 1.1 : 1,
              rotate: isHovered ? 5 : 0
            }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <Icon className={cn('w-6 h-6', color)} />
          </motion.div>

          <div>
            <div className="flex items-center justify-center space-x-2 mb-2">
              <h3 className="text-lg font-semibold text-white">{title}</h3>
              {isInteracted && enableGamification && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center"
                >
                  <Trophy className="w-2 h-2 text-white" />
                </motion.div>
              )}
            </div>
            <p className="text-sm text-gray-300 leading-relaxed">{description}</p>
          </div>

          {/* Interactive Action Buttons */}
          <div className={cn(
            'flex items-center space-x-2',
            'opacity-0 group-hover:opacity-100 transition-opacity duration-300'
          )}>
            {/* Preview Button */}
            {previewContent && (
              <motion.button
                className={cn(
                  'flex items-center space-x-2 px-3 py-1 rounded-full text-xs',
                  'bg-white/10 hover:bg-white/20 border border-white/20'
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  setShowPreview(true);
                  handleCardInteraction('preview');
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Eye className="w-3 h-3" />
                <span>Preview</span>
              </motion.button>
            )}

            {/* Code Editor Button */}
            {codeSnippet && interactive && (
              <motion.button
                className={cn(
                  'flex items-center space-x-2 px-3 py-1 rounded-full text-xs',
                  'bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 text-blue-400'
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  setShowCodeEditor(true);
                  handleCardInteraction('code_edit');
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Edit3 className="w-3 h-3" />
                <span>Edit Code</span>
              </motion.button>
            )}

            {/* Quick Action Button */}
            {interactive && (
              <motion.button
                className={cn(
                  'flex items-center space-x-2 px-3 py-1 rounded-full text-xs',
                  'bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 text-green-400'
                )}
                onClick={(e) => {
                  e.stopPropagation();
                  handleCardInteraction('quick_action');
                  if (onClick) onClick();
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Zap className="w-3 h-3" />
                <span>Try Now</span>
              </motion.button>
            )}
          </div>
        </div>

        {/* Shine effect */}
        <motion.div
          className="absolute inset-0 opacity-0 group-hover:opacity-100"
          animate={{
            background: isHovered
              ? 'linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%)'
              : 'transparent'
          }}
          transition={{ duration: 0.6 }}
        />
      </motion.div>

      {/* Preview Overlay */}
      {showPreview && previewContent && (
        <motion.div
          className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setShowPreview(false)}
        >
          <motion.div
            className="glass max-w-2xl w-full max-h-[80vh] overflow-auto rounded-xl border border-white/20 backdrop-blur-md bg-white/10 p-6"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-xl font-semibold text-white">{title} Preview</h3>
              <button
                onClick={() => setShowPreview(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                ✕
              </button>
            </div>
            {typeof previewContent === 'function' ? previewContent() : previewContent}
          </motion.div>
        </motion.div>
      )}

      {/* Code Editor Modal */}
      <AnimatePresence>
        {showCodeEditor && codeSnippet && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowCodeEditor(false)}
          >
            <motion.div
              className="glass max-w-4xl w-full max-h-[80vh] overflow-hidden rounded-xl border border-white/20 backdrop-blur-md bg-white/10"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="flex justify-between items-center p-4 border-b border-white/20">
                <div className="flex items-center space-x-3">
                  <Code className="w-5 h-5 text-blue-400" />
                  <h3 className="text-xl font-semibold text-white">Interactive Code Editor</h3>
                  <div className={cn(
                    'px-2 py-1 rounded text-xs font-medium',
                    getDifficultyColor()
                  )}>
                    {difficulty}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <EnhancedButton
                    onClick={handleCodeRun}
                    size="sm"
                    className="bg-green-600 hover:bg-green-700 text-white"
                  >
                    <Play className="w-4 h-4 mr-1" />
                    Run
                  </EnhancedButton>
                  <button
                    onClick={() => setShowCodeEditor(false)}
                    className="text-gray-400 hover:text-white transition-colors p-1"
                  >
                    ✕
                  </button>
                </div>
              </div>

              {/* Code Editor */}
              <div className="p-4">
                <div className="bg-gray-900 rounded-lg border border-gray-700 overflow-hidden">
                  <div className="flex items-center justify-between px-4 py-2 bg-gray-800 border-b border-gray-700">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => navigator.clipboard.writeText(editableCode)}
                        className="text-gray-400 hover:text-white transition-colors p-1"
                        title="Copy code"
                      >
                        <Copy className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => {
                          const blob = new Blob([editableCode], { type: 'text/plain' });
                          const url = URL.createObjectURL(blob);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = `${title.toLowerCase().replace(/\s+/g, '-')}.sol`;
                          a.click();
                          URL.revokeObjectURL(url);
                        }}
                        className="text-gray-400 hover:text-white transition-colors p-1"
                        title="Download code"
                      >
                        <Download className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                  <textarea
                    value={editableCode}
                    onChange={(e) => handleCodeEdit(e.target.value)}
                    className="w-full h-64 p-4 bg-gray-900 text-green-400 font-mono text-sm resize-none focus:outline-none"
                    placeholder="// Start coding here..."
                    spellCheck={false}
                  />
                </div>

                {/* Code Actions */}
                <div className="flex items-center justify-between mt-4">
                  <div className="flex items-center space-x-2 text-sm text-gray-400">
                    <span>Press Ctrl+Enter to run</span>
                    {enableGamification && (
                      <span className="text-yellow-400">• +5 XP for modifications</span>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <EnhancedButton
                      onClick={() => setEditableCode(codeSnippet || '')}
                      variant="ghost"
                      size="sm"
                      className="text-gray-400 hover:text-white"
                    >
                      Reset
                    </EnhancedButton>
                    <EnhancedButton
                      onClick={handleCodeRun}
                      size="sm"
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                    >
                      <Play className="w-4 h-4 mr-1" />
                      Execute Code
                    </EnhancedButton>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

/**
 * Particle Background Effect
 */
export function ParticleEffect({ 
  className = '', 
  particleCount = 50,
  color = '#3b82f6' 
}: ParticleEffectProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    // Create particles
    const particles: HTMLDivElement[] = [];
    
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-1 h-1 rounded-full opacity-30';
      particle.style.backgroundColor = color;
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
      
      container.appendChild(particle);
      particles.push(particle);

      // Animate particle
      gsap.to(particle, {
        y: -100,
        x: Math.random() * 100 - 50,
        opacity: 0,
        duration: Math.random() * 3 + 2,
        repeat: -1,
        delay: Math.random() * 2,
        ease: "power2.out"
      });
    }

    return () => {
      particles.forEach(particle => {
        if (particle.parentNode) {
          particle.parentNode.removeChild(particle);
        }
      });
    };
  }, [particleCount, color]);

  return (
    <div
      ref={containerRef}
      className={cn('absolute inset-0 pointer-events-none overflow-hidden', className)}
      aria-hidden="true"
    />
  );
}

/**
 * Interactive Features Grid
 */
export function InteractiveFeaturesGrid({ className = '' }: { className?: string }) {
  const features = [
    {
      title: 'AI-Powered Learning',
      description: 'Get personalized tutoring with advanced AI assistance and real-time code analysis',
      icon: Sparkles,
      color: 'text-purple-400',
      difficulty: 'beginner' as const,
      xpReward: 25,
      achievementId: 'ai-explorer',
      category: 'tutorial' as const,
      codeSnippet: `// AI-assisted smart contract
pragma solidity ^0.8.0;

contract AIOptimized {
    // AI suggests using memory for temporary data
    function processData(uint[] memory data) public pure returns (uint) {
        uint sum = 0;
        for (uint i = 0; i < data.length; i++) {
            sum += data[i];
        }
        return sum;
    }
}`,
      previewContent: () => (
        <div className="space-y-4">
          <div className="bg-gray-900 rounded-lg p-4">
            <div className="text-green-400 text-sm font-mono">
              {'> AI analyzing your code...'}
            </div>
            <div className="text-yellow-400 text-sm font-mono mt-2">
              {'✓ Optimization suggestion: Use memory instead of storage'}
            </div>
            <div className="text-blue-400 text-sm font-mono mt-1">
              {'✓ Gas cost reduced by 40%'}
            </div>
          </div>
          <p className="text-gray-300 text-sm">
            Our AI tutor provides instant feedback and suggestions to improve your Solidity code.
          </p>
        </div>
      )
    },
    {
      title: 'Interactive Coding',
      description: 'Write, compile, and deploy smart contracts in real-time with our integrated IDE',
      icon: Code,
      color: 'text-blue-400',
      difficulty: 'intermediate' as const,
      xpReward: 35,
      achievementId: 'code-master',
      category: 'tool' as const,
      interactive: true,
      codeSnippet: `pragma solidity ^0.8.0;

contract HelloWorld {
    string public message;

    constructor(string memory _message) {
        message = _message;
    }

    function updateMessage(string memory _newMessage) public {
        message = _newMessage;
    }

    function getMessage() public view returns (string memory) {
        return message;
    }
}`,
      previewContent: () => (
        <div className="space-y-4">
          <div className="bg-gray-900 rounded-lg p-4 font-mono text-sm">
            <div className="text-blue-400">pragma solidity ^0.8.0;</div>
            <div className="text-white mt-2">contract HelloWorld {</div>
            <div className="text-green-400 ml-4">string public message;</div>
            <div className="text-yellow-400 ml-4">constructor(string memory _message) {</div>
            <div className="text-white ml-8">message = _message;</div>
            <div className="text-yellow-400 ml-4">}</div>
            <div className="text-white">}</div>
          </div>
          <p className="text-gray-300 text-sm">
            Full-featured IDE with syntax highlighting, auto-completion, and instant compilation.
          </p>
        </div>
      )
    },
    {
      title: 'Live Collaboration',
      description: 'Code together with other developers in real-time collaborative sessions',
      icon: Users,
      color: 'text-green-400',
      difficulty: 'intermediate' as const,
      xpReward: 30,
      achievementId: 'collaborator',
      category: 'tool' as const,
      interactive: true,
      codeSnippet: `// Collaborative smart contract development
pragma solidity ^0.8.0;

contract CollaborativeProject {
    mapping(address => bool) public contributors;
    string[] public codeVersions;

    modifier onlyContributor() {
        require(contributors[msg.sender], "Not a contributor");
        _;
    }

    function addContributor(address _contributor) public onlyContributor {
        contributors[_contributor] = true;
    }

    function submitCode(string memory _code) public onlyContributor {
        codeVersions.push(_code);
    }
}`,
      previewContent: () => (
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
              A
            </div>
            <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-xs">
              B
            </div>
            <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center text-white text-xs">
              C
            </div>
            <span className="text-gray-300 text-sm">3 developers coding together</span>
          </div>
          <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-3">
            <div className="text-green-400 text-sm">✓ Real-time sync active</div>
            <div className="text-gray-300 text-xs mt-1">Live cursors, shared editing, voice chat</div>
          </div>
          <p className="text-gray-300 text-sm">
            Real-time collaboration with live cursors, shared editing, and voice chat.
          </p>
        </div>
      )
    },
    {
      title: 'Interactive Tutorials',
      description: 'Learn through hands-on tutorials with step-by-step guidance and instant feedback',
      icon: BookOpen,
      color: 'text-yellow-400',
      difficulty: 'beginner' as const,
      xpReward: 20,
      achievementId: 'tutorial-master',
      category: 'tutorial' as const,
      interactive: true,
      codeSnippet: `// Tutorial: Your first smart contract
pragma solidity ^0.8.0;

contract MyFirstContract {
    // Step 1: Declare a state variable
    uint256 public myNumber;

    // Step 2: Create a constructor
    constructor(uint256 _initialNumber) {
        myNumber = _initialNumber;
    }

    // Step 3: Add a function to update the number
    function updateNumber(uint256 _newNumber) public {
        myNumber = _newNumber;
    }

    // Step 4: Add a function to get the number
    function getNumber() public view returns (uint256) {
        return myNumber;
    }
}`,
      previewContent: () => (
        <div className="space-y-4">
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
            <h4 className="text-yellow-400 font-semibold mb-2">Step 3: Deploy Contract</h4>
            <p className="text-gray-300 text-sm">
              Now let's deploy your contract to the testnet. Click the deploy button below.
            </p>
            <div className="mt-3 flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-green-400 text-xs">Step completed: +20 XP earned</span>
            </div>
          </div>
          <div className="grid grid-cols-4 gap-2">
            <div className="h-2 bg-green-500 rounded"></div>
            <div className="h-2 bg-green-500 rounded"></div>
            <div className="h-2 bg-green-500 rounded"></div>
            <div className="h-2 bg-gray-600 rounded"></div>
          </div>
          <p className="text-gray-300 text-sm">
            Interactive tutorials guide you through each concept with practical examples and XP rewards.
          </p>
        </div>
      )
    }
  ];

  return (
    <div className={cn('grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6', className)}>
      {features.map((feature, index) => (
        <motion.div
          key={feature.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1, duration: 0.6 }}
        >
          <InteractiveCard
            {...feature}
            enableGamification={true}
            interactive={feature.interactive ?? true}
          />
        </motion.div>
      ))}
    </div>
  );
}
