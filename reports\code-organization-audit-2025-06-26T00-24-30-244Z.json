{"timestamp": "2025-06-26T00:24:30.237Z", "summary": {"totalSourceFiles": 432, "totalAssetFiles": 8, "unusedSourceFiles": 86, "unusedAssetFiles": 0, "totalExports": 1582, "unusedExports": 1568, "filesWithDeadCode": 342, "structureIssues": 2}, "findings": {"unusedFiles": {"source": [{"path": "components/achievements/AchievementNotificationSystem.tsx", "size": 15015, "lastModified": "2025-06-23T15:04:38.928Z", "confidence": "medium"}, {"path": "components/achievements/AchievementsPage.tsx", "size": 10586, "lastModified": "2025-06-23T15:07:11.423Z", "confidence": "medium"}, {"path": "components/AchievementsPage.tsx", "size": 5544, "lastModified": "2025-06-19T17:40:42.360Z", "confidence": "medium"}, {"path": "components/admin/CommunityControls.tsx", "size": 18305, "lastModified": "2025-06-24T14:17:12.120Z", "confidence": "medium"}, {"path": "components/admin/ContentVersionControl.tsx", "size": 20877, "lastModified": "2025-06-24T13:20:01.305Z", "confidence": "medium"}, {"path": "components/admin/PerformanceDashboard.tsx", "size": 9650, "lastModified": "2025-06-24T22:07:35.952Z", "confidence": "medium"}, {"path": "components/admin/SafetyConfirmation.tsx", "size": 28648, "lastModified": "2025-06-24T13:22:26.008Z", "confidence": "medium"}, {"path": "components/admin/UserAnalytics.tsx", "size": 11710, "lastModified": "2025-06-24T13:17:20.506Z", "confidence": "medium"}, {"path": "components/ai/AICodeAnalyzer.tsx", "size": 19938, "lastModified": "2025-06-21T14:39:04.694Z", "confidence": "medium"}, {"path": "components/ai/AIContractGenerator.tsx", "size": 30417, "lastModified": "2025-06-21T16:31:41.899Z", "confidence": "medium"}, {"path": "components/ai/AILearningPath.tsx", "size": 20955, "lastModified": "2025-06-21T17:58:39.257Z", "confidence": "medium"}, {"path": "components/ai/EnhancedAIAssistant.tsx", "size": 18979, "lastModified": "2025-06-21T17:23:52.759Z", "confidence": "medium"}, {"path": "components/auth/EnhancedLoginModal.tsx", "size": 16576, "lastModified": "2025-06-23T14:38:17.244Z", "confidence": "medium"}, {"path": "components/auth/PasswordResetModal.tsx", "size": 18300, "lastModified": "2025-06-23T23:36:30.452Z", "confidence": "medium"}, {"path": "components/auth/PasswordStrengthIndicator.tsx", "size": 9026, "lastModified": "2025-06-23T23:38:01.427Z", "confidence": "medium"}, {"path": "components/blockchain/BlockchainIntegration.tsx", "size": 17051, "lastModified": "2025-06-21T15:14:26.564Z", "confidence": "medium"}, {"path": "components/code/CodeLab.tsx", "size": 29464, "lastModified": "2025-06-23T13:29:08.391Z", "confidence": "medium"}, {"path": "components/collaboration/ComprehensiveCollaborationDashboard.tsx", "size": 19019, "lastModified": "2025-06-23T10:33:41.264Z", "confidence": "medium"}, {"path": "components/ConfirmationModal.tsx", "size": 2044, "lastModified": "2025-06-09T20:04:23.136Z", "confidence": "medium"}, {"path": "components/curriculum/PrerequisiteDisplay.tsx", "size": 10687, "lastModified": "2025-06-24T01:17:04.574Z", "confidence": "medium"}, {"path": "components/discovery/SmartTooltip.tsx", "size": 11336, "lastModified": "2025-06-24T23:01:23.287Z", "confidence": "medium"}, {"path": "components/editor/AdvancedIDEInterface.tsx", "size": 27366, "lastModified": "2025-06-25T00:29:34.779Z", "confidence": "medium"}, {"path": "components/error/ErrorBoundaryFallback.tsx", "size": 2452, "lastModified": "2025-06-22T14:56:25.538Z", "confidence": "medium"}, {"path": "components/error-handling/AsyncErrorBoundary.tsx", "size": 10683, "lastModified": "2025-06-25T02:59:28.926Z", "confidence": "medium"}, {"path": "components/forms/ContactForm.tsx", "size": 9446, "lastModified": "2025-06-23T23:02:44.520Z", "confidence": "medium"}, {"path": "components/GeminiChat.tsx", "size": 5837, "lastModified": "2025-06-19T17:40:42.361Z", "confidence": "medium"}, {"path": "components/help/ContextualTooltip.tsx", "size": 10267, "lastModified": "2025-06-24T22:55:36.089Z", "confidence": "medium"}, {"path": "components/LandingPage.tsx", "size": 10332, "lastModified": "2025-06-10T14:26:19.311Z", "confidence": "medium"}, {"path": "components/lazy/LazyComponents.tsx", "size": 9571, "lastModified": "2025-06-24T21:04:08.775Z", "confidence": "medium"}, {"path": "components/lazy/LazyMonacoEditor.tsx", "size": 4853, "lastModified": "2025-06-24T21:03:26.066Z", "confidence": "medium"}, {"path": "components/learning/ComprehensiveLearningPlatform.tsx", "size": 27503, "lastModified": "2025-06-23T14:32:57.744Z", "confidence": "medium"}, {"path": "components/learning/LearningDashboard.tsx", "size": 28796, "lastModified": "2025-06-23T13:52:46.007Z", "confidence": "medium"}, {"path": "components/MobileNavigation.tsx", "size": 5964, "lastModified": "2025-06-19T17:47:22.252Z", "confidence": "medium"}, {"path": "components/ModuleContent.tsx", "size": 11718, "lastModified": "2025-06-22T13:12:39.754Z", "confidence": "medium"}, {"path": "components/navigation/AuthenticatedNavbar.tsx", "size": 9109, "lastModified": "2025-06-23T21:13:49.778Z", "confidence": "medium"}, {"path": "components/navigation/GuidedOnboarding.tsx", "size": 16762, "lastModified": "2025-06-25T09:44:37.059Z", "confidence": "medium"}, {"path": "components/navigation/NavigationFlowOptimizer.tsx", "size": 15871, "lastModified": "2025-06-25T09:43:00.468Z", "confidence": "medium"}, {"path": "components/navigation/SmartNavigation.tsx", "size": 13459, "lastModified": "2025-06-25T09:41:45.981Z", "confidence": "medium"}, {"path": "components/onboarding/InteractiveTutorial.tsx", "size": 10416, "lastModified": "2025-06-24T22:59:30.247Z", "confidence": "medium"}, {"path": "components/profile/UserProfile.tsx", "size": 19213, "lastModified": "2025-06-23T16:27:08.824Z", "confidence": "medium"}, {"path": "components/progress/ProgressDashboard.tsx", "size": 33012, "lastModified": "2025-06-23T06:43:22.858Z", "confidence": "medium"}, {"path": "components/providers/FallbackProvider.tsx", "size": 9366, "lastModified": "2025-06-25T02:15:12.789Z", "confidence": "medium"}, {"path": "components/providers/SessionProvider.tsx", "size": 510, "lastModified": "2025-06-23T17:04:25.211Z", "confidence": "medium"}, {"path": "components/settings/__tests__/integration.test.tsx", "size": 12977, "lastModified": "2025-06-25T01:25:10.031Z", "confidence": "medium"}, {"path": "components/settings/__tests__/ProfileSection.test.tsx", "size": 6698, "lastModified": "2025-06-25T01:21:20.501Z", "confidence": "medium"}, {"path": "components/settings/__tests__/SecuritySection.test.tsx", "size": 10604, "lastModified": "2025-06-25T01:22:02.378Z", "confidence": "medium"}, {"path": "components/settings/__tests__/SettingsPage.test.tsx", "size": 9316, "lastModified": "2025-06-25T01:16:49.034Z", "confidence": "medium"}, {"path": "components/testing/FeedbackCollectionSystem.tsx", "size": 18824, "lastModified": "2025-06-23T07:43:27.691Z", "confidence": "medium"}, {"path": "components/testing/NotificationTestingPage.tsx", "size": 16552, "lastModified": "2025-06-24T23:48:10.113Z", "confidence": "medium"}, {"path": "components/testing/UATDashboard.tsx", "size": 20107, "lastModified": "2025-06-23T11:46:24.099Z", "confidence": "medium"}, {"path": "components/ui/AdvancedAnimations.tsx", "size": 11013, "lastModified": "2025-06-21T14:20:27.307Z", "confidence": "medium"}, {"path": "components/ui/AnimatedButton.tsx", "size": 3155, "lastModified": "2025-06-21T22:35:24.034Z", "confidence": "medium"}, {"path": "components/ui/AnimationShowcase.tsx", "size": 11689, "lastModified": "2025-06-21T18:32:34.710Z", "confidence": "medium"}, {"path": "components/ui/Branding.tsx", "size": 10267, "lastModified": "2025-06-19T18:08:02.911Z", "confidence": "medium"}, {"path": "components/ui/EnhancedButton.stories.tsx", "size": 10888, "lastModified": "2025-06-25T17:37:12.722Z", "confidence": "medium"}, {"path": "components/ui/EnhancedCard.tsx", "size": 1609, "lastModified": "2025-06-21T22:35:35.999Z", "confidence": "medium"}, {"path": "components/ui/EnhancedLoadingStates.tsx", "size": 13385, "lastModified": "2025-06-25T02:52:18.666Z", "confidence": "medium"}, {"path": "components/ui/EnhancedProgress.tsx", "size": 10325, "lastModified": "2025-06-23T16:27:27.233Z", "confidence": "medium"}, {"path": "components/ui/ErrorHandling.tsx", "size": 9437, "lastModified": "2025-06-21T18:32:48.146Z", "confidence": "medium"}, {"path": "components/ui/FeatureState.tsx", "size": 12427, "lastModified": "2025-06-25T02:09:23.337Z", "confidence": "medium"}, {"path": "components/ui/GlassCard.stories.tsx", "size": 7524, "lastModified": "2025-06-25T17:36:19.186Z", "confidence": "medium"}, {"path": "components/ui/GlassmorphismModal.tsx", "size": 3387, "lastModified": "2025-06-21T14:20:27.347Z", "confidence": "medium"}, {"path": "components/ui/GlassNeumorphDemo.tsx", "size": 11797, "lastModified": "2025-06-21T17:12:22.616Z", "confidence": "medium"}, {"path": "components/ui/index.ts", "size": 2684, "lastModified": "2025-06-23T23:26:30.782Z", "confidence": "medium"}, {"path": "components/ui/LazyLoadingComponents.tsx", "size": 10119, "lastModified": "2025-06-25T02:53:07.070Z", "confidence": "medium"}, {"path": "components/ui/NotificationCenter.tsx", "size": 16256, "lastModified": "2025-06-24T23:43:08.775Z", "confidence": "medium"}, {"path": "components/ui/OptimizedImage.tsx", "size": 7622, "lastModified": "2025-06-24T20:59:22.941Z", "confidence": "medium"}, {"path": "components/ui/PageTransition.tsx", "size": 5747, "lastModified": "2025-06-19T17:51:09.463Z", "confidence": "medium"}, {"path": "components/ui/SkeletonLoader.tsx", "size": 12240, "lastModified": "2025-06-25T02:48:30.379Z", "confidence": "medium"}, {"path": "components/ui/Typography.tsx", "size": 11898, "lastModified": "2025-06-23T16:29:05.753Z", "confidence": "medium"}, {"path": "components/ui/VisualFeedbackSystem.tsx", "size": 12988, "lastModified": "2025-06-23T16:28:46.449Z", "confidence": "medium"}, {"path": "lib/auth/navigationGuard.ts", "size": 10832, "lastModified": "2025-06-24T00:17:19.718Z", "confidence": "medium"}, {"path": "lib/config/secrets.ts", "size": 10458, "lastModified": "2025-06-23T10:49:36.970Z", "confidence": "medium"}, {"path": "lib/hooks/useEnhancedKeyboardNavigation.ts", "size": 8448, "lastModified": "2025-06-24T19:22:04.361Z", "confidence": "medium"}, {"path": "lib/hooks/useKeyboardNavigation.ts", "size": 8068, "lastModified": "2025-06-23T23:04:43.338Z", "confidence": "medium"}, {"path": "lib/hooks/useSolidityAnalyzer.ts", "size": 11431, "lastModified": "2025-06-25T00:21:51.504Z", "confidence": "medium"}, {"path": "lib/hooks/__tests__/useSettings.test.ts", "size": 9920, "lastModified": "2025-06-25T01:22:41.110Z", "confidence": "medium"}, {"path": "lib/security/headers.ts", "size": 12547, "lastModified": "2025-06-23T11:40:55.440Z", "confidence": "medium"}, {"path": "lib/testing/ux-testing.ts", "size": 20925, "lastModified": "2025-06-25T09:46:33.250Z", "confidence": "medium"}, {"path": "lib/theme/ThemeProvider.tsx", "size": 13823, "lastModified": "2025-06-23T16:30:29.354Z", "confidence": "medium"}, {"path": "lib/utils/assetOptimization.ts", "size": 7786, "lastModified": "2025-06-24T21:00:33.759Z", "confidence": "medium"}, {"path": "hooks/useLoadingState.ts", "size": 5942, "lastModified": "2025-06-19T18:04:15.920Z", "confidence": "medium"}, {"path": "hooks/usePerformance.ts", "size": 9204, "lastModified": "2025-06-21T14:42:52.182Z", "confidence": "medium"}, {"path": "hooks/useProgress.ts", "size": 1856, "lastModified": "2025-06-19T17:40:42.369Z", "confidence": "medium"}, {"path": "types/global.d.ts", "size": 1585, "lastModified": "2025-06-21T11:39:38.553Z", "confidence": "medium"}, {"path": "types/next-auth.d.ts", "size": 484, "lastModified": "2025-06-21T11:30:44.935Z", "confidence": "medium"}], "assets": []}, "unusedExports": [{"file": "app/achievements/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/admin/audit/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/admin/community/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/admin/content/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/admin/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/admin/security/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/admin/users/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/api/achievements/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/auth/login/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/auth/register/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/chat/delete/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/chat/pin/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/collaboration/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/community/stats/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/compile/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/courses/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/courses/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/courses/[id]/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/courses/[id]/route.ts", "name": "PUT", "confidence": "low"}, {"file": "app/api/courses/[id]/route.ts", "name": "DELETE", "confidence": "low"}, {"file": "app/api/errors/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/errors/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/errors/route.ts", "name": "DELETE", "confidence": "low"}, {"file": "app/api/leaderboard/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/learning-paths/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/metrics/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/projects/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/settings/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/settings/route.ts", "name": "PUT", "confidence": "low"}, {"file": "app/api/socket/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/user/activity-feed/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/user/code-stats/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/user/community-stats/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/user/profile/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/user/progress/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/user/progress-stats/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/user/study-schedule/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/api/v1/admin/maintenance/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/v1/admin/maintenance/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/v1/admin/maintenance/schedules/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/v1/admin/maintenance/schedules/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/v1/admin/maintenance/schedules/[id]/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/v1/admin/maintenance/schedules/[id]/route.ts", "name": "PUT", "confidence": "low"}, {"file": "app/api/v1/admin/maintenance/schedules/[id]/route.ts", "name": "DELETE", "confidence": "low"}, {"file": "app/api/v1/admin/maintenance/schedules/[id]/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/v1/auth/login/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/v1/auth/refresh/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/v1/auth/register/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/v1/health/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/v1/lessons/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/v1/lessons/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/v1/users/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/v1/users/route.ts", "name": "POST", "confidence": "low"}, {"file": "app/api/v1/users/[id]/route.ts", "name": "GET", "confidence": "low"}, {"file": "app/api/v1/users/[id]/route.ts", "name": "PUT", "confidence": "low"}, {"file": "app/api/v1/users/[id]/route.ts", "name": "DELETE", "confidence": "low"}, {"file": "app/auth/demo/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/auth/local-test/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/auth/test/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/auth-testing/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/button-testing/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/code/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/collaborate/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/community/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/contact/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/cookies/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/dashboard/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/documentation/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/error-testing/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/examples/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/instructor/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/layout.tsx", "name": "metadata", "confidence": "low"}, {"file": "app/layout.tsx", "name": "default", "confidence": "low"}, {"file": "app/learn/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/mentor/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/not-found.tsx", "name": "metadata", "confidence": "low"}, {"file": "app/not-found.tsx", "name": "default", "confidence": "low"}, {"file": "app/offline/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/ping/route.ts", "name": "dynamic", "confidence": "low"}, {"file": "app/privacy/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/profile/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/providers.tsx", "name": "Providers", "confidence": "low"}, {"file": "app/session-expired/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/settings/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/terms/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/tutorials/page.tsx", "name": "default", "confidence": "low"}, {"file": "app/unauthorized/page.tsx", "name": "default", "confidence": "low"}, {"file": "components/achievements/AchievementCard.tsx", "name": "AchievementCard", "confidence": "low"}, {"file": "components/achievements/AchievementGrid.tsx", "name": "<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/achievements/AchievementNotification.tsx", "name": "AchievementNotification", "confidence": "low"}, {"file": "components/achievements/AchievementNotification.tsx", "name": "AchievementNotificationManager", "confidence": "low"}, {"file": "components/achievements/AchievementNotification.tsx", "name": "AchievementToast", "confidence": "low"}, {"file": "components/achievements/AchievementNotificationSystem.tsx", "name": "StudyReminderSystem", "confidence": "low"}, {"file": "components/achievements/AchievementNotificationSystem.tsx", "name": "AchievementNotificationSystem", "confidence": "low"}, {"file": "components/achievements/AchievementsPage.tsx", "name": "AchievementsPage", "confidence": "low"}, {"file": "components/AchievementsPage.tsx", "name": "default", "confidence": "low"}, {"file": "components/admin/AdminDashboard.tsx", "name": "AdminDashboard", "confidence": "low"}, {"file": "components/admin/AdminGuard.tsx", "name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/admin/AdminGuard.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/admin/AdminGuard.tsx", "name": "useAdminAuth", "confidence": "low"}, {"file": "components/admin/AdminLayout.tsx", "name": "AdminLayout", "confidence": "low"}, {"file": "components/admin/AuditLogViewer.tsx", "name": "AuditLogViewer", "confidence": "low"}, {"file": "components/admin/CommunityControls.tsx", "name": "CommunityControls", "confidence": "low"}, {"file": "components/admin/CommunityManagement.tsx", "name": "CommunityManagement", "confidence": "low"}, {"file": "components/admin/ContentManagement.tsx", "name": "ContentManagement", "confidence": "low"}, {"file": "components/admin/ContentVersionControl.tsx", "name": "ContentVersionControl", "confidence": "low"}, {"file": "components/admin/PerformanceDashboard.tsx", "name": "PerformanceDashboard", "confidence": "low"}, {"file": "components/admin/PerformanceDashboard.tsx", "name": "default", "confidence": "low"}, {"file": "components/admin/SafetyConfirmation.tsx", "name": "SafetyConfirmation", "confidence": "low"}, {"file": "components/admin/SafetyConfirmation.tsx", "name": "SoftDeleteManager", "confidence": "low"}, {"file": "components/admin/SafetyConfirmation.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/admin/SecurityManagement.tsx", "name": "SecurityManagement", "confidence": "low"}, {"file": "components/admin/UserAnalytics.tsx", "name": "UserAnalytics", "confidence": "low"}, {"file": "components/admin/UserManagement.tsx", "name": "UserManagement", "confidence": "low"}, {"file": "components/ai/AICodeAnalyzer.tsx", "name": "AICodeAnalyzer", "confidence": "low"}, {"file": "components/ai/AICodeAnalyzer.tsx", "name": "default", "confidence": "low"}, {"file": "components/ai/AIContractGenerator.tsx", "name": "AIContractGenerator", "confidence": "low"}, {"file": "components/ai/AIContractGenerator.tsx", "name": "default", "confidence": "low"}, {"file": "components/ai/AILearningPath.tsx", "name": "AILearningPath", "confidence": "low"}, {"file": "components/ai/AILearningPath.tsx", "name": "default", "confidence": "low"}, {"file": "components/ai/EnhancedAIAssistant.tsx", "name": "EnhancedAIAssistant", "confidence": "low"}, {"file": "components/ai/EnhancedAIAssistant.tsx", "name": "default", "confidence": "low"}, {"file": "components/auth/AuthModal.tsx", "name": "AuthModal", "confidence": "low"}, {"file": "components/auth/AuthTesting.tsx", "name": "AuthTesting", "confidence": "low"}, {"file": "components/auth/EnhancedAuthProvider.tsx", "name": "Enhan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/auth/EnhancedAuthProvider.tsx", "name": "useAuth", "confidence": "low"}, {"file": "components/auth/EnhancedAuthProvider.tsx", "name": "UserProfileCard", "confidence": "low"}, {"file": "components/auth/EnhancedAuthProvider.tsx", "name": "PermissionGuard", "confidence": "low"}, {"file": "components/auth/EnhancedAuthProvider.tsx", "name": "RoleBadge", "confidence": "low"}, {"file": "components/auth/EnhancedAuthProvider.tsx", "name": "default", "confidence": "low"}, {"file": "components/auth/EnhancedLoginModal.tsx", "name": "EnhancedLoginModal", "confidence": "low"}, {"file": "components/auth/EnhancedLoginModal.tsx", "name": "default", "confidence": "low"}, {"file": "components/auth/PasswordResetModal.tsx", "name": "PasswordResetModal", "confidence": "low"}, {"file": "components/auth/PasswordStrengthIndicator.tsx", "name": "PasswordStrength", "confidence": "low"}, {"file": "components/auth/PasswordStrengthIndicator.tsx", "name": "PasswordStrengthIndicator", "confidence": "low"}, {"file": "components/auth/PasswordStrengthIndicator.tsx", "name": "calculatePasswordStrength", "confidence": "low"}, {"file": "components/auth/PasswordStrengthIndicator.tsx", "name": "usePasswordValidation", "confidence": "low"}, {"file": "components/auth/ProtectedRoute.tsx", "name": "UserRole", "confidence": "low"}, {"file": "components/auth/ProtectedRoute.tsx", "name": "RoutePermission", "confidence": "low"}, {"file": "components/auth/ProtectedRoute.tsx", "name": "ProtectedRouteProps", "confidence": "low"}, {"file": "components/auth/ProtectedRoute.tsx", "name": "ROUTE_PERMISSIONS", "confidence": "low"}, {"file": "components/auth/ProtectedRoute.tsx", "name": "ProtectedRoute", "confidence": "low"}, {"file": "components/auth/ProtectedRoute.tsx", "name": "<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/auth/ProtectedRoute.tsx", "name": "useRouteProtection", "confidence": "low"}, {"file": "components/auth/ProtectedRoute.tsx", "name": "useAuthGuard", "confidence": "low"}, {"file": "components/blockchain/BlockchainIntegration.tsx", "name": "BlockchainIntegration", "confidence": "low"}, {"file": "components/blockchain/BlockchainIntegration.tsx", "name": "default", "confidence": "low"}, {"file": "components/blockchain/ContractDeployer.tsx", "name": "ContractDeployer", "confidence": "low"}, {"file": "components/blockchain/WalletConnect.tsx", "name": "WalletConnect", "confidence": "low"}, {"file": "components/code/CodeLab.tsx", "name": "CodeLab", "confidence": "low"}, {"file": "components/code/CodeLab.tsx", "name": "StaticCodeLab", "confidence": "low"}, {"file": "components/collaboration/AdvancedUserPresence.tsx", "name": "AdvancedUserPresence", "confidence": "low"}, {"file": "components/collaboration/AdvancedUserPresence.tsx", "name": "default", "confidence": "low"}, {"file": "components/collaboration/CollaborationChat.tsx", "name": "CollaborationChat", "confidence": "low"}, {"file": "components/collaboration/CollaborationChat.tsx", "name": "useChatMessages", "confidence": "low"}, {"file": "components/collaboration/CollaborationHub.tsx", "name": "CollaborationHub", "confidence": "low"}, {"file": "components/collaboration/CollaborativeEditor.tsx", "name": "CollaborativeEditor", "confidence": "low"}, {"file": "components/collaboration/ComprehensiveCollaborationDashboard.tsx", "name": "ComprehensiveCollaborationDashboard", "confidence": "low"}, {"file": "components/collaboration/ComprehensiveCollaborationDashboard.tsx", "name": "default", "confidence": "low"}, {"file": "components/collaboration/ConnectionStatusIndicator.tsx", "name": "ConnectionStatusIndicator", "confidence": "low"}, {"file": "components/collaboration/FileSharing.tsx", "name": "FileSharing", "confidence": "low"}, {"file": "components/collaboration/LiveChatSystem.tsx", "name": "LiveChatSystem", "confidence": "low"}, {"file": "components/collaboration/LiveChatSystem.tsx", "name": "default", "confidence": "low"}, {"file": "components/collaboration/MonacoCollaborativeEditor.tsx", "name": "MonacoCollaborativeEditor", "confidence": "low"}, {"file": "components/collaboration/MonacoCollaborativeEditor.tsx", "name": "default", "confidence": "low"}, {"file": "components/collaboration/RealTimeCodeEditor.tsx", "name": "RealTimeCodeEditor", "confidence": "low"}, {"file": "components/collaboration/RealTimeCodeEditor.tsx", "name": "default", "confidence": "low"}, {"file": "components/collaboration/SessionRecovery.tsx", "name": "SessionRecovery", "confidence": "low"}, {"file": "components/collaboration/SessionRecovery.tsx", "name": "useSessionRecovery", "confidence": "low"}, {"file": "components/collaboration/UserPresenceIndicator.tsx", "name": "UserPresenceIndicator", "confidence": "low"}, {"file": "components/collaboration/UserPresenceIndicator.tsx", "name": "default", "confidence": "low"}, {"file": "components/collaboration/UserPresencePanel.tsx", "name": "UserPresencePanel", "confidence": "low"}, {"file": "components/community/CommunityHub.tsx", "name": "CommunityHub", "confidence": "low"}, {"file": "components/community/CommunityStats.tsx", "name": "CommunityStats", "confidence": "low"}, {"file": "components/community/Leaderboards.tsx", "name": "Leaderboards", "confidence": "low"}, {"file": "components/ConfirmationModal.tsx", "name": "default", "confidence": "low"}, {"file": "components/CopyButton.tsx", "name": "default", "confidence": "low"}, {"file": "components/curriculum/CurriculumDashboard.tsx", "name": "CurriculumDashboard", "confidence": "low"}, {"file": "components/curriculum/LearningAnalytics.tsx", "name": "LearningAnalytics", "confidence": "low"}, {"file": "components/curriculum/LearningPathVisualization.tsx", "name": "LearningPathVisualization", "confidence": "low"}, {"file": "components/curriculum/LessonCard.tsx", "name": "LessonCard", "confidence": "low"}, {"file": "components/curriculum/ModuleCard.tsx", "name": "ModuleCard", "confidence": "low"}, {"file": "components/curriculum/PrerequisiteDisplay.tsx", "name": "PrerequisiteDisplay", "confidence": "low"}, {"file": "components/debugging/SolidityDebuggerInterface.tsx", "name": "SolidityDebuggerInterfaceProps", "confidence": "low"}, {"file": "components/debugging/SolidityDebuggerInterface.tsx", "name": "SolidityDebuggerInterface", "confidence": "low"}, {"file": "components/dev/AccessibilityTester.tsx", "name": "AccessibilityTester", "confidence": "low"}, {"file": "components/dev/AccessibilityTester.tsx", "name": "default", "confidence": "low"}, {"file": "components/discovery/DiscoveryProvider.tsx", "name": "DiscoveryProvider", "confidence": "low"}, {"file": "components/discovery/DiscoveryProvider.tsx", "name": "useDiscovery", "confidence": "low"}, {"file": "components/discovery/DiscoveryProvider.tsx", "name": "FeatureTracker", "confidence": "low"}, {"file": "components/discovery/DiscoveryProvider.tsx", "name": "DiscoverySettings", "confidence": "low"}, {"file": "components/discovery/DiscoveryProvider.tsx", "name": "default", "confidence": "low"}, {"file": "components/discovery/FeatureSpotlight.tsx", "name": "FeatureSpotlight", "confidence": "low"}, {"file": "components/discovery/FeatureSpotlight.tsx", "name": "PLATFORM_FEATURES", "confidence": "low"}, {"file": "components/discovery/FeatureSpotlight.tsx", "name": "default", "confidence": "low"}, {"file": "components/discovery/SmartTooltip.tsx", "name": "SmartTooltip", "confidence": "low"}, {"file": "components/discovery/SmartTooltip.tsx", "name": "default", "confidence": "low"}, {"file": "components/editor/AdvancedCollaborativeMonacoEditor.tsx", "name": "AdvancedCollaborativeMonacoEditorProps", "confidence": "low"}, {"file": "components/editor/AdvancedCollaborativeMonacoEditor.tsx", "name": "AdvancedCollaborativeMonacoEditor", "confidence": "low"}, {"file": "components/editor/AdvancedIDEInterface.tsx", "name": "IDELayout", "confidence": "low"}, {"file": "components/editor/AdvancedIDEInterface.tsx", "name": "AdvancedIDEInterfaceProps", "confidence": "low"}, {"file": "components/editor/AdvancedIDEInterface.tsx", "name": "AdvancedIDEInterface", "confidence": "low"}, {"file": "components/error/ErrorBoundaryFallback.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/error/ErrorBoundaryFallback.tsx", "name": "default", "confidence": "low"}, {"file": "components/error-handling/AsyncErrorBoundary.tsx", "name": "AsyncErrorBoundary", "confidence": "low"}, {"file": "components/error-handling/AsyncErrorBoundary.tsx", "name": "ApiErrorBoundary", "confidence": "low"}, {"file": "components/error-handling/AsyncErrorBoundary.tsx", "name": "UploadErrorBoundary", "confidence": "low"}, {"file": "components/error-handling/AsyncErrorBoundary.tsx", "name": "AuthErrorBoundary", "confidence": "low"}, {"file": "components/error-handling/ErrorBoundary.tsx", "name": "Error<PERSON>ou<PERSON><PERSON>", "confidence": "low"}, {"file": "components/error-handling/ErrorBoundary.tsx", "name": "Page<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/error-handling/ErrorBoundary.tsx", "name": "SectionErrorBoundary", "confidence": "low"}, {"file": "components/error-handling/ErrorBoundary.tsx", "name": "ComponentErrorBoundary", "confidence": "low"}, {"file": "components/error-handling/NotFoundPage.tsx", "name": "NotFoundPage", "confidence": "low"}, {"file": "components/errors/ErrorBoundary.tsx", "name": "Error<PERSON>ou<PERSON><PERSON>", "confidence": "low"}, {"file": "components/errors/ErrorBoundary.tsx", "name": "Page<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/errors/ErrorBoundary.tsx", "name": "FeatureErrorBoundary", "confidence": "low"}, {"file": "components/errors/ErrorBoundary.tsx", "name": "ComponentErrorBoundary", "confidence": "low"}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "name": "CodeEditorErrorBoundary", "confidence": "low"}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "name": "LearningModuleErrorBoundary", "confidence": "low"}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "name": "AuthErrorBoundary", "confidence": "low"}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "name": "FileUploadErrorBoundary", "confidence": "low"}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "name": "NetworkErrorBoundary", "confidence": "low"}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "name": "SettingsErrorBoundary", "confidence": "low"}, {"file": "components/errors/SpecializedErrorBoundaries.tsx", "name": "AsyncComponentErrorBoundary", "confidence": "low"}, {"file": "components/forms/ContactForm.tsx", "name": "ContactForm", "confidence": "low"}, {"file": "components/forms/ContactForm.tsx", "name": "ContactFormPage", "confidence": "low"}, {"file": "components/GeminiChat.tsx", "name": "default", "confidence": "low"}, {"file": "components/help/ContextualTooltip.tsx", "name": "ContextualTooltip", "confidence": "low"}, {"file": "components/help/ContextualTooltip.tsx", "name": "TOOLTIP_CONTENTS", "confidence": "low"}, {"file": "components/help/ContextualTooltip.tsx", "name": "default", "confidence": "low"}, {"file": "components/help/HelpProvider.tsx", "name": "HelpProvider", "confidence": "low"}, {"file": "components/help/HelpProvider.tsx", "name": "useHelp", "confidence": "low"}, {"file": "components/help/HelpProvider.tsx", "name": "HelpTrigger", "confidence": "low"}, {"file": "components/help/HelpProvider.tsx", "name": "ShortcutsTrigger", "confidence": "low"}, {"file": "components/help/HelpProvider.tsx", "name": "default", "confidence": "low"}, {"file": "components/help/HelpSystem.tsx", "name": "HelpSystem", "confidence": "low"}, {"file": "components/help/HelpSystem.tsx", "name": "default", "confidence": "low"}, {"file": "components/help/KeyboardShortcuts.tsx", "name": "KeyboardShortcuts", "confidence": "low"}, {"file": "components/help/KeyboardShortcuts.tsx", "name": "default", "confidence": "low"}, {"file": "components/icons/BotIcon.tsx", "name": "default", "confidence": "low"}, {"file": "components/icons/CheckIcon.tsx", "name": "default", "confidence": "low"}, {"file": "components/icons/MenuIcon.tsx", "name": "default", "confidence": "low"}, {"file": "components/icons/SendIcon.tsx", "name": "default", "confidence": "low"}, {"file": "components/icons/SpinnerIcon.tsx", "name": "default", "confidence": "low"}, {"file": "components/icons/UserIcon.tsx", "name": "default", "confidence": "low"}, {"file": "components/LandingPage.tsx", "name": "default", "confidence": "low"}, {"file": "components/layout/Footer.tsx", "name": "Footer", "confidence": "low"}, {"file": "components/layout/Navigation.tsx", "name": "Navigation", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "LazyGamificationDashboard", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "LazyAITutoringInterface", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "LazyRealTimeCollaboration", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "LazyAdvancedAnalytics", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "LazyUserManagement", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "preloadGamificationDashboard", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "preloadAITutoringInterface", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "preloadRealTimeCollaboration", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "preloadAdvancedAnalytics", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "preloadAllHeavyComponents", "confidence": "low"}, {"file": "components/lazy/LazyComponents.tsx", "name": "default", "confidence": "low"}, {"file": "components/lazy/LazyMonacoEditor.tsx", "name": "LazyMonacoEditor", "confidence": "low"}, {"file": "components/lazy/LazyMonacoEditor.tsx", "name": "preloadMonacoEditor", "confidence": "low"}, {"file": "components/lazy/LazyMonacoEditor.tsx", "name": "useMonacoEditor", "confidence": "low"}, {"file": "components/lazy/LazyMonacoEditor.tsx", "name": "default", "confidence": "low"}, {"file": "components/learning/ComprehensiveLearningPlatform.tsx", "name": "ComprehensiveLearningPlatform", "confidence": "low"}, {"file": "components/learning/ComprehensiveLearningPlatform.tsx", "name": "default", "confidence": "low"}, {"file": "components/learning/GamificationSystem.tsx", "name": "GamificationSystem", "confidence": "low"}, {"file": "components/learning/GamificationSystem.tsx", "name": "default", "confidence": "low"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "name": "InteractiveCodeEditor", "confidence": "low"}, {"file": "components/learning/InteractiveCodeEditor.tsx", "name": "default", "confidence": "low"}, {"file": "components/learning/LearningDashboard.tsx", "name": "LearningDashboard", "confidence": "low"}, {"file": "components/learning/LearningDashboard.tsx", "name": "StaticLearningDashboard", "confidence": "low"}, {"file": "components/learning/LessonProgressTracker.tsx", "name": "LessonProgressTracker", "confidence": "low"}, {"file": "components/learning/ProjectBasedLearning.tsx", "name": "ProjectBasedLearning", "confidence": "low"}, {"file": "components/learning/ProjectBasedLearning.tsx", "name": "default", "confidence": "low"}, {"file": "components/learning/StructuredCurriculum.tsx", "name": "StructuredCurriculum", "confidence": "low"}, {"file": "components/learning/StructuredCurriculum.tsx", "name": "default", "confidence": "low"}, {"file": "components/MobileNavigation.tsx", "name": "default", "confidence": "low"}, {"file": "components/ModuleContent.tsx", "name": "default", "confidence": "low"}, {"file": "components/monitoring/PerformanceMonitor.tsx", "name": "PerformanceMonitor", "confidence": "low"}, {"file": "components/monitoring/PerformanceMonitor.tsx", "name": "usePerformanceMetrics", "confidence": "low"}, {"file": "components/monitoring/PerformanceMonitor.tsx", "name": "default", "confidence": "low"}, {"file": "components/navigation/AuthenticatedNavbar.tsx", "name": "AuthenticatedNavbar", "confidence": "low"}, {"file": "components/navigation/GuidedOnboarding.tsx", "name": "GuidedOnboarding", "confidence": "low"}, {"file": "components/navigation/NavigationFlowOptimizer.tsx", "name": "NavigationFlowOptimizer", "confidence": "low"}, {"file": "components/navigation/SmartNavigation.tsx", "name": "SmartBackButton", "confidence": "low"}, {"file": "components/navigation/SmartNavigation.tsx", "name": "SmartBreadcrumbs", "confidence": "low"}, {"file": "components/navigation/SmartNavigation.tsx", "name": "ContinueLearning", "confidence": "low"}, {"file": "components/navigation/SmartNavigation.tsx", "name": "NavigationStatus", "confidence": "low"}, {"file": "components/notifications/NotificationIntegrations.tsx", "name": "NotificationIntegrations", "confidence": "low"}, {"file": "components/notifications/NotificationIntegrations.tsx", "name": "useManualNotificationTriggers", "confidence": "low"}, {"file": "components/onboarding/InteractiveTutorial.tsx", "name": "InteractiveTutorial", "confidence": "low"}, {"file": "components/onboarding/InteractiveTutorial.tsx", "name": "default", "confidence": "low"}, {"file": "components/onboarding/OnboardingFlow.tsx", "name": "OnboardingFlow", "confidence": "low"}, {"file": "components/onboarding/OnboardingFlow.tsx", "name": "TutorialStep", "confidence": "low"}, {"file": "components/onboarding/OnboardingFlow.tsx", "name": "FEATURE_TUTORIALS", "confidence": "low"}, {"file": "components/onboarding/OnboardingFlow.tsx", "name": "default", "confidence": "low"}, {"file": "components/performance/PerformanceOptimizer.tsx", "name": "PerformanceOptimizer", "confidence": "low"}, {"file": "components/performance/PerformanceOptimizer.tsx", "name": "CriticalCSS", "confidence": "low"}, {"file": "components/performance/PerformanceOptimizer.tsx", "name": "Font<PERSON><PERSON><PERSON>der", "confidence": "low"}, {"file": "components/performance/PerformanceOptimizer.tsx", "name": "ResourceHints", "confidence": "low"}, {"file": "components/performance/PerformanceOptimizer.tsx", "name": "PerformanceMonitor", "confidence": "low"}, {"file": "components/performance/PerformanceOptimizer.tsx", "name": "default", "confidence": "low"}, {"file": "components/performance/ServiceWorkerManager.tsx", "name": "ServiceWorkerManager", "confidence": "low"}, {"file": "components/performance/ServiceWorkerManager.tsx", "name": "useServiceWorker", "confidence": "low"}, {"file": "components/performance/ServiceWorkerManager.tsx", "name": "default", "confidence": "low"}, {"file": "components/profile/UserProfile.tsx", "name": "UserProfile", "confidence": "low"}, {"file": "components/progress/ProgressDashboard.tsx", "name": "ProgressDashboard", "confidence": "low"}, {"file": "components/progress/ProgressDashboard.tsx", "name": "default", "confidence": "low"}, {"file": "components/providers/FallbackProvider.tsx", "name": "useFallback", "confidence": "low"}, {"file": "components/providers/FallbackProvider.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/providers/FallbackProvider.tsx", "name": "withFallbackProtection", "confidence": "low"}, {"file": "components/providers/FallbackProvider.tsx", "name": "useAsyncFallback", "confidence": "low"}, {"file": "components/providers/FallbackProvider.tsx", "name": "ErrorMetrics", "confidence": "low"}, {"file": "components/providers/FallbackProvider.tsx", "name": "setupGlobalErrorHandling", "confidence": "low"}, {"file": "components/providers/SessionProvider.tsx", "name": "Session<PERSON>rov<PERSON>", "confidence": "low"}, {"file": "components/QuizComponent.tsx", "name": "default", "confidence": "low"}, {"file": "components/sections/CompetitiveAnalysisSection.tsx", "name": "CompetitiveAnalysisSection", "confidence": "low"}, {"file": "components/sections/CTASection.tsx", "name": "CTASection", "confidence": "low"}, {"file": "components/sections/EnhancedFeaturesShowcase.tsx", "name": "EnhancedFeaturesShowcase", "confidence": "low"}, {"file": "components/sections/FeaturesSection.tsx", "name": "FeaturesSection", "confidence": "low"}, {"file": "components/sections/GamificationPreview.tsx", "name": "GamificationPreview", "confidence": "low"}, {"file": "components/sections/HeroSection.tsx", "name": "HeroSection", "confidence": "low"}, {"file": "components/sections/InteractiveDemoSection.tsx", "name": "InteractiveDemoSection", "confidence": "low"}, {"file": "components/sections/TestimonialsSection.tsx", "name": "TestimonialsSection", "confidence": "low"}, {"file": "components/settings/AccessibilitySection.tsx", "name": "AccessibilitySectionProps", "confidence": "low"}, {"file": "components/settings/AccessibilitySection.tsx", "name": "AccessibilitySection", "confidence": "low"}, {"file": "components/settings/LearningPreferencesSection.tsx", "name": "LearningPreferencesSectionProps", "confidence": "low"}, {"file": "components/settings/LearningPreferencesSection.tsx", "name": "LearningPreferencesSection", "confidence": "low"}, {"file": "components/settings/NotificationSection.tsx", "name": "NotificationSectionProps", "confidence": "low"}, {"file": "components/settings/NotificationSection.tsx", "name": "NotificationSection", "confidence": "low"}, {"file": "components/settings/PrivacySection.tsx", "name": "PrivacySectionProps", "confidence": "low"}, {"file": "components/settings/PrivacySection.tsx", "name": "PrivacySection", "confidence": "low"}, {"file": "components/settings/PrivacySection.tsx", "name": "handleDataExport", "confidence": "low"}, {"file": "components/settings/ProfileSection.tsx", "name": "ProfileSectionProps", "confidence": "low"}, {"file": "components/settings/ProfileSection.tsx", "name": "ProfileSection", "confidence": "low"}, {"file": "components/settings/SecuritySection.tsx", "name": "SecuritySectionProps", "confidence": "low"}, {"file": "components/settings/SecuritySection.tsx", "name": "SecuritySection", "confidence": "low"}, {"file": "components/settings/SettingsPage.tsx", "name": "SettingsPageProps", "confidence": "low"}, {"file": "components/settings/SettingsPage.tsx", "name": "SettingsPage", "confidence": "low"}, {"file": "components/Sidebar.tsx", "name": "default", "confidence": "low"}, {"file": "components/testing/FeedbackCollectionSystem.tsx", "name": "FeedbackCollectionSystem", "confidence": "low"}, {"file": "components/testing/FeedbackCollectionSystem.tsx", "name": "default", "confidence": "low"}, {"file": "components/testing/NotificationTestingPage.tsx", "name": "NotificationTestingPage", "confidence": "low"}, {"file": "components/testing/UATDashboard.tsx", "name": "UATDashboard", "confidence": "low"}, {"file": "components/testing/UATDashboard.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/Accessibility.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/AccessibleForm.tsx", "name": "AccessibleField", "confidence": "low"}, {"file": "components/ui/AccessibleForm.tsx", "name": "AccessibleForm", "confidence": "low"}, {"file": "components/ui/AccessibleForm.tsx", "name": "FormSuccess", "confidence": "low"}, {"file": "components/ui/AccessibleForm.tsx", "name": "FormErrorSummary", "confidence": "low"}, {"file": "components/ui/AccessibleForm.tsx", "name": "AccessibleSubmitButton", "confidence": "low"}, {"file": "components/ui/AccessibleForm.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/AdvancedAnimations.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/AnimatedButton.tsx", "name": "AnimatedButton", "confidence": "low"}, {"file": "components/ui/AnimatedButton.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/AnimationShowcase.tsx", "name": "AnimationShowcase", "confidence": "low"}, {"file": "components/ui/AnimationShowcase.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/badge.tsx", "name": "BadgeProps", "confidence": "low"}, {"file": "components/ui/Branding.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/button.tsx", "name": "ButtonProps", "confidence": "low"}, {"file": "components/ui/ButtonTesting.tsx", "name": "ButtonTesting", "confidence": "low"}, {"file": "components/ui/CelebrationAnimations.tsx", "name": "CelebrationModal", "confidence": "low"}, {"file": "components/ui/CelebrationAnimations.tsx", "name": "QuickSuccessAnimation", "confidence": "low"}, {"file": "components/ui/CelebrationAnimations.tsx", "name": "ProgressCelebration", "confidence": "low"}, {"file": "components/ui/checkbox.tsx", "name": "Checkbox", "confidence": "low"}, {"file": "components/ui/checkbox.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/ContextualHelp.tsx", "name": "ContextualHelp", "confidence": "low"}, {"file": "components/ui/ContextualHelp.tsx", "name": "QuickHelp", "confidence": "low"}, {"file": "components/ui/CustomToast.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "EmptyState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "EmptyCoursesState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "EmptySearchState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "EmptyLessonsState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "EmptyProgressState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "EmptyNotificationsState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "EmptyAchievementsState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "EmptyPlaygroundState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "ErrorState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "OfflineState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "LoadingErrorState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "UnauthorizedState", "confidence": "low"}, {"file": "components/ui/EmptyState.tsx", "name": "MaintenanceState", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "Variants", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "Sizes", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "WithIcons", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "LoadingStates", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "DisabledStates", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "MobileOptimized", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "WithRipple", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "AccessibilityDemo", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "RealWorldExamples", "confidence": "low"}, {"file": "components/ui/EnhancedButton.stories.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/EnhancedButton.tsx", "name": "Enhanced<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/ui/EnhancedButton.tsx", "name": "PrimaryButton", "confidence": "low"}, {"file": "components/ui/EnhancedButton.tsx", "name": "SecondaryButton", "confidence": "low"}, {"file": "components/ui/EnhancedButton.tsx", "name": "SuccessButton", "confidence": "low"}, {"file": "components/ui/EnhancedButton.tsx", "name": "DangerButton", "confidence": "low"}, {"file": "components/ui/EnhancedButton.tsx", "name": "FloatingActionButton", "confidence": "low"}, {"file": "components/ui/EnhancedButton.tsx", "name": "AsyncSubmitButton", "confidence": "low"}, {"file": "components/ui/EnhancedButton.tsx", "name": "AsyncSaveButton", "confidence": "low"}, {"file": "components/ui/EnhancedButton.tsx", "name": "AsyncDeleteButton", "confidence": "low"}, {"file": "components/ui/EnhancedCard.tsx", "name": "EnhancedCard", "confidence": "low"}, {"file": "components/ui/EnhancedCard.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/EnhancedLoadingStates.tsx", "name": "FormLoadingOverlay", "confidence": "low"}, {"file": "components/ui/EnhancedLoadingStates.tsx", "name": "FileUploadProgress", "confidence": "low"}, {"file": "components/ui/EnhancedLoadingStates.tsx", "name": "SearchLoading", "confidence": "low"}, {"file": "components/ui/EnhancedProgress.tsx", "name": "EnhancedProgress", "confidence": "low"}, {"file": "components/ui/EnhancedProgress.tsx", "name": "XPProgress", "confidence": "low"}, {"file": "components/ui/EnhancedProgress.tsx", "name": "LearningProgress", "confidence": "low"}, {"file": "components/ui/EnhancedProgress.tsx", "name": "AchievementProgress", "confidence": "low"}, {"file": "components/ui/ErrorHandling.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/ErrorMessage.tsx", "name": "ErrorMessageProps", "confidence": "low"}, {"file": "components/ui/ErrorMessage.tsx", "name": "ErrorMessage", "confidence": "low"}, {"file": "components/ui/ErrorMessage.tsx", "name": "ErrorToast", "confidence": "low"}, {"file": "components/ui/ErrorMessage.tsx", "name": "InlineFormError", "confidence": "low"}, {"file": "components/ui/ErrorMessage.tsx", "name": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/ui/ErrorMessage.tsx", "name": "ErrorPage", "confidence": "low"}, {"file": "components/ui/ErrorTesting.tsx", "name": "ErrorTesting", "confidence": "low"}, {"file": "components/ui/FeatureState.tsx", "name": "FeatureStateComponent", "confidence": "low"}, {"file": "components/ui/FeatureState.tsx", "name": "FeatureGate", "confidence": "low"}, {"file": "components/ui/FeatureState.tsx", "name": "ComingSoonPlaceholder", "confidence": "low"}, {"file": "components/ui/FeatureState.tsx", "name": "BetaFeatureNotice", "confidence": "low"}, {"file": "components/ui/FeedbackIndicators.tsx", "name": "FeedbackIndicatorProps", "confidence": "low"}, {"file": "components/ui/FeedbackIndicators.tsx", "name": "FeedbackIndicator", "confidence": "low"}, {"file": "components/ui/FeedbackIndicators.tsx", "name": "SuccessIndicator", "confidence": "low"}, {"file": "components/ui/FeedbackIndicators.tsx", "name": "ErrorIndicator", "confidence": "low"}, {"file": "components/ui/FeedbackIndicators.tsx", "name": "InlineFeedback", "confidence": "low"}, {"file": "components/ui/FeedbackIndicators.tsx", "name": "ToastNotification", "confidence": "low"}, {"file": "components/ui/FeedbackIndicators.tsx", "name": "ProgressFeedback", "confidence": "low"}, {"file": "components/ui/FeedbackIndicators.tsx", "name": "GlassErrorFeedback", "confidence": "low"}, {"file": "components/ui/FeedbackIndicators.tsx", "name": "NeumorphismErrorFeedback", "confidence": "low"}, {"file": "components/ui/GlassCard.stories.tsx", "name": "<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/ui/GlassCard.stories.tsx", "name": "HighBlur", "confidence": "low"}, {"file": "components/ui/GlassCard.stories.tsx", "name": "Subtle", "confidence": "low"}, {"file": "components/ui/GlassCard.stories.tsx", "name": "Interactive", "confidence": "low"}, {"file": "components/ui/GlassCard.stories.tsx", "name": "Achievement", "confidence": "low"}, {"file": "components/ui/GlassCard.stories.tsx", "name": "Notification", "confidence": "low"}, {"file": "components/ui/GlassCard.stories.tsx", "name": "HighContrast", "confidence": "low"}, {"file": "components/ui/GlassCard.stories.tsx", "name": "Mobile", "confidence": "low"}, {"file": "components/ui/GlassCard.stories.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/Glassmorphism.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "GlassmorphismButtonProps", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "GlassmorphismButton", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "NeumorphismButtonProps", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "NeumorphismButton", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "GlassPrimaryButton", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "GlassSecondaryButton", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "GlassSuccessButton", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "GlassDangerButton", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "GlassFloatingActionButton", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "GlassButtonGroup", "confidence": "low"}, {"file": "components/ui/GlassmorphismButtons.tsx", "name": "ButtonShowcase", "confidence": "low"}, {"file": "components/ui/GlassmorphismModal.tsx", "name": "GlassmorphismModal", "confidence": "low"}, {"file": "components/ui/GlassmorphismModal.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/GlassNeumorphDemo.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/GSAPAnimations.tsx", "name": "GSAPTimelineAnimation", "confidence": "low"}, {"file": "components/ui/GSAPAnimations.tsx", "name": "GSAPTextAnimation", "confidence": "low"}, {"file": "components/ui/GSAPAnimations.tsx", "name": "GSAPMorphingShape", "confidence": "low"}, {"file": "components/ui/GSAPAnimations.tsx", "name": "GSAPScrollAnimation", "confidence": "low"}, {"file": "components/ui/GSAPAnimations.tsx", "name": "GSAPMagneticButton", "confidence": "low"}, {"file": "components/ui/GSAPAnimations.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/input.tsx", "name": "InputProps", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "LazyLoadingWrapper", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "LazyCodeEditor", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "LazyVideoPlayer", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "LazyThreeVisualization", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "LazyAdvancedSettings", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "LazyCollaborationPanel", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "LazyAnalyticsDashboard", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "useDebouncedLoading", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "DebouncedLoading", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "ProgressiveLoading", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "IntersectionLoading", "confidence": "low"}, {"file": "components/ui/LazyLoadingComponents.tsx", "name": "NetworkAwareLoading", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "Skeleton", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "MonacoEditorSkeleton", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "DashboardCardSkeleton", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "LessonCardSkeleton", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "UserProfileSkeleton", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "ChatMessageSkeleton", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "AchievementCardSkeleton", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "NavigationSkeleton", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "TableSkeleton", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "PageSkeleton", "confidence": "low"}, {"file": "components/ui/LoadingSkeletons.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/LoadingSpinner.tsx", "name": "LoadingSpinnerProps", "confidence": "low"}, {"file": "components/ui/LoadingSpinner.tsx", "name": "LoadingSpinner", "confidence": "low"}, {"file": "components/ui/LoadingSpinner.tsx", "name": "ButtonLoadingSpinner", "confidence": "low"}, {"file": "components/ui/LoadingSpinner.tsx", "name": "PageLoadingSpinner", "confidence": "low"}, {"file": "components/ui/LoadingStates.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/LottieAnimations.tsx", "name": "<PERSON>tiePlayer", "confidence": "low"}, {"file": "components/ui/LottieAnimations.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/ui/LottieAnimations.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/ui/LottieAnimations.tsx", "name": "LottieSuccess", "confidence": "low"}, {"file": "components/ui/LottieAnimations.tsx", "name": "<PERSON><PERSON>ScrollTrigger", "confidence": "low"}, {"file": "components/ui/LottieAnimations.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/Neumorphism.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/NotificationCenter.tsx", "name": "NotificationCenter", "confidence": "low"}, {"file": "components/ui/NotificationHistory.tsx", "name": "NotificationHistoryModal", "confidence": "low"}, {"file": "components/ui/NotificationPreferences.tsx", "name": "NotificationPreferencesModal", "confidence": "low"}, {"file": "components/ui/NotificationSystem.tsx", "name": "NotificationType", "confidence": "low"}, {"file": "components/ui/NotificationSystem.tsx", "name": "NotificationVariant", "confidence": "low"}, {"file": "components/ui/NotificationSystem.tsx", "name": "NotificationAction", "confidence": "low"}, {"file": "components/ui/NotificationSystem.tsx", "name": "NotificationMetadata", "confidence": "low"}, {"file": "components/ui/NotificationSystem.tsx", "name": "Notification", "confidence": "low"}, {"file": "components/ui/NotificationSystem.tsx", "name": "NotificationGroup", "confidence": "low"}, {"file": "components/ui/NotificationSystem.tsx", "name": "NotificationPreferences", "confidence": "low"}, {"file": "components/ui/NotificationSystem.tsx", "name": "NotificationProvider", "confidence": "low"}, {"file": "components/ui/NotificationSystem.tsx", "name": "useNotifications", "confidence": "low"}, {"file": "components/ui/Onboarding.tsx", "name": "useOnboarding", "confidence": "low"}, {"file": "components/ui/Onboarding.tsx", "name": "OnboardingProvider", "confidence": "low"}, {"file": "components/ui/Onboarding.tsx", "name": "WelcomeModal", "confidence": "low"}, {"file": "components/ui/Onboarding.tsx", "name": "HelpButton", "confidence": "low"}, {"file": "components/ui/Onboarding.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/OptimizedImage.tsx", "name": "OptimizedImage", "confidence": "low"}, {"file": "components/ui/OptimizedImage.tsx", "name": "OptimizedAvatar", "confidence": "low"}, {"file": "components/ui/OptimizedImage.tsx", "name": "HeroImage", "confidence": "low"}, {"file": "components/ui/OptimizedImage.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/PageTransition.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/SaveStatusIndicator.tsx", "name": "SaveStatusIndicator", "confidence": "low"}, {"file": "components/ui/SaveStatusIndicator.tsx", "name": "FloatingSaveStatus", "confidence": "low"}, {"file": "components/ui/SaveStatusIndicator.tsx", "name": "SaveProgressIndicator", "confidence": "low"}, {"file": "components/ui/SessionStatusIndicator.tsx", "name": "SessionStatusIndicator", "confidence": "low"}, {"file": "components/ui/SessionStatusIndicator.tsx", "name": "SessionStatusBadge", "confidence": "low"}, {"file": "components/ui/SkeletonLoader.tsx", "name": "Skeleton", "confidence": "low"}, {"file": "components/ui/SkeletonLoader.tsx", "name": "CourseCardSkeleton", "confidence": "low"}, {"file": "components/ui/SkeletonLoader.tsx", "name": "LessonContentSkeleton", "confidence": "low"}, {"file": "components/ui/SkeletonLoader.tsx", "name": "UserProfileSkeleton", "confidence": "low"}, {"file": "components/ui/SkeletonLoader.tsx", "name": "DashboardSkeleton", "confidence": "low"}, {"file": "components/ui/SkeletonLoader.tsx", "name": "ListSkeleton", "confidence": "low"}, {"file": "components/ui/SkeletonLoader.tsx", "name": "TableSkeleton", "confidence": "low"}, {"file": "components/ui/SkeletonLoader.tsx", "name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/ui/slider.tsx", "name": "Slide<PERSON>", "confidence": "low"}, {"file": "components/ui/slider.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/SmartSearch.tsx", "name": "SmartSearch", "confidence": "low"}, {"file": "components/ui/SVGAnimations.tsx", "name": "SVGLoadingSpinner", "confidence": "low"}, {"file": "components/ui/SVGAnimations.tsx", "name": "SVGCheckmark", "confidence": "low"}, {"file": "components/ui/SVGAnimations.tsx", "name": "SVGAnimatedArrow", "confidence": "low"}, {"file": "components/ui/SVGAnimations.tsx", "name": "SVGMorphingIcon", "confidence": "low"}, {"file": "components/ui/SVGAnimations.tsx", "name": "SVGProgressRing", "confidence": "low"}, {"file": "components/ui/SVGAnimations.tsx", "name": "SVGWave", "confidence": "low"}, {"file": "components/ui/SVGAnimations.tsx", "name": "SVGBlockchainIcon", "confidence": "low"}, {"file": "components/ui/SVGAnimations.tsx", "name": "SVGInteractiveButton", "confidence": "low"}, {"file": "components/ui/SVGAnimations.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/textarea.tsx", "name": "TextareaProps", "confidence": "low"}, {"file": "components/ui/ThreeJSComponents.tsx", "name": "BlockchainVisualization", "confidence": "low"}, {"file": "components/ui/ThreeJSComponents.tsx", "name": "ParticleBackground", "confidence": "low"}, {"file": "components/ui/ThreeJSComponents.tsx", "name": "Interactive3DCard", "confidence": "low"}, {"file": "components/ui/ThreeJSComponents.tsx", "name": "MorphingGeometry", "confidence": "low"}, {"file": "components/ui/ThreeJSComponents.tsx", "name": "SolanaVisualization", "confidence": "low"}, {"file": "components/ui/ThreeJSComponents.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/toaster.tsx", "name": "Toaster", "confidence": "low"}, {"file": "components/ui/Tooltip.tsx", "name": "default", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "H1", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "H2", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "H3", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "H4", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "Paragraph", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "Lead", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "Large", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "Small", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "Muted", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "Code", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "Pre", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "List", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "ListItem", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "Blockquote", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "Table", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "TableHeader", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "TableBody", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "TableRow", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "TableHead", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "TableCell", "confidence": "low"}, {"file": "components/ui/Typography.tsx", "name": "ReadableContent", "confidence": "low"}, {"file": "components/ui/use-toast.tsx", "name": "reducer", "confidence": "low"}, {"file": "components/ui/UserAvatar.tsx", "name": "UserAvatar", "confidence": "low"}, {"file": "components/ui/VisualFeedbackSystem.tsx", "name": "VisualFeedbackProvider", "confidence": "low"}, {"file": "components/ui/VisualFeedbackSystem.tsx", "name": "useVisualFeedback", "confidence": "low"}, {"file": "components/ui/VisualFeedbackSystem.tsx", "name": "FeedbackInput", "confidence": "low"}, {"file": "components/ui/VisualFeedbackSystem.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "components/vcs/VersionControlInterface.tsx", "name": "VersionControlInterfaceProps", "confidence": "low"}, {"file": "components/vcs/VersionControlInterface.tsx", "name": "VersionControlInterface", "confidence": "low"}, {"file": "components/xp/LevelUpCelebration.tsx", "name": "LevelUpData", "confidence": "low"}, {"file": "components/xp/LevelUpCelebration.tsx", "name": "LevelUpCelebration", "confidence": "low"}, {"file": "components/xp/LevelUpCelebration.tsx", "name": "LevelUpManager", "confidence": "low"}, {"file": "components/xp/LevelUpCelebration.tsx", "name": "useLevelUp", "confidence": "low"}, {"file": "components/xp/ProgressBar.tsx", "name": "ProgressBar", "confidence": "low"}, {"file": "components/xp/ProgressBar.tsx", "name": "LevelProgressBar", "confidence": "low"}, {"file": "components/xp/XPCounter.tsx", "name": "XPCounter", "confidence": "low"}, {"file": "components/xp/XPCounter.tsx", "name": "SessionXPSummary", "confidence": "low"}, {"file": "components/xp/XPCounter.tsx", "name": "CompactXPDisplay", "confidence": "low"}, {"file": "components/xp/XPNotification.tsx", "name": "XPGain", "confidence": "low"}, {"file": "components/xp/XPNotification.tsx", "name": "XPNotification", "confidence": "low"}, {"file": "components/xp/XPNotification.tsx", "name": "XPNotificationManager", "confidence": "low"}, {"file": "components/xp/XPNotification.tsx", "name": "useXPNotifications", "confidence": "low"}, {"file": "lib/accessibility/AccessibilityTester.ts", "name": "AccessibilityTestResult", "confidence": "low"}, {"file": "lib/accessibility/AccessibilityTester.ts", "name": "AccessibilityTestOptions", "confidence": "low"}, {"file": "lib/accessibility/AccessibilityTester.ts", "name": "AccessibilityTester", "confidence": "low"}, {"file": "lib/accessibility/AccessibilityTester.ts", "name": "accessibilityTester", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "hexToRgb", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "rgbToHex", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "getLuminance", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "getContrastRatio", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "meetsWCAGAA", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "meetsWCAGAAA", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "accessibleColors", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "generateAccessibleVariation", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "generateFocusRing", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "textSizes", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "accessibleSpacing", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "respectsReducedMotion", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "getAnimationDuration", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "announceToScreenReader", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "trapFocus", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "simulateColorBlindness", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "testColorAccessibility", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "generateAccessibleScheme", "confidence": "low"}, {"file": "lib/accessibility/contrast-utils.ts", "name": "auditPageAccessibility", "confidence": "low"}, {"file": "lib/accessibility/EditorAccessibility.ts", "name": "EditorAccessibilityManager", "confidence": "low"}, {"file": "lib/achievements/data.ts", "name": "ACHIEVEMENTS", "confidence": "low"}, {"file": "lib/achievements/data.ts", "name": "ACHIEVEMENT_CATEGORIES", "confidence": "low"}, {"file": "lib/achievements/data.ts", "name": "ACHIEVEMENT_RARITIES", "confidence": "low"}, {"file": "lib/achievements/data.ts", "name": "LEVEL_PROGRESSION", "confidence": "low"}, {"file": "lib/achievements/data.ts", "name": "getAchievementById", "confidence": "low"}, {"file": "lib/achievements/data.ts", "name": "getAchievementsByCategory", "confidence": "low"}, {"file": "lib/achievements/data.ts", "name": "getAchievementsByRarity", "confidence": "low"}, {"file": "lib/achievements/data.ts", "name": "getLevelInfo", "confidence": "low"}, {"file": "lib/achievements/data.ts", "name": "calculateAchievementProgress", "confidence": "low"}, {"file": "lib/achievements/manager.ts", "name": "AchievementManager", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementStatus", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementCategory", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementRarity", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementRequirement", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementReward", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "Achievement", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "UserAchievement", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementProgress", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementNotification", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementEvent", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementFilter", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementStats", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementConfig", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "LevelInfo", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "LeaderboardEntry", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "Leaderboard", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementManager", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementEventHandler", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "AchievementUnlockHandler", "confidence": "low"}, {"file": "lib/achievements/types.ts", "name": "ProgressUpdateHandler", "confidence": "low"}, {"file": "lib/admin/auditLogger.ts", "name": "AuditLogger", "confidence": "low"}, {"file": "lib/admin/auditLogger.ts", "name": "auditLogger", "confidence": "low"}, {"file": "lib/admin/auditLogger.ts", "name": "auditActions", "confidence": "low"}, {"file": "lib/admin/auth.ts", "name": "AdminAuthManager", "confidence": "low"}, {"file": "lib/admin/auth.ts", "name": "ADMIN_PERMISSIONS", "confidence": "low"}, {"file": "lib/admin/auth.ts", "name": "adminAuth", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminUser", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AuditLog", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminPermission", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminRole", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "CommunityReport", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "SecurityEvent", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminStats", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "BulkOperation", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminNotification", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "Admin<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminSort", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminPagination", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminSearchParams", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminActionResult", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminConfirmation", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminUndo", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "ContentVersion", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "ModerationAction", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "AdminDashboardWidget", "confidence": "low"}, {"file": "lib/admin/types.ts", "name": "SystemHealth", "confidence": "low"}, {"file": "lib/ai/LearningAssistant.ts", "name": "AIResponse", "confidence": "low"}, {"file": "lib/ai/LearningAssistant.ts", "name": "LearningContext", "confidence": "low"}, {"file": "lib/ai/LearningAssistant.ts", "name": "LearningAssistant", "confidence": "low"}, {"file": "lib/analysis/SolidityCodeAnalyzer.ts", "name": "AnalysisIssue", "confidence": "low"}, {"file": "lib/analysis/SolidityCodeAnalyzer.ts", "name": "OptimizationSuggestion", "confidence": "low"}, {"file": "lib/analysis/SolidityCodeAnalyzer.ts", "name": "SecurityVulnerability", "confidence": "low"}, {"file": "lib/analysis/SolidityCodeAnalyzer.ts", "name": "AnalysisResult", "confidence": "low"}, {"file": "lib/analysis/SolidityCodeAnalyzer.ts", "name": "SolidityCodeAnalyzer", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "buttonVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "cardVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "progressVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "achievementVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "spinnerVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "notificationVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "inputVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "pageVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "staggerVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "staggerItemVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "fabVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "tooltipVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "modalVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "backdropVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "codeEditorVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "xpGainVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "cursorVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "attentionVariants", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "createSpringTransition", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "createEaseTransition", "confidence": "low"}, {"file": "lib/animations/micro-interactions.ts", "name": "animationPresets", "confidence": "low"}, {"file": "lib/api/auth.ts", "name": "JwtPayload", "confidence": "low"}, {"file": "lib/api/auth.ts", "name": "RefreshTokenPayload", "confidence": "low"}, {"file": "lib/api/auth.ts", "name": "AuthService", "confidence": "low"}, {"file": "lib/api/auth.ts", "name": "PasswordValidator", "confidence": "low"}, {"file": "lib/api/auth.ts", "name": "SECURITY_HEADERS", "confidence": "low"}, {"file": "lib/api/auth.ts", "name": "CORS_CONFIG", "confidence": "low"}, {"file": "lib/api/auth.ts", "name": "InputSanitizer", "confidence": "low"}, {"file": "lib/api/cache.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/cache.ts", "name": "cacheMiddleware", "confidence": "low"}, {"file": "lib/api/cache.ts", "name": "CachePatterns", "confidence": "low"}, {"file": "lib/api/cache.ts", "name": "CacheTTL", "confidence": "low"}, {"file": "lib/api/cache.ts", "name": "cache", "confidence": "low"}, {"file": "lib/api/cache.ts", "name": "invalidateUserCache", "confidence": "low"}, {"file": "lib/api/cache.ts", "name": "invalidate<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/cache.ts", "name": "invalidateCourseCache", "confidence": "low"}, {"file": "lib/api/documentation.ts", "name": "ApiDocumentationGenerator", "confidence": "low"}, {"file": "lib/api/documentation.ts", "name": "apiDocumentation", "confidence": "low"}, {"file": "lib/api/documentation.ts", "name": "generateApiDocumentation", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "ApiError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "UnauthorizedError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "ForbiddenError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "InvalidTokenError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "TokenExpiredError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "ValidationError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "InvalidInputError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "Missing<PERSON>ieldE<PERSON>r", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "NotFoundError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "AlreadyExistsError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "ConflictError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "RateLimitError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "TooManyRequestsError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "InternalServerError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "DatabaseError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "ServiceUnavailableError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "FileTooLargeError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "InvalidFileTypeError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "UploadFailedError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "FeatureDisabledError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "FeatureNotAvailableError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "InsufficientXPError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "LessonNotCompletedError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "CourseNotAccessibleError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "AchievementAlreadyEarnedError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "createValidationErrors", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "createNotFoundError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "createAlreadyExistsError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "isApiError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "isOperationalError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "isValidationError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "isAuthenticationError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "isAuthorizationError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "isRateLimitError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "isServerError", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "ErrorSeverity", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "getErrorSeverity", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "ErrorContext", "confidence": "low"}, {"file": "lib/api/errors.ts", "name": "createErrorContext", "confidence": "low"}, {"file": "lib/api/integration.ts", "name": "createApiRoute", "confidence": "low"}, {"file": "lib/api/integration.ts", "name": "trackApiError", "confidence": "low"}, {"file": "lib/api/integration.ts", "name": "createFallbackMiddleware", "confidence": "low"}, {"file": "lib/api/integration.ts", "name": "createAccessibilityMiddleware", "confidence": "low"}, {"file": "lib/api/integration.ts", "name": "createPerformanceMiddleware", "confidence": "low"}, {"file": "lib/api/integration.ts", "name": "createCompleteApiMiddleware", "confidence": "low"}, {"file": "lib/api/integration.ts", "name": "api", "confidence": "low"}, {"file": "lib/api/logger.ts", "name": "LogLevel", "confidence": "low"}, {"file": "lib/api/logger.ts", "name": "LogEntry", "confidence": "low"}, {"file": "lib/api/logger.ts", "name": "LoggerConfig", "confidence": "low"}, {"file": "lib/api/logger.ts", "name": "<PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/logger.ts", "name": "AuditLogger", "confidence": "low"}, {"file": "lib/api/logger.ts", "name": "logger", "confidence": "low"}, {"file": "lib/api/logger.ts", "name": "auditLogger", "confidence": "low"}, {"file": "lib/api/logger.ts", "name": "logApiRequest", "confidence": "low"}, {"file": "lib/api/logger.ts", "name": "logApiResponse", "confidence": "low"}, {"file": "lib/api/logger.ts", "name": "logApiError", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "LogLevel", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "RequestContext", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "ResponseContext", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "LogEntry", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "logRequest", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "logResponse", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "logError", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "logInfo", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "log<PERSON>arn", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "logDebug", "confidence": "low"}, {"file": "lib/api/logging.ts", "name": "createLoggingMiddleware", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "MiddlewareContext", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "MiddlewareOptions", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "RequestLogger", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "corsMiddleware", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "responseMiddleware", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "withMiddleware", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "publicEndpoint", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "protectedEndpoint", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "adminEndpoint", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "authEndpoint", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "uploadEndpoint", "confidence": "low"}, {"file": "lib/api/middleware.ts", "name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/optimizedApiClient.ts", "name": "ApiRequestConfig", "confidence": "low"}, {"file": "lib/api/optimizedApiClient.ts", "name": "ApiResponse", "confidence": "low"}, {"file": "lib/api/optimizedApiClient.ts", "name": "apiClient", "confidence": "low"}, {"file": "lib/api/optimizedApiClient.ts", "name": "createQueryKey", "confidence": "low"}, {"file": "lib/api/optimizedApiClient.ts", "name": "useOptimizedQuery", "confidence": "low"}, {"file": "lib/api/optimizedApiClient.ts", "name": "useOptimizedMutation", "confidence": "low"}, {"file": "lib/api/optimizedApiClient.ts", "name": "default", "confidence": "low"}, {"file": "lib/api/rate-limiting.ts", "name": "RateLimitConfig", "confidence": "low"}, {"file": "lib/api/rate-limiting.ts", "name": "RateLimitStore", "confidence": "low"}, {"file": "lib/api/rate-limiting.ts", "name": "RedisRateLimitStore", "confidence": "low"}, {"file": "lib/api/rate-limiting.ts", "name": "MemoryRateLimitStore", "confidence": "low"}, {"file": "lib/api/rate-limiting.ts", "name": "RateLimiter", "confidence": "low"}, {"file": "lib/api/rate-limiting.ts", "name": "rateLimitConfigs", "confidence": "low"}, {"file": "lib/api/rate-limiting.ts", "name": "rateLimiters", "confidence": "low"}, {"file": "lib/api/rate-limiting.ts", "name": "withRateLimit", "confidence": "low"}, {"file": "lib/api/rateLimit.ts", "name": "RateLimitConfig", "confidence": "low"}, {"file": "lib/api/rateLimit.ts", "name": "RATE_LIMIT_CONFIGS", "confidence": "low"}, {"file": "lib/api/rateLimit.ts", "name": "RateLimiter", "confidence": "low"}, {"file": "lib/api/rateLimit.ts", "name": "RateLimitManager", "confidence": "low"}, {"file": "lib/api/rateLimit.ts", "name": "getRateLimitManager", "confidence": "low"}, {"file": "lib/api/rateLimit.ts", "name": "AdaptiveRateLimiter", "confidence": "low"}, {"file": "lib/api/rateLimit.ts", "name": "RateLimitMetrics", "confidence": "low"}, {"file": "lib/api/rateLimit.ts", "name": "RateLimitMonitor", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "ApiResponseBuilder", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "ApiException", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "ValidationException", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "UnauthorizedException", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "ForbiddenException", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "NotFoundException", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "ConflictException", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "RateLimitException", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "createPaginationMeta", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "sanitizeForResponse", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "filterByPermissions", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "addSecurityHeaders", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "addCorsHeaders", "confidence": "low"}, {"file": "lib/api/response.ts", "name": "addRateLimitHeaders", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "Id", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "Email", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "Password", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "Pagin<PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "SearchQuery", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "CreateUserRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "UpdateUserRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "UpdateUserProfileRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "UpdateUserPreferencesRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ChangePasswordRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "LoginRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "RegisterRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ForgotPasswordRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ResetPasswordRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "RefreshTokenRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "CreateLessonRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "UpdateLessonRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "PublishLessonRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "CreateCourseRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "UpdateCourseRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "CreateProgressRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "UpdateProgressRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "CompleteProgressRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "CreateAchievementRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "UpdateAchievementRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "LeaderboardFilters", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "StatsFilters", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "RateLimitConfig", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "FileUploadRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ImageUploadRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "SettingsUpdateRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ProfileSettingsRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "SecuritySettingsRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "LearningSettingsRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "EditorSettingsRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "SearchRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ErrorReportRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "MetricsQuery", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "FeatureFlagQuery", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ApiResponse", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ValidationError", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ValidatedRequest", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "PaginatedResponse", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "SearchResponse", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ErrorMetricsResponse", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ApiMetricsResponse", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "FeatureFlagResponse", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "RateLimitInfo", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "FileUploadResponse", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "isApiResponse", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "isValidationError", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "isPaginatedResponse", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ApiEndpoint", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "ValidatedApiEndpoint", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "SchemaRegistry", "confidence": "low"}, {"file": "lib/api/schema-types.ts", "name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/security.ts", "name": "SecurityConfig", "confidence": "low"}, {"file": "lib/api/security.ts", "name": "SecurityMiddleware", "confidence": "low"}, {"file": "lib/api/security.ts", "name": "InputSanitizer", "confidence": "low"}, {"file": "lib/api/security.ts", "name": "securityMiddleware", "confidence": "low"}, {"file": "lib/api/security.ts", "name": "withSecurity", "confidence": "low"}, {"file": "lib/api/security.ts", "name": "withTimeout", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "TestRequestBuilder", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "TestResponseAssertions", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "TestApiClient", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "MockDataGenerator", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "TestDatabase", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "createTestResponse", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "createTestApiResponse", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "describeApiEndpoint", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "itShouldRequireAuth", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "itShouldValidateInput", "confidence": "low"}, {"file": "lib/api/testing.ts", "name": "itShouldRateLimit", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ApiResponse", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ApiError", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ValidationError", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ResponseMeta", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "PaginationMeta", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "Pagin<PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ApiErrorCode", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "HttpStatus", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ApiUser", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "UserProfile", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "UserPreferences", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "UserRole", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "UserStatus", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "LessonType", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "DifficultyLevel", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "LessonStatus", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ApiProgress", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ProgressStatus", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ApiCourse", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "CourseStatus", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ApiAchievement", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "AchievementCategory", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "AchievementType", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "AchievementRarity", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "AchievementRequirement", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ApiLog", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "RateLimitInfo", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "HealthCheck", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ServiceHealth", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "FileUploadRequest", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "FileUploadResponse", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "SearchRequest", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "SearchResult", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "SearchResponse", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "FeatureFlagResponse", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ErrorReportRequest", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ErrorMetricsResponse", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "SettingsUpdateRequest", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "LoginRequest", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "RegisterRequest", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "AuthResponse", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "RefreshTokenRequest", "confidence": "low"}, {"file": "lib/api/types.ts", "name": "ApiConfig", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "generateRequestId", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "getClientIP", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "createApiResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "createApiError", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "createResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "successResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "errorResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "validationErrorResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "notFoundResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "unauthorizedResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "forbiddenResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "rateLimitResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "internalErrorResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "parsePaginationParams", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "createPaginationMeta", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "sanitizeData", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "parseUserAgent", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "validate<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "methodNotAllowedResponse", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/api/utils.ts", "name": "createCorsHeaders", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "IdSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "EmailSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "PasswordSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "PaginationSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "SearchSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "CreateUserSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "UpdateUserSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "UpdateUserProfileSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "UpdateUserPreferencesSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "ChangePasswordSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "LoginSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "RegisterSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "ForgotPasswordSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "ResetPasswordSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "RefreshTokenSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "CreateLessonSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "UpdateLessonSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "PublishLessonSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "CreateCourseSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "UpdateCourseSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "CreateProgressSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "UpdateProgressSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "CompleteProgressSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "CreateAchievementSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "UpdateAchievementSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "LeaderboardFiltersSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "StatsFiltersSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "validateSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "validate<PERSON><PERSON>y", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "validateBody", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "sanitizeString", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "sanitizeObject", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "RateLimitConfigSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "FileUploadSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "ImageUploadSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "SettingsUpdateSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "ProfileSettingsSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "SecuritySettingsSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "LearningSettingsSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "EditorSettingsSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "SearchQuerySchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "ErrorReportSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "MetricsQuerySchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "FeatureFlagQuerySchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "createValidationMiddleware", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "validateAndSanitize", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "createPaginatedSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "createSearchableSchema", "confidence": "low"}, {"file": "lib/api/validation.ts", "name": "validateResponse", "confidence": "low"}, {"file": "lib/auth/config.ts", "name": "authOptions", "confidence": "low"}, {"file": "lib/auth/mock-auth.tsx", "name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/auth/mock-auth.tsx", "name": "MockAuthState", "confidence": "low"}, {"file": "lib/auth/mock-auth.tsx", "name": "MockAuthActions", "confidence": "low"}, {"file": "lib/auth/mock-auth.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/auth/mock-auth.tsx", "name": "useMockAuth", "confidence": "low"}, {"file": "lib/auth/mock-auth.tsx", "name": "useMockPermissions", "confidence": "low"}, {"file": "lib/auth/mock-auth.tsx", "name": "useMockAuthStatus", "confidence": "low"}, {"file": "lib/auth/navigationGuard.ts", "name": "NavigationGuardConfig", "confidence": "low"}, {"file": "lib/auth/navigationGuard.ts", "name": "NavigationContext", "confidence": "low"}, {"file": "lib/auth/navigationGuard.ts", "name": "RedirectOptions", "confidence": "low"}, {"file": "lib/auth/navigationGuard.ts", "name": "NavigationGuard", "confidence": "low"}, {"file": "lib/auth/navigationGuard.ts", "name": "useNavigationGuard", "confidence": "low"}, {"file": "lib/auth/navigationGuard.ts", "name": "createNavigationContext", "confidence": "low"}, {"file": "lib/auth/password.ts", "name": "passwordSchema", "confidence": "low"}, {"file": "lib/auth/password.ts", "name": "emailSchema", "confidence": "low"}, {"file": "lib/auth/password.ts", "name": "registrationSchema", "confidence": "low"}, {"file": "lib/auth/password.ts", "name": "loginSchema", "confidence": "low"}, {"file": "lib/auth/password.ts", "name": "PasswordUtils", "confidence": "low"}, {"file": "lib/auth/password.ts", "name": "RegistrationData", "confidence": "low"}, {"file": "lib/auth/password.ts", "name": "LoginData", "confidence": "low"}, {"file": "lib/auth/sessionManager.ts", "name": "SessionData", "confidence": "low"}, {"file": "lib/auth/sessionManager.ts", "name": "SessionConfig", "confidence": "low"}, {"file": "lib/auth/sessionManager.ts", "name": "SessionStatus", "confidence": "low"}, {"file": "lib/auth/sessionManager.ts", "name": "SessionEvent", "confidence": "low"}, {"file": "lib/auth/sessionManager.ts", "name": "Session<PERSON>anager", "confidence": "low"}, {"file": "lib/blockchain/Web3Provider.tsx", "name": "useWeb3", "confidence": "low"}, {"file": "lib/blockchain/Web3Provider.tsx", "name": "SUPPORTED_NETWORKS", "confidence": "low"}, {"file": "lib/blockchain/Web3Provider.tsx", "name": "Web3Provider", "confidence": "low"}, {"file": "lib/collaboration/AdvancedCollaborativeEditor.ts", "name": "CollaboratorInfo", "confidence": "low"}, {"file": "lib/collaboration/AdvancedCollaborativeEditor.ts", "name": "DocumentState", "confidence": "low"}, {"file": "lib/collaboration/AdvancedCollaborativeEditor.ts", "name": "ChangeEvent", "confidence": "low"}, {"file": "lib/collaboration/AdvancedCollaborativeEditor.ts", "name": "CursorEvent", "confidence": "low"}, {"file": "lib/collaboration/AdvancedCollaborativeEditor.ts", "name": "ConflictEvent", "confidence": "low"}, {"file": "lib/collaboration/AdvancedCollaborativeEditor.ts", "name": "AdvancedCollaborativeEditor", "confidence": "low"}, {"file": "lib/collaboration/CollaborationClient.ts", "name": "CollaborationUser", "confidence": "low"}, {"file": "lib/collaboration/CollaborationClient.ts", "name": "CollaborationMessage", "confidence": "low"}, {"file": "lib/collaboration/CollaborationClient.ts", "name": "CollaborationSession", "confidence": "low"}, {"file": "lib/collaboration/CollaborationClient.ts", "name": "ConnectionStatus", "confidence": "low"}, {"file": "lib/collaboration/CollaborationClient.ts", "name": "CollaborationClient", "confidence": "low"}, {"file": "lib/collaboration/CollaborativeEditor.ts", "name": "CursorDecoration", "confidence": "low"}, {"file": "lib/collaboration/CollaborativeEditor.ts", "name": "CollaborativeEditorOptions", "confidence": "low"}, {"file": "lib/collaboration/CollaborativeEditor.ts", "name": "CollaborativeEditor", "confidence": "low"}, {"file": "lib/collaboration/ConnectionManager.ts", "name": "ConnectionManager", "confidence": "low"}, {"file": "lib/collaboration/OperationalTransform.ts", "name": "Operation", "confidence": "low"}, {"file": "lib/collaboration/OperationalTransform.ts", "name": "SelectionRange", "confidence": "low"}, {"file": "lib/collaboration/OperationalTransform.ts", "name": "TextOperation", "confidence": "low"}, {"file": "lib/collaboration/OperationalTransform.ts", "name": "ConflictResolution", "confidence": "low"}, {"file": "lib/collaboration/OperationalTransform.ts", "name": "OperationResult", "confidence": "low"}, {"file": "lib/collaboration/OperationalTransform.ts", "name": "OperationalTransform", "confidence": "low"}, {"file": "lib/community/leaderboard.ts", "name": "LeaderboardManager", "confidence": "low"}, {"file": "lib/community/leaderboard.ts", "name": "leaderboardManager", "confidence": "low"}, {"file": "lib/community/leaderboard.ts", "name": "LeaderboardUtils", "confidence": "low"}, {"file": "lib/community/statistics.ts", "name": "CommunityStatsManager", "confidence": "low"}, {"file": "lib/community/statistics.ts", "name": "communityStatsManager", "confidence": "low"}, {"file": "lib/community/statistics.ts", "name": "StatsUtils", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "LeaderboardUser", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "Badge", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "Achievement", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "LeaderboardCategory", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "LeaderboardFilters", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "LeaderboardResponse", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "CommunityStats", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "TrendingTopic", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "CommunityMilestone", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "StatsFilters", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "ExportOptions", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "RealTimeUpdate", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "WebSocketMessage", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "LeaderboardCache", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "CommunityConfig", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "UserProgress", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "LeaderboardEvent", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "CommunityFeatureFlags", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "AdminCommunityControls", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "CommunityError", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "CommunityErrorCode", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "PerformanceMetrics", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "LoadingState", "confidence": "low"}, {"file": "lib/community/types.ts", "name": "CommunityNotification", "confidence": "low"}, {"file": "lib/community/websocket.ts", "name": "CommunityWebSocket", "confidence": "low"}, {"file": "lib/community/websocket.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/community/websocket.ts", "name": "RealTimeManager", "confidence": "low"}, {"file": "lib/community/websocket.ts", "name": "realTimeManager", "confidence": "low"}, {"file": "lib/compiler/SolidityCompiler.ts", "name": "CompilationResult", "confidence": "low"}, {"file": "lib/compiler/SolidityCompiler.ts", "name": "SecurityIssue", "confidence": "low"}, {"file": "lib/compiler/SolidityCompiler.ts", "name": "SolidityCompiler", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "env", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "isProduction", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "isStaging", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "isDevelopment", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "isServer", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "isClient", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "features", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "betaFeatures", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "dbConfig", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "redisConfig", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "rateLimitConfig", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "aiConfig", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "socketConfig", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "securityConfig", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "monitoringConfig", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "validateCriticalConfig", "confidence": "low"}, {"file": "lib/config/environment.ts", "name": "Environment", "confidence": "low"}, {"file": "lib/config/secrets.ts", "name": "secretsManager", "confidence": "low"}, {"file": "lib/config/secrets.ts", "name": "getSecret", "confidence": "low"}, {"file": "lib/config/secrets.ts", "name": "getValidatedSecret", "confidence": "low"}, {"file": "lib/config/secrets.ts", "name": "checkSecretRotation", "confidence": "low"}, {"file": "lib/config/secrets.ts", "name": "initializeSecrets", "confidence": "low"}, {"file": "lib/context/CollaborationContext.tsx", "name": "CollaborationProvider", "confidence": "low"}, {"file": "lib/context/CollaborationContext.tsx", "name": "useCollaboration", "confidence": "low"}, {"file": "lib/context/LearningContext.tsx", "name": "useLearning", "confidence": "low"}, {"file": "lib/context/LearningContext.tsx", "name": "LearningProvider", "confidence": "low"}, {"file": "lib/curriculum/data.ts", "name": "SOLIDITY_LESSONS", "confidence": "low"}, {"file": "lib/curriculum/data.ts", "name": "SOLIDITY_MODULES", "confidence": "low"}, {"file": "lib/curriculum/data.ts", "name": "LEARNING_PATHS", "confidence": "low"}, {"file": "lib/curriculum/data.ts", "name": "getModuleById", "confidence": "low"}, {"file": "lib/curriculum/data.ts", "name": "getLessonById", "confidence": "low"}, {"file": "lib/curriculum/data.ts", "name": "getLearningPathById", "confidence": "low"}, {"file": "lib/curriculum/data.ts", "name": "getModulesForPath", "confidence": "low"}, {"file": "lib/curriculum/data.ts", "name": "calculateModuleProgress", "confidence": "low"}, {"file": "lib/curriculum/data.ts", "name": "getNextAvailableLesson", "confidence": "low"}, {"file": "lib/curriculum/data.ts", "name": "checkPrerequisites", "confidence": "low"}, {"file": "lib/curriculum/manager.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "LessonStatus", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "ModuleStatus", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "DifficultyLevel", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "LessonType", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "Prerequisite", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "LessonProgress", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "Lesson", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "ModuleProgress", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "LearningPath", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "UserCurriculumProgress", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "CurriculumRecommendation", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "LearningAnalytics", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "Cur<PERSON>ulum<PERSON><PERSON>er", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "ProgressUpdateHandler", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "CompletionHandler", "confidence": "low"}, {"file": "lib/curriculum/types.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "CleanupOperation", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "CleanupCategory", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "CleanupSeverity", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "CleanupOptions", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "CleanupResult", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "CleanupReport", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "BackupService", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "CleanupManager", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "SafetyUtils", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "cleanupManager", "confidence": "low"}, {"file": "lib/database/cleanup.ts", "name": "backupService", "confidence": "low"}, {"file": "lib/database/data-removal.ts", "name": "registerDataRemovalOperations", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "MaintenanceSchedule", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "CronSchedule", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "NotificationConfig", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "MaintenanceReport", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "SystemMetrics", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "TableStats", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "QueryStats", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "DiskUsage", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "MemoryUsage", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "MaintenanceScheduler", "confidence": "low"}, {"file": "lib/database/maintenance.ts", "name": "maintenanceScheduler", "confidence": "low"}, {"file": "lib/database/migrations.ts", "name": "Migration", "confidence": "low"}, {"file": "lib/database/migrations.ts", "name": "MigrationStatus", "confidence": "low"}, {"file": "lib/database/migrations.ts", "name": "MigrationResult", "confidence": "low"}, {"file": "lib/database/migrations.ts", "name": "MigrationPlan", "confidence": "low"}, {"file": "lib/database/migrations.ts", "name": "<PERSON><PERSON><PERSON>anager", "confidence": "low"}, {"file": "lib/database/migrations.ts", "name": "migrationManager", "confidence": "low"}, {"file": "lib/database/orphaned-data.ts", "name": "registerOrphanedDataOperations", "confidence": "low"}, {"file": "lib/debugging/SolidityDebugger.ts", "name": "BreakpointInfo", "confidence": "low"}, {"file": "lib/debugging/SolidityDebugger.ts", "name": "VariableInfo", "confidence": "low"}, {"file": "lib/debugging/SolidityDebugger.ts", "name": "CallStackFrame", "confidence": "low"}, {"file": "lib/debugging/SolidityDebugger.ts", "name": "ExecutionState", "confidence": "low"}, {"file": "lib/debugging/SolidityDebugger.ts", "name": "DebugSession", "confidence": "low"}, {"file": "lib/debugging/SolidityDebugger.ts", "name": "StepResult", "confidence": "low"}, {"file": "lib/debugging/SolidityDebugger.ts", "name": "SolidityDebugger", "confidence": "low"}, {"file": "lib/editor/AdvancedEditorConfig.ts", "name": "AdvancedEditorConfig", "confidence": "low"}, {"file": "lib/editor/ErrorHighlighting.ts", "name": "ErrorHighlightingManager", "confidence": "low"}, {"file": "lib/editor/MonacoSoliditySetup.ts", "name": "solidityTheme", "confidence": "low"}, {"file": "lib/editor/MonacoSoliditySetup.ts", "name": "MonacoSoliditySetup", "confidence": "low"}, {"file": "lib/editor/RealTimeSyntaxChecker.ts", "name": "RealTimeSyntaxChecker", "confidence": "low"}, {"file": "lib/editor/SolidityIntelliSense.ts", "name": "SoliditySymbol", "confidence": "low"}, {"file": "lib/editor/SolidityIntelliSense.ts", "name": "CompletionContext", "confidence": "low"}, {"file": "lib/editor/SolidityIntelliSense.ts", "name": "SolidityIntelliSense", "confidence": "low"}, {"file": "lib/editor/SolidityLanguageDefinition.ts", "name": "solidityLanguageConfig", "confidence": "low"}, {"file": "lib/editor/SolidityLanguageDefinition.ts", "name": "solidity<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/editor/SolidityLanguageDefinition.ts", "name": "solidityCompletionProvider", "confidence": "low"}, {"file": "lib/editor/SolidityLanguageDefinition.ts", "name": "solidityHoverProvider", "confidence": "low"}, {"file": "lib/editor/SolidityLanguageDefinition.ts", "name": "soliditySignatureHelpProvider", "confidence": "low"}, {"file": "lib/editor/SoliditySemanticAnalyzer.ts", "name": "SolidityError", "confidence": "low"}, {"file": "lib/editor/SoliditySemanticAnalyzer.ts", "name": "SoliditySymbol", "confidence": "low"}, {"file": "lib/editor/SoliditySemanticAnalyzer.ts", "name": "SolidityParameter", "confidence": "low"}, {"file": "lib/editor/SoliditySemanticAnalyzer.ts", "name": "AnalysisResult", "confidence": "low"}, {"file": "lib/editor/SoliditySemanticAnalyzer.ts", "name": "SoliditySemanticAnalyzer", "confidence": "low"}, {"file": "lib/errors/ErrorContext.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/errors/ErrorContext.tsx", "name": "useError", "confidence": "low"}, {"file": "lib/errors/ErrorContext.tsx", "name": "useAsyncError", "confidence": "low"}, {"file": "lib/errors/recovery.ts", "name": "RetryConfig", "confidence": "low"}, {"file": "lib/errors/recovery.ts", "name": "OfflineConfig", "confidence": "low"}, {"file": "lib/errors/recovery.ts", "name": "ErrorAnalytics", "confidence": "low"}, {"file": "lib/errors/recovery.ts", "name": "RetryManager", "confidence": "low"}, {"file": "lib/errors/recovery.ts", "name": "OfflineManager", "confidence": "low"}, {"file": "lib/errors/recovery.ts", "name": "ErrorAnalyticsManager", "confidence": "low"}, {"file": "lib/errors/recovery.ts", "name": "UserErrorReporter", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "ErrorSeverity", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "Error<PERSON>ate<PERSON><PERSON>", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "ErrorContext", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "BaseError", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "ErrorAction", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "ApiError", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "FormError", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "NavigationError", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "<PERSON>th<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "UploadError", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "NetworkError", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "AppError", "confidence": "low"}, {"file": "lib/errors/types.ts", "name": "ErrorFactory", "confidence": "low"}, {"file": "lib/features/feature-flags.ts", "name": "UserRole", "confidence": "low"}, {"file": "lib/features/feature-flags.ts", "name": "FeatureState", "confidence": "low"}, {"file": "lib/features/feature-flags.ts", "name": "FeatureFlag", "confidence": "low"}, {"file": "lib/features/feature-flags.ts", "name": "FEATURE_FLAGS", "confidence": "low"}, {"file": "lib/features/feature-flags.ts", "name": "isFeatureEnabled", "confidence": "low"}, {"file": "lib/features/feature-flags.ts", "name": "getFeatureInfo", "confidence": "low"}, {"file": "lib/features/feature-flags.ts", "name": "getUserFeatures", "confidence": "low"}, {"file": "lib/features/feature-flags.ts", "name": "getFeaturesByState", "confidence": "low"}, {"file": "lib/features/feature-flags.ts", "name": "areDependenciesMet", "confidence": "low"}, {"file": "lib/features/feature-flags.ts", "name": "getFeatureAccessReason", "confidence": "low"}, {"file": "lib/forms/form-handler.ts", "name": "FormField", "confidence": "low"}, {"file": "lib/forms/form-handler.ts", "name": "FormState", "confidence": "low"}, {"file": "lib/forms/form-handler.ts", "name": "FormConfig", "confidence": "low"}, {"file": "lib/forms/form-handler.ts", "name": "useForm", "confidence": "low"}, {"file": "lib/forms/form-handler.ts", "name": "formSchemas", "confidence": "low"}, {"file": "lib/forms/form-handler.ts", "name": "submitForm", "confidence": "low"}, {"file": "lib/forms/form-handler.ts", "name": "updateForm", "confidence": "low"}, {"file": "lib/git/CommitManager.ts", "name": "CommitManager", "confidence": "low"}, {"file": "lib/git/CommitManager.ts", "name": "commit<PERSON>anager", "confidence": "low"}, {"file": "lib/git/GitIntegration.ts", "name": "GitIntegrationManager", "confidence": "low"}, {"file": "lib/git/GitIntegration.ts", "name": "gitIntegration", "confidence": "low"}, {"file": "lib/hooks/useAchievements.ts", "name": "UseAchievementsReturn", "confidence": "low"}, {"file": "lib/hooks/useAchievements.ts", "name": "useAchievements", "confidence": "low"}, {"file": "lib/hooks/useAchievements.ts", "name": "useAchievementProgress", "confidence": "low"}, {"file": "lib/hooks/useAchievements.ts", "name": "useAchievementNotifications", "confidence": "low"}, {"file": "lib/hooks/useAchievements.ts", "name": "useGamificationStats", "confidence": "low"}, {"file": "lib/hooks/useAdvancedCollaborativeEditor.ts", "name": "UseAdvancedCollaborativeEditorOptions", "confidence": "low"}, {"file": "lib/hooks/useAdvancedCollaborativeEditor.ts", "name": "CollaborativeEditorState", "confidence": "low"}, {"file": "lib/hooks/useAdvancedCollaborativeEditor.ts", "name": "useAdvancedCollaborativeEditor", "confidence": "low"}, {"file": "lib/hooks/useApiData.ts", "name": "useUserProgress", "confidence": "low"}, {"file": "lib/hooks/useApiData.ts", "name": "useAchievements", "confidence": "low"}, {"file": "lib/hooks/useApiData.ts", "name": "useLearningPaths", "confidence": "low"}, {"file": "lib/hooks/useApiData.ts", "name": "useProjects", "confidence": "low"}, {"file": "lib/hooks/useApiData.ts", "name": "useCommunityStats", "confidence": "low"}, {"file": "lib/hooks/useAsyncButton.ts", "name": "AsyncButtonState", "confidence": "low"}, {"file": "lib/hooks/useAsyncButton.ts", "name": "AsyncButtonOptions", "confidence": "low"}, {"file": "lib/hooks/useAsyncButton.ts", "name": "useAsyncButton", "confidence": "low"}, {"file": "lib/hooks/useAsyncButton.ts", "name": "useDebouncedAsyncButton", "confidence": "low"}, {"file": "lib/hooks/useAsyncButton.ts", "name": "useFormSubmitButton", "confidence": "low"}, {"file": "lib/hooks/useAsyncButton.ts", "name": "useApiButton", "confidence": "low"}, {"file": "lib/hooks/useAuth.ts", "name": "AuthUser", "confidence": "low"}, {"file": "lib/hooks/useAuth.ts", "name": "AuthState", "confidence": "low"}, {"file": "lib/hooks/useAuth.ts", "name": "AuthActions", "confidence": "low"}, {"file": "lib/hooks/useAuth.ts", "name": "useAuth", "confidence": "low"}, {"file": "lib/hooks/useAuth.ts", "name": "usePermissions", "confidence": "low"}, {"file": "lib/hooks/useAuth.ts", "name": "useAuthStatus", "confidence": "low"}, {"file": "lib/hooks/useEnhancedKeyboardNavigation.ts", "name": "EnhancedKeyboardNavigationOptions", "confidence": "low"}, {"file": "lib/hooks/useEnhancedKeyboardNavigation.ts", "name": "useEnhancedKeyboardNavigation", "confidence": "low"}, {"file": "lib/hooks/useEnhancedKeyboardNavigation.ts", "name": "useModalKeyboardNavigation", "confidence": "low"}, {"file": "lib/hooks/useEnhancedKeyboardNavigation.ts", "name": "useDropdownKeyboardNavigation", "confidence": "low"}, {"file": "lib/hooks/useEnhancedKeyboardNavigation.ts", "name": "useFormKeyboardNavigation", "confidence": "low"}, {"file": "lib/hooks/useErrorRecovery.ts", "name": "useRetry", "confidence": "low"}, {"file": "lib/hooks/useErrorRecovery.ts", "name": "useOfflineDetection", "confidence": "low"}, {"file": "lib/hooks/useErrorRecovery.ts", "name": "useErrorAnalytics", "confidence": "low"}, {"file": "lib/hooks/useErrorRecovery.ts", "name": "useErrorReporting", "confidence": "low"}, {"file": "lib/hooks/useErrorRecovery.ts", "name": "useErrorHandler", "confidence": "low"}, {"file": "lib/hooks/useErrorRecovery.ts", "name": "useFormErrorHandler", "confidence": "low"}, {"file": "lib/hooks/useErrorRecovery.ts", "name": "useUploadErrorHandler", "confidence": "low"}, {"file": "lib/hooks/useFeatureFlags.tsx", "name": "useFeatureFlags", "confidence": "low"}, {"file": "lib/hooks/useFeatureFlags.tsx", "name": "useFeature", "confidence": "low"}, {"file": "lib/hooks/useFeatureFlags.tsx", "name": "useFeatures", "confidence": "low"}, {"file": "lib/hooks/useFeatureFlags.tsx", "name": "useBetaFeatures", "confidence": "low"}, {"file": "lib/hooks/useFeatureFlags.tsx", "name": "useComingSoonFeatures", "confidence": "low"}, {"file": "lib/hooks/useFeatureFlags.tsx", "name": "useDevelopmentFeatures", "confidence": "low"}, {"file": "lib/hooks/useFeatureFlags.tsx", "name": "FeatureFlagsProvider", "confidence": "low"}, {"file": "lib/hooks/useFeatureFlags.tsx", "name": "useFeatureFlagsContext", "confidence": "low"}, {"file": "lib/hooks/useFeatureFlags.tsx", "name": "useFeatureGate", "confidence": "low"}, {"file": "lib/hooks/useKeyboardNavigation.ts", "name": "KeyboardNavigationOptions", "confidence": "low"}, {"file": "lib/hooks/useKeyboardNavigation.ts", "name": "useKeyboardNavigation", "confidence": "low"}, {"file": "lib/hooks/useKeyboardNavigation.ts", "name": "useModalFocus", "confidence": "low"}, {"file": "lib/hooks/useKeyboardNavigation.ts", "name": "useDropdownFocus", "confidence": "low"}, {"file": "lib/hooks/useKeyboardNavigation.ts", "name": "useFormFocus", "confidence": "low"}, {"file": "lib/hooks/useKeyboardNavigation.ts", "name": "useGridFocus", "confidence": "low"}, {"file": "lib/hooks/useLazyLoading.ts", "name": "LazyLoadingOptions", "confidence": "low"}, {"file": "lib/hooks/useLazyLoading.ts", "name": "useIntersectionObserver", "confidence": "low"}, {"file": "lib/hooks/useLazyLoading.ts", "name": "useLazyComponent", "confidence": "low"}, {"file": "lib/hooks/useLazyLoading.ts", "name": "useLazyImage", "confidence": "low"}, {"file": "lib/hooks/useLazyLoading.ts", "name": "useLazyData", "confidence": "low"}, {"file": "lib/hooks/useLazyLoading.ts", "name": "useVirtualScrolling", "confidence": "low"}, {"file": "lib/hooks/useLazyLoading.ts", "name": "useProgressiveImage", "confidence": "low"}, {"file": "lib/hooks/useLazyLoading.ts", "name": "useLazyLoadingWithRetry", "confidence": "low"}, {"file": "lib/hooks/useLazyLoading.ts", "name": "default", "confidence": "low"}, {"file": "lib/hooks/useNotificationIntegrations.ts", "name": "useErrorNotifications", "confidence": "low"}, {"file": "lib/hooks/useNotificationIntegrations.ts", "name": "useAuthNotifications", "confidence": "low"}, {"file": "lib/hooks/useNotificationIntegrations.ts", "name": "useGamificationNotifications", "confidence": "low"}, {"file": "lib/hooks/useNotificationIntegrations.ts", "name": "useCollaborationNotifications", "confidence": "low"}, {"file": "lib/hooks/useNotificationIntegrations.ts", "name": "useAITutoringNotifications", "confidence": "low"}, {"file": "lib/hooks/useNotificationIntegrations.ts", "name": "useSystemNotifications", "confidence": "low"}, {"file": "lib/hooks/useNotificationSocket.ts", "name": "useNotificationSocket", "confidence": "low"}, {"file": "lib/hooks/useRealTimeXP.ts", "name": "XPUpdate", "confidence": "low"}, {"file": "lib/hooks/useRealTimeXP.ts", "name": "LevelInfo", "confidence": "low"}, {"file": "lib/hooks/useRealTimeXP.ts", "name": "SessionXPData", "confidence": "low"}, {"file": "lib/hooks/useRealTimeXP.ts", "name": "useRealTimeXP", "confidence": "low"}, {"file": "lib/hooks/useRealTimeXP.ts", "name": "useRealTimeProgress", "confidence": "low"}, {"file": "lib/hooks/useRealTimeXP.ts", "name": "useXPSync", "confidence": "low"}, {"file": "lib/hooks/useSessionStatus.ts", "name": "SessionStatusHookReturn", "confidence": "low"}, {"file": "lib/hooks/useSessionStatus.ts", "name": "useSessionStatus", "confidence": "low"}, {"file": "lib/hooks/useSessionStatus.ts", "name": "useSessionWarnings", "confidence": "low"}, {"file": "lib/hooks/useSessionStatus.ts", "name": "useSessionSync", "confidence": "low"}, {"file": "lib/hooks/useSessionStatus.ts", "name": "useSessionAnalytics", "confidence": "low"}, {"file": "lib/hooks/useSettings.ts", "name": "UseSettingsOptions", "confidence": "low"}, {"file": "lib/hooks/useSettings.ts", "name": "UseSettingsReturn", "confidence": "low"}, {"file": "lib/hooks/useSettings.ts", "name": "useSettings", "confidence": "low"}, {"file": "lib/hooks/useSolidityAnalyzer.ts", "name": "AnalyzerState", "confidence": "low"}, {"file": "lib/hooks/useSolidityAnalyzer.ts", "name": "UseAnalyzerOptions", "confidence": "low"}, {"file": "lib/hooks/useSolidityAnalyzer.ts", "name": "useSolidityAnalyzer", "confidence": "low"}, {"file": "lib/hooks/useSolidityDebugger.ts", "name": "DebuggerState", "confidence": "low"}, {"file": "lib/hooks/useSolidityDebugger.ts", "name": "UseDebuggerOptions", "confidence": "low"}, {"file": "lib/hooks/useSolidityDebugger.ts", "name": "useSolidityDebugger", "confidence": "low"}, {"file": "lib/hooks/useSolidityVersionControl.ts", "name": "VCSState", "confidence": "low"}, {"file": "lib/hooks/useSolidityVersionControl.ts", "name": "UseVCSOptions", "confidence": "low"}, {"file": "lib/hooks/useSolidityVersionControl.ts", "name": "useSolidityVersionControl", "confidence": "low"}, {"file": "lib/hooks/useSwipeGesture.ts", "name": "useSwipeGesture", "confidence": "low"}, {"file": "lib/hooks/useSwipeGesture.ts", "name": "useOutsideClick", "confidence": "low"}, {"file": "lib/hooks/useSwipeGesture.ts", "name": "useKeyboardNavigation", "confidence": "low"}, {"file": "lib/hooks/__tests__/useSettings.test.ts", "name": "exportResult", "confidence": "low"}, {"file": "lib/monitoring/analytics.ts", "name": "analytics", "confidence": "low"}, {"file": "lib/monitoring/analytics.ts", "name": "useAnalytics", "confidence": "low"}, {"file": "lib/monitoring/apiPerformance.ts", "name": "APIMetrics", "confidence": "low"}, {"file": "lib/monitoring/apiPerformance.ts", "name": "PerformanceThresholds", "confidence": "low"}, {"file": "lib/monitoring/apiPerformance.ts", "name": "apiPerformanceMonitor", "confidence": "low"}, {"file": "lib/monitoring/apiPerformance.ts", "name": "requestDeduplicator", "confidence": "low"}, {"file": "lib/monitoring/apiPerformance.ts", "name": "<PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/monitoring/apiPerformance.ts", "name": "OptimizedAPIClient", "confidence": "low"}, {"file": "lib/monitoring/apiPerformance.ts", "name": "apiClient", "confidence": "low"}, {"file": "lib/monitoring/apiPerformance.ts", "name": "default", "confidence": "low"}, {"file": "lib/monitoring/error-tracking.ts", "name": "ErrorEvent", "confidence": "low"}, {"file": "lib/monitoring/error-tracking.ts", "name": "ErrorMetrics", "confidence": "low"}, {"file": "lib/monitoring/error-tracking.ts", "name": "errorTracker", "confidence": "low"}, {"file": "lib/monitoring/error-tracking.ts", "name": "captureError", "confidence": "low"}, {"file": "lib/monitoring/error-tracking.ts", "name": "captureWarning", "confidence": "low"}, {"file": "lib/monitoring/error-tracking.ts", "name": "captureInfo", "confidence": "low"}, {"file": "lib/monitoring/error-tracking.ts", "name": "useErrorTracking", "confidence": "low"}, {"file": "lib/monitoring/errorTracking.ts", "name": "errorTracker", "confidence": "low"}, {"file": "lib/monitoring/errorTracking.ts", "name": "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/monitoring/errorTracking.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/monitoring/logger.ts", "name": "logger", "confidence": "low"}, {"file": "lib/monitoring/logger.ts", "name": "withPerformanceLogging", "confidence": "low"}, {"file": "lib/monitoring/logger.ts", "name": "createRequestLogger", "confidence": "low"}, {"file": "lib/performance/PerformanceMonitor.ts", "name": "PerformanceMonitor", "confidence": "low"}, {"file": "lib/performance/PerformanceMonitor.ts", "name": "performanceMonitor", "confidence": "low"}, {"file": "lib/prisma-client-wrapper.ts", "name": "prisma", "confidence": "low"}, {"file": "lib/prisma-client-wrapper.ts", "name": "default", "confidence": "low"}, {"file": "lib/prisma.ts", "name": "prisma", "confidence": "low"}, {"file": "lib/security/config.ts", "name": "SECURITY_CONFIG", "confidence": "low"}, {"file": "lib/security/config.ts", "name": "SecurityValidators", "confidence": "low"}, {"file": "lib/security/config.ts", "name": "SecurityUtils", "confidence": "low"}, {"file": "lib/security/config.ts", "name": "SecurityConfig", "confidence": "low"}, {"file": "lib/security/headers.ts", "name": "applySecurityHeaders", "confidence": "low"}, {"file": "lib/security/headers.ts", "name": "handleCORS", "confidence": "low"}, {"file": "lib/security/headers.ts", "name": "applyCORSHeaders", "confidence": "low"}, {"file": "lib/security/headers.ts", "name": "createSecurityMiddleware", "confidence": "low"}, {"file": "lib/security/headers.ts", "name": "withSecurity", "confidence": "low"}, {"file": "lib/security/headers.ts", "name": "socketCORSConfig", "confidence": "low"}, {"file": "lib/security/headers.ts", "name": "validate<PERSON><PERSON><PERSON>", "confidence": "low"}, {"file": "lib/security/headers.ts", "name": "generateSecurityReport", "confidence": "low"}, {"file": "lib/security/middleware.ts", "name": "createSecurityMiddleware", "confidence": "low"}, {"file": "lib/security/middleware.ts", "name": "authSecurityMiddleware", "confidence": "low"}, {"file": "lib/security/middleware.ts", "name": "apiSecurityMiddleware", "confidence": "low"}, {"file": "lib/security/middleware.ts", "name": "uploadSecurityMiddleware", "confidence": "low"}, {"file": "lib/security/rateLimiting.ts", "name": "rateLimiter", "confidence": "low"}, {"file": "lib/security/rateLimiting.ts", "name": "rateLimitConfigs", "confidence": "low"}, {"file": "lib/security/rateLimiting.ts", "name": "withRateLimit", "confidence": "low"}, {"file": "lib/security/session.ts", "name": "sessionSecurity", "confidence": "low"}, {"file": "lib/security/session.ts", "name": "getClientInfo", "confidence": "low"}, {"file": "lib/security/session.ts", "name": "validateCSRF", "confidence": "low"}, {"file": "lib/security/session.ts", "name": "createSessionMiddleware", "confidence": "low"}, {"file": "lib/security/session.ts", "name": "getSecureCookieOptions", "confidence": "low"}, {"file": "lib/security/session.ts", "name": "detectSuspiciousActivity", "confidence": "low"}, {"file": "lib/security/validation.ts", "name": "commonSchemas", "confidence": "low"}, {"file": "lib/security/validation.ts", "name": "sanitize", "confidence": "low"}, {"file": "lib/security/validation.ts", "name": "validate", "confidence": "low"}, {"file": "lib/security/validation.ts", "name": "validateRequest", "confidence": "low"}, {"file": "lib/security/validation.ts", "name": "sanitizeAndValidate", "confidence": "low"}, {"file": "lib/security/validation.ts", "name": "apiSchemas", "confidence": "low"}, {"file": "lib/services/SettingsService.ts", "name": "SettingsService", "confidence": "low"}, {"file": "lib/socket/client.ts", "name": "useSocket", "confidence": "low"}, {"file": "lib/socket/client.ts", "name": "useCollaborationSessions", "confidence": "low"}, {"file": "lib/socket/NotificationSocketService.ts", "name": "SocketNotificationEvent", "confidence": "low"}, {"file": "lib/socket/NotificationSocketService.ts", "name": "SocketCollaborationEvent", "confidence": "low"}, {"file": "lib/socket/NotificationSocketService.ts", "name": "SocketGamificationEvent", "confidence": "low"}, {"file": "lib/socket/NotificationSocketService.ts", "name": "SocketSystemEvent", "confidence": "low"}, {"file": "lib/socket/NotificationSocketService.ts", "name": "SocketEvent", "confidence": "low"}, {"file": "lib/socket/NotificationSocketService.ts", "name": "NotificationSocketService", "confidence": "low"}, {"file": "lib/socket/server.ts", "name": "NextApiResponseServerIO", "confidence": "low"}, {"file": "lib/socket/server.ts", "name": "initializeSocket", "confidence": "low"}, {"file": "lib/socket/SocketProvider.tsx", "name": "useSocket", "confidence": "low"}, {"file": "lib/socket/SocketProvider.tsx", "name": "SocketProvider", "confidence": "low"}, {"file": "lib/storage/CodePersistence.ts", "name": "codePersistence", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "UXTestScenario", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "UXTestStep", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "NetworkSimulator", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "LoadingStateTestUtils", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "ErrorBoundaryTestUtils", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "ToastTestUtils", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "NavigationTestUtils", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "AccessibilityTestUtils", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "PerformanceTestUtils", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "UX_TEST_SCENARIOS", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "IntegrationTestUtils", "confidence": "low"}, {"file": "lib/testing/ux-testing.ts", "name": "UXTestRunner", "confidence": "low"}, {"file": "lib/theme/ThemeProvider.tsx", "name": "ThemeProvider", "confidence": "low"}, {"file": "lib/theme/ThemeProvider.tsx", "name": "useTheme", "confidence": "low"}, {"file": "lib/theme/ThemeProvider.tsx", "name": "ThemeToggle", "confidence": "low"}, {"file": "lib/theme/ThemeProvider.tsx", "name": "EyeComfortSettings", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "AccessibilityIssue", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "AccessibilityReport", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "checkColorContrast", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "checkAriaLabels", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "checkKeyboardAccessibility", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "checkFocusManagement", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "checkTouchTargets", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "auditAccessibility", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "generateAccessibilityReport", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "announceToScreenReader", "confidence": "low"}, {"file": "lib/utils/accessibility.ts", "name": "announceFocusChange", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "ImageOptimizationConfig", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "OPTIMIZATION_PRESETS", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "generateResponsiveImageSources", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "generateBlurDataURL", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "supportsWebP", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "supportsAVIF", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "preloadImage", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "preloadCSS", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "LazyImageLoader", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "preloadFont", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "extractCriticalCSS", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "PerformanceMetrics", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "measureWebVitals", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "setCacheHeaders", "confidence": "low"}, {"file": "lib/utils/assetOptimization.ts", "name": "default", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "CriticalCSSConfig", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "extractCriticalCSS", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "minifyCSS", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "getUnusedSelectors", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "preloadCriticalFonts", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "optimizeFontDisplay", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "GLASSMORPHISM_CRITICAL_CSS", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "CSSLoader", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "measureCSSPerformance", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "deferNonCriticalCSS", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "CSS_OPTIMIZATION_CONFIG", "confidence": "low"}, {"file": "lib/utils/cssOptimization.ts", "name": "default", "confidence": "low"}, {"file": "lib/utils/fallback-integration.ts", "name": "applyAccessibilitySettings", "confidence": "low"}, {"file": "lib/utils/fallback-integration.ts", "name": "get<PERSON>allback<PERSON><PERSON>nt", "confidence": "low"}, {"file": "lib/utils/fallback-integration.ts", "name": "trackFallbackUsage", "confidence": "low"}, {"file": "lib/utils/fallback-integration.ts", "name": "generateContextualHelp", "confidence": "low"}, {"file": "lib/utils/fallback-integration.ts", "name": "SmartRetry", "confidence": "low"}, {"file": "lib/utils/fallback-integration.ts", "name": "OfflineDataManager", "confidence": "low"}, {"file": "lib/utils/fallback-integration.ts", "name": "smartRetry", "confidence": "low"}, {"file": "lib/utils/fallback-integration.ts", "name": "offlineData", "confidence": "low"}, {"file": "lib/utils/redirects.ts", "name": "RedirectRule", "confidence": "low"}, {"file": "lib/utils/redirects.ts", "name": "RedirectSuggestion", "confidence": "low"}, {"file": "lib/utils/redirects.ts", "name": "generateRedirectSuggestions", "confidence": "low"}, {"file": "lib/utils/redirects.ts", "name": "shouldAutoRedirect", "confidence": "low"}, {"file": "lib/utils/redirects.ts", "name": "NotFoundEvent", "confidence": "low"}, {"file": "lib/utils/redirects.ts", "name": "trackNotFoundError", "confidence": "low"}, {"file": "lib/utils/redirects.ts", "name": "getContextualSuggestions", "confidence": "low"}, {"file": "lib/utils.ts", "name": "cn", "confidence": "low"}, {"file": "lib/vcs/SolidityVersionControl.ts", "name": "Commit", "confidence": "low"}, {"file": "lib/vcs/SolidityVersionControl.ts", "name": "FileChange", "confidence": "low"}, {"file": "lib/vcs/SolidityVersionControl.ts", "name": "DiffHunk", "confidence": "low"}, {"file": "lib/vcs/SolidityVersionControl.ts", "name": "DiffLine", "confidence": "low"}, {"file": "lib/vcs/SolidityVersionControl.ts", "name": "Branch", "confidence": "low"}, {"file": "lib/vcs/SolidityVersionControl.ts", "name": "MergeRequest", "confidence": "low"}, {"file": "lib/vcs/SolidityVersionControl.ts", "name": "ConflictInfo", "confidence": "low"}, {"file": "lib/vcs/SolidityVersionControl.ts", "name": "SolidityVersionControl", "confidence": "low"}, {"file": "hooks/useAutoSave.ts", "name": "useAutoSave", "confidence": "low"}, {"file": "hooks/useAutoSave.ts", "name": "useCodeSessions", "confidence": "low"}, {"file": "hooks/useCollaborationConnection.ts", "name": "useCollaborationConnection", "confidence": "low"}, {"file": "hooks/useGitIntegration.ts", "name": "useGitIntegration", "confidence": "low"}, {"file": "hooks/useGitIntegration.ts", "name": "useAutoGit", "confidence": "low"}, {"file": "hooks/useLessonProgress.ts", "name": "useLessonProgress", "confidence": "low"}, {"file": "hooks/useLoadingState.ts", "name": "useLoadingState", "confidence": "low"}, {"file": "hooks/useLoadingState.ts", "name": "useMultipleLoadingStates", "confidence": "low"}, {"file": "hooks/useLoadingState.ts", "name": "useDebouncedLoading", "confidence": "low"}, {"file": "hooks/useLoadingState.ts", "name": "default", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "useDebounce", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "useThrottle", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "useIntersectionObserver", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "useVirtualScroll", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "useMemoWithDeps", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "usePerformanceMonitor", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "useLazyImage", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "useBatchUpdates", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "useResourcePreloader", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "useMemoryMonitor", "confidence": "low"}, {"file": "hooks/usePerformance.ts", "name": "default", "confidence": "low"}, {"file": "hooks/useProgress.ts", "name": "useProgress", "confidence": "low"}, {"file": "hooks/useUserPresence.ts", "name": "useUserPresence", "confidence": "low"}, {"file": "hooks/useUserPresence.ts", "name": "useUserColors", "confidence": "low"}, {"file": "types/global.d.ts", "name": "GoogleGenerativeAI", "confidence": "low"}, {"file": "types/global.d.ts", "name": "UserRole", "confidence": "low"}, {"file": "types/global.d.ts", "name": "PrismaClient", "confidence": "low"}, {"file": "types/settings.ts", "name": "UserProfile", "confidence": "low"}, {"file": "types/settings.ts", "name": "SecuritySettings", "confidence": "low"}, {"file": "types/settings.ts", "name": "NotificationSettings", "confidence": "low"}, {"file": "types/settings.ts", "name": "LearningPreferences", "confidence": "low"}, {"file": "types/settings.ts", "name": "EditorPreferences", "confidence": "low"}, {"file": "types/settings.ts", "name": "CollaborationPreferences", "confidence": "low"}, {"file": "types/settings.ts", "name": "AccessibilitySettings", "confidence": "low"}, {"file": "types/settings.ts", "name": "PrivacySettings", "confidence": "low"}, {"file": "types/settings.ts", "name": "UserSettings", "confidence": "low"}, {"file": "types/settings.ts", "name": "SettingsUpdateRequest", "confidence": "low"}, {"file": "types/settings.ts", "name": "SettingsValidationError", "confidence": "low"}, {"file": "types/settings.ts", "name": "SettingsUpdateResponse", "confidence": "low"}, {"file": "types/settings.ts", "name": "AuditLogEntry", "confidence": "low"}, {"file": "types/settings.ts", "name": "ActiveSession", "confidence": "low"}, {"file": "types/settings.ts", "name": "PasswordRequirements", "confidence": "low"}, {"file": "types/settings.ts", "name": "TwoFactorSetup", "confidence": "low"}, {"file": "types/settings.ts", "name": "DataExportRequest", "confidence": "low"}, {"file": "types/settings.ts", "name": "AccountDeletionRequest", "confidence": "low"}, {"file": "types/settings.ts", "name": "DEFAULT_USER_SETTINGS", "confidence": "low"}, {"file": "services/geminiService.ts", "name": "initializeChatForModule", "confidence": "low"}, {"file": "services/geminiService.ts", "name": "sendMessageToGeminiChat", "confidence": "low"}, {"file": "services/geminiService.ts", "name": "generateDiagramForConcept", "confidence": "low"}, {"file": "services/geminiService.ts", "name": "getTopicExplanation", "confidence": "low"}], "deadCodePatterns": {"filesWithDeadCode": 342, "unreachableCode": 331, "commentedCode": 21, "debugStatements": 185, "unusedVariables": 192}, "organizationIssues": {"issues": ["Route auth missing page file", "Route ping missing page file"], "recommendations": ["Consider adding index.ts to components for cleaner imports", "Consider adding index.ts to lib for cleaner imports", "Consider adding index.ts to hooks for cleaner imports", "Consider adding index.ts to utils for cleaner imports", "Consider adding index.ts to types for cleaner imports"]}}, "potentialSavings": {"files": 86, "estimatedSizeKB": 1037}}