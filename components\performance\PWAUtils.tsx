'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, Wifi, WifiOff, RefreshCw, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface PWAInstallPromptProps {
  onInstall?: () => void;
  onDismiss?: () => void;
}

interface ServiceWorkerState {
  isSupported: boolean;
  isRegistered: boolean;
  isUpdateAvailable: boolean;
  isOffline: boolean;
  registration: ServiceWorkerRegistration | null;
}

// PWA Install Prompt Component
export function PWAInstallPrompt({ onInstall, onDismiss }: PWAInstallPromptProps) {
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [showPrompt, setShowPrompt] = useState(false);
  const [isInstalling, setIsInstalling] = useState(false);

  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent the mini-infobar from appearing on mobile
      e.preventDefault();
      setDeferredPrompt(e);
      setShowPrompt(true);
    };

    const handleAppInstalled = () => {
      console.log('PWA was installed');
      setShowPrompt(false);
      setDeferredPrompt(null);
      onInstall?.();
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, [onInstall]);

  const handleInstall = async () => {
    if (!deferredPrompt) return;

    setIsInstalling(true);
    
    try {
      // Show the install prompt
      deferredPrompt.prompt();
      
      // Wait for the user to respond to the prompt
      const { outcome } = await deferredPrompt.userChoice;
      
      if (outcome === 'accepted') {
        console.log('User accepted the install prompt');
        onInstall?.();
      } else {
        console.log('User dismissed the install prompt');
      }
      
      setDeferredPrompt(null);
      setShowPrompt(false);
    } catch (error) {
      console.error('Error during PWA installation:', error);
    } finally {
      setIsInstalling(false);
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
    onDismiss?.();
  };

  if (!showPrompt || !deferredPrompt) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:max-w-sm z-50"
        initial={{ opacity: 0, y: 100 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: 100 }}
        transition={{ duration: 0.3 }}
      >
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg shadow-lg border border-blue-500/50 p-4">
          <button
            onClick={handleDismiss}
            className="absolute top-2 right-2 p-1 text-white/60 hover:text-white/80 transition-colors"
            aria-label="Dismiss install prompt"
          >
            <X className="w-4 h-4" />
          </button>
          
          <div className="flex items-start space-x-3">
            <div className="p-2 bg-white/20 rounded-lg">
              <Download className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-white mb-1">Install App</h3>
              <p className="text-white/90 text-sm mb-3">
                Install our app for faster access and offline learning capabilities.
              </p>
              <div className="flex space-x-2">
                <EnhancedButton
                  onClick={handleInstall}
                  disabled={isInstalling}
                  className="bg-white text-blue-600 hover:bg-gray-100 text-sm px-3 py-1.5"
                  size="sm"
                >
                  {isInstalling ? (
                    <RefreshCw className="w-3 h-3 mr-1 animate-spin" />
                  ) : (
                    <Download className="w-3 h-3 mr-1" />
                  )}
                  Install
                </EnhancedButton>
                <EnhancedButton
                  onClick={handleDismiss}
                  variant="ghost"
                  className="text-white/80 hover:text-white text-sm px-3 py-1.5"
                  size="sm"
                >
                  Later
                </EnhancedButton>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

// Service Worker Manager Hook
export function useServiceWorker() {
  const [state, setState] = useState<ServiceWorkerState>({
    isSupported: false,
    isRegistered: false,
    isUpdateAvailable: false,
    isOffline: false,
    registration: null
  });

  useEffect(() => {
    // Check if service workers are supported
    if ('serviceWorker' in navigator) {
      setState(prev => ({ ...prev, isSupported: true }));
      registerServiceWorker();
    }

    // Check online/offline status
    const updateOnlineStatus = () => {
      setState(prev => ({ ...prev, isOffline: !navigator.onLine }));
    };

    updateOnlineStatus();
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      
      setState(prev => ({ 
        ...prev, 
        isRegistered: true, 
        registration 
      }));

      // Check for updates
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              setState(prev => ({ ...prev, isUpdateAvailable: true }));
            }
          });
        }
      });

      console.log('Service Worker registered successfully');
    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  };

  const updateServiceWorker = useCallback(async () => {
    if (state.registration) {
      const newWorker = state.registration.waiting;
      if (newWorker) {
        newWorker.postMessage({ type: 'SKIP_WAITING' });
        window.location.reload();
      }
    }
  }, [state.registration]);

  const cacheLessonContent = useCallback(async (lessonData: any) => {
    if (state.registration) {
      state.registration.active?.postMessage({
        type: 'CACHE_LESSON',
        payload: lessonData
      });
    }
  }, [state.registration]);

  const clearCache = useCallback(async () => {
    if (state.registration) {
      state.registration.active?.postMessage({ type: 'CLEAR_CACHE' });
    }
  }, [state.registration]);

  return {
    ...state,
    updateServiceWorker,
    cacheLessonContent,
    clearCache
  };
}

// Offline Status Indicator
export function OfflineIndicator() {
  const { isOffline } = useServiceWorker();
  const [showIndicator, setShowIndicator] = useState(false);

  useEffect(() => {
    if (isOffline) {
      setShowIndicator(true);
    } else {
      // Hide after a delay when coming back online
      const timer = setTimeout(() => setShowIndicator(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [isOffline]);

  return (
    <AnimatePresence>
      {showIndicator && (
        <motion.div
          className={cn(
            'fixed top-4 left-1/2 transform -translate-x-1/2 z-50 px-4 py-2 rounded-lg shadow-lg',
            isOffline 
              ? 'bg-red-600 text-white' 
              : 'bg-green-600 text-white'
          )}
          initial={{ opacity: 0, y: -50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -50 }}
          transition={{ duration: 0.3 }}
        >
          <div className="flex items-center space-x-2">
            {isOffline ? (
              <WifiOff className="w-4 h-4" />
            ) : (
              <Wifi className="w-4 h-4" />
            )}
            <span className="text-sm font-medium">
              {isOffline ? 'You\'re offline' : 'Back online'}
            </span>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Update Available Notification
export function UpdateNotification() {
  const { isUpdateAvailable, updateServiceWorker } = useServiceWorker();
  const [showNotification, setShowNotification] = useState(false);

  useEffect(() => {
    setShowNotification(isUpdateAvailable);
  }, [isUpdateAvailable]);

  const handleUpdate = () => {
    updateServiceWorker();
  };

  const handleDismiss = () => {
    setShowNotification(false);
  };

  return (
    <AnimatePresence>
      {showNotification && (
        <motion.div
          className="fixed top-4 right-4 max-w-sm z-50"
          initial={{ opacity: 0, x: 100 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 100 }}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-blue-600 rounded-lg shadow-lg border border-blue-500/50 p-4">
            <button
              onClick={handleDismiss}
              className="absolute top-2 right-2 p-1 text-white/60 hover:text-white/80 transition-colors"
              aria-label="Dismiss update notification"
            >
              <X className="w-4 h-4" />
            </button>
            
            <div className="flex items-start space-x-3">
              <div className="p-2 bg-white/20 rounded-lg">
                <RefreshCw className="w-5 h-5 text-white" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-white mb-1">Update Available</h3>
                <p className="text-white/90 text-sm mb-3">
                  A new version of the app is available with improvements and bug fixes.
                </p>
                <div className="flex space-x-2">
                  <EnhancedButton
                    onClick={handleUpdate}
                    className="bg-white text-blue-600 hover:bg-gray-100 text-sm px-3 py-1.5"
                    size="sm"
                  >
                    <RefreshCw className="w-3 h-3 mr-1" />
                    Update
                  </EnhancedButton>
                  <EnhancedButton
                    onClick={handleDismiss}
                    variant="ghost"
                    className="text-white/80 hover:text-white text-sm px-3 py-1.5"
                    size="sm"
                  >
                    Later
                  </EnhancedButton>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Offline Learning Cache Manager
export function useOfflineLearning() {
  const { cacheLessonContent, isOffline } = useServiceWorker();
  const [cachedLessons, setCachedLessons] = useState<string[]>([]);

  const cacheLesson = useCallback(async (lessonId: string, lessonData: any) => {
    try {
      await cacheLessonContent(lessonData);
      setCachedLessons(prev => [...prev, lessonId]);
      
      // Also store in localStorage for quick access
      localStorage.setItem(`lesson_${lessonId}`, JSON.stringify(lessonData));
      
      console.log('Lesson cached for offline access:', lessonId);
    } catch (error) {
      console.error('Failed to cache lesson:', error);
    }
  }, [cacheLessonContent]);

  const getCachedLesson = useCallback(async (lessonId: string) => {
    try {
      // Try localStorage first
      const cached = localStorage.getItem(`lesson_${lessonId}`);
      if (cached) {
        return JSON.parse(cached);
      }
      
      // Try service worker cache
      if ('caches' in window) {
        const cache = await caches.open('offline-v1');
        const response = await cache.match(`/api/lessons/${lessonId}`);
        if (response) {
          return await response.json();
        }
      }
      
      return null;
    } catch (error) {
      console.error('Failed to get cached lesson:', error);
      return null;
    }
  }, []);

  const isCached = useCallback((lessonId: string) => {
    return cachedLessons.includes(lessonId) || 
           localStorage.getItem(`lesson_${lessonId}`) !== null;
  }, [cachedLessons]);

  return {
    cacheLesson,
    getCachedLesson,
    isCached,
    isOffline,
    cachedLessons
  };
}

// PWA Provider Component
export function PWAProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      {children}
      <PWAInstallPrompt />
      <OfflineIndicator />
      <UpdateNotification />
    </>
  );
}

// Enhanced PWA Context for comprehensive offline support
interface EnhancedPWAContextType {
  isOnline: boolean;
  isInstallable: boolean;
  isInstalled: boolean;
  updateAvailable: boolean;
  installApp: () => Promise<void>;
  updateApp: () => Promise<void>;
  enableNotifications: () => Promise<boolean>;
  shareContent: (data: ShareData) => Promise<void>;
  offlineCapabilities: {
    canCacheLessons: boolean;
    canSyncProgress: boolean;
    hasOfflineContent: boolean;
  };
}

const EnhancedPWAContext = React.createContext<EnhancedPWAContextType | null>(null);

interface EnhancedPWAProviderProps {
  children: React.ReactNode;
  enableOfflineSync?: boolean;
  enablePushNotifications?: boolean;
  enableBackgroundSync?: boolean;
}

export function EnhancedPWAProvider({
  children,
  enableOfflineSync = true,
  enablePushNotifications = true,
  enableBackgroundSync = true
}: EnhancedPWAProviderProps) {
  const [isOnline, setIsOnline] = useState(true);
  const [isInstallable, setIsInstallable] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<any>(null);
  const [serviceWorkerRegistration, setServiceWorkerRegistration] = useState<ServiceWorkerRegistration | null>(null);

  // Check if app is installed
  useEffect(() => {
    const checkInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isInWebAppiOS = (window.navigator as any).standalone === true;
      setIsInstalled(isStandalone || isInWebAppiOS);
    };

    checkInstalled();
    window.addEventListener('appinstalled', checkInstalled);
    return () => window.removeEventListener('appinstalled', checkInstalled);
  }, []);

  // Monitor online status
  useEffect(() => {
    const updateOnlineStatus = () => setIsOnline(navigator.onLine);

    setIsOnline(navigator.onLine);
    window.addEventListener('online', updateOnlineStatus);
    window.addEventListener('offline', updateOnlineStatus);

    return () => {
      window.removeEventListener('online', updateOnlineStatus);
      window.removeEventListener('offline', updateOnlineStatus);
    };
  }, []);

  // Service Worker registration with enhanced features
  useEffect(() => {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          setServiceWorkerRegistration(registration);

          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setUpdateAvailable(true);
                }
              });
            }
          });

          // Enable background sync if supported
          if (enableBackgroundSync && 'sync' in window.ServiceWorkerRegistration.prototype) {
            registration.sync.register('background-sync');
          }
        })
        .catch((error) => {
          console.error('Service Worker registration failed:', error);
        });

      // Listen for messages from service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'UPDATE_AVAILABLE') {
          setUpdateAvailable(true);
        }
      });
    }
  }, [enableBackgroundSync]);

  // Install prompt handling
  useEffect(() => {
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e);
      setIsInstallable(true);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    return () => window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
  }, []);

  const installApp = useCallback(async () => {
    if (!deferredPrompt) return;

    try {
      await deferredPrompt.prompt();
      const choiceResult = await deferredPrompt.userChoice;

      if (choiceResult.outcome === 'accepted') {
        setIsInstallable(false);
        setDeferredPrompt(null);
      }
    } catch (error) {
      console.error('Installation failed:', error);
    }
  }, [deferredPrompt]);

  const updateApp = useCallback(async () => {
    if (!serviceWorkerRegistration) return;

    try {
      const waitingWorker = serviceWorkerRegistration.waiting;
      if (waitingWorker) {
        waitingWorker.postMessage({ type: 'SKIP_WAITING' });
        window.location.reload();
      }
    } catch (error) {
      console.error('Update failed:', error);
    }
  }, [serviceWorkerRegistration]);

  const enableNotifications = useCallback(async (): Promise<boolean> => {
    if (!enablePushNotifications || !('Notification' in window)) {
      return false;
    }

    try {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    } catch (error) {
      console.error('Notification permission failed:', error);
      return false;
    }
  }, [enablePushNotifications]);

  const shareContent = useCallback(async (data: ShareData) => {
    if (navigator.share) {
      try {
        await navigator.share(data);
      } catch (error) {
        console.error('Sharing failed:', error);
        // Fallback to clipboard
        if (data.url) {
          await navigator.clipboard.writeText(data.url);
        }
      }
    } else {
      // Fallback for browsers without Web Share API
      if (data.url) {
        await navigator.clipboard.writeText(data.url);
      }
    }
  }, []);

  const offlineCapabilities = {
    canCacheLessons: enableOfflineSync && 'serviceWorker' in navigator,
    canSyncProgress: enableBackgroundSync && 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype,
    hasOfflineContent: isOnline === false && enableOfflineSync
  };

  const contextValue: EnhancedPWAContextType = {
    isOnline,
    isInstallable,
    isInstalled,
    updateAvailable,
    installApp,
    updateApp,
    enableNotifications,
    shareContent,
    offlineCapabilities
  };

  return (
    <EnhancedPWAContext.Provider value={contextValue}>
      {children}
    </EnhancedPWAContext.Provider>
  );
}

export function useEnhancedPWA() {
  const context = useContext(EnhancedPWAContext);
  if (!context) {
    throw new Error('useEnhancedPWA must be used within EnhancedPWAProvider');
  }
  return context;
}
