'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  ChevronLeft, 
  ChevronRight, 
  Skip, 
  Play, 
  Code, 
  Zap, 
  Trophy, 
  BookOpen,
  Target,
  CheckCircle,
  ArrowDown,
  MousePointer
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useXPNotifications } from '@/components/xp/XPNotification';
import { useMicroAchievements } from '@/components/gamification/MicroAchievements';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface TourStep {
  id: string;
  title: string;
  description: string;
  target: string; // CSS selector for the target element
  position: 'top' | 'bottom' | 'left' | 'right' | 'center';
  icon?: React.ComponentType<{ className?: string }>;
  action?: 'click' | 'hover' | 'scroll' | 'none';
  actionText?: string;
  xpReward?: number;
  achievementId?: string;
  isInteractive?: boolean;
  highlightStyle?: 'pulse' | 'glow' | 'border' | 'spotlight';
  customContent?: React.ReactNode;
  beforeShow?: () => void;
  afterShow?: () => void;
  validation?: () => boolean;
}

interface GuidedTourSystemProps {
  steps: TourStep[];
  tourId: string;
  autoStart?: boolean;
  enableGamification?: boolean;
  enableAccessibility?: boolean;
  enableSound?: boolean;
  onTourComplete?: () => void;
  onTourSkip?: () => void;
  className?: string;
}

export function GuidedTourSystem({
  steps,
  tourId,
  autoStart = false,
  enableGamification = true,
  enableAccessibility = true,
  enableSound = false,
  onTourComplete,
  onTourSkip,
  className
}: GuidedTourSystemProps) {
  const [isActive, setIsActive] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [isCompleted, setIsCompleted] = useState(false);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const [targetRect, setTargetRect] = useState<DOMRect | null>(null);
  const [hasUserInteracted, setHasUserInteracted] = useState(false);
  const [stepValidated, setStepValidated] = useState(false);

  const overlayRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const soundRef = useRef<HTMLAudioElement>(null);

  // Gamification hooks
  const { triggerXPGain } = useXPNotifications();
  const { triggerMicroAchievement } = useMicroAchievements();

  // Check if tour was already completed
  useEffect(() => {
    const completed = localStorage.getItem(`tour-${tourId}-completed`);
    if (completed === 'true') {
      setIsCompleted(true);
    } else if (autoStart && !completed) {
      startTour();
    }
  }, [tourId, autoStart]);

  // Update target element and position when step changes
  useEffect(() => {
    if (!isActive || !steps[currentStep]) return;

    const step = steps[currentStep];
    const element = document.querySelector(step.target) as HTMLElement;
    
    if (element) {
      setTargetElement(element);
      updateTargetRect(element);
      
      // Execute beforeShow callback
      if (step.beforeShow) {
        step.beforeShow();
      }

      // Add highlight to target element
      element.classList.add('tour-highlight');
      element.style.position = 'relative';
      element.style.zIndex = '1001';

      // Scroll element into view
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center',
        inline: 'center'
      });

      // Execute afterShow callback
      setTimeout(() => {
        if (step.afterShow) {
          step.afterShow();
        }
      }, 500);

      return () => {
        element.classList.remove('tour-highlight');
        element.style.zIndex = '';
      };
    }
  }, [isActive, currentStep, steps]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (targetElement) {
        updateTargetRect(targetElement);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [targetElement]);

  // Keyboard navigation
  useEffect(() => {
    if (!isActive || !enableAccessibility) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'Escape':
          skipTour();
          break;
        case 'ArrowRight':
        case 'Enter':
          e.preventDefault();
          nextStep();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          previousStep();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isActive, enableAccessibility]);

  const updateTargetRect = (element: HTMLElement) => {
    const rect = element.getBoundingClientRect();
    setTargetRect(rect);
  };

  const startTour = useCallback(() => {
    setIsActive(true);
    setCurrentStep(0);
    setHasUserInteracted(false);
    setStepValidated(false);
    
    if (enableSound && soundRef.current) {
      soundRef.current.play().catch(() => {});
    }
  }, [enableSound]);

  const nextStep = useCallback(() => {
    const step = steps[currentStep];
    
    // Validate step if required
    if (step.validation && !step.validation()) {
      return;
    }

    // Award XP for step completion
    if (enableGamification && step.xpReward) {
      triggerXPGain(step.xpReward, 'onboarding', `Completed tour step: ${step.title}`);
    }

    // Trigger achievement
    if (enableGamification && step.achievementId) {
      triggerMicroAchievement(step.achievementId);
    }

    if (currentStep < steps.length - 1) {
      setCurrentStep(prev => prev + 1);
      setStepValidated(false);
    } else {
      completeTour();
    }
  }, [currentStep, steps, enableGamification, triggerXPGain, triggerMicroAchievement]);

  const previousStep = useCallback(() => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
      setStepValidated(false);
    }
  }, [currentStep]);

  const skipTour = useCallback(() => {
    setIsActive(false);
    setIsCompleted(true);
    localStorage.setItem(`tour-${tourId}-skipped`, 'true');
    
    if (onTourSkip) {
      onTourSkip();
    }
  }, [tourId, onTourSkip]);

  const completeTour = useCallback(() => {
    setIsActive(false);
    setIsCompleted(true);
    localStorage.setItem(`tour-${tourId}-completed`, 'true');
    
    // Award completion XP and achievement
    if (enableGamification) {
      triggerXPGain(100, 'onboarding', 'Completed guided tour');
      triggerMicroAchievement('tour-master');
    }

    if (onTourComplete) {
      onTourComplete();
    }
  }, [tourId, enableGamification, triggerXPGain, triggerMicroAchievement, onTourComplete]);

  const handleInteractiveAction = useCallback(() => {
    const step = steps[currentStep];
    if (step.isInteractive) {
      setHasUserInteracted(true);
      setStepValidated(true);
    }
  }, [currentStep, steps]);

  const getTooltipPosition = () => {
    if (!targetRect) return { top: '50%', left: '50%' };

    const step = steps[currentStep];
    const padding = 20;
    
    switch (step.position) {
      case 'top':
        return {
          top: targetRect.top - padding,
          left: targetRect.left + targetRect.width / 2,
          transform: 'translate(-50%, -100%)'
        };
      case 'bottom':
        return {
          top: targetRect.bottom + padding,
          left: targetRect.left + targetRect.width / 2,
          transform: 'translate(-50%, 0)'
        };
      case 'left':
        return {
          top: targetRect.top + targetRect.height / 2,
          left: targetRect.left - padding,
          transform: 'translate(-100%, -50%)'
        };
      case 'right':
        return {
          top: targetRect.top + targetRect.height / 2,
          left: targetRect.right + padding,
          transform: 'translate(0, -50%)'
        };
      default:
        return {
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)'
        };
    }
  };

  const getSpotlightStyle = () => {
    if (!targetRect) return {};

    return {
      clipPath: `circle(${Math.max(targetRect.width, targetRect.height) / 2 + 10}px at ${
        targetRect.left + targetRect.width / 2
      }px ${targetRect.top + targetRect.height / 2}px)`
    };
  };

  if (!isActive || isCompleted) {
    return (
      <>
        {/* Tour Start Button */}
        {!isCompleted && (
          <EnhancedButton
            onClick={startTour}
            className={cn(
              'fixed bottom-4 left-4 z-50 bg-gradient-to-r from-blue-600 to-purple-600',
              'text-white shadow-lg hover:shadow-xl',
              className
            )}
            touchTarget
          >
            <Play className="w-4 h-4 mr-2" />
            Start Tour
          </EnhancedButton>
        )}
        
        {/* Sound element */}
        {enableSound && (
          <audio ref={soundRef} preload="auto">
            <source src="/sounds/tour-start.mp3" type="audio/mpeg" />
          </audio>
        )}
      </>
    );
  }

  const currentStepData = steps[currentStep];
  const progress = ((currentStep + 1) / steps.length) * 100;

  return (
    <AnimatePresence>
      <motion.div
        ref={overlayRef}
        className="fixed inset-0 z-[9999] pointer-events-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        role={enableAccessibility ? "dialog" : undefined}
        aria-label={enableAccessibility ? "Guided tour" : undefined}
        aria-modal={enableAccessibility ? "true" : undefined}
      >
        {/* Backdrop with spotlight effect */}
        <div 
          className="absolute inset-0 bg-black/70 backdrop-blur-sm"
          style={currentStepData.highlightStyle === 'spotlight' ? getSpotlightStyle() : {}}
        />

        {/* Target highlight */}
        {targetRect && currentStepData.highlightStyle !== 'spotlight' && (
          <motion.div
            className={cn(
              'absolute border-2 rounded-lg pointer-events-none',
              currentStepData.highlightStyle === 'pulse' && 'border-blue-400 animate-pulse',
              currentStepData.highlightStyle === 'glow' && 'border-blue-400 shadow-lg shadow-blue-400/50',
              currentStepData.highlightStyle === 'border' && 'border-blue-400'
            )}
            style={{
              top: targetRect.top - 4,
              left: targetRect.left - 4,
              width: targetRect.width + 8,
              height: targetRect.height + 8
            }}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.3 }}
          />
        )}

        {/* Interactive hotspot */}
        {currentStepData.isInteractive && targetRect && (
          <motion.div
            className="absolute pointer-events-auto cursor-pointer"
            style={{
              top: targetRect.top,
              left: targetRect.left,
              width: targetRect.width,
              height: targetRect.height
            }}
            onClick={handleInteractiveAction}
            animate={{ scale: [1, 1.05, 1] }}
            transition={{ duration: 1, repeat: Infinity }}
          >
            <div className="absolute inset-0 bg-blue-400/20 rounded-lg" />
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <MousePointer className="w-6 h-6 text-blue-400 animate-bounce" />
            </div>
          </motion.div>
        )}

        {/* Tooltip */}
        <motion.div
          ref={tooltipRef}
          className="absolute pointer-events-auto"
          style={getTooltipPosition()}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <div className="bg-white rounded-lg shadow-2xl border border-gray-200 max-w-sm w-80">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                {currentStepData.icon && (
                  <currentStepData.icon className="w-5 h-5 text-blue-600" />
                )}
                <h3 className="font-semibold text-gray-900">
                  {currentStepData.title}
                </h3>
              </div>
              <button
                onClick={skipTour}
                className="text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="Close tour"
              >
                <X className="w-4 h-4" />
              </button>
            </div>

            {/* Content */}
            <div className="p-4">
              <p className="text-gray-600 text-sm leading-relaxed mb-4">
                {currentStepData.description}
              </p>

              {/* Custom content */}
              {currentStepData.customContent && (
                <div className="mb-4">
                  {currentStepData.customContent}
                </div>
              )}

              {/* Interactive action prompt */}
              {currentStepData.isInteractive && !hasUserInteracted && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                  <div className="flex items-center space-x-2">
                    <Target className="w-4 h-4 text-blue-600" />
                    <span className="text-blue-800 text-sm font-medium">
                      {currentStepData.actionText || 'Click the highlighted area to continue'}
                    </span>
                  </div>
                </div>
              )}

              {/* XP reward indicator */}
              {enableGamification && currentStepData.xpReward && (
                <div className="flex items-center space-x-2 text-yellow-600 text-sm mb-4">
                  <Trophy className="w-4 h-4" />
                  <span>+{currentStepData.xpReward} XP for completing this step</span>
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="flex items-center justify-between p-4 border-t border-gray-200 bg-gray-50 rounded-b-lg">
              {/* Progress indicator */}
              <div className="flex items-center space-x-2">
                <span className="text-xs text-gray-500">
                  Step {currentStep + 1} of {steps.length}
                </span>
                <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
                  <motion.div
                    className="h-full bg-blue-600"
                    initial={{ width: 0 }}
                    animate={{ width: `${progress}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
              </div>

              {/* Navigation buttons */}
              <div className="flex items-center space-x-2">
                <EnhancedButton
                  onClick={previousStep}
                  disabled={currentStep === 0}
                  variant="ghost"
                  size="sm"
                  className="text-gray-600"
                >
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </EnhancedButton>

                <EnhancedButton
                  onClick={skipTour}
                  variant="ghost"
                  size="sm"
                  className="text-gray-600"
                >
                  <Skip className="w-4 h-4 mr-1" />
                  Skip
                </EnhancedButton>

                <EnhancedButton
                  onClick={nextStep}
                  disabled={currentStepData.isInteractive && !hasUserInteracted}
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {currentStep === steps.length - 1 ? (
                    <>
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Finish
                    </>
                  ) : (
                    <>
                      Next
                      <ChevronRight className="w-4 h-4 ml-1" />
                    </>
                  )}
                </EnhancedButton>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
