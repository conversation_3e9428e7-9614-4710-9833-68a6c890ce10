'use client';

import { useEffect } from 'react';
import { initializeRoutePreloading } from '@/lib/utils/routePreloader';

/**
 * Client component to initialize route preloading
 */
export function RoutePreloader() {
  useEffect(() => {
    // Initialize route preloading after component mounts
    initializeRoutePreloading();
  }, []);

  // This component doesn't render anything
  return null;
}

export default RoutePreloader;
