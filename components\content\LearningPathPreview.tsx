'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Calendar, 
  Clock, 
  Target, 
  CheckCircle, 
  Circle, 
  Star, 
  BookOpen, 
  Code, 
  Zap, 
  Award,
  TrendingUp,
  Users,
  ChevronRight,
  ChevronLeft,
  Play,
  Lock,
  Unlock
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface LearningMilestone {
  id: string;
  day: number;
  week: number;
  title: string;
  description: string;
  objectives: string[];
  timeCommitment: string; // e.g., "2 hours"
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  prerequisites: string[];
  deliverables: string[];
  completionRate: number; // percentage of users who complete this milestone
  averageTime: string; // actual time users spend
  skills: string[];
  isCompleted?: boolean;
  isLocked?: boolean;
}

interface LearningWeek {
  week: number;
  title: string;
  description: string;
  focus: string;
  milestones: LearningMilestone[];
  totalTime: string;
  completionRate: number;
}

interface UserSkillLevel {
  level: 'beginner' | 'intermediate' | 'advanced';
  programmingExperience: boolean;
  blockchainKnowledge: boolean;
  preferredPace: 'relaxed' | 'standard' | 'intensive';
}

// Mock learning path data
const learningWeeks: LearningWeek[] = [
  {
    week: 1,
    title: 'Solidity Fundamentals',
    description: 'Master the basics of Solidity programming and blockchain concepts',
    focus: 'Foundation Building',
    totalTime: '14 hours',
    completionRate: 94,
    milestones: [
      {
        id: 'day1',
        day: 1,
        week: 1,
        title: 'Blockchain Basics & Setup',
        description: 'Understand blockchain fundamentals and set up your development environment',
        objectives: [
          'Understand what blockchain is and how it works',
          'Learn about Ethereum and smart contracts',
          'Set up MetaMask and development tools',
          'Connect to test networks'
        ],
        timeCommitment: '2 hours',
        difficulty: 'beginner',
        prerequisites: [],
        deliverables: ['MetaMask wallet setup', 'First testnet transaction'],
        completionRate: 96,
        averageTime: '1.8 hours',
        skills: ['Blockchain Basics', 'MetaMask', 'Testnets']
      },
      {
        id: 'day2',
        day: 2,
        week: 1,
        title: 'Your First Smart Contract',
        description: 'Write and deploy your first "Hello World" smart contract',
        objectives: [
          'Learn Solidity syntax basics',
          'Write a simple smart contract',
          'Compile and deploy to testnet',
          'Interact with your contract'
        ],
        timeCommitment: '2.5 hours',
        difficulty: 'beginner',
        prerequisites: ['Blockchain Basics & Setup'],
        deliverables: ['Hello World contract', 'Successful deployment'],
        completionRate: 92,
        averageTime: '2.3 hours',
        skills: ['Solidity Syntax', 'Contract Deployment', 'Remix IDE']
      },
      {
        id: 'day3',
        day: 3,
        week: 1,
        title: 'Variables and Data Types',
        description: 'Master Solidity data types and variable declarations',
        objectives: [
          'Learn about state and local variables',
          'Understand different data types',
          'Practice with arrays and mappings',
          'Implement getter functions'
        ],
        timeCommitment: '2 hours',
        difficulty: 'beginner',
        prerequisites: ['Your First Smart Contract'],
        deliverables: ['Data types practice contract', 'Variable manipulation examples'],
        completionRate: 89,
        averageTime: '2.1 hours',
        skills: ['Data Types', 'Variables', 'Arrays', 'Mappings']
      }
    ]
  },
  {
    week: 2,
    title: 'Functions & Control Flow',
    description: 'Learn to write complex logic with functions, modifiers, and control structures',
    focus: 'Logic Implementation',
    totalTime: '16 hours',
    completionRate: 87,
    milestones: [
      {
        id: 'day8',
        day: 8,
        week: 2,
        title: 'Functions and Modifiers',
        description: 'Master function declarations, visibility, and custom modifiers',
        objectives: [
          'Understand function visibility (public, private, internal, external)',
          'Learn about function modifiers',
          'Implement access control',
          'Practice with function parameters and returns'
        ],
        timeCommitment: '2.5 hours',
        difficulty: 'intermediate',
        prerequisites: ['Variables and Data Types'],
        deliverables: ['Access control contract', 'Custom modifier examples'],
        completionRate: 85,
        averageTime: '2.7 hours',
        skills: ['Functions', 'Modifiers', 'Access Control', 'Visibility']
      }
    ]
  }
];

export function LearningPathPreview({ 
  className,
  userSkillLevel,
  showPersonalization = true 
}: { 
  className?: string;
  userSkillLevel?: UserSkillLevel;
  showPersonalization?: boolean;
}) {
  const [selectedWeek, setSelectedWeek] = useState(1);
  const [currentDay, setCurrentDay] = useState(1);
  const [personalizedPath, setPersonalizedPath] = useState(learningWeeks);
  const [showSkillAssessment, setShowSkillAssessment] = useState(false);

  // Personalize learning path based on user skill level
  useEffect(() => {
    if (userSkillLevel) {
      const personalized = learningWeeks.map(week => ({
        ...week,
        milestones: week.milestones.map(milestone => {
          let adjustedTime = milestone.timeCommitment;
          let adjustedDifficulty = milestone.difficulty;

          // Adjust based on programming experience
          if (userSkillLevel.programmingExperience) {
            // Reduce time for experienced programmers
            const hours = parseFloat(adjustedTime);
            adjustedTime = `${Math.max(1, hours * 0.8)} hours`;
          }

          // Adjust based on blockchain knowledge
          if (userSkillLevel.blockchainKnowledge && milestone.skills.includes('Blockchain Basics')) {
            adjustedTime = `${Math.max(0.5, parseFloat(adjustedTime) * 0.6)} hours`;
          }

          // Adjust based on preferred pace
          if (userSkillLevel.preferredPace === 'intensive') {
            adjustedTime = `${parseFloat(adjustedTime) * 1.3} hours`;
          } else if (userSkillLevel.preferredPace === 'relaxed') {
            adjustedTime = `${parseFloat(adjustedTime) * 0.7} hours`;
          }

          return {
            ...milestone,
            timeCommitment: adjustedTime,
            difficulty: adjustedDifficulty
          };
        })
      }));
      setPersonalizedPath(personalized);
    }
  }, [userSkillLevel]);

  const selectedWeekData = personalizedPath.find(week => week.week === selectedWeek);
  const totalDays = personalizedPath.reduce((sum, week) => sum + week.milestones.length, 0);
  const completedDays = personalizedPath.reduce((sum, week) => 
    sum + week.milestones.filter(m => m.isCompleted).length, 0
  );

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'intermediate':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'advanced':
        return 'text-red-400 bg-red-500/20 border-red-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  return (
    <div className={cn('max-w-6xl mx-auto', className)}>
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          className="text-3xl md:text-4xl font-bold text-white mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          30-Day Solidity Learning Path
        </motion.h2>
        <motion.p
          className="text-gray-300 text-lg max-w-3xl mx-auto mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          A structured roadmap to master Solidity development with daily milestones, 
          hands-on projects, and real-world applications
        </motion.p>

        {/* Progress Overview */}
        <motion.div
          className="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-8 mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <div className="flex items-center space-x-2">
            <Calendar className="w-5 h-5 text-blue-400" />
            <span className="text-white font-medium">30 Days</span>
          </div>
          <div className="flex items-center space-x-2">
            <Clock className="w-5 h-5 text-green-400" />
            <span className="text-white font-medium">2-3 hours/day</span>
          </div>
          <div className="flex items-center space-x-2">
            <Target className="w-5 h-5 text-purple-400" />
            <span className="text-white font-medium">Job-Ready Skills</span>
          </div>
          <div className="flex items-center space-x-2">
            <Award className="w-5 h-5 text-yellow-400" />
            <span className="text-white font-medium">Certificate</span>
          </div>
        </motion.div>

        {/* Personalization CTA */}
        {showPersonalization && !userSkillLevel && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <EnhancedButton
              onClick={() => setShowSkillAssessment(true)}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
              touchTarget
            >
              <Star className="w-4 h-4 mr-2" />
              Personalize Your Path
            </EnhancedButton>
          </motion.div>
        )}
      </div>

      {/* Week Navigation */}
      <motion.div
        className="flex flex-wrap justify-center gap-3 mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.8 }}
      >
        {personalizedPath.map((week) => (
          <button
            key={week.week}
            onClick={() => setSelectedWeek(week.week)}
            className={cn(
              'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border',
              selectedWeek === week.week
                ? 'bg-blue-600 text-white border-blue-500'
                : 'bg-white/10 text-gray-300 hover:bg-white/20 border-white/20'
            )}
          >
            Week {week.week}
            <div className="text-xs opacity-75 mt-1">{week.focus}</div>
          </button>
        ))}
      </motion.div>

      {/* Selected Week Overview */}
      {selectedWeekData && (
        <motion.div
          key={selectedWeek}
          className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
            <div>
              <h3 className="text-xl font-bold text-white mb-2">
                Week {selectedWeekData.week}: {selectedWeekData.title}
              </h3>
              <p className="text-gray-300">{selectedWeekData.description}</p>
            </div>
            <div className="mt-4 md:mt-0 md:text-right">
              <div className="text-2xl font-bold text-blue-400">{selectedWeekData.totalTime}</div>
              <div className="text-sm text-gray-400">Total Time</div>
              <div className="text-sm text-green-400 mt-1">
                {selectedWeekData.completionRate}% completion rate
              </div>
            </div>
          </div>

          {/* Week Milestones */}
          <div className="space-y-4">
            {selectedWeekData.milestones.map((milestone, index) => (
              <motion.div
                key={milestone.id}
                className="bg-white/5 rounded-lg p-4 border border-white/10"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      {milestone.isCompleted ? (
                        <CheckCircle className="w-5 h-5 text-green-400" />
                      ) : milestone.isLocked ? (
                        <Lock className="w-5 h-5 text-gray-400" />
                      ) : (
                        <Circle className="w-5 h-5 text-blue-400" />
                      )}
                    </div>
                    <div>
                      <div className="flex items-center space-x-3 mb-2">
                        <h4 className="font-semibold text-white">
                          Day {milestone.day}: {milestone.title}
                        </h4>
                        <div className={cn('px-2 py-1 rounded text-xs font-medium border', getDifficultyColor(milestone.difficulty))}>
                          {milestone.difficulty}
                        </div>
                      </div>
                      <p className="text-gray-300 text-sm mb-3">{milestone.description}</p>
                      
                      {/* Time and Stats */}
                      <div className="flex flex-wrap items-center gap-4 text-xs text-gray-400 mb-3">
                        <div className="flex items-center space-x-1">
                          <Clock className="w-3 h-3" />
                          <span>{milestone.timeCommitment}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Users className="w-3 h-3" />
                          <span>{milestone.completionRate}% complete this</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <TrendingUp className="w-3 h-3" />
                          <span>Avg: {milestone.averageTime}</span>
                        </div>
                      </div>

                      {/* Learning Objectives */}
                      <div className="mb-3">
                        <h5 className="text-sm font-medium text-white mb-2">Learning Objectives:</h5>
                        <ul className="text-sm text-gray-300 space-y-1">
                          {milestone.objectives.map((objective, objIndex) => (
                            <li key={objIndex} className="flex items-start space-x-2">
                              <Target className="w-3 h-3 text-blue-400 mt-0.5 flex-shrink-0" />
                              <span>{objective}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Skills */}
                      <div className="flex flex-wrap gap-2">
                        {milestone.skills.map((skill) => (
                          <span
                            key={skill}
                            className="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full border border-blue-500/30"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  {/* Action Button */}
                  <div className="flex-shrink-0 ml-4">
                    {milestone.isCompleted ? (
                      <span className="text-green-400 text-sm font-medium">Completed</span>
                    ) : milestone.isLocked ? (
                      <span className="text-gray-400 text-sm">Locked</span>
                    ) : (
                      <EnhancedButton
                        size="sm"
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        <Play className="w-3 h-3 mr-1" />
                        Start
                      </EnhancedButton>
                    )}
                  </div>
                </div>

                {/* Deliverables */}
                {milestone.deliverables.length > 0 && (
                  <div className="mt-3 pt-3 border-t border-white/10">
                    <h5 className="text-sm font-medium text-white mb-2">Deliverables:</h5>
                    <div className="flex flex-wrap gap-2">
                      {milestone.deliverables.map((deliverable, delIndex) => (
                        <span
                          key={delIndex}
                          className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded border border-green-500/30"
                        >
                          {deliverable}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Success Metrics */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.0 }}
      >
        <div className="bg-gradient-to-br from-green-600/20 to-emerald-600/20 rounded-lg p-6 border border-green-500/30 text-center">
          <div className="text-3xl font-bold text-green-400 mb-2">94%</div>
          <div className="text-white font-medium mb-1">Completion Rate</div>
          <div className="text-green-300 text-sm">Students who finish the full path</div>
        </div>
        <div className="bg-gradient-to-br from-blue-600/20 to-indigo-600/20 rounded-lg p-6 border border-blue-500/30 text-center">
          <div className="text-3xl font-bold text-blue-400 mb-2">87%</div>
          <div className="text-white font-medium mb-1">Job Placement</div>
          <div className="text-blue-300 text-sm">Land blockchain roles within 6 months</div>
        </div>
        <div className="bg-gradient-to-br from-purple-600/20 to-pink-600/20 rounded-lg p-6 border border-purple-500/30 text-center">
          <div className="text-3xl font-bold text-purple-400 mb-2">$85k</div>
          <div className="text-white font-medium mb-1">Average Salary</div>
          <div className="text-purple-300 text-sm">Starting salary for graduates</div>
        </div>
      </motion.div>

      {/* CTA Section */}
      <motion.div
        className="text-center bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg p-8 border border-blue-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.2 }}
      >
        <h3 className="text-2xl font-bold text-white mb-4">Ready to Start Your Journey?</h3>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          Join thousands of developers who have successfully transitioned to blockchain development 
          with our proven 30-day learning path.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <EnhancedButton
            className="bg-blue-600 hover:bg-blue-700 text-white"
            touchTarget
          >
            <Play className="w-4 h-4 mr-2" />
            Start Learning Path
          </EnhancedButton>
          <EnhancedButton
            variant="ghost"
            className="text-blue-400 hover:text-blue-300 border-blue-400/30"
            touchTarget
          >
            <BookOpen className="w-4 h-4 mr-2" />
            View Full Curriculum
          </EnhancedButton>
        </div>
      </motion.div>
    </div>
  );
}
