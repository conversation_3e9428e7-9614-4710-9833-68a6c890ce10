'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Search, 
  ChevronDown, 
  ChevronUp, 
  HelpCircle, 
  BookOpen, 
  CreditCard, 
  Settings,
  Star,
  Clock,
  Users
} from 'lucide-react';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  popularity: number;
  helpful: number;
  notHelpful: number;
}

interface InteractiveFAQProps {
  className?: string;
  showSearch?: boolean;
  showCategories?: boolean;
  showPopularity?: boolean;
  maxItems?: number;
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'How do I get started with learning Solidity?',
    answer: 'Start with our beginner-friendly course that covers the basics of blockchain, Ethereum, and Solidity syntax. No prior blockchain experience is required! We recommend completing the "Solidity Fundamentals" course first, which takes about 2-3 weeks to complete.',
    category: 'Getting Started',
    tags: ['beginner', 'solidity', 'course', 'fundamentals'],
    popularity: 95,
    helpful: 234,
    notHelpful: 12
  },
  {
    id: '2',
    question: 'What programming experience do I need?',
    answer: 'Basic programming knowledge is helpful but not required. If you know JavaScript, Python, or any object-oriented language, you\'ll pick up Solidity quickly. We provide programming fundamentals as part of our curriculum.',
    category: 'Getting Started',
    tags: ['prerequisites', 'programming', 'experience'],
    popularity: 88,
    helpful: 189,
    notHelpful: 8
  },
  {
    id: '3',
    question: 'How long does it take to complete a course?',
    answer: 'Course duration varies: Beginner courses (2-4 weeks), Intermediate courses (4-6 weeks), Advanced courses (6-8 weeks). You can learn at your own pace, and most students spend 5-10 hours per week.',
    category: 'Learning',
    tags: ['duration', 'time', 'pace', 'schedule'],
    popularity: 82,
    helpful: 156,
    notHelpful: 15
  },
  {
    id: '4',
    question: 'Do you offer certificates upon completion?',
    answer: 'Yes! You receive a verified certificate for each completed course. Our certificates are blockchain-verified and can be shared on LinkedIn, added to your resume, or displayed in your professional portfolio.',
    category: 'Learning',
    tags: ['certificate', 'completion', 'verification', 'linkedin'],
    popularity: 79,
    helpful: 143,
    notHelpful: 9
  },
  {
    id: '5',
    question: 'What is your refund policy?',
    answer: 'We offer a 30-day money-back guarantee. If you\'re not satisfied with your course within the first 30 days, contact our support team for a full refund. No questions asked!',
    category: 'Billing',
    tags: ['refund', 'money-back', 'guarantee', 'policy'],
    popularity: 76,
    helpful: 98,
    notHelpful: 5
  },
  {
    id: '6',
    question: 'Can I access courses on mobile devices?',
    answer: 'Absolutely! Our platform is fully responsive and works great on mobile devices. You can watch videos, read content, and even practice coding on your phone or tablet. We also have a PWA for offline access.',
    category: 'Technical',
    tags: ['mobile', 'responsive', 'pwa', 'offline'],
    popularity: 71,
    helpful: 87,
    notHelpful: 7
  },
  {
    id: '7',
    question: 'Do you provide job placement assistance?',
    answer: 'Yes! We have partnerships with blockchain companies and provide career support including resume reviews, interview preparation, and job placement assistance for our advanced students.',
    category: 'Career',
    tags: ['jobs', 'career', 'placement', 'interview'],
    popularity: 85,
    helpful: 167,
    notHelpful: 11
  },
  {
    id: '8',
    question: 'How do I reset my password?',
    answer: 'Click "Forgot Password" on the login page, enter your email address, and we\'ll send you a reset link. If you don\'t receive the email within 5 minutes, check your spam folder or contact support.',
    category: 'Technical',
    tags: ['password', 'reset', 'login', 'email'],
    popularity: 45,
    helpful: 67,
    notHelpful: 3
  }
];

const categories = [
  { id: 'all', name: 'All Questions', icon: <HelpCircle className="w-4 h-4" />, color: 'text-gray-600' },
  { id: 'Getting Started', name: 'Getting Started', icon: <BookOpen className="w-4 h-4" />, color: 'text-blue-600' },
  { id: 'Learning', name: 'Learning', icon: <Star className="w-4 h-4" />, color: 'text-green-600' },
  { id: 'Technical', name: 'Technical', icon: <Settings className="w-4 h-4" />, color: 'text-purple-600' },
  { id: 'Billing', name: 'Billing', icon: <CreditCard className="w-4 h-4" />, color: 'text-orange-600' },
  { id: 'Career', name: 'Career', icon: <Users className="w-4 h-4" />, color: 'text-indigo-600' }
];

export function InteractiveFAQ({
  className,
  showSearch = true,
  showCategories = true,
  showPopularity = true,
  maxItems = 20
}: InteractiveFAQProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [helpfulVotes, setHelpfulVotes] = useState<Record<string, 'helpful' | 'not-helpful' | null>>({});

  const filteredFAQs = useMemo(() => {
    let filtered = faqData;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.question.toLowerCase().includes(query) ||
        item.answer.toLowerCase().includes(query) ||
        item.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Sort by popularity if enabled
    if (showPopularity) {
      filtered = filtered.sort((a, b) => b.popularity - a.popularity);
    }

    return filtered.slice(0, maxItems);
  }, [searchQuery, selectedCategory, showPopularity, maxItems]);

  const toggleExpanded = (id: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleHelpfulVote = (id: string, vote: 'helpful' | 'not-helpful') => {
    setHelpfulVotes(prev => ({
      ...prev,
      [id]: prev[id] === vote ? null : vote
    }));
  };

  return (
    <div className={cn('w-full max-w-4xl mx-auto', className)}>
      {/* Header */}
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
          Frequently Asked Questions
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          Find answers to common questions about our Solidity learning platform
        </p>
      </div>

      {/* Search */}
      {showSearch && (
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <input
            type="text"
            placeholder="Search questions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
          />
        </div>
      )}

      {/* Categories */}
      {showCategories && (
        <div className="flex flex-wrap gap-2 mb-8">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={cn(
                'flex items-center space-x-2 px-4 py-2 rounded-lg border transition-all duration-200',
                selectedCategory === category.id
                  ? 'bg-blue-500 text-white border-blue-500'
                  : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border-gray-300 dark:border-gray-600 hover:border-blue-500 hover:text-blue-500'
              )}
            >
              <span className={selectedCategory === category.id ? 'text-white' : category.color}>
                {category.icon}
              </span>
              <span className="text-sm font-medium">{category.name}</span>
            </button>
          ))}
        </div>
      )}

      {/* Results count */}
      <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
        {filteredFAQs.length} question{filteredFAQs.length !== 1 ? 's' : ''} found
        {searchQuery && ` for "${searchQuery}"`}
      </div>

      {/* FAQ Items */}
      <div className="space-y-4">
        <AnimatePresence>
          {filteredFAQs.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.05 }}
              className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-shadow"
            >
              <button
                onClick={() => toggleExpanded(item.id)}
                className="w-full p-6 text-left focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                aria-expanded={expandedItems.has(item.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                        {item.question}
                      </h3>
                      {showPopularity && (
                        <div className="flex items-center space-x-1 text-xs text-gray-500">
                          <Clock className="w-3 h-3" />
                          <span>{item.popularity}% helpful</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                      <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                        {item.category}
                      </span>
                      <div className="flex space-x-1">
                        {item.tags.slice(0, 3).map((tag) => (
                          <span key={tag} className="text-xs opacity-75">
                            #{tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="ml-4">
                    {expandedItems.has(item.id) ? (
                      <ChevronUp className="w-5 h-5 text-gray-400" />
                    ) : (
                      <ChevronDown className="w-5 h-5 text-gray-400" />
                    )}
                  </div>
                </div>
              </button>

              <AnimatePresence>
                {expandedItems.has(item.id) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-6">
                      <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
                          {item.answer}
                        </p>
                        
                        {/* Helpful voting */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <span>Was this helpful?</span>
                            <div className="flex space-x-2">
                              <button
                                onClick={() => handleHelpfulVote(item.id, 'helpful')}
                                className={cn(
                                  'px-3 py-1 rounded-full border transition-colors',
                                  helpfulVotes[item.id] === 'helpful'
                                    ? 'bg-green-100 border-green-500 text-green-700'
                                    : 'border-gray-300 hover:border-green-500 hover:text-green-600'
                                )}
                              >
                                👍 Yes ({item.helpful})
                              </button>
                              <button
                                onClick={() => handleHelpfulVote(item.id, 'not-helpful')}
                                className={cn(
                                  'px-3 py-1 rounded-full border transition-colors',
                                  helpfulVotes[item.id] === 'not-helpful'
                                    ? 'bg-red-100 border-red-500 text-red-700'
                                    : 'border-gray-300 hover:border-red-500 hover:text-red-600'
                                )}
                              >
                                👎 No ({item.notHelpful})
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* No results */}
      {filteredFAQs.length === 0 && (
        <div className="text-center py-12">
          <HelpCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            No questions found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Try adjusting your search or browse different categories
          </p>
          <button
            onClick={() => {
              setSearchQuery('');
              setSelectedCategory('all');
            }}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Clear filters
          </button>
        </div>
      )}

      {/* Contact support */}
      <div className="mt-12 p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Still have questions?
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            Our support team is here to help you succeed in your learning journey
          </p>
          <button className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium">
            Contact Support
          </button>
        </div>
      </div>
    </div>
  );
}

// Hook for FAQ analytics
export function useFAQAnalytics() {
  const [analytics, setAnalytics] = useState({
    searchQueries: [] as string[],
    popularQuestions: [] as string[],
    helpfulVotes: {} as Record<string, { helpful: number; notHelpful: number }>
  });

  const trackSearch = (query: string) => {
    setAnalytics(prev => ({
      ...prev,
      searchQueries: [...prev.searchQueries, query].slice(-100) // Keep last 100 searches
    }));
  };

  const trackQuestionView = (questionId: string) => {
    setAnalytics(prev => ({
      ...prev,
      popularQuestions: [...prev.popularQuestions, questionId].slice(-100)
    }));
  };

  const trackHelpfulVote = (questionId: string, vote: 'helpful' | 'not-helpful') => {
    setAnalytics(prev => ({
      ...prev,
      helpfulVotes: {
        ...prev.helpfulVotes,
        [questionId]: {
          helpful: prev.helpfulVotes[questionId]?.helpful || 0 + (vote === 'helpful' ? 1 : 0),
          notHelpful: prev.helpfulVotes[questionId]?.notHelpful || 0 + (vote === 'not-helpful' ? 1 : 0)
        }
      }
    }));
  };

  return {
    analytics,
    trackSearch,
    trackQuestionView,
    trackHelpfulVote
  };
}

export default InteractiveFAQ;
