'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded';
  width?: string | number;
  height?: string | number;
  animation?: 'pulse' | 'wave' | 'none';
  lines?: number;
  respectMotion?: boolean;
}

// Base skeleton component with accessibility support
export function Skeleton({
  className,
  variant = 'rectangular',
  width,
  height,
  animation = 'pulse',
  lines = 1,
  respectMotion = true
}: SkeletonProps) {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    if (respectMotion) {
      const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      setPrefersReducedMotion(mediaQuery.matches);
      
      const handleChange = (e: MediaQueryListEvent) => {
        setPrefersReducedMotion(e.matches);
      };
      
      mediaQuery.addEventListener('change', handleChange);
      return () => mediaQuery.removeEventListener('change', handleChange);
    }
  }, [respectMotion]);

  const getVariantClasses = () => {
    switch (variant) {
      case 'text':
        return 'h-4 rounded';
      case 'circular':
        return 'rounded-full';
      case 'rounded':
        return 'rounded-lg';
      case 'rectangular':
      default:
        return 'rounded';
    }
  };

  const getAnimationClasses = () => {
    if (prefersReducedMotion) return 'bg-gray-700';
    
    switch (animation) {
      case 'wave':
        return 'bg-gradient-to-r from-gray-700 via-gray-600 to-gray-700 bg-[length:200%_100%] animate-[wave_1.5s_ease-in-out_infinite]';
      case 'pulse':
        return 'bg-gray-700 animate-pulse';
      case 'none':
      default:
        return 'bg-gray-700';
    }
  };

  const style = {
    width: width || '100%',
    height: height || (variant === 'text' ? '1rem' : '100%')
  };

  if (variant === 'text' && lines > 1) {
    return (
      <div className={cn('space-y-2', className)} role="status" aria-label="Loading content">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(getVariantClasses(), getAnimationClasses())}
            style={{
              ...style,
              width: index === lines - 1 ? '75%' : '100%' // Last line shorter
            }}
          />
        ))}
        <span className="sr-only">Loading...</span>
      </div>
    );
  }

  return (
    <div
      className={cn(getVariantClasses(), getAnimationClasses(), className)}
      style={style}
      role="status"
      aria-label="Loading content"
    >
      <span className="sr-only">Loading...</span>
    </div>
  );
}

// Hero section skeleton
export function HeroSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('space-y-8 p-8', className)} role="status" aria-label="Loading hero section">
      {/* Title skeleton */}
      <div className="text-center space-y-4">
        <Skeleton variant="text" height="3rem" width="80%" className="mx-auto" />
        <Skeleton variant="text" height="1.5rem" width="60%" className="mx-auto" />
        <Skeleton variant="text" lines={2} className="max-w-2xl mx-auto" />
      </div>

      {/* CTA buttons skeleton */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Skeleton variant="rounded" width="200px" height="48px" />
        <Skeleton variant="rounded" width="180px" height="48px" />
      </div>

      {/* Stats skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
        {Array.from({ length: 3 }).map((_, index) => (
          <div key={index} className="text-center space-y-2">
            <Skeleton variant="circular" width="60px" height="60px" className="mx-auto" />
            <Skeleton variant="text" width="120px" className="mx-auto" />
            <Skeleton variant="text" width="80px" className="mx-auto" />
          </div>
        ))}
      </div>
      
      <span className="sr-only">Loading hero section content...</span>
    </div>
  );
}

// Learning path preview skeleton
export function LearningPathSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('space-y-6', className)} role="status" aria-label="Loading learning path">
      {/* Header */}
      <div className="text-center space-y-4">
        <Skeleton variant="text" height="2rem" width="300px" className="mx-auto" />
        <Skeleton variant="text" lines={2} className="max-w-xl mx-auto" />
      </div>

      {/* Week cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <div key={index} className="bg-white/5 rounded-lg p-6 space-y-4">
            <div className="flex items-center space-x-3">
              <Skeleton variant="circular" width="40px" height="40px" />
              <div className="flex-1">
                <Skeleton variant="text" width="80px" />
                <Skeleton variant="text" width="120px" />
              </div>
            </div>
            
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, milestoneIndex) => (
                <div key={milestoneIndex} className="flex items-start space-x-3">
                  <Skeleton variant="circular" width="20px" height="20px" />
                  <div className="flex-1 space-y-1">
                    <Skeleton variant="text" width="90%" />
                    <Skeleton variant="text" width="60%" />
                  </div>
                </div>
              ))}
            </div>
            
            <Skeleton variant="rounded" height="36px" />
          </div>
        ))}
      </div>
      
      <span className="sr-only">Loading learning path content...</span>
    </div>
  );
}

// FAQ section skeleton
export function FAQSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('space-y-6', className)} role="status" aria-label="Loading FAQ section">
      {/* Header */}
      <div className="text-center space-y-4">
        <Skeleton variant="text" height="2rem" width="200px" className="mx-auto" />
        <Skeleton variant="text" lines={2} className="max-w-xl mx-auto" />
      </div>

      {/* Search bar */}
      <div className="max-w-2xl mx-auto">
        <Skeleton variant="rounded" height="48px" />
      </div>

      {/* FAQ items */}
      <div className="space-y-4 max-w-4xl mx-auto">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="bg-white/5 rounded-lg p-6 space-y-3">
            <div className="flex items-center justify-between">
              <Skeleton variant="text" width="70%" height="1.25rem" />
              <Skeleton variant="circular" width="24px" height="24px" />
            </div>
            {index % 2 === 0 && (
              <Skeleton variant="text" lines={2} />
            )}
          </div>
        ))}
      </div>
      
      <span className="sr-only">Loading FAQ content...</span>
    </div>
  );
}

// Comparison table skeleton
export function ComparisonTableSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('space-y-6', className)} role="status" aria-label="Loading comparison table">
      {/* Header */}
      <div className="text-center space-y-4">
        <Skeleton variant="text" height="2rem" width="300px" className="mx-auto" />
        <Skeleton variant="text" lines={2} className="max-w-xl mx-auto" />
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-3 justify-center">
        {Array.from({ length: 5 }).map((_, index) => (
          <Skeleton key={index} variant="rounded" width="120px" height="36px" />
        ))}
      </div>

      {/* Table */}
      <div className="bg-white/5 rounded-lg overflow-hidden">
        {/* Table header */}
        <div className="grid grid-cols-5 gap-4 p-4 border-b border-white/10">
          <Skeleton variant="text" />
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="text-center space-y-2">
              <Skeleton variant="circular" width="48px" height="48px" className="mx-auto" />
              <Skeleton variant="text" width="80px" className="mx-auto" />
            </div>
          ))}
        </div>

        {/* Table rows */}
        {Array.from({ length: 8 }).map((_, rowIndex) => (
          <div key={rowIndex} className="grid grid-cols-5 gap-4 p-4 border-b border-white/5">
            <div className="space-y-2">
              <Skeleton variant="text" width="90%" />
              <Skeleton variant="text" width="70%" />
            </div>
            {Array.from({ length: 4 }).map((_, colIndex) => (
              <div key={colIndex} className="text-center">
                <Skeleton variant="circular" width="20px" height="20px" className="mx-auto" />
              </div>
            ))}
          </div>
        ))}
      </div>
      
      <span className="sr-only">Loading comparison table...</span>
    </div>
  );
}

// Conversion components skeleton
export function ConversionSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('space-y-8', className)} role="status" aria-label="Loading conversion components">
      {/* Trial signup section */}
      <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg p-8 space-y-6">
        <div className="text-center space-y-4">
          <Skeleton variant="text" height="2rem" width="400px" className="mx-auto" />
          <Skeleton variant="text" lines={2} className="max-w-2xl mx-auto" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="bg-white/5 rounded-lg p-4 space-y-3">
              <Skeleton variant="circular" width="32px" height="32px" className="mx-auto" />
              <Skeleton variant="text" className="text-center" />
              <Skeleton variant="text" lines={2} />
            </div>
          ))}
        </div>
        
        <div className="text-center space-y-4">
          <Skeleton variant="rounded" width="200px" height="48px" className="mx-auto" />
          <Skeleton variant="text" width="300px" className="mx-auto" />
        </div>
      </div>

      {/* Urgency timer skeleton */}
      <div className="bg-gradient-to-r from-red-600/20 to-orange-600/20 rounded-lg p-6 space-y-4">
        <div className="flex items-center space-x-4">
          <Skeleton variant="circular" width="48px" height="48px" />
          <div className="flex-1 space-y-2">
            <Skeleton variant="text" width="60%" />
            <Skeleton variant="text" width="80%" />
          </div>
        </div>
        
        <div className="grid grid-cols-4 gap-2">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className="text-center space-y-1">
              <Skeleton variant="text" height="2rem" />
              <Skeleton variant="text" width="40px" className="mx-auto" />
            </div>
          ))}
        </div>
        
        <Skeleton variant="rounded" height="44px" />
      </div>
      
      <span className="sr-only">Loading conversion components...</span>
    </div>
  );
}

// Generic content skeleton
export function ContentSkeleton({ 
  lines = 3, 
  showImage = false, 
  showButton = false,
  className 
}: { 
  lines?: number; 
  showImage?: boolean; 
  showButton?: boolean;
  className?: string;
}) {
  return (
    <div className={cn('space-y-4', className)} role="status" aria-label="Loading content">
      {showImage && (
        <Skeleton variant="rounded" height="200px" />
      )}
      
      <Skeleton variant="text" lines={lines} />
      
      {showButton && (
        <Skeleton variant="rounded" width="120px" height="40px" />
      )}
      
      <span className="sr-only">Loading content...</span>
    </div>
  );
}

// Loading state wrapper with fade transition
export function LoadingWrapper({
  isLoading,
  skeleton,
  children,
  className,
  fallback
}: {
  isLoading: boolean;
  skeleton: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  fallback?: React.ReactNode;
}) {
  return (
    <div className={className}>
      <AnimatePresence mode="wait">
        {isLoading ? (
          <motion.div
            key="skeleton"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {skeleton}
          </motion.div>
        ) : (
          <motion.div
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {children || fallback}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Hook for managing loading states
export function useLoadingState(initialState = true) {
  const [isLoading, setIsLoading] = useState(initialState);
  const [error, setError] = useState<string | null>(null);

  const startLoading = () => {
    setIsLoading(true);
    setError(null);
  };

  const stopLoading = () => {
    setIsLoading(false);
  };

  const setLoadingError = (errorMessage: string) => {
    setIsLoading(false);
    setError(errorMessage);
  };

  return {
    isLoading,
    error,
    startLoading,
    stopLoading,
    setLoadingError
  };
}
