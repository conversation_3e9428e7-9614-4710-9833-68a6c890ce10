name: Bundle Size Monitoring

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'

jobs:
  bundle-monitoring:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0 # Fetch full history for comparison
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
      env:
        NODE_ENV: production
        NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
        NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
        DATABASE_URL: ${{ secrets.DATABASE_URL }}
        REDIS_URL: ${{ secrets.REDIS_URL }}
    
    - name: Run bundle size analysis
      id: bundle-analysis
      run: |
        node scripts/bundle-monitor.js > bundle-report.txt 2>&1
        echo "status=$?" >> $GITHUB_OUTPUT
      continue-on-error: true
    
    - name: Upload bundle report
      uses: actions/upload-artifact@v4
      with:
        name: bundle-report-${{ github.sha }}
        path: |
          bundle-report.txt
          reports/bundle-monitoring/
        retention-days: 30
    
    - name: Comment on PR (if applicable)
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          
          try {
            const reportContent = fs.readFileSync('bundle-report.txt', 'utf8');
            const reportPath = 'reports/bundle-monitoring/latest-bundle-report.json';
            
            let bundleData = {};
            if (fs.existsSync(reportPath)) {
              bundleData = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
            }
            
            const comment = `## 📊 Bundle Size Report
            
            **Current Bundle Size:**
            - JavaScript: ${bundleData.javascript || 'N/A'}KB
            - CSS: ${bundleData.css || 'N/A'}KB
            - Total: ${bundleData.total || 'N/A'}KB
            
            **Thresholds:**
            - JavaScript: ≤400KB
            - CSS: ≤100KB
            - Total: ≤500KB
            
            <details>
            <summary>📋 Detailed Report</summary>
            
            \`\`\`
            ${reportContent}
            \`\`\`
            
            </details>
            
            ${bundleData.total > 500 ? '⚠️ **Warning:** Bundle size exceeds recommended thresholds!' : '✅ Bundle size within acceptable limits.'}
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          } catch (error) {
            console.log('Could not create PR comment:', error.message);
          }
    
    - name: Fail if bundle size regression
      if: steps.bundle-analysis.outputs.status != '0'
      run: |
        echo "❌ Bundle size monitoring failed!"
        cat bundle-report.txt
        exit 1

  performance-regression-check:
    runs-on: ubuntu-latest
    needs: bundle-monitoring
    if: github.event_name == 'pull_request'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
    
    - name: Performance budget check
      run: |
        node -e "
        const fs = require('fs');
        const path = '.lighthouseci/lhr-*.json';
        const glob = require('glob');
        
        const files = glob.sync(path);
        if (files.length === 0) {
          console.log('No Lighthouse reports found');
          process.exit(0);
        }
        
        const report = JSON.parse(fs.readFileSync(files[0], 'utf8'));
        const metrics = report.audits;
        
        const budgets = {
          'first-contentful-paint': 2000,
          'largest-contentful-paint': 2500,
          'speed-index': 3000,
          'interactive': 3500
        };
        
        let failed = false;
        
        Object.entries(budgets).forEach(([metric, budget]) => {
          const value = metrics[metric]?.numericValue;
          if (value && value > budget) {
            console.log(\`❌ \${metric}: \${Math.round(value)}ms > \${budget}ms\`);
            failed = true;
          } else if (value) {
            console.log(\`✅ \${metric}: \${Math.round(value)}ms ≤ \${budget}ms\`);
          }
        });
        
        if (failed) {
          console.log('Performance regression detected!');
          process.exit(1);
        } else {
          console.log('Performance within acceptable limits.');
        }
        "

  bundle-size-tracking:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    needs: bundle-monitoring
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Download bundle report
      uses: actions/download-artifact@v4
      with:
        name: bundle-report-${{ github.sha }}
    
    - name: Update bundle size tracking
      run: |
        # Create or update bundle size history
        mkdir -p .github/bundle-history
        
        # Extract bundle size data
        if [ -f "reports/bundle-monitoring/latest-bundle-report.json" ]; then
          echo "$(date -Iseconds),$(jq -r '.javascript' reports/bundle-monitoring/latest-bundle-report.json),$(jq -r '.css' reports/bundle-monitoring/latest-bundle-report.json),$(jq -r '.total' reports/bundle-monitoring/latest-bundle-report.json)" >> .github/bundle-history/bundle-sizes.csv
        fi
    
    - name: Commit bundle size history
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add .github/bundle-history/
        git diff --staged --quiet || git commit -m "Update bundle size tracking [skip ci]"
        git push
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
