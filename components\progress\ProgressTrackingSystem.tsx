'use client';

import React, { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Trophy, 
  Target, 
  Clock, 
  Code, 
  BookOpen, 
  Zap, 
  Star,
  TrendingUp,
  CheckCircle,
  Circle,
  BarChart3,
  Calendar,
  Award,
  Flame
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Progress tracking interfaces
interface LearningMilestone {
  id: string;
  title: string;
  description: string;
  category: 'tour' | 'compilation' | 'deployment' | 'achievement' | 'lesson' | 'project';
  xpReward: number;
  completedAt?: Date;
  isCompleted: boolean;
  prerequisites?: string[];
  icon: React.ComponentType<{ className?: string }>;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
}

interface LearningPath {
  id: string;
  title: string;
  description: string;
  milestones: LearningMilestone[];
  totalXP: number;
  estimatedTime: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  category: string;
}

interface UserProgress {
  totalXP: number;
  level: number;
  completedMilestones: string[];
  currentStreak: number;
  longestStreak: number;
  lastActivity: Date;
  joinedAt: Date;
  timeSpent: number; // in minutes
  lessonsCompleted: number;
  contractsDeployed: number;
  achievementsUnlocked: number;
  tourProgress: {
    platform: boolean;
    quickstart: boolean;
    advanced: boolean;
  };
}

interface ProgressContextType {
  userProgress: UserProgress;
  learningPaths: LearningPath[];
  updateProgress: (milestone: string, category: string) => void;
  addXP: (amount: number, source: string) => void;
  getRecommendations: () => LearningMilestone[];
  getProgressPercentage: (pathId?: string) => number;
  saveProgress: () => void;
  loadProgress: () => void;
}

// Create context
const ProgressContext = createContext<ProgressContextType | null>(null);

// Default learning paths and milestones
const defaultLearningPaths: LearningPath[] = [
  {
    id: 'beginner-path',
    title: 'Solidity Fundamentals',
    description: 'Master the basics of smart contract development',
    difficulty: 'beginner',
    category: 'fundamentals',
    estimatedTime: '2-3 weeks',
    totalXP: 500,
    milestones: [
      {
        id: 'platform-tour',
        title: 'Complete Platform Tour',
        description: 'Learn about all platform features and tools',
        category: 'tour',
        xpReward: 50,
        isCompleted: false,
        icon: BookOpen,
        difficulty: 'beginner'
      },
      {
        id: 'first-compilation',
        title: 'First Successful Compilation',
        description: 'Compile your first Solidity smart contract',
        category: 'compilation',
        xpReward: 75,
        isCompleted: false,
        prerequisites: ['platform-tour'],
        icon: Code,
        difficulty: 'beginner'
      },
      {
        id: 'first-deployment',
        title: 'Deploy to Testnet',
        description: 'Deploy your first contract to a test network',
        category: 'deployment',
        xpReward: 100,
        isCompleted: false,
        prerequisites: ['first-compilation'],
        icon: Zap,
        difficulty: 'beginner'
      },
      {
        id: 'basic-syntax',
        title: 'Master Basic Syntax',
        description: 'Complete lessons on variables, functions, and modifiers',
        category: 'lesson',
        xpReward: 125,
        isCompleted: false,
        icon: BookOpen,
        difficulty: 'beginner'
      },
      {
        id: 'first-project',
        title: 'Build Your First DApp',
        description: 'Create a simple decentralized application',
        category: 'project',
        xpReward: 150,
        isCompleted: false,
        prerequisites: ['first-deployment', 'basic-syntax'],
        icon: Trophy,
        difficulty: 'intermediate'
      }
    ]
  },
  {
    id: 'intermediate-path',
    title: 'Advanced Solidity',
    description: 'Dive deeper into smart contract development',
    difficulty: 'intermediate',
    category: 'advanced',
    estimatedTime: '4-6 weeks',
    totalXP: 1000,
    milestones: [
      {
        id: 'gas-optimization',
        title: 'Gas Optimization Master',
        description: 'Learn to write gas-efficient smart contracts',
        category: 'lesson',
        xpReward: 200,
        isCompleted: false,
        prerequisites: ['first-project'],
        icon: TrendingUp,
        difficulty: 'intermediate'
      },
      {
        id: 'security-patterns',
        title: 'Security Patterns',
        description: 'Master common security patterns and best practices',
        category: 'lesson',
        xpReward: 250,
        isCompleted: false,
        icon: Target,
        difficulty: 'intermediate'
      },
      {
        id: 'defi-project',
        title: 'Build a DeFi Protocol',
        description: 'Create a decentralized finance application',
        category: 'project',
        xpReward: 300,
        isCompleted: false,
        prerequisites: ['gas-optimization', 'security-patterns'],
        icon: Star,
        difficulty: 'advanced'
      }
    ]
  }
];

const defaultUserProgress: UserProgress = {
  totalXP: 0,
  level: 1,
  completedMilestones: [],
  currentStreak: 0,
  longestStreak: 0,
  lastActivity: new Date(),
  joinedAt: new Date(),
  timeSpent: 0,
  lessonsCompleted: 0,
  contractsDeployed: 0,
  achievementsUnlocked: 0,
  tourProgress: {
    platform: false,
    quickstart: false,
    advanced: false
  }
};

// Progress Provider Component
export function ProgressTrackingProvider({ children }: { children: React.ReactNode }) {
  const [userProgress, setUserProgress] = useState<UserProgress>(defaultUserProgress);
  const [learningPaths] = useState<LearningPath[]>(defaultLearningPaths);

  // Load progress from localStorage on mount
  useEffect(() => {
    loadProgress();
  }, []);

  // Auto-save progress periodically
  useEffect(() => {
    const interval = setInterval(saveProgress, 30000); // Save every 30 seconds
    return () => clearInterval(interval);
  }, [userProgress]);

  const loadProgress = useCallback(() => {
    try {
      const saved = localStorage.getItem('solidity-learning-progress');
      if (saved) {
        const parsed = JSON.parse(saved);
        setUserProgress({
          ...defaultUserProgress,
          ...parsed,
          lastActivity: new Date(parsed.lastActivity),
          joinedAt: new Date(parsed.joinedAt)
        });
      }
    } catch (error) {
      console.error('Failed to load progress:', error);
    }
  }, []);

  const saveProgress = useCallback(() => {
    try {
      localStorage.setItem('solidity-learning-progress', JSON.stringify(userProgress));
    } catch (error) {
      console.error('Failed to save progress:', error);
    }
  }, [userProgress]);

  const calculateLevel = (xp: number): number => {
    // Level calculation: 100 XP for level 1, then +50 XP per level
    return Math.floor((xp + 50) / 100) + 1;
  };

  const updateProgress = useCallback((milestoneId: string, category: string) => {
    setUserProgress(prev => {
      if (prev.completedMilestones.includes(milestoneId)) {
        return prev; // Already completed
      }

      // Find the milestone
      const milestone = learningPaths
        .flatMap(path => path.milestones)
        .find(m => m.id === milestoneId);

      if (!milestone) return prev;

      // Check prerequisites
      if (milestone.prerequisites) {
        const hasPrerequisites = milestone.prerequisites.every(prereq =>
          prev.completedMilestones.includes(prereq)
        );
        if (!hasPrerequisites) return prev;
      }

      const newXP = prev.totalXP + milestone.xpReward;
      const newLevel = calculateLevel(newXP);
      const leveledUp = newLevel > prev.level;

      // Update streak
      const now = new Date();
      const lastActivity = new Date(prev.lastActivity);
      const daysSinceLastActivity = Math.floor((now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60 * 24));
      
      let newStreak = prev.currentStreak;
      if (daysSinceLastActivity === 0) {
        // Same day, maintain streak
      } else if (daysSinceLastActivity === 1) {
        // Next day, increment streak
        newStreak += 1;
      } else {
        // Streak broken
        newStreak = 1;
      }

      // Update category-specific counters
      const updates: Partial<UserProgress> = {};
      switch (category) {
        case 'lesson':
          updates.lessonsCompleted = prev.lessonsCompleted + 1;
          break;
        case 'deployment':
          updates.contractsDeployed = prev.contractsDeployed + 1;
          break;
        case 'achievement':
          updates.achievementsUnlocked = prev.achievementsUnlocked + 1;
          break;
      }

      return {
        ...prev,
        ...updates,
        totalXP: newXP,
        level: newLevel,
        completedMilestones: [...prev.completedMilestones, milestoneId],
        currentStreak: newStreak,
        longestStreak: Math.max(prev.longestStreak, newStreak),
        lastActivity: now
      };
    });
  }, [learningPaths]);

  const addXP = useCallback((amount: number, source: string) => {
    setUserProgress(prev => {
      const newXP = prev.totalXP + amount;
      const newLevel = calculateLevel(newXP);
      
      return {
        ...prev,
        totalXP: newXP,
        level: newLevel,
        lastActivity: new Date()
      };
    });
  }, []);

  const getRecommendations = useCallback((): LearningMilestone[] => {
    const allMilestones = learningPaths.flatMap(path => path.milestones);
    
    return allMilestones
      .filter(milestone => {
        // Not completed
        if (userProgress.completedMilestones.includes(milestone.id)) return false;
        
        // Prerequisites met
        if (milestone.prerequisites) {
          return milestone.prerequisites.every(prereq =>
            userProgress.completedMilestones.includes(prereq)
          );
        }
        
        return true;
      })
      .sort((a, b) => {
        // Sort by difficulty and XP reward
        const difficultyOrder = { beginner: 0, intermediate: 1, advanced: 2 };
        if (difficultyOrder[a.difficulty] !== difficultyOrder[b.difficulty]) {
          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
        }
        return a.xpReward - b.xpReward;
      })
      .slice(0, 3); // Top 3 recommendations
  }, [userProgress.completedMilestones, learningPaths]);

  const getProgressPercentage = useCallback((pathId?: string): number => {
    if (pathId) {
      const path = learningPaths.find(p => p.id === pathId);
      if (!path) return 0;
      
      const completed = path.milestones.filter(m =>
        userProgress.completedMilestones.includes(m.id)
      ).length;
      
      return Math.round((completed / path.milestones.length) * 100);
    }
    
    // Overall progress
    const totalMilestones = learningPaths.reduce((sum, path) => sum + path.milestones.length, 0);
    const completedMilestones = userProgress.completedMilestones.length;
    
    return Math.round((completedMilestones / totalMilestones) * 100);
  }, [userProgress.completedMilestones, learningPaths]);

  const contextValue: ProgressContextType = {
    userProgress,
    learningPaths,
    updateProgress,
    addXP,
    getRecommendations,
    getProgressPercentage,
    saveProgress,
    loadProgress
  };

  return (
    <ProgressContext.Provider value={contextValue}>
      {children}
    </ProgressContext.Provider>
  );
}

// Hook to use progress context
export function useProgressTracking() {
  const context = useContext(ProgressContext);
  if (!context) {
    throw new Error('useProgressTracking must be used within a ProgressTrackingProvider');
  }
  return context;
}
