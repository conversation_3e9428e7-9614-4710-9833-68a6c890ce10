'use client';

import React, { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Trophy, 
  Star, 
  Zap, 
  Target, 
  Award, 
  X, 
  Volume2, 
  VolumeX,
  Share2,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { AchievementShareButton } from '@/components/social/SocialSharingHooks';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  xpReward: number;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  category: 'learning' | 'social' | 'streak' | 'milestone' | 'special';
  unlockedAt: Date;
}

interface NotificationSettings {
  enabled: boolean;
  soundEnabled: boolean;
  confettiEnabled: boolean;
  position: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center';
  duration: number; // in milliseconds
  maxVisible: number;
}

interface NotificationContextType {
  showAchievement: (achievement: Achievement) => void;
  settings: NotificationSettings;
  updateSettings: (settings: Partial<NotificationSettings>) => void;
  activeNotifications: Achievement[];
  dismissNotification: (id: string) => void;
}

// Create context
const NotificationContext = createContext<NotificationContextType | null>(null);

// Default settings
const defaultSettings: NotificationSettings = {
  enabled: true,
  soundEnabled: true,
  confettiEnabled: true,
  position: 'top-right',
  duration: 8000,
  maxVisible: 3
};

// Confetti component
function ConfettiEffect({ isActive }: { isActive: boolean }) {
  const [particles, setParticles] = useState<Array<{ id: number; x: number; y: number; color: string; delay: number }>>([]);

  useEffect(() => {
    if (!isActive) return;

    const colors = ['#fbbf24', '#f59e0b', '#10b981', '#3b82f6', '#8b5cf6', '#ef4444'];
    const newParticles = Array.from({ length: 50 }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      color: colors[Math.floor(Math.random() * colors.length)],
      delay: Math.random() * 0.5
    }));

    setParticles(newParticles);

    const timer = setTimeout(() => {
      setParticles([]);
    }, 3000);

    return () => clearTimeout(timer);
  }, [isActive]);

  if (!isActive || particles.length === 0) return null;

  return (
    <div className="fixed inset-0 pointer-events-none z-[9999] overflow-hidden">
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute w-2 h-2 rounded-full"
          style={{
            backgroundColor: particle.color,
            left: `${particle.x}%`,
            top: `${particle.y}%`
          }}
          initial={{ scale: 0, y: 0, rotate: 0 }}
          animate={{ 
            scale: [0, 1, 0], 
            y: [0, -100, -200], 
            rotate: [0, 180, 360],
            opacity: [0, 1, 0]
          }}
          transition={{ 
            duration: 2, 
            delay: particle.delay,
            ease: "easeOut"
          }}
        />
      ))}
    </div>
  );
}

// Individual notification component
function AchievementNotification({ 
  achievement, 
  onDismiss, 
  settings,
  index 
}: { 
  achievement: Achievement; 
  onDismiss: () => void;
  settings: NotificationSettings;
  index: number;
}) {
  const [isVisible, setIsVisible] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [soundPlayed, setSoundPlayed] = useState(false);

  // Auto-dismiss timer
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onDismiss, 300); // Wait for exit animation
    }, settings.duration);

    return () => clearTimeout(timer);
  }, [settings.duration, onDismiss]);

  // Show animation and effects
  useEffect(() => {
    setIsVisible(true);
    
    if (settings.confettiEnabled) {
      setTimeout(() => setShowConfetti(true), 200);
    }

    if (settings.soundEnabled && !soundPlayed) {
      // Play achievement sound
      const audio = new Audio('/sounds/achievement.mp3');
      audio.volume = 0.3;
      audio.play().catch(() => {}); // Ignore errors if sound fails
      setSoundPlayed(true);
    }
  }, [settings.confettiEnabled, settings.soundEnabled, soundPlayed]);

  const getRarityColor = () => {
    switch (achievement.rarity) {
      case 'common':
        return 'from-gray-600 to-gray-800 border-gray-500';
      case 'rare':
        return 'from-blue-600 to-blue-800 border-blue-500';
      case 'epic':
        return 'from-purple-600 to-purple-800 border-purple-500';
      case 'legendary':
        return 'from-yellow-600 to-orange-600 border-yellow-500';
      default:
        return 'from-gray-600 to-gray-800 border-gray-500';
    }
  };

  const getPositionClasses = () => {
    const baseOffset = index * 120; // Stack notifications
    
    switch (settings.position) {
      case 'top-right':
        return `top-4 right-4 translate-y-[${baseOffset}px]`;
      case 'top-left':
        return `top-4 left-4 translate-y-[${baseOffset}px]`;
      case 'bottom-right':
        return `bottom-4 right-4 -translate-y-[${baseOffset}px]`;
      case 'bottom-left':
        return `bottom-4 left-4 -translate-y-[${baseOffset}px]`;
      case 'top-center':
        return `top-4 left-1/2 -translate-x-1/2 translate-y-[${baseOffset}px]`;
      default:
        return `top-4 right-4 translate-y-[${baseOffset}px]`;
    }
  };

  return (
    <>
      <ConfettiEffect isActive={showConfetti} />
      
      <AnimatePresence>
        {isVisible && (
          <motion.div
            className={cn(
              'fixed z-50 max-w-sm w-full',
              getPositionClasses()
            )}
            initial={{ 
              opacity: 0, 
              scale: 0.8,
              x: settings.position.includes('right') ? 100 : settings.position.includes('left') ? -100 : 0,
              y: settings.position.includes('bottom') ? 100 : -100
            }}
            animate={{ 
              opacity: 1, 
              scale: 1,
              x: 0,
              y: index * (settings.position.includes('bottom') ? -120 : 120)
            }}
            exit={{ 
              opacity: 0, 
              scale: 0.8,
              x: settings.position.includes('right') ? 100 : settings.position.includes('left') ? -100 : 0
            }}
            transition={{ 
              type: "spring", 
              stiffness: 300, 
              damping: 30,
              duration: 0.5
            }}
          >
            <div className={cn(
              'bg-gradient-to-br backdrop-blur-md rounded-lg border shadow-2xl overflow-hidden',
              getRarityColor()
            )}>
              {/* Header */}
              <div className="p-4">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-white/20 rounded-lg">
                      <achievement.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="font-bold text-white text-sm">Achievement Unlocked!</h3>
                      <div className={cn(
                        'px-2 py-1 rounded text-xs font-medium mt-1',
                        achievement.rarity === 'legendary' ? 'bg-yellow-500/20 text-yellow-300' :
                        achievement.rarity === 'epic' ? 'bg-purple-500/20 text-purple-300' :
                        achievement.rarity === 'rare' ? 'bg-blue-500/20 text-blue-300' :
                        'bg-gray-500/20 text-gray-300'
                      )}>
                        {achievement.rarity.toUpperCase()}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setIsVisible(false);
                      setTimeout(onDismiss, 300);
                    }}
                    className="text-white/60 hover:text-white transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>

                {/* Achievement Details */}
                <div className="mb-4">
                  <h4 className="font-semibold text-white text-lg mb-1">
                    {achievement.title}
                  </h4>
                  <p className="text-white/90 text-sm leading-relaxed">
                    {achievement.description}
                  </p>
                </div>

                {/* XP Reward */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <Star className="w-4 h-4 text-yellow-400" />
                    <span className="text-yellow-400 font-medium text-sm">
                      +{achievement.xpReward} XP
                    </span>
                  </div>
                  <div className="text-white/60 text-xs">
                    {new Date(achievement.unlockedAt).toLocaleTimeString()}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center space-x-2">
                  <AchievementShareButton 
                    achievementId={achievement.id}
                    className="flex-1"
                  />
                  <EnhancedButton
                    onClick={() => {
                      setIsVisible(false);
                      setTimeout(onDismiss, 300);
                    }}
                    variant="ghost"
                    size="sm"
                    className="text-white/80 hover:text-white border-white/20"
                  >
                    <CheckCircle className="w-4 h-4 mr-1" />
                    Got it!
                  </EnhancedButton>
                </div>
              </div>

              {/* Progress bar for auto-dismiss */}
              <motion.div
                className="h-1 bg-white/20"
                initial={{ width: '100%' }}
                animate={{ width: '0%' }}
                transition={{ duration: settings.duration / 1000, ease: "linear" }}
              />
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

// Main notification provider
export function SiteWideNotificationProvider({ children }: { children: React.ReactNode }) {
  const [activeNotifications, setActiveNotifications] = useState<Achievement[]>([]);
  const [settings, setSettings] = useState<NotificationSettings>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('notification_settings');
      return saved ? { ...defaultSettings, ...JSON.parse(saved) } : defaultSettings;
    }
    return defaultSettings;
  });

  // Save settings to localStorage
  useEffect(() => {
    localStorage.setItem('notification_settings', JSON.stringify(settings));
  }, [settings]);

  const showAchievement = useCallback((achievement: Achievement) => {
    if (!settings.enabled) return;

    setActiveNotifications(prev => {
      const newNotifications = [achievement, ...prev];
      // Limit to maxVisible notifications
      return newNotifications.slice(0, settings.maxVisible);
    });
  }, [settings.enabled, settings.maxVisible]);

  const dismissNotification = useCallback((id: string) => {
    setActiveNotifications(prev => prev.filter(notif => notif.id !== id));
  }, []);

  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  }, []);

  // Listen for achievement events
  useEffect(() => {
    const handleAchievementUnlocked = (event: CustomEvent) => {
      const { achievementId, achievement } = event.detail;
      
      // If achievement object is provided, use it; otherwise create a mock one
      const achievementData = achievement || {
        id: achievementId,
        title: 'Achievement Unlocked!',
        description: 'You\'ve reached a new milestone in your learning journey.',
        icon: Trophy,
        xpReward: 50,
        rarity: 'common' as const,
        category: 'learning' as const,
        unlockedAt: new Date()
      };

      showAchievement(achievementData);
    };

    window.addEventListener('achievement_unlocked', handleAchievementUnlocked as EventListener);
    
    return () => {
      window.removeEventListener('achievement_unlocked', handleAchievementUnlocked as EventListener);
    };
  }, [showAchievement]);

  const contextValue: NotificationContextType = {
    showAchievement,
    settings,
    updateSettings,
    activeNotifications,
    dismissNotification
  };

  return (
    <NotificationContext.Provider value={contextValue}>
      {children}
      
      {/* Render active notifications */}
      {activeNotifications.map((achievement, index) => (
        <AchievementNotification
          key={achievement.id}
          achievement={achievement}
          onDismiss={() => dismissNotification(achievement.id)}
          settings={settings}
          index={index}
        />
      ))}
    </NotificationContext.Provider>
  );
}

// Hook to use notification context
export function useAchievementNotifications() {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useAchievementNotifications must be used within a SiteWideNotificationProvider');
  }
  return context;
}

// Settings component
export function NotificationSettings({ className }: { className?: string }) {
  const { settings, updateSettings } = useAchievementNotifications();

  return (
    <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10', className)}>
      <h3 className="text-lg font-semibold text-white mb-4">Notification Settings</h3>
      
      <div className="space-y-4">
        {/* Enable/Disable */}
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Enable Notifications</span>
          <button
            onClick={() => updateSettings({ enabled: !settings.enabled })}
            className={cn(
              'w-12 h-6 rounded-full transition-colors relative',
              settings.enabled ? 'bg-green-600' : 'bg-gray-600'
            )}
          >
            <div className={cn(
              'w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform',
              settings.enabled ? 'translate-x-6' : 'translate-x-0.5'
            )} />
          </button>
        </div>

        {/* Sound */}
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Sound Effects</span>
          <button
            onClick={() => updateSettings({ soundEnabled: !settings.soundEnabled })}
            className={cn(
              'w-12 h-6 rounded-full transition-colors relative',
              settings.soundEnabled ? 'bg-green-600' : 'bg-gray-600'
            )}
          >
            <div className={cn(
              'w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform',
              settings.soundEnabled ? 'translate-x-6' : 'translate-x-0.5'
            )} />
          </button>
        </div>

        {/* Confetti */}
        <div className="flex items-center justify-between">
          <span className="text-gray-300">Confetti Effects</span>
          <button
            onClick={() => updateSettings({ confettiEnabled: !settings.confettiEnabled })}
            className={cn(
              'w-12 h-6 rounded-full transition-colors relative',
              settings.confettiEnabled ? 'bg-green-600' : 'bg-gray-600'
            )}
          >
            <div className={cn(
              'w-5 h-5 bg-white rounded-full absolute top-0.5 transition-transform',
              settings.confettiEnabled ? 'translate-x-6' : 'translate-x-0.5'
            )} />
          </button>
        </div>

        {/* Position */}
        <div>
          <span className="text-gray-300 block mb-2">Position</span>
          <select
            value={settings.position}
            onChange={(e) => updateSettings({ position: e.target.value as any })}
            className="w-full bg-gray-800 border border-gray-600 rounded-lg px-3 py-2 text-white"
          >
            <option value="top-right">Top Right</option>
            <option value="top-left">Top Left</option>
            <option value="bottom-right">Bottom Right</option>
            <option value="bottom-left">Bottom Left</option>
            <option value="top-center">Top Center</option>
          </select>
        </div>

        {/* Duration */}
        <div>
          <span className="text-gray-300 block mb-2">Duration: {settings.duration / 1000}s</span>
          <input
            type="range"
            min="3000"
            max="15000"
            step="1000"
            value={settings.duration}
            onChange={(e) => updateSettings({ duration: parseInt(e.target.value) })}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
}
