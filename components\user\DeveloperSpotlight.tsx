'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Crown, 
  Star, 
  MapPin,
  Users
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { Avatar, UserProfile } from './AvatarSystem';

// Developer Spotlight component
export function DeveloperSpotlight({ className }: { className?: string }) {
  const [currentSpotlight, setCurrentSpotlight] = useState(0);
  
  // Mock spotlight data
  const spotlightUsers: UserProfile[] = [
    {
      id: '1',
      name: '<PERSON>',
      username: 'alex<PERSON>',
      email: '<EMAIL>',
      avatar: {
        type: 'initials',
        initials: 'AC',
        backgroundColor: 'bg-gradient-to-br from-blue-500 to-purple-500'
      },
      level: 25,
      xp: 15420,
      achievements: ['Smart Contract Master', 'De<PERSON>i Expert', 'Security Specialist'],
      joinedAt: new Date('2023-06-01'),
      location: 'San Francisco, CA',
      bio: 'Full-stack blockchain developer with 3+ years experience. Passionate about DeFi and smart contract security.',
      skills: ['Solidity', 'React', 'Node.js', 'DeFi', 'Security Auditing'],
      isVerified: true,
      isPremium: true
    },
    {
      id: '2',
      name: 'Maria <PERSON>',
      username: 'mariarodriguez',
      email: '<EMAIL>',
      avatar: {
        type: 'initials',
        initials: 'MR',
        backgroundColor: 'bg-gradient-to-br from-green-500 to-teal-500'
      },
      level: 22,
      xp: 12890,
      achievements: ['NFT Creator', 'Community Leader', 'Tutorial Master'],
      joinedAt: new Date('2023-07-15'),
      location: 'Barcelona, Spain',
      bio: 'NFT artist and smart contract developer. Creating the future of digital art on the blockchain.',
      skills: ['Solidity', 'NFTs', 'IPFS', 'Web3.js', 'Digital Art'],
      isVerified: true,
      isPremium: false
    },
    {
      id: '3',
      name: 'Kenji Tanaka',
      username: 'kenjitanaka',
      email: '<EMAIL>',
      avatar: {
        type: 'initials',
        initials: 'KT',
        backgroundColor: 'bg-gradient-to-br from-orange-500 to-red-500'
      },
      level: 28,
      xp: 18750,
      achievements: ['Layer 2 Expert', 'Gas Optimizer', 'Protocol Designer'],
      joinedAt: new Date('2023-05-20'),
      location: 'Tokyo, Japan',
      bio: 'Layer 2 scaling solutions architect. Building the infrastructure for the next billion blockchain users.',
      skills: ['Solidity', 'Layer 2', 'Optimism', 'Arbitrum', 'Polygon'],
      isVerified: true,
      isPremium: true
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSpotlight((prev) => (prev + 1) % spotlightUsers.length);
    }, 10000); // Change every 10 seconds

    return () => clearInterval(interval);
  }, [spotlightUsers.length]);

  const currentUser = spotlightUsers[currentSpotlight];

  return (
    <div className={cn('bg-gradient-to-br from-purple-600/20 to-blue-600/20 rounded-lg p-6 border border-purple-500/30', className)}>
      <div className="flex items-center space-x-2 mb-4">
        <Crown className="w-5 h-5 text-yellow-400" />
        <h3 className="text-lg font-semibold text-white">Developer Spotlight</h3>
      </div>

      <AnimatePresence mode="wait">
        <motion.div
          key={currentSpotlight}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-start space-x-4">
            <Avatar 
              user={currentUser} 
              size="lg" 
              showLevel={true} 
              showBadges={true}
            />
            <div className="flex-1">
              <div className="flex items-center space-x-2 mb-1">
                <h4 className="font-semibold text-white">{currentUser.name}</h4>
                {currentUser.isVerified && <Star className="w-4 h-4 text-blue-400" />}
                {currentUser.isPremium && <Crown className="w-4 h-4 text-yellow-400" />}
              </div>
              <div className="flex items-center space-x-1 mb-2">
                <MapPin className="w-3 h-3 text-gray-500" />
                <p className="text-gray-400 text-sm">{currentUser.location}</p>
              </div>
              <p className="text-gray-300 text-sm leading-relaxed mb-3">{currentUser.bio}</p>
              
              <div className="flex items-center space-x-4 text-xs">
                <span className="text-blue-400">Level {currentUser.level}</span>
                <span className="text-green-400">{currentUser.xp.toLocaleString()} XP</span>
                <span className="text-purple-400">{currentUser.achievements.length} achievements</span>
              </div>
            </div>
          </div>

          {/* Skills */}
          <div className="mt-4">
            <div className="flex flex-wrap gap-2">
              {currentUser.skills.slice(0, 4).map((skill) => (
                <span
                  key={skill}
                  className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded-full border border-purple-500/30"
                >
                  {skill}
                </span>
              ))}
              {currentUser.skills.length > 4 && (
                <span className="px-2 py-1 bg-gray-500/20 text-gray-400 text-xs rounded-full">
                  +{currentUser.skills.length - 4} more
                </span>
              )}
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Indicators */}
      <div className="flex justify-center space-x-2 mt-4">
        {spotlightUsers.map((_, index) => (
          <button
            key={index}
            onClick={() => setCurrentSpotlight(index)}
            className={cn(
              'w-2 h-2 rounded-full transition-colors',
              index === currentSpotlight ? 'bg-purple-400' : 'bg-gray-600'
            )}
          />
        ))}
      </div>
    </div>
  );
}

// Live Activity Feed with Avatars
export function LiveActivityFeed({ className }: { className?: string }) {
  const [activities, setActivities] = useState([
    {
      id: '1',
      user: {
        name: 'Sarah Kim',
        avatar: { type: 'initials' as const, initials: 'SK', backgroundColor: 'bg-gradient-to-br from-pink-500 to-red-500' },
        level: 15
      },
      action: 'completed the DeFi Fundamentals course',
      timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
      location: 'Seoul, South Korea'
    },
    {
      id: '2',
      user: {
        name: 'James Wilson',
        avatar: { type: 'initials' as const, initials: 'JW', backgroundColor: 'bg-gradient-to-br from-blue-500 to-indigo-500' },
        level: 8
      },
      action: 'deployed their first smart contract',
      timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      location: 'London, UK'
    },
    {
      id: '3',
      user: {
        name: 'Ana Silva',
        avatar: { type: 'initials' as const, initials: 'AS', backgroundColor: 'bg-gradient-to-br from-green-500 to-emerald-500' },
        level: 12
      },
      action: 'earned the "Security Expert" achievement',
      timestamp: new Date(Date.now() - 8 * 60 * 1000), // 8 minutes ago
      location: 'São Paulo, Brazil'
    },
    {
      id: '4',
      user: {
        name: 'David Chen',
        avatar: { type: 'initials' as const, initials: 'DC', backgroundColor: 'bg-gradient-to-br from-yellow-500 to-orange-500' },
        level: 19
      },
      action: 'reached Level 19',
      timestamp: new Date(Date.now() - 12 * 60 * 1000), // 12 minutes ago
      location: 'Vancouver, Canada'
    }
  ]);

  // Simulate new activities
  useEffect(() => {
    const interval = setInterval(() => {
      const names = ['Emma Thompson', 'David Lee', 'Sofia Garcia', 'Michael Brown', 'Lisa Wang', 'Carlos Rodriguez'];
      const actions = [
        'completed a coding challenge',
        'unlocked a new achievement',
        'deployed a smart contract',
        'joined the platform',
        'reached a new level',
        'earned their first NFT',
        'completed the Solidity basics course',
        'shared their progress on social media'
      ];
      const locations = [
        'New York, USA',
        'Tokyo, Japan',
        'Berlin, Germany',
        'Sydney, Australia',
        'Toronto, Canada',
        'Mumbai, India',
        'São Paulo, Brazil',
        'London, UK'
      ];
      const colors = [
        'bg-gradient-to-br from-purple-500 to-pink-500',
        'bg-gradient-to-br from-yellow-500 to-orange-500',
        'bg-gradient-to-br from-teal-500 to-cyan-500',
        'bg-gradient-to-br from-red-500 to-pink-500',
        'bg-gradient-to-br from-indigo-500 to-purple-500',
        'bg-gradient-to-br from-green-500 to-blue-500'
      ];

      const randomName = names[Math.floor(Math.random() * names.length)];
      const newActivity = {
        id: Date.now().toString(),
        user: {
          name: randomName,
          avatar: { 
            type: 'initials' as const, 
            initials: randomName.split(' ').map(n => n[0]).join(''), 
            backgroundColor: colors[Math.floor(Math.random() * colors.length)]
          },
          level: Math.floor(Math.random() * 25) + 1
        },
        action: actions[Math.floor(Math.random() * actions.length)],
        timestamp: new Date(),
        location: locations[Math.floor(Math.random() * locations.length)]
      };

      setActivities(prev => [newActivity, ...prev.slice(0, 4)]); // Keep only 5 activities
    }, 15000); // New activity every 15 seconds

    return () => clearInterval(interval);
  }, []);

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  return (
    <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10', className)}>
      <div className="flex items-center space-x-2 mb-4">
        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
        <h3 className="font-semibold text-white">Live Activity</h3>
        <span className="text-gray-400 text-sm">({activities.length})</span>
      </div>

      <div className="space-y-3 max-h-80 overflow-y-auto">
        <AnimatePresence>
          {activities.map((activity) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.3 }}
              className="flex items-start space-x-3 p-2 hover:bg-white/5 rounded-lg transition-colors"
            >
              <Avatar 
                user={{
                  id: activity.id,
                  name: activity.user.name,
                  username: '',
                  email: '',
                  avatar: activity.user.avatar,
                  level: activity.user.level,
                  xp: 0,
                  achievements: [],
                  joinedAt: new Date(),
                  isVerified: false,
                  isPremium: false,
                  skills: []
                }}
                size="sm"
                showStatus={true}
              />
              <div className="flex-1 min-w-0">
                <p className="text-gray-300 text-sm">
                  <span className="font-medium text-white">{activity.user.name}</span>
                  {' '}{activity.action}
                </p>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-gray-500 text-xs">{formatTimeAgo(activity.timestamp)}</span>
                  <span className="text-gray-500 text-xs">•</span>
                  <span className="text-gray-500 text-xs">{activity.location}</span>
                </div>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Activity Stats */}
      <div className="mt-4 pt-3 border-t border-white/10">
        <div className="flex items-center justify-between text-xs text-gray-400">
          <div className="flex items-center space-x-1">
            <Users className="w-3 h-3" />
            <span>{Math.floor(Math.random() * 50) + 150} developers online</span>
          </div>
          <span>Updated {formatTimeAgo(new Date(Date.now() - 30000))}</span>
        </div>
      </div>
    </div>
  );
}

// Compact live activity for hero section
export function HeroLiveActivity({ className }: { className?: string }) {
  const [currentActivity, setCurrentActivity] = useState(0);
  
  const activities = [
    { name: 'Alex', action: 'deployed a smart contract', location: 'SF' },
    { name: 'Maria', action: 'completed DeFi course', location: 'Madrid' },
    { name: 'Kenji', action: 'earned Level 20', location: 'Tokyo' },
    { name: 'Sarah', action: 'unlocked achievement', location: 'NYC' }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentActivity((prev) => (prev + 1) % activities.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [activities.length]);

  return (
    <div className={cn('flex items-center space-x-2 text-sm', className)}>
      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
      <AnimatePresence mode="wait">
        <motion.span
          key={currentActivity}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
          className="text-gray-300"
        >
          <span className="font-medium text-white">{activities[currentActivity].name}</span>
          {' '}{activities[currentActivity].action} in {activities[currentActivity].location}
        </motion.span>
      </AnimatePresence>
    </div>
  );
}
