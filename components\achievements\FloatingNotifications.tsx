'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Trophy, 
  Star, 
  Zap, 
  Target, 
  Award, 
  TrendingUp,
  CheckCircle,
  X,
  Share2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAchievementNotifications } from './SiteWideNotificationSystem';
import { QuickShareButton } from '@/components/social/SocialSharingHooks';

interface FloatingNotificationProps {
  type: 'xp_gain' | 'level_up' | 'streak' | 'milestone' | 'compilation' | 'deployment';
  title: string;
  message: string;
  xpAmount?: number;
  level?: number;
  streakDays?: number;
  icon?: React.ComponentType<{ className?: string }>;
  duration?: number;
  position?: 'navigation' | 'editor' | 'lesson' | 'global';
  onDismiss?: () => void;
  enableSharing?: boolean;
  className?: string;
}

export function FloatingNotification({
  type,
  title,
  message,
  xpAmount,
  level,
  streakDays,
  icon: CustomIcon,
  duration = 5000,
  position = 'global',
  onDismiss,
  enableSharing = true,
  className
}: FloatingNotificationProps) {
  const [isVisible, setIsVisible] = useState(true);
  const [isHovered, setIsHovered] = useState(false);

  // Auto-dismiss timer (paused on hover)
  useEffect(() => {
    if (isHovered) return;

    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(() => onDismiss?.(), 300);
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onDismiss, isHovered]);

  const getIcon = () => {
    if (CustomIcon) return CustomIcon;
    
    switch (type) {
      case 'xp_gain':
        return Star;
      case 'level_up':
        return Trophy;
      case 'streak':
        return Zap;
      case 'milestone':
        return Target;
      case 'compilation':
        return CheckCircle;
      case 'deployment':
        return Award;
      default:
        return Star;
    }
  };

  const getColor = () => {
    switch (type) {
      case 'xp_gain':
        return 'from-yellow-600 to-orange-600 border-yellow-500';
      case 'level_up':
        return 'from-purple-600 to-pink-600 border-purple-500';
      case 'streak':
        return 'from-orange-600 to-red-600 border-orange-500';
      case 'milestone':
        return 'from-blue-600 to-indigo-600 border-blue-500';
      case 'compilation':
        return 'from-green-600 to-emerald-600 border-green-500';
      case 'deployment':
        return 'from-indigo-600 to-purple-600 border-indigo-500';
      default:
        return 'from-gray-600 to-gray-800 border-gray-500';
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'navigation':
        return 'top-16 right-4';
      case 'editor':
        return 'top-4 left-4';
      case 'lesson':
        return 'bottom-4 right-4';
      case 'global':
      default:
        return 'top-4 right-4';
    }
  };

  const getShareData = () => {
    switch (type) {
      case 'level_up':
        return {
          shareType: 'level_up' as const,
          data: { progressData: { level: level || 1, totalXP: 0, completionPercentage: 0, streak: 0 } }
        };
      case 'streak':
        return {
          shareType: 'streak' as const,
          data: { progressData: { level: 1, totalXP: 0, completionPercentage: 0, streak: streakDays || 1 } }
        };
      default:
        return null;
    }
  };

  const Icon = getIcon();
  const shareData = getShareData();

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className={cn(
            'fixed z-40 max-w-sm w-full',
            getPositionClasses(),
            className
          )}
          initial={{ opacity: 0, scale: 0.8, x: 100 }}
          animate={{ opacity: 1, scale: 1, x: 0 }}
          exit={{ opacity: 0, scale: 0.8, x: 100 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className={cn(
            'bg-gradient-to-br backdrop-blur-md rounded-lg border shadow-lg overflow-hidden',
            getColor()
          )}>
            <div className="p-4">
              {/* Header */}
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <div className="p-1.5 bg-white/20 rounded-lg">
                    <Icon className="w-4 h-4 text-white" />
                  </div>
                  <h3 className="font-semibold text-white text-sm">{title}</h3>
                </div>
                <button
                  onClick={() => {
                    setIsVisible(false);
                    setTimeout(() => onDismiss?.(), 300);
                  }}
                  className="text-white/60 hover:text-white transition-colors"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>

              {/* Content */}
              <p className="text-white/90 text-sm mb-3">{message}</p>

              {/* Stats */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {xpAmount && (
                    <div className="flex items-center space-x-1">
                      <Star className="w-3 h-3 text-yellow-400" />
                      <span className="text-yellow-400 text-xs font-medium">+{xpAmount} XP</span>
                    </div>
                  )}
                  {level && (
                    <div className="flex items-center space-x-1">
                      <Trophy className="w-3 h-3 text-purple-400" />
                      <span className="text-purple-400 text-xs font-medium">Level {level}</span>
                    </div>
                  )}
                  {streakDays && (
                    <div className="flex items-center space-x-1">
                      <Zap className="w-3 h-3 text-orange-400" />
                      <span className="text-orange-400 text-xs font-medium">{streakDays} days</span>
                    </div>
                  )}
                </div>

                {/* Share button */}
                {enableSharing && shareData && (
                  <QuickShareButton
                    shareType={shareData.shareType}
                    data={shareData.data}
                    variant="icon"
                    className="opacity-80 hover:opacity-100"
                  />
                )}
              </div>
            </div>

            {/* Progress bar for auto-dismiss */}
            {!isHovered && (
              <motion.div
                className="h-0.5 bg-white/30"
                initial={{ width: '100%' }}
                animate={{ width: '0%' }}
                transition={{ duration: duration / 1000, ease: "linear" }}
              />
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Hook for managing floating notifications
export function useFloatingNotifications() {
  const [notifications, setNotifications] = useState<Array<FloatingNotificationProps & { id: string }>>([]);

  const showNotification = (notification: Omit<FloatingNotificationProps, 'onDismiss'>) => {
    const id = Date.now().toString();
    const newNotification = {
      ...notification,
      id,
      onDismiss: () => dismissNotification(id)
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove after duration + animation time
    setTimeout(() => {
      dismissNotification(id);
    }, (notification.duration || 5000) + 500);
  };

  const dismissNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const showXPGain = (amount: number, source: string) => {
    showNotification({
      type: 'xp_gain',
      title: 'XP Gained!',
      message: `Earned ${amount} XP from ${source}`,
      xpAmount: amount,
      duration: 3000
    });
  };

  const showLevelUp = (newLevel: number) => {
    showNotification({
      type: 'level_up',
      title: 'Level Up!',
      message: `Congratulations! You've reached level ${newLevel}!`,
      level: newLevel,
      duration: 6000,
      enableSharing: true
    });
  };

  const showStreakMilestone = (days: number) => {
    showNotification({
      type: 'streak',
      title: 'Streak Milestone!',
      message: `Amazing! You've maintained a ${days}-day learning streak!`,
      streakDays: days,
      duration: 5000,
      enableSharing: true
    });
  };

  const showCompilationSuccess = () => {
    showNotification({
      type: 'compilation',
      title: 'Compilation Successful!',
      message: 'Your smart contract compiled without errors.',
      xpAmount: 25,
      duration: 4000
    });
  };

  const showDeploymentSuccess = () => {
    showNotification({
      type: 'deployment',
      title: 'Deployment Successful!',
      message: 'Your contract has been deployed to the testnet!',
      xpAmount: 50,
      duration: 5000
    });
  };

  const showMilestone = (title: string, description: string, xp?: number) => {
    showNotification({
      type: 'milestone',
      title,
      message: description,
      xpAmount: xp,
      duration: 5000
    });
  };

  return {
    notifications,
    showNotification,
    showXPGain,
    showLevelUp,
    showStreakMilestone,
    showCompilationSuccess,
    showDeploymentSuccess,
    showMilestone,
    dismissNotification
  };
}

// Notification container component
export function FloatingNotificationContainer() {
  const { notifications } = useFloatingNotifications();

  return (
    <>
      {notifications.map((notification, index) => (
        <FloatingNotification
          key={notification.id}
          {...notification}
          className={`transform translate-y-[${index * 80}px]`}
        />
      ))}
    </>
  );
}

// Integration component for specific areas
export function NavigationNotifications() {
  const { notifications } = useFloatingNotifications();
  
  return (
    <>
      {notifications
        .filter(n => n.position === 'navigation')
        .map((notification, index) => (
          <FloatingNotification
            key={notification.id}
            {...notification}
            className={`transform translate-y-[${index * 60}px]`}
          />
        ))}
    </>
  );
}

export function EditorNotifications() {
  const { notifications } = useFloatingNotifications();
  
  return (
    <>
      {notifications
        .filter(n => n.position === 'editor')
        .map((notification, index) => (
          <FloatingNotification
            key={notification.id}
            {...notification}
            className={`transform translate-y-[${index * 60}px]`}
          />
        ))}
    </>
  );
}

export function LessonNotifications() {
  const { notifications } = useFloatingNotifications();
  
  return (
    <>
      {notifications
        .filter(n => n.position === 'lesson')
        .map((notification, index) => (
          <FloatingNotification
            key={notification.id}
            {...notification}
            className={`transform -translate-y-[${index * 60}px]`}
          />
        ))}
    </>
  );
}
