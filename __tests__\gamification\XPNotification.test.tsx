import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import '@testing-library/jest-dom';
import { XPNotification, XPNotificationManager, useXPNotifications } from '@/components/xp/XPNotification';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock analytics
jest.mock('@/lib/analytics/GamificationAnalytics', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    trackXPGained: jest.fn(),
    measureOperation: jest.fn((name, fn) => fn()),
  })),
}));

// Mock storage
jest.mock('@/lib/storage/OfflineProgressStorage', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    addXP: jest.fn(),
  })),
}));

describe('XPNotification', () => {
  const mockXPGain = {
    id: 'test-xp-1',
    amount: 100,
    source: 'lesson',
    description: 'Completed lesson',
    timestamp: new Date(),
    position: { x: 100, y: 100 },
  };

  const mockOnComplete = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock performance.now
    global.performance.now = jest.fn(() => 1000);
  });

  it('renders XP notification with correct amount', () => {
    render(
      <XPNotification
        xpGain={mockXPGain}
        onComplete={mockOnComplete}
      />
    );

    expect(screen.getByText('+100')).toBeInTheDocument();
    expect(screen.getByText('XP')).toBeInTheDocument();
    expect(screen.getByText('Completed lesson')).toBeInTheDocument();
  });

  it('displays multiplier when provided', () => {
    const xpGainWithMultiplier = {
      ...mockXPGain,
      multiplier: 2,
    };

    render(
      <XPNotification
        xpGain={xpGainWithMultiplier}
        onComplete={mockOnComplete}
      />
    );

    expect(screen.getByText('2x')).toBeInTheDocument();
  });

  it('displays streak indicator when provided', () => {
    const xpGainWithStreak = {
      ...mockXPGain,
      streak: 5,
    };

    render(
      <XPNotification
        xpGain={xpGainWithStreak}
        onComplete={mockOnComplete}
      />
    );

    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('calls onComplete after duration', async () => {
    jest.useFakeTimers();

    render(
      <XPNotification
        xpGain={mockXPGain}
        onComplete={mockOnComplete}
        duration={1000}
      />
    );

    // Fast-forward time
    act(() => {
      jest.advanceTimersByTime(1500); // Duration + exit animation
    });

    await waitFor(() => {
      expect(mockOnComplete).toHaveBeenCalled();
    });

    jest.useRealTimers();
  });

  it('applies correct color class based on source', () => {
    const { container } = render(
      <XPNotification
        xpGain={{ ...mockXPGain, source: 'achievement' }}
        onComplete={mockOnComplete}
      />
    );

    // Check if achievement-specific styling is applied
    const notification = container.querySelector('[class*="yellow"]');
    expect(notification).toBeInTheDocument();
  });

  it('handles bonus XP styling', () => {
    const bonusXPGain = {
      ...mockXPGain,
      isBonus: true,
    };

    const { container } = render(
      <XPNotification
        xpGain={bonusXPGain}
        onComplete={mockOnComplete}
      />
    );

    // Check for bonus-specific styling
    const bonusElement = container.querySelector('[class*="ring"]');
    expect(bonusElement).toBeInTheDocument();
  });
});

describe('XPNotificationManager', () => {
  beforeEach(() => {
    // Clear any existing global functions
    delete (window as any).addXPNotification;
  });

  it('renders without crashing', () => {
    render(<XPNotificationManager />);
  });

  it('exposes addXPNotification function globally', () => {
    render(<XPNotificationManager />);
    
    expect(typeof (window as any).addXPNotification).toBe('function');
  });

  it('limits number of notifications', () => {
    const { container } = render(
      <XPNotificationManager maxNotifications={2} />
    );

    // Add multiple notifications
    act(() => {
      (window as any).addXPNotification({
        amount: 100,
        source: 'lesson',
        description: 'Test 1',
      });
      (window as any).addXPNotification({
        amount: 200,
        source: 'quiz',
        description: 'Test 2',
      });
      (window as any).addXPNotification({
        amount: 300,
        source: 'project',
        description: 'Test 3',
      });
    });

    // Should only show 2 notifications (maxNotifications)
    const notifications = container.querySelectorAll('[class*="fixed"]');
    expect(notifications.length).toBeLessThanOrEqual(2);
  });
});

describe('useXPNotifications hook', () => {
  const TestComponent = () => {
    const {
      triggerXPGain,
      triggerLessonXP,
      triggerQuizXP,
      triggerStreakXP,
      triggerBonusXP,
      triggerAchievementXP,
    } = useXPNotifications();

    return (
      <div>
        <button onClick={() => triggerXPGain(100, 'test', 'Test XP')}>
          Trigger XP
        </button>
        <button onClick={() => triggerLessonXP(50, 'Test Lesson')}>
          Trigger Lesson XP
        </button>
        <button onClick={() => triggerQuizXP(75, 85)}>
          Trigger Quiz XP
        </button>
        <button onClick={() => triggerStreakXP(25, 3)}>
          Trigger Streak XP
        </button>
        <button onClick={() => triggerBonusXP(200, 'Speed bonus', 2)}>
          Trigger Bonus XP
        </button>
        <button onClick={() => triggerAchievementXP(150, 'Test Achievement')}>
          Trigger Achievement XP
        </button>
      </div>
    );
  };

  beforeEach(() => {
    // Mock the global function
    (window as any).addXPNotification = jest.fn();
  });

  it('triggerXPGain calls global function with correct parameters', () => {
    render(<TestComponent />);
    
    fireEvent.click(screen.getByText('Trigger XP'));
    
    expect((window as any).addXPNotification).toHaveBeenCalledWith({
      amount: 100,
      source: 'test',
      description: 'Test XP',
      position: undefined,
    });
  });

  it('triggerLessonXP uses correct icon and color', () => {
    render(<TestComponent />);
    
    fireEvent.click(screen.getByText('Trigger Lesson XP'));
    
    expect((window as any).addXPNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        amount: 50,
        source: 'lesson',
        description: 'Completed: Test Lesson',
        color: 'text-blue-400',
      })
    );
  });

  it('triggerQuizXP formats score correctly', () => {
    render(<TestComponent />);
    
    fireEvent.click(screen.getByText('Trigger Quiz XP'));
    
    expect((window as any).addXPNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        amount: 75,
        source: 'quiz',
        description: 'Quiz Score: 85%',
        color: 'text-green-400',
      })
    );
  });

  it('triggerStreakXP includes streak data', () => {
    render(<TestComponent />);
    
    fireEvent.click(screen.getByText('Trigger Streak XP'));
    
    expect((window as any).addXPNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        amount: 25,
        source: 'streak',
        description: '3 day streak!',
        streak: 3,
        color: 'text-orange-400',
      })
    );
  });

  it('triggerBonusXP marks as bonus with multiplier', () => {
    render(<TestComponent />);
    
    fireEvent.click(screen.getByText('Trigger Bonus XP'));
    
    expect((window as any).addXPNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        amount: 200,
        source: 'bonus',
        description: 'Speed bonus',
        multiplier: 2,
        isBonus: true,
        color: 'text-pink-400',
      })
    );
  });

  it('triggerAchievementXP marks as bonus', () => {
    render(<TestComponent />);
    
    fireEvent.click(screen.getByText('Trigger Achievement XP'));
    
    expect((window as any).addXPNotification).toHaveBeenCalledWith(
      expect.objectContaining({
        amount: 150,
        source: 'achievement',
        description: 'Achievement: Test Achievement',
        isBonus: true,
        color: 'text-yellow-400',
      })
    );
  });
});

describe('Performance Tests', () => {
  it('XP notification renders within performance threshold', async () => {
    const startTime = performance.now();
    
    render(
      <XPNotification
        xpGain={mockXPGain}
        onComplete={() => {}}
      />
    );
    
    const renderTime = performance.now() - startTime;
    
    // Should render within 50ms
    expect(renderTime).toBeLessThan(50);
  });

  it('handles multiple simultaneous notifications efficiently', () => {
    const startTime = performance.now();
    
    const { container } = render(<XPNotificationManager />);
    
    // Trigger multiple notifications
    act(() => {
      for (let i = 0; i < 10; i++) {
        (window as any).addXPNotification({
          amount: 100 + i,
          source: 'test',
          description: `Test ${i}`,
        });
      }
    });
    
    const processingTime = performance.now() - startTime;
    
    // Should process 10 notifications within 100ms
    expect(processingTime).toBeLessThan(100);
  });
});

describe('Accessibility Tests', () => {
  it('has proper ARIA labels', () => {
    render(
      <XPNotification
        xpGain={mockXPGain}
        onComplete={() => {}}
      />
    );

    // Check for screen reader accessible content
    expect(screen.getByText('XP')).toBeInTheDocument();
    expect(screen.getByText('Completed lesson')).toBeInTheDocument();
  });

  it('respects reduced motion preferences', () => {
    // Mock reduced motion preference
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    const { container } = render(
      <XPNotification
        xpGain={mockXPGain}
        onComplete={() => {}}
      />
    );

    // Should still render but with reduced animations
    expect(container.firstChild).toBeInTheDocument();
  });

  it('provides sufficient color contrast', () => {
    const { container } = render(
      <XPNotification
        xpGain={mockXPGain}
        onComplete={() => {}}
      />
    );

    // Check for high contrast text
    const xpText = screen.getByText('+100');
    const computedStyle = window.getComputedStyle(xpText);
    
    // Should have sufficient contrast (this is a basic check)
    expect(computedStyle.color).toBeTruthy();
  });
});
