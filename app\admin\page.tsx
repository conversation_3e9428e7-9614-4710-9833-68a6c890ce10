'use client';

import React, { Suspense, lazy } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { AdminGuard } from '@/components/admin/AdminGuard';
import { ADMIN_PERMISSIONS } from '@/lib/admin/auth';

// Dynamically import AdminDashboard for better code splitting
const AdminDashboard = lazy(() => import('@/components/admin/AdminDashboard').then(mod => ({ default: mod.AdminDashboard })));

export default function AdminPage() {
  return (
    <AdminGuard requiredPermission={ADMIN_PERMISSIONS.SYSTEM_READ}>
      <AdminLayout currentPage="dashboard">
        <Suspense fallback={
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-400">Loading Admin Dashboard...</p>
            </div>
          </div>
        }>
          <AdminDashboard />
        </Suspense>
      </AdminLayout>
    </AdminGuard>
  );
}

