/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AdvancedCodeEditor } from '../AdvancedCodeEditor';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
}));

// Mock clipboard API
Object.assign(navigator, {
  clipboard: {
    writeText: jest.fn(() => Promise.resolve()),
  },
});

describe('AdvancedCodeEditor', () => {
  const defaultProps = {
    initialCode: 'pragma solidity ^0.8.0;\n\ncontract Test {}',
    language: 'solidity' as const,
    filename: 'Test.sol',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with default props', () => {
    render(<AdvancedCodeEditor {...defaultProps} />);
    
    expect(screen.getByText('Test.sol')).toBeInTheDocument();
    expect(screen.getByText(/pragma solidity/)).toBeInTheDocument();
  });

  it('displays line numbers when enabled', () => {
    render(<AdvancedCodeEditor {...defaultProps} showLineNumbers={true} />);
    
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
    expect(screen.getByText('3')).toBeInTheDocument();
  });

  it('hides line numbers when disabled', () => {
    render(<AdvancedCodeEditor {...defaultProps} showLineNumbers={false} />);
    
    expect(screen.queryByText('1')).not.toBeInTheDocument();
  });

  it('copies code to clipboard when copy button is clicked', async () => {
    const user = userEvent.setup();
    render(<AdvancedCodeEditor {...defaultProps} copyable={true} />);
    
    const copyButton = screen.getByText('Copy');
    await user.click(copyButton);
    
    expect(navigator.clipboard.writeText).toHaveBeenCalledWith(defaultProps.initialCode);
    expect(screen.getByText('Copied!')).toBeInTheDocument();
  });

  it('shows copy success feedback temporarily', async () => {
    const user = userEvent.setup();
    jest.useFakeTimers();
    
    render(<AdvancedCodeEditor {...defaultProps} copyable={true} />);
    
    const copyButton = screen.getByText('Copy');
    await user.click(copyButton);
    
    expect(screen.getByText('Copied!')).toBeInTheDocument();
    
    jest.advanceTimersByTime(2000);
    
    await waitFor(() => {
      expect(screen.getByText('Copy')).toBeInTheDocument();
    });
    
    jest.useRealTimers();
  });

  it('enables editing when editable is true', async () => {
    const user = userEvent.setup();
    render(<AdvancedCodeEditor {...defaultProps} editable={true} />);
    
    const codeDisplay = screen.getByRole('code').parentElement;
    await user.click(codeDisplay!);
    
    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });

  it('calls onCodeChange when code is modified', async () => {
    const user = userEvent.setup();
    const onCodeChange = jest.fn();
    
    render(
      <AdvancedCodeEditor 
        {...defaultProps} 
        editable={true} 
        onCodeChange={onCodeChange}
      />
    );
    
    const codeDisplay = screen.getByRole('code').parentElement;
    await user.click(codeDisplay!);
    
    const textarea = screen.getByRole('textbox');
    await user.clear(textarea);
    await user.type(textarea, 'new code');
    
    expect(onCodeChange).toHaveBeenCalledWith('new code');
  });

  it('resets code to initial value when reset button is clicked', async () => {
    const user = userEvent.setup();
    const onCodeChange = jest.fn();
    
    render(
      <AdvancedCodeEditor 
        {...defaultProps} 
        editable={true} 
        onCodeChange={onCodeChange}
      />
    );
    
    // Edit the code first
    const codeDisplay = screen.getByRole('code').parentElement;
    await user.click(codeDisplay!);
    
    const textarea = screen.getByRole('textbox');
    await user.clear(textarea);
    await user.type(textarea, 'modified code');
    
    // Click reset
    const resetButton = screen.getByText('Reset');
    await user.click(resetButton);
    
    expect(onCodeChange).toHaveBeenLastCalledWith(defaultProps.initialCode);
  });

  it('formats code when format button is clicked', async () => {
    const user = userEvent.setup();
    const onCodeChange = jest.fn();
    
    render(
      <AdvancedCodeEditor 
        {...defaultProps} 
        editable={true} 
        onCodeChange={onCodeChange}
      />
    );
    
    const formatButton = screen.getByText('Format');
    await user.click(formatButton);
    
    expect(onCodeChange).toHaveBeenCalled();
  });

  it('calls onCompile when compile button is clicked', async () => {
    const user = userEvent.setup();
    const onCompile = jest.fn();
    
    render(
      <AdvancedCodeEditor 
        {...defaultProps} 
        onCompile={onCompile}
      />
    );
    
    const compileButton = screen.getByText('Compile');
    await user.click(compileButton);
    
    expect(onCompile).toHaveBeenCalledWith(defaultProps.initialCode);
  });

  it('displays annotations with tooltips', async () => {
    const user = userEvent.setup();
    const annotations = [
      {
        line: 1,
        type: 'info' as const,
        title: 'Pragma Directive',
        content: 'Specifies compiler version',
      },
    ];
    
    render(
      <AdvancedCodeEditor 
        {...defaultProps} 
        annotations={annotations}
      />
    );
    
    const annotationIcon = screen.getByRole('img', { hidden: true });
    await user.hover(annotationIcon);
    
    await waitFor(() => {
      expect(screen.getByText('Pragma Directive')).toBeInTheDocument();
      expect(screen.getByText('Specifies compiler version')).toBeInTheDocument();
    });
  });

  it('highlights syntax for Solidity code', () => {
    render(<AdvancedCodeEditor {...defaultProps} />);
    
    // Check that syntax highlighting classes are applied
    const codeElement = screen.getByRole('code');
    expect(codeElement.innerHTML).toContain('text-purple-400'); // Keywords
  });

  it('applies custom className', () => {
    render(<AdvancedCodeEditor {...defaultProps} className="custom-editor" />);
    
    const container = screen.getByText('Test.sol').closest('.custom-editor');
    expect(container).toBeInTheDocument();
  });

  it('disables copy button when copyable is false', () => {
    render(<AdvancedCodeEditor {...defaultProps} copyable={false} />);
    
    expect(screen.queryByText('Copy')).not.toBeInTheDocument();
  });

  it('disables editing controls when editable is false', () => {
    render(<AdvancedCodeEditor {...defaultProps} editable={false} />);
    
    expect(screen.queryByText('Format')).not.toBeInTheDocument();
    expect(screen.queryByText('Reset')).not.toBeInTheDocument();
  });

  it('handles empty code gracefully', () => {
    render(<AdvancedCodeEditor {...defaultProps} initialCode="" />);
    
    expect(screen.getByText('Test.sol')).toBeInTheDocument();
  });

  it('handles long code with proper scrolling', () => {
    const longCode = Array(100).fill('// This is a long line of code').join('\n');
    
    render(<AdvancedCodeEditor {...defaultProps} initialCode={longCode} />);
    
    const codeContainer = screen.getByRole('code').parentElement;
    expect(codeContainer).toHaveClass('overflow-x-auto');
  });

  it('shows annotation links when provided', async () => {
    const user = userEvent.setup();
    const annotations = [
      {
        line: 1,
        type: 'info' as const,
        title: 'Learn More',
        content: 'Click to learn more',
        link: 'https://example.com',
      },
    ];
    
    render(
      <AdvancedCodeEditor 
        {...defaultProps} 
        annotations={annotations}
      />
    );
    
    const annotationIcon = screen.getByRole('img', { hidden: true });
    await user.hover(annotationIcon);
    
    await waitFor(() => {
      const link = screen.getByText('Learn more →');
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', 'https://example.com');
    });
  });

  it('handles multiple annotations on different lines', () => {
    const annotations = [
      {
        line: 1,
        type: 'info' as const,
        title: 'First annotation',
        content: 'First content',
      },
      {
        line: 3,
        type: 'warning' as const,
        title: 'Second annotation',
        content: 'Second content',
      },
    ];
    
    render(
      <AdvancedCodeEditor 
        {...defaultProps} 
        annotations={annotations}
      />
    );
    
    const annotationIcons = screen.getAllByRole('img', { hidden: true });
    expect(annotationIcons).toHaveLength(2);
  });

  it('maintains focus when switching between edit and display modes', async () => {
    const user = userEvent.setup();
    
    render(<AdvancedCodeEditor {...defaultProps} editable={true} />);
    
    const codeDisplay = screen.getByRole('code').parentElement;
    await user.click(codeDisplay!);
    
    const textarea = screen.getByRole('textbox');
    expect(textarea).toHaveFocus();
    
    await user.click(document.body);
    
    expect(screen.queryByRole('textbox')).not.toBeInTheDocument();
  });
});
