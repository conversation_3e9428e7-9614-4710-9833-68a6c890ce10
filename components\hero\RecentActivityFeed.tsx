'use client';

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BookOpen, 
  Trophy, 
  Code, 
  Users, 
  Zap, 
  Star, 
  Award,
  Rocket,
  Target,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ActivityItem {
  id: string;
  type: 'lesson_completed' | 'achievement_earned' | 'course_started' | 'project_deployed' | 'milestone_reached';
  user: string;
  action: string;
  target: string;
  timestamp: Date;
  icon: string;
  color: string;
  xpGained?: number;
}

interface RecentActivityFeedProps {
  className?: string;
  maxItems?: number;
  showXP?: boolean;
  variant?: 'full' | 'compact' | 'minimal';
}

/**
 * Recent Activity Feed Component
 * Shows real-time user activity to create social proof and engagement
 */
export function RecentActivityFeed({ 
  className = '', 
  maxItems = 5,
  showXP = true,
  variant = 'full' 
}: RecentActivityFeedProps) {
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Mock activity data - in real app this would come from API/WebSocket
  const mockActivities: ActivityItem[] = [
    {
      id: '1',
      type: 'lesson_completed',
      user: 'Sarah Chen',
      action: 'completed',
      target: 'Smart Contract Basics',
      timestamp: new Date(Date.now() - 2 * 60 * 1000),
      icon: '📚',
      color: 'text-blue-400',
      xpGained: 150
    },
    {
      id: '2',
      type: 'achievement_earned',
      user: 'Alex Rodriguez',
      action: 'earned achievement',
      target: 'Gas Optimizer',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      icon: '🏆',
      color: 'text-yellow-400',
      xpGained: 500
    },
    {
      id: '3',
      type: 'course_started',
      user: 'Maya Patel',
      action: 'started course',
      target: 'Advanced Solidity',
      timestamp: new Date(Date.now() - 8 * 60 * 1000),
      icon: '🚀',
      color: 'text-green-400'
    },
    {
      id: '4',
      type: 'project_deployed',
      user: 'David Kim',
      action: 'deployed contract',
      target: 'DeFi Token Swap',
      timestamp: new Date(Date.now() - 12 * 60 * 1000),
      icon: '⚡',
      color: 'text-purple-400',
      xpGained: 750
    },
    {
      id: '5',
      type: 'milestone_reached',
      user: 'Emma Wilson',
      action: 'reached milestone',
      target: '100 Lessons Completed',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      icon: '🎯',
      color: 'text-pink-400',
      xpGained: 1000
    },
    {
      id: '6',
      type: 'lesson_completed',
      user: 'James Thompson',
      action: 'completed',
      target: 'NFT Marketplace Tutorial',
      timestamp: new Date(Date.now() - 18 * 60 * 1000),
      icon: '🎨',
      color: 'text-indigo-400',
      xpGained: 200
    },
    {
      id: '7',
      type: 'achievement_earned',
      user: 'Lisa Zhang',
      action: 'earned achievement',
      target: 'Security Expert',
      timestamp: new Date(Date.now() - 22 * 60 * 1000),
      icon: '🛡️',
      color: 'text-red-400',
      xpGained: 800
    }
  ];

  useEffect(() => {
    // Initialize with mock data
    setActivities(mockActivities.slice(0, maxItems));

    // Simulate real-time updates
    const interval = setInterval(() => {
      // Add new activity occasionally
      if (Math.random() > 0.7) {
        const newActivity: ActivityItem = {
          id: Date.now().toString(),
          type: 'lesson_completed',
          user: `User ${Math.floor(Math.random() * 1000)}`,
          action: 'completed',
          target: 'Solidity Fundamentals',
          timestamp: new Date(),
          icon: '✨',
          color: 'text-cyan-400',
          xpGained: Math.floor(Math.random() * 300) + 100
        };

        setActivities(prev => [newActivity, ...prev.slice(0, maxItems - 1)]);
      }
    }, 15000); // Add new activity every 15 seconds

    return () => clearInterval(interval);
  }, [maxItems]);

  // Cycle through activities for compact/minimal variants
  useEffect(() => {
    if (variant === 'full') return;

    const interval = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % activities.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [variant, activities.length]);

  const formatTimeAgo = (timestamp: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  if (variant === 'minimal') {
    const currentActivity = activities[currentIndex];
    if (!currentActivity) return null;

    return (
      <AnimatePresence mode="wait">
        <motion.div
          key={currentActivity.id}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
          className={cn(
            'flex items-center space-x-2 text-sm text-gray-300',
            className
          )}
        >
          <span className="text-base">{currentActivity.icon}</span>
          <span>
            <strong className="text-white">{currentActivity.user}</strong> {currentActivity.action} {currentActivity.target}
          </span>
          <span className="text-gray-400">• {formatTimeAgo(currentActivity.timestamp)}</span>
        </motion.div>
      </AnimatePresence>
    );
  }

  if (variant === 'compact') {
    return (
      <motion.div
        className={cn(
          'glass rounded-lg p-4 border border-white/10 backdrop-blur-md bg-white/5',
          className
        )}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 0.6 }}
      >
        <div className="flex items-center space-x-2 mb-3">
          <Sparkles className="w-4 h-4 text-purple-400" />
          <h3 className="text-sm font-semibold text-white">Live Activity</h3>
        </div>
        
        <div className="space-y-2 max-h-32 overflow-hidden">
          <AnimatePresence mode="popLayout">
            {activities.slice(0, 3).map((activity, index) => (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ delay: index * 0.1, duration: 0.3 }}
                className="flex items-center space-x-2 text-xs"
              >
                <span className="text-sm">{activity.icon}</span>
                <span className="text-gray-300 truncate">
                  <span className="text-white font-medium">{activity.user}</span> {activity.action} {activity.target}
                </span>
                <span className="text-gray-500 text-xs whitespace-nowrap">
                  {formatTimeAgo(activity.timestamp)}
                </span>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </motion.div>
    );
  }

  // Full variant
  return (
    <motion.div
      className={cn(
        'glass rounded-xl p-6 border border-white/10 backdrop-blur-md bg-white/5',
        className
      )}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 2.2, duration: 0.8 }}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 rounded-full bg-gradient-to-br from-green-500/20 to-blue-500/20 flex items-center justify-center">
            <Sparkles className="w-4 h-4 text-green-400" />
          </div>
          <h3 className="text-lg font-semibold text-white">Live Activity Feed</h3>
        </div>
        
        <motion.div
          animate={{ scale: [1, 1.1, 1] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="w-2 h-2 bg-green-400 rounded-full"
        />
      </div>
      
      <div className="space-y-3 max-h-64 overflow-y-auto custom-scrollbar">
        <AnimatePresence mode="popLayout">
          {activities.map((activity, index) => (
            <motion.div
              key={activity.id}
              initial={{ opacity: 0, x: -20, scale: 0.95 }}
              animate={{ opacity: 1, x: 0, scale: 1 }}
              exit={{ opacity: 0, x: 20, scale: 0.95 }}
              transition={{ delay: index * 0.05, duration: 0.3 }}
              className="flex items-center space-x-3 p-3 rounded-lg hover:bg-white/5 transition-colors duration-200 group"
            >
              <div className="flex-shrink-0">
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-white/10 to-white/5 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                  <span className="text-lg">{activity.icon}</span>
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className="text-white font-medium truncate">
                    {activity.user}
                  </span>
                  <span className="text-gray-300 text-sm">
                    {activity.action}
                  </span>
                </div>
                <div className="flex items-center space-x-2 mt-1">
                  <span className={cn('text-sm font-medium', activity.color)}>
                    {activity.target}
                  </span>
                  {showXP && activity.xpGained && (
                    <span className="text-xs text-yellow-400 font-mono">
                      +{activity.xpGained} XP
                    </span>
                  )}
                </div>
              </div>
              
              <div className="flex-shrink-0 text-xs text-gray-400">
                {formatTimeAgo(activity.timestamp)}
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
      
      <motion.div
        className="mt-4 pt-3 border-t border-white/10 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 2.8, duration: 0.6 }}
      >
        <p className="text-xs text-gray-400">
          Join <span className="text-green-400 font-medium">127 developers</span> learning right now
        </p>
      </motion.div>
    </motion.div>
  );
}
