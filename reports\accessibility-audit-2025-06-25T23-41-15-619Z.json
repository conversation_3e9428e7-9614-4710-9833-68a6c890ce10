{"timestamp": "2025-06-25T23:41:15.620Z", "summary": {"success": true, "totalViolations": 0, "totalPasses": 0, "criticalIssues": [], "allIssues": [{"page": "Homepage", "type": "color-contrast", "issue": "Text and background colors are identical"}, {"page": "Dashboard", "type": "color-contrast", "issue": "Text and background colors are identical"}, {"page": "<PERSON><PERSON>", "type": "color-contrast", "issue": "Text and background colors are identical"}, {"page": "Code Editor", "type": "color-contrast", "issue": "Text and background colors are identical"}, {"page": "<PERSON><PERSON>", "type": "color-contrast", "issue": "Text and background colors are identical"}, {"page": "Register Page", "type": "color-contrast", "issue": "Text and background colors are identical"}], "results": [{"url": "http://localhost:3000/", "name": "Homepage", "axeResults": null, "keyboardResults": {"focusableElements": 72, "tabOrder": [{"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}], "issues": []}, "contrastResults": [{"element": "SPAN", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "issue": "Text and background colors are identical"}]}, {"url": "http://localhost:3000/dashboard", "name": "Dashboard", "axeResults": null, "keyboardResults": {"focusableElements": 69, "tabOrder": [{"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}], "issues": []}, "contrastResults": [{"element": "SPAN", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "issue": "Text and background colors are identical"}]}, {"url": "http://localhost:3000/learn", "name": "<PERSON><PERSON>", "axeResults": null, "keyboardResults": {"focusableElements": 60, "tabOrder": [{"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}], "issues": []}, "contrastResults": [{"element": "SPAN", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "issue": "Text and background colors are identical"}]}, {"url": "http://localhost:3000/code", "name": "Code Editor", "axeResults": null, "keyboardResults": {"focusableElements": 60, "tabOrder": [{"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}], "issues": []}, "contrastResults": [{"element": "SPAN", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "issue": "Text and background colors are identical"}]}, {"url": "http://localhost:3000/auth/login", "name": "<PERSON><PERSON>", "axeResults": null, "keyboardResults": {"focusableElements": 69, "tabOrder": [{"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}], "issues": []}, "contrastResults": [{"element": "SPAN", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "issue": "Text and background colors are identical"}]}, {"url": "http://localhost:3000/auth/register", "name": "Register Page", "axeResults": null, "keyboardResults": {"focusableElements": 69, "tabOrder": [{"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "text": null}], "issues": []}, "contrastResults": [{"element": "SPAN", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "issue": "Text and background colors are identical"}]}]}, "config": {"baseUrl": "http://localhost:3000", "pages": [{"path": "/", "name": "Homepage"}, {"path": "/dashboard", "name": "Dashboard"}, {"path": "/learn", "name": "<PERSON><PERSON>"}, {"path": "/code", "name": "Code Editor"}, {"path": "/auth/login", "name": "<PERSON><PERSON>"}, {"path": "/auth/register", "name": "Register Page"}], "timeout": 30000, "viewport": {"width": 1280, "height": 720}}}