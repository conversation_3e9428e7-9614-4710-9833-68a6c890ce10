#!/usr/bin/env node

/**
 * Phase 2 Validation & Measurement Script
 * Comprehensive testing, bundle measurement, and performance validation
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  bundleDirectory: '.next/static/chunks',
  reportDirectory: 'reports/phase2-validation',
  testTimeout: 300000, // 5 minutes
  buildTimeout: 600000, // 10 minutes
  expectedReductions: {
    devDependencies: 300, // KB
    dynamicImports: 150, // KB
    thirdPartyOptimization: 100, // KB
    unusedExports: 75, // KB
    advancedCodeSplitting: 200, // KB
    debugCleanup: 10, // KB
    total: 835 // KB
  }
};

// Colors for output
const colors = {
  reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m',
  yellow: '\x1b[33m', blue: '\x1b[34m', cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

// Ensure report directory exists
function ensureReportDirectory() {
  if (!fs.existsSync(CONFIG.reportDirectory)) {
    fs.mkdirSync(CONFIG.reportDirectory, { recursive: true });
  }
}

// Run TypeScript compilation check
function validateTypeScript() {
  logHeader('TypeScript Validation');
  
  try {
    log('Running TypeScript compilation check...', 'blue');
    execSync('npx tsc --noEmit', { 
      stdio: 'pipe',
      timeout: 60000 
    });
    logSuccess('TypeScript compilation: PASSED');
    return { passed: true, errors: [] };
  } catch (error) {
    const output = error.stdout ? error.stdout.toString() : error.message;
    logError('TypeScript compilation: FAILED');
    log(output, 'red');
    return { passed: false, errors: [output] };
  }
}

// Run build validation
function validateBuild() {
  logHeader('Build Validation');
  
  try {
    log('Running production build...', 'blue');
    execSync('npm run build', { 
      stdio: 'pipe',
      timeout: CONFIG.buildTimeout 
    });
    logSuccess('Production build: PASSED');
    return { passed: true, buildTime: Date.now() };
  } catch (error) {
    const output = error.stdout ? error.stdout.toString() : error.message;
    logError('Production build: FAILED');
    log(output, 'red');
    return { passed: false, error: output };
  }
}

// Measure bundle size
function measureBundleSize() {
  logHeader('Bundle Size Measurement');
  
  const bundleInfo = {
    javascript: 0,
    css: 0,
    chunks: [],
    total: 0,
    chunkCount: 0
  };

  if (!fs.existsSync(CONFIG.bundleDirectory)) {
    logWarning('Bundle directory not found');
    return bundleInfo;
  }

  const files = fs.readdirSync(CONFIG.bundleDirectory);
  
  files.forEach(file => {
    const filePath = path.join(CONFIG.bundleDirectory, file);
    const stats = fs.statSync(filePath);
    const sizeKB = Math.round(stats.size / 1024);
    
    const chunkInfo = {
      name: file,
      size: sizeKB,
      type: file.endsWith('.css') ? 'css' : 'javascript'
    };
    
    bundleInfo.chunks.push(chunkInfo);
    bundleInfo.chunkCount++;
    
    if (file.endsWith('.css')) {
      bundleInfo.css += sizeKB;
    } else {
      bundleInfo.javascript += sizeKB;
    }
  });

  bundleInfo.total = bundleInfo.javascript + bundleInfo.css;
  
  log(`📊 Bundle Analysis:`, 'blue');
  log(`   JavaScript: ${bundleInfo.javascript}KB`, 'blue');
  log(`   CSS: ${bundleInfo.css}KB`, 'blue');
  log(`   Total: ${bundleInfo.total}KB`, 'blue');
  log(`   Chunks: ${bundleInfo.chunkCount}`, 'blue');
  
  return bundleInfo;
}

// Validate Phase 2 optimizations
function validateOptimizations() {
  logHeader('Phase 2 Optimization Validation');
  
  const validations = [];
  
  // Check for dynamic imports
  log('Checking dynamic imports implementation...', 'blue');
  const dynamicImportFiles = [
    'app/achievements/page.tsx',
    'app/admin/page.tsx',
    'components/learning/InteractiveCodeEditor.tsx'
  ];
  
  let dynamicImportsFound = 0;
  dynamicImportFiles.forEach(file => {
    if (fs.existsSync(file)) {
      const content = fs.readFileSync(file, 'utf8');
      if (content.includes('lazy(') || content.includes('dynamic(')) {
        dynamicImportsFound++;
      }
    }
  });
  
  validations.push({
    name: 'Dynamic Imports',
    passed: dynamicImportsFound >= 2,
    details: `${dynamicImportsFound}/${dynamicImportFiles.length} files using dynamic imports`
  });
  
  // Check for development dependency exclusion
  log('Checking development dependency exclusion...', 'blue');
  const nextConfigContent = fs.existsSync('next.config.js') ? 
    fs.readFileSync('next.config.js', 'utf8') : '';
  const hasDevExclusion = nextConfigContent.includes('@tanstack/react-query-devtools') &&
                         nextConfigContent.includes('externals');
  
  validations.push({
    name: 'Development Dependencies Exclusion',
    passed: hasDevExclusion,
    details: hasDevExclusion ? 'Dev dependencies excluded from production' : 'Dev dependencies not excluded'
  });
  
  // Check for code splitting configuration
  log('Checking advanced code splitting...', 'blue');
  const hasAdvancedSplitting = nextConfigContent.includes('splitChunks') &&
                              nextConfigContent.includes('cacheGroups');
  
  validations.push({
    name: 'Advanced Code Splitting',
    passed: hasAdvancedSplitting,
    details: hasAdvancedSplitting ? 'Advanced code splitting configured' : 'Advanced code splitting not found'
  });
  
  // Check for route preloader
  log('Checking route preloading...', 'blue');
  const hasRoutePreloader = fs.existsSync('lib/utils/routePreloader.ts') &&
                           fs.existsSync('components/performance/RoutePreloader.tsx');
  
  validations.push({
    name: 'Route Preloading',
    passed: hasRoutePreloader,
    details: hasRoutePreloader ? 'Route preloading implemented' : 'Route preloading not found'
  });
  
  // Check for bundle monitoring
  log('Checking bundle monitoring setup...', 'blue');
  const hasBundleMonitoring = fs.existsSync('scripts/bundle-monitor.js') &&
                             fs.existsSync('.github/workflows/bundle-monitoring.yml');
  
  validations.push({
    name: 'Bundle Monitoring',
    passed: hasBundleMonitoring,
    details: hasBundleMonitoring ? 'Bundle monitoring configured' : 'Bundle monitoring not found'
  });
  
  // Display results
  validations.forEach(validation => {
    if (validation.passed) {
      logSuccess(`${validation.name}: ${validation.details}`);
    } else {
      logWarning(`${validation.name}: ${validation.details}`);
    }
  });
  
  return validations;
}

// Calculate Phase 2 impact
function calculatePhase2Impact(bundleSize) {
  logHeader('Phase 2 Impact Assessment');
  
  // Load Phase 1 baseline (if available)
  const phase1ReportPath = 'reports/bundle-size-measurement-results.md';
  let phase1Bundle = { total: 870 }; // Estimated from Phase 1 report
  
  if (fs.existsSync(phase1ReportPath)) {
    // Try to extract bundle size from Phase 1 report
    const phase1Content = fs.readFileSync(phase1ReportPath, 'utf8');
    const match = phase1Content.match(/Total: ~(\d+)KB/);
    if (match) {
      phase1Bundle.total = parseInt(match[1]);
    }
  }
  
  const reduction = phase1Bundle.total - bundleSize.total;
  const reductionPercent = ((reduction / phase1Bundle.total) * 100);
  
  log(`📈 Phase 2 Impact Analysis:`, 'blue');
  log(`   Phase 1 Bundle: ${phase1Bundle.total}KB`, 'blue');
  log(`   Current Bundle: ${bundleSize.total}KB`, 'blue');
  log(`   Reduction: ${reduction}KB (${reductionPercent.toFixed(1)}%)`, 'blue');
  
  const targetReduction = CONFIG.expectedReductions.total;
  const achievementPercent = (reduction / targetReduction) * 100;
  
  log(`   Target Reduction: ${targetReduction}KB`, 'blue');
  log(`   Achievement: ${achievementPercent.toFixed(1)}% of target`, 'blue');
  
  return {
    phase1Bundle: phase1Bundle.total,
    currentBundle: bundleSize.total,
    reduction,
    reductionPercent,
    targetReduction,
    achievementPercent
  };
}

// Generate comprehensive report
function generateValidationReport(results) {
  ensureReportDirectory();
  
  const timestamp = new Date().toISOString();
  const report = {
    timestamp,
    phase: 'Phase 2 Validation',
    results,
    summary: {
      overallStatus: results.typescript.passed && results.build.passed ? 'PASSED' : 'FAILED',
      bundleSize: results.bundleSize,
      optimizations: results.optimizations,
      impact: results.impact
    },
    recommendations: []
  };
  
  // Generate recommendations
  if (!results.typescript.passed) {
    report.recommendations.push('Fix TypeScript compilation errors before deployment');
  }
  
  if (!results.build.passed) {
    report.recommendations.push('Resolve build issues before proceeding');
  }
  
  if (results.impact.achievementPercent < 80) {
    report.recommendations.push('Consider additional optimization strategies to reach target reduction');
  }
  
  const reportPath = path.join(CONFIG.reportDirectory, `phase2-validation-${timestamp.replace(/[:.]/g, '-')}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  return { report, reportPath };
}

// Main validation function
function runPhase2Validation() {
  logHeader('Phase 2 Comprehensive Validation');
  
  const results = {
    timestamp: new Date().toISOString(),
    typescript: validateTypeScript(),
    build: validateBuild(),
    bundleSize: null,
    optimizations: [],
    impact: null
  };
  
  // Only proceed with bundle analysis if build passed
  if (results.build.passed) {
    results.bundleSize = measureBundleSize();
    results.optimizations = validateOptimizations();
    results.impact = calculatePhase2Impact(results.bundleSize);
  }
  
  // Generate report
  const { report, reportPath } = generateValidationReport(results);
  
  // Final summary
  logHeader('Phase 2 Validation Summary');
  
  if (report.summary.overallStatus === 'PASSED') {
    logSuccess('🎉 Phase 2 validation completed successfully!');
    
    if (results.bundleSize) {
      logSuccess(`📦 Final bundle size: ${results.bundleSize.total}KB`);
      logSuccess(`📉 Total reduction: ${results.impact.reduction}KB (${results.impact.reductionPercent.toFixed(1)}%)`);
      logSuccess(`🎯 Target achievement: ${results.impact.achievementPercent.toFixed(1)}%`);
    }
  } else {
    logError('❌ Phase 2 validation failed');
    
    if (report.recommendations.length > 0) {
      log('\n💡 Recommendations:', 'yellow');
      report.recommendations.forEach(rec => {
        log(`   • ${rec}`, 'yellow');
      });
    }
  }
  
  log(`\n📄 Detailed report saved to: ${reportPath}`, 'blue');
  
  return report.summary.overallStatus === 'PASSED' ? 0 : 1;
}

// CLI execution
if (require.main === module) {
  try {
    const exitCode = runPhase2Validation();
    process.exit(exitCode);
  } catch (error) {
    logError(`Validation failed: ${error.message}`);
    process.exit(1);
  }
}

module.exports = {
  runPhase2Validation,
  CONFIG
};
