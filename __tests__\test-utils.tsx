/**
 * Test Utilities for Solidity Learning Platform
 * 
 * Provides mock data, helper functions, and test setup utilities
 * for comprehensive testing of all platform components.
 */

import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Mock data interfaces
interface MockUserContext {
  id: string;
  name: string;
  email: string;
  currentPage: string;
  progress: {
    currentLesson: string;
    completedLessons: number;
    currentCourse: string;
    totalXP: number;
    level: number;
  };
  preferences: {
    reducedMotion: boolean;
    notifications: boolean;
    sounds: boolean;
  };
}

interface MockAnalyticsConfig {
  googleAnalyticsId: string;
  hotjarId: string;
  sentryDsn: string;
  enableHeatmaps: boolean;
  enableSessionRecordings: boolean;
  enableUserConsent: boolean;
  enableABTesting: boolean;
  enablePerformanceMonitoring: boolean;
  enableFeedbackWidgets: boolean;
  privacyCompliant: boolean;
  trackingLevel: 'minimal' | 'standard' | 'enhanced';
}

interface MockContentItem {
  id: string;
  type: 'article' | 'video' | 'tutorial' | 'documentation' | 'example' | 'quiz';
  title: string;
  description: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    role: string;
  };
  metadata: {
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    duration: number;
    tags: string[];
    category: string;
    prerequisites: string[];
    learningObjectives: string[];
    lastUpdated: Date;
    version: string;
  };
  engagement: {
    views: number;
    likes: number;
    bookmarks: number;
    comments: number;
    rating: number;
    completions: number;
  };
  accessibility: {
    hasTranscript: boolean;
    hasClosedCaptions: boolean;
    hasAudioDescription: boolean;
    readingLevel: number;
  };
  seo: {
    slug: string;
    metaTitle: string;
    metaDescription: string;
    keywords: string[];
    canonicalUrl?: string;
  };
}

// Mock user context factory
export function createMockUserContext(overrides: Partial<MockUserContext> = {}): MockUserContext {
  return {
    id: 'test-user-123',
    name: 'Test User',
    email: '<EMAIL>',
    currentPage: '/',
    progress: {
      currentLesson: 'solidity-basics-1',
      completedLessons: 5,
      currentCourse: 'solidity-fundamentals',
      totalXP: 250,
      level: 3
    },
    preferences: {
      reducedMotion: false,
      notifications: true,
      sounds: true
    },
    ...overrides
  };
}

// Mock analytics config factory
export function createMockAnalyticsConfig(overrides: Partial<MockAnalyticsConfig> = {}): MockAnalyticsConfig {
  return {
    googleAnalyticsId: 'GA-TEST-123456',
    hotjarId: 'HJ-TEST-789',
    sentryDsn: 'https://<EMAIL>/123456',
    enableHeatmaps: true,
    enableSessionRecordings: true,
    enableUserConsent: true,
    enableABTesting: true,
    enablePerformanceMonitoring: true,
    enableFeedbackWidgets: true,
    privacyCompliant: true,
    trackingLevel: 'standard',
    ...overrides
  };
}

// Mock content items factory
export function createMockContentItems(count: number = 3): MockContentItem[] {
  return Array.from({ length: count }, (_, index) => ({
    id: `content-${index + 1}`,
    type: ['article', 'video', 'tutorial'][index % 3] as MockContentItem['type'],
    title: `Test Content Item ${index + 1}`,
    description: `Description for test content item ${index + 1}`,
    content: `# Test Content ${index + 1}\n\nThis is test content for item ${index + 1}.`,
    author: {
      id: `author-${index + 1}`,
      name: `Test Author ${index + 1}`,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=author${index + 1}`,
      role: 'Instructor'
    },
    metadata: {
      difficulty: ['beginner', 'intermediate', 'advanced'][index % 3] as MockContentItem['metadata']['difficulty'],
      duration: 15 + (index * 5),
      tags: [`tag${index + 1}`, 'solidity', 'blockchain'],
      category: `Category ${index + 1}`,
      prerequisites: index > 0 ? [`content-${index}`] : [],
      learningObjectives: [
        `Learn objective ${index + 1}A`,
        `Learn objective ${index + 1}B`
      ],
      lastUpdated: new Date(2024, 0, index + 1),
      version: `1.${index}.0`
    },
    engagement: {
      views: 1000 + (index * 500),
      likes: 100 + (index * 50),
      bookmarks: 50 + (index * 25),
      comments: 20 + (index * 10),
      rating: 4.5 + (index * 0.1),
      completions: 800 + (index * 200)
    },
    accessibility: {
      hasTranscript: true,
      hasClosedCaptions: index % 2 === 0,
      hasAudioDescription: index % 3 === 0,
      readingLevel: 60 + (index * 5)
    },
    seo: {
      slug: `test-content-item-${index + 1}`,
      metaTitle: `Test Content Item ${index + 1} | Solidity Learning`,
      metaDescription: `Learn about test content item ${index + 1} in our comprehensive guide.`,
      keywords: [`content${index + 1}`, 'solidity', 'tutorial'],
      canonicalUrl: `https://example.com/content/test-content-item-${index + 1}`
    }
  }));
}

// Mock API responses
export const mockApiResponses = {
  analytics: {
    metrics: {
      activeUsers: 247,
      conversionRate: 3.2,
      avgSessionDuration: 8.5,
      bounceRate: 32.1,
      pageLoadTime: 1.2,
      errorRate: 0.3
    },
    conversionFunnel: [
      {
        step: 'awareness',
        name: 'Landing Page Visit',
        users: 10000,
        conversions: 3000,
        conversionRate: 30,
        dropOffRate: 70,
        averageTime: 45
      },
      {
        step: 'interest',
        name: 'Course Preview',
        users: 3000,
        conversions: 1200,
        conversionRate: 40,
        dropOffRate: 60,
        averageTime: 120
      }
    ]
  },
  content: {
    search: {
      results: createMockContentItems(5),
      totalCount: 25,
      facets: {
        types: ['article', 'video', 'tutorial'],
        difficulties: ['beginner', 'intermediate', 'advanced'],
        categories: ['Programming', 'Security', 'DeFi']
      }
    }
  },
  user: {
    profile: {
      id: 'test-user-123',
      name: 'Test User',
      email: '<EMAIL>',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=testuser',
      role: 'student',
      createdAt: '2024-01-01T00:00:00.000Z',
      lastLoginAt: '2024-01-15T10:30:00.000Z'
    },
    progress: {
      coursesEnrolled: 3,
      coursesCompleted: 1,
      lessonsCompleted: 15,
      totalXP: 750,
      currentLevel: 5,
      streak: 7,
      achievements: [
        {
          id: 'first-lesson',
          name: 'First Steps',
          description: 'Completed your first lesson',
          earnedAt: '2024-01-02T00:00:00.000Z'
        }
      ]
    }
  }
};

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  queryClient?: QueryClient;
  initialEntries?: string[];
}

export function renderWithProviders(
  ui: React.ReactElement,
  options: CustomRenderOptions = {}
) {
  const { queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  }), ...renderOptions } = options;

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <QueryClientProvider client={queryClient}>
        {children}
      </QueryClientProvider>
    );
  }

  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Mock localStorage
export const mockLocalStorage = {
  store: {} as Record<string, string>,
  getItem: jest.fn((key: string) => mockLocalStorage.store[key] || null),
  setItem: jest.fn((key: string, value: string) => {
    mockLocalStorage.store[key] = value;
  }),
  removeItem: jest.fn((key: string) => {
    delete mockLocalStorage.store[key];
  }),
  clear: jest.fn(() => {
    mockLocalStorage.store = {};
  })
};

// Mock sessionStorage
export const mockSessionStorage = {
  store: {} as Record<string, string>,
  getItem: jest.fn((key: string) => mockSessionStorage.store[key] || null),
  setItem: jest.fn((key: string, value: string) => {
    mockSessionStorage.store[key] = value;
  }),
  removeItem: jest.fn((key: string) => {
    delete mockSessionStorage.store[key];
  }),
  clear: jest.fn(() => {
    mockSessionStorage.store = {};
  })
};

// Mock fetch
export function createMockFetch(responses: Record<string, any> = {}) {
  return jest.fn((url: string, options?: RequestInit) => {
    const method = options?.method || 'GET';
    const key = `${method} ${url}`;
    
    if (responses[key]) {
      return Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve(responses[key]),
        text: () => Promise.resolve(JSON.stringify(responses[key]))
      });
    }
    
    return Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({}),
      text: () => Promise.resolve('{}')
    });
  });
}

// Performance testing utilities
export function measureRenderTime(renderFn: () => void): number {
  const start = performance.now();
  renderFn();
  const end = performance.now();
  return end - start;
}

export function createPerformanceObserver(): PerformanceObserver {
  const entries: PerformanceEntry[] = [];
  
  return {
    observe: jest.fn(),
    disconnect: jest.fn(),
    takeRecords: jest.fn(() => entries)
  } as any;
}

// Accessibility testing utilities
export function createAccessibilityTestConfig() {
  return {
    rules: {
      // Disable color-contrast rule for skeleton loading states
      'color-contrast': { enabled: false },
      // Custom rules for our specific use cases
      'aria-required-attr': { enabled: true },
      'aria-valid-attr': { enabled: true },
      'button-name': { enabled: true },
      'bypass': { enabled: true },
      'color-contrast': { enabled: true },
      'focus-order-semantics': { enabled: true },
      'form-field-multiple-labels': { enabled: true },
      'frame-title': { enabled: true },
      'html-has-lang': { enabled: true },
      'html-lang-valid': { enabled: true },
      'image-alt': { enabled: true },
      'input-image-alt': { enabled: true },
      'keyboard': { enabled: true },
      'label': { enabled: true },
      'link-name': { enabled: true },
      'list': { enabled: true },
      'listitem': { enabled: true },
      'meta-refresh': { enabled: true },
      'meta-viewport': { enabled: true },
      'object-alt': { enabled: true },
      'role-img-alt': { enabled: true },
      'scrollable-region-focusable': { enabled: true },
      'select-name': { enabled: true },
      'server-side-image-map': { enabled: true },
      'svg-img-alt': { enabled: true },
      'td-headers-attr': { enabled: true },
      'th-has-data-cells': { enabled: true },
      'valid-lang': { enabled: true },
      'video-caption': { enabled: true }
    }
  };
}

// Animation testing utilities
export function mockAnimationFrame() {
  let callbacks: FrameRequestCallback[] = [];
  let id = 0;

  const requestAnimationFrame = jest.fn((callback: FrameRequestCallback) => {
    callbacks.push(callback);
    return ++id;
  });

  const cancelAnimationFrame = jest.fn((id: number) => {
    // Implementation not needed for basic tests
  });

  const flush = () => {
    const currentCallbacks = callbacks;
    callbacks = [];
    currentCallbacks.forEach(callback => callback(performance.now()));
  };

  return {
    requestAnimationFrame,
    cancelAnimationFrame,
    flush
  };
}

// Error boundary for testing
export class TestErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Test Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div data-testid="error-boundary">Something went wrong.</div>;
    }

    return this.props.children;
  }
}

// Export all utilities
export * from '@testing-library/react';
export { default as userEvent } from '@testing-library/user-event';
