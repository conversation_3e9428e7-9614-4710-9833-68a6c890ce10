{"version": 2, "name": "solidity-learning-platform", "framework": "nextjs", "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm ci", "outputDirectory": ".next", "regions": ["iad1", "sfo1"], "build": {"env": {"NODE_VERSION": "20.x", "TURBOPACK": "1"}}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "env": {"NEXTAUTH_URL": "@nextauth_url", "NEXTAUTH_SECRET": "@nextauth_secret", "DATABASE_URL": "@database_url", "GITHUB_CLIENT_ID": "@github_client_id", "GITHUB_CLIENT_SECRET": "@github_client_secret", "GOOGLE_CLIENT_ID": "@google_client_id", "GOOGLE_CLIENT_SECRET": "@google_client_secret"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "rewrites": [{"source": "/docs/:path*", "destination": "/docs/:path*"}]}