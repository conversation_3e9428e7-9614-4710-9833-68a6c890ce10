{"name": "solana-learn-platform", "private": true, "version": "2.0.0", "description": "Next-generation Solidity learning platform with AI-powered features", "homepage": "https://github.com/ezekaj/learning_sol", "scripts": {"dev": "next dev --turbopack", "dev:webpack": "next dev", "build": "prisma generate && next build", "build:frontend": "next build", "build:analyze": "ANALYZE=true npm run build:frontend", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rimraf .next dist node_modules/.cache", "clean:all": "rimraf .next dist node_modules", "clean:build": "rimraf .next dist", "prebuild": "npm run clean:build && npx prisma generate", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset --force", "db:studio": "prisma studio", "test": "npm run test:unit && npm run test:e2e", "test:unit": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:performance": "playwright test --grep=\"Performance Testing\"", "lighthouse": "lhci autorun", "lighthouse:mobile": "lhci autorun --config=lighthouserc.mobile.js", "performance:analyze": "npm run build:analyze && npm run lighthouse", "test:e2e:debug": "playwright test --debug", "test:security": "playwright test --grep=\"Security Testing\"", "test:collaboration": "playwright test --grep=\"Real-time Collaboration\"", "test:ai": "playwright test --grep=\"AI Tutoring\"", "test:auth": "echo 'Visit http://localhost:3000/auth/local-test to test authentication'", "test:mobile": "playwright test --project=\"Mobile Chrome\" --project=\"Mobile Safari\"", "test:cross-browser": "playwright test --project=\"chromium\" --project=\"firefox\" --project=\"webkit\"", "test:ci": "playwright test --reporter=github", "test:report": "playwright show-report", "playwright:install": "playwright install", "playwright:install-deps": "playwright install-deps", "setup:env": "node scripts/setup-environment.js", "setup:env:unix": "node scripts/setup-environment.js", "validate:env": "node scripts/validate-environment.js", "setup:db": "node scripts/setup-database.js", "fix:ts": "node scripts/fix-typescript-errors.js", "test:db": "node scripts/test-database.js", "deployment:checklist": "node scripts/deployment-checklist.js", "bundle:monitor": "node scripts/bundle-monitor.js", "bundle:analyze": "npm run build && npm run bundle:monitor", "postinstall": "npm run playwright:install", "docker:build": "docker build -t solana-learn .", "docker:run": "docker run -p 3000:3000 solana-learn", "legacy:dev": "vite", "legacy:build": "vite build", "legacy:preview": "vite preview", "socket:dev": "cd socket-server && npm run dev", "socket:start": "cd socket-server && npm start", "deploy:vercel": "vercel --prod", "deploy:railway": "railway up", "deploy:preview": "vercel", "postbuild": "next-sitemap", "db:deploy": "prisma migrate deploy", "db:seed:prod": "NODE_ENV=production npm run db:seed", "dev:clean": "rm -rf .next && npm run dev", "dev:debug": "NODE_OPTIONS='--inspect' npm run dev", "test:components": "echo 'Visit http://localhost:3000/auth/demo to see component showcase'", "check:all": "npm run type-check && npm run lint", "fix:all": "npm run lint -- --fix && npm run format"}, "dependencies": {"@google/genai": "^1.3.0", "@google/generative-ai": "^0.24.1", "@gsap/react": "^2.1.2", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^3.10.0", "@monaco-editor/react": "^4.7.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.10.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@react-spring/web": "^10.0.1", "@react-three/drei": "^10.3.0", "@react-three/fiber": "^9.1.2", "@sentry/nextjs": "^8.55.0", "@solidity-parser/parser": "^0.20.1", "@tanstack/react-query": "^5.8.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.8.0", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "@types/validator": "^13.15.2", "autoprefixer": "^10.4.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^2.30.0", "ethers": "^6.8.0", "framer-motion": "^12.19.1", "gsap": "^3.13.0", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.25.0", "jsonwebtoken": "^9.0.2", "lottie-react": "^2.4.1", "lucide-react": "^0.518.0", "marked": "^13.0.0", "monaco-editor": "^0.52.2", "next": "^15.3.4", "next-auth": "^4.24.0", "postcss": "^8.4.0", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "react-markdown": "^9.0.0", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.8.0", "redis": "^4.6.0", "remark-gfm": "^4.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "solc": "^0.8.21", "solc-js": "^1.0.1", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "three": "^0.177.0", "typescript": "^5.7.2", "validator": "^13.15.15", "web3": "^4.2.0", "winston": "^3.17.0", "zod": "^3.25.67"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@axe-core/react": "^4.10.2", "@tanstack/react-query-devtools": "^5.8.0", "@jest/globals": "^30.0.3", "@next/bundle-analyzer": "^15.3.4", "@playwright/test": "^1.53.1", "@tailwindcss/postcss": "^4.1.8", "@testing-library/jest-dom": "^6.1.0", "@testing-library/react": "^16.0.0", "@types/react-syntax-highlighter": "^15.5.0", "@types/three": "^0.177.0", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "@vitejs/plugin-react": "^4.5.2", "axe-core": "^4.10.3", "eslint": "^9.17.0", "eslint-config-next": "^15.3.4", "gh-pages": "^6.3.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "playwright": "^1.53.1", "rimraf": "^6.0.1", "tsx": "^4.20.3", "vite": "^6.2.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}