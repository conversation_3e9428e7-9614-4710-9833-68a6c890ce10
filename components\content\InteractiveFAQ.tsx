'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Search, 
  ChevronDown, 
  ChevronUp, 
  HelpCircle, 
  BookOpen, 
  CreditCard, 
  Award, 
  Settings,
  ThumbsUp,
  ThumbsDown,
  MessageCircle,
  ExternalLink,
  Filter,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: 'getting-started' | 'technical' | 'billing' | 'certificates' | 'general';
  tags: string[];
  popularity: number;
  lastUpdated: Date;
  helpful: number;
  notHelpful: number;
  relatedLinks?: Array<{
    title: string;
    url: string;
    type: 'internal' | 'external';
  }>;
}

interface FAQAnalytics {
  questionViews: Record<string, number>;
  searchQueries: Array<{
    query: string;
    timestamp: Date;
    resultsCount: number;
  }>;
  helpfulRatings: Record<string, { helpful: number; notHelpful: number }>;
  categoryViews: Record<string, number>;
}

const faqCategories = {
  'getting-started': {
    label: 'Getting Started',
    icon: BookOpen,
    color: 'text-green-400 bg-green-500/20 border-green-500/30'
  },
  'technical': {
    label: 'Technical',
    icon: Settings,
    color: 'text-blue-400 bg-blue-500/20 border-blue-500/30'
  },
  'billing': {
    label: 'Billing',
    icon: CreditCard,
    color: 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30'
  },
  'certificates': {
    label: 'Certificates',
    icon: Award,
    color: 'text-purple-400 bg-purple-500/20 border-purple-500/30'
  },
  'general': {
    label: 'General',
    icon: HelpCircle,
    color: 'text-gray-400 bg-gray-500/20 border-gray-500/30'
  }
};

// Mock FAQ data
const mockFAQs: FAQItem[] = [
  {
    id: '1',
    question: 'How long does it take to learn Solidity?',
    answer: 'The time to learn Solidity depends on your programming background. Complete beginners typically need 3-6 months to become proficient, while experienced developers can learn the basics in 4-8 weeks. Our structured learning path is designed to get you job-ready in 12 weeks with 2-3 hours of daily practice.',
    category: 'getting-started',
    tags: ['timeline', 'learning', 'beginner'],
    popularity: 95,
    lastUpdated: new Date('2024-01-15'),
    helpful: 234,
    notHelpful: 12,
    relatedLinks: [
      { title: '30-Day Learning Path', url: '/learning-path', type: 'internal' },
      { title: 'Skill Assessment', url: '/assessment', type: 'internal' }
    ]
  },
  {
    id: '2',
    question: 'Do I need prior programming experience?',
    answer: 'While prior programming experience is helpful, it\'s not required. Our platform starts with programming fundamentals and gradually introduces Solidity concepts. We recommend basic familiarity with JavaScript or Python, but our beginner track covers everything you need to know.',
    category: 'getting-started',
    tags: ['prerequisites', 'beginner', 'programming'],
    popularity: 88,
    lastUpdated: new Date('2024-01-10'),
    helpful: 189,
    notHelpful: 8,
    relatedLinks: [
      { title: 'Programming Fundamentals', url: '/fundamentals', type: 'internal' },
      { title: 'JavaScript Basics', url: '/javascript-basics', type: 'internal' }
    ]
  },
  {
    id: '3',
    question: 'What blockchain networks do you support?',
    answer: 'We support all major Ethereum-compatible networks including Ethereum Mainnet, Sepolia Testnet, Polygon, Arbitrum, Optimism, and Binance Smart Chain. Students can deploy and test their smart contracts on these networks as part of the learning experience.',
    category: 'technical',
    tags: ['blockchain', 'networks', 'deployment'],
    popularity: 76,
    lastUpdated: new Date('2024-01-12'),
    helpful: 156,
    notHelpful: 5,
    relatedLinks: [
      { title: 'Supported Networks', url: '/networks', type: 'internal' },
      { title: 'Testnet Guide', url: '/testnet-guide', type: 'internal' }
    ]
  },
  {
    id: '4',
    question: 'Do you offer certificates upon completion?',
    answer: 'Yes! We offer industry-recognized certificates for course completion. Our certificates are blockchain-verified and include detailed skill assessments. Premium users also receive LinkedIn-ready certificates and direct connections to our hiring partners.',
    category: 'certificates',
    tags: ['certification', 'completion', 'verification'],
    popularity: 82,
    lastUpdated: new Date('2024-01-08'),
    helpful: 201,
    notHelpful: 7,
    relatedLinks: [
      { title: 'Certificate Program', url: '/certificates', type: 'internal' },
      { title: 'Hiring Partners', url: '/partners', type: 'internal' }
    ]
  },
  {
    id: '5',
    question: 'What is the cost of the premium plan?',
    answer: 'Our premium plan costs $49/month or $399/year (33% savings). It includes unlimited access to all courses, 1-on-1 mentoring sessions, priority support, job placement assistance, and blockchain-verified certificates. We also offer a 7-day free trial.',
    category: 'billing',
    tags: ['pricing', 'premium', 'subscription'],
    popularity: 91,
    lastUpdated: new Date('2024-01-14'),
    helpful: 167,
    notHelpful: 15,
    relatedLinks: [
      { title: 'Pricing Plans', url: '/pricing', type: 'internal' },
      { title: 'Free Trial', url: '/trial', type: 'internal' }
    ]
  },
  {
    id: '6',
    question: 'Can I get a refund if I\'m not satisfied?',
    answer: 'Absolutely! We offer a 30-day money-back guarantee for all premium subscriptions. If you\'re not completely satisfied with your learning experience, contact our support team for a full refund within 30 days of purchase.',
    category: 'billing',
    tags: ['refund', 'guarantee', 'satisfaction'],
    popularity: 64,
    lastUpdated: new Date('2024-01-09'),
    helpful: 98,
    notHelpful: 3,
    relatedLinks: [
      { title: 'Refund Policy', url: '/refund-policy', type: 'internal' },
      { title: 'Contact Support', url: '/support', type: 'internal' }
    ]
  }
];

export function InteractiveFAQ({ className }: { className?: string }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const [analytics, setAnalytics] = useState<FAQAnalytics>({
    questionViews: {},
    searchQueries: [],
    helpfulRatings: {},
    categoryViews: {}
  });
  const [showFilters, setShowFilters] = useState(false);

  // Filter FAQs based on search and category
  const filteredFAQs = useMemo(() => {
    let filtered = mockFAQs;

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(faq => 
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query) ||
        faq.tags.some(tag => tag.toLowerCase().includes(query))
      );

      // Track search analytics
      setAnalytics(prev => ({
        ...prev,
        searchQueries: [
          ...prev.searchQueries.slice(-99), // Keep last 100 searches
          {
            query: searchQuery,
            timestamp: new Date(),
            resultsCount: filtered.length
          }
        ]
      }));
    }

    // Sort by popularity
    return filtered.sort((a, b) => b.popularity - a.popularity);
  }, [searchQuery, selectedCategory]);

  // Track FAQ item view
  const trackFAQView = (faqId: string) => {
    setAnalytics(prev => ({
      ...prev,
      questionViews: {
        ...prev.questionViews,
        [faqId]: (prev.questionViews[faqId] || 0) + 1
      }
    }));
  };

  // Track category view
  const trackCategoryView = (category: string) => {
    setAnalytics(prev => ({
      ...prev,
      categoryViews: {
        ...prev.categoryViews,
        [category]: (prev.categoryViews[category] || 0) + 1
      }
    }));
  };

  // Handle helpful rating
  const handleHelpfulRating = (faqId: string, isHelpful: boolean) => {
    setAnalytics(prev => ({
      ...prev,
      helpfulRatings: {
        ...prev.helpfulRatings,
        [faqId]: {
          helpful: (prev.helpfulRatings[faqId]?.helpful || 0) + (isHelpful ? 1 : 0),
          notHelpful: (prev.helpfulRatings[faqId]?.notHelpful || 0) + (!isHelpful ? 1 : 0)
        }
      }
    }));
  };

  // Toggle FAQ expansion
  const toggleFAQ = (faqId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(faqId)) {
      newExpanded.delete(faqId);
    } else {
      newExpanded.add(faqId);
      trackFAQView(faqId);
    }
    setExpandedItems(newExpanded);
  };

  // Handle category selection
  const handleCategorySelect = (category: string | null) => {
    setSelectedCategory(category);
    if (category) {
      trackCategoryView(category);
    }
  };

  return (
    <div className={cn('max-w-4xl mx-auto', className)}>
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          className="text-3xl md:text-4xl font-bold text-white mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Frequently Asked Questions
        </motion.h2>
        <motion.p
          className="text-gray-300 text-lg max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          Find answers to common questions about our Solidity learning platform
        </motion.p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8">
        {/* Search Input */}
        <div className="relative mb-6">
          <input
            type="text"
            placeholder="Search questions..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full px-4 py-3 bg-gray-800/50 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Category Filters */}
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => handleCategorySelect(null)}
            className={cn(
              'px-4 py-2 rounded-full text-sm font-medium transition-colors',
              selectedCategory === null
                ? 'bg-blue-500 text-white'
                : 'bg-gray-800/50 text-gray-300 hover:bg-gray-700'
            )}
          >
            All
          </button>
          {FAQ_CATEGORIES.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategorySelect(category.id)}
              className={cn(
                'px-4 py-2 rounded-full text-sm font-medium transition-colors',
                selectedCategory === category.id
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-800/50 text-gray-300 hover:bg-gray-700'
              )}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* FAQ Items */}
      <div className="space-y-4">
        {filteredFAQs.map((faq) => (
          <motion.div
            key={faq.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-gray-800/30 border border-gray-700 rounded-lg overflow-hidden"
          >
            <button
              onClick={() => handleToggleExpand(faq.id)}
              className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-700/30 transition-colors"
              aria-expanded={expandedItems.has(faq.id)}
            >
              <span className="font-medium text-white">{faq.question}</span>
              <motion.div
                animate={{ rotate: expandedItems.has(faq.id) ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                ▼
              </motion.div>
            </button>

            <AnimatePresence>
              {expandedItems.has(faq.id) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="overflow-hidden"
                >
                  <div className="px-6 pb-4">
                    <div className="text-gray-300 mb-4">{faq.answer}</div>

                    {/* Helpful Voting */}
                    <div className="flex items-center gap-4 text-sm">
                      <span className="text-gray-400">Was this helpful?</span>
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleHelpfulVote(faq.id, true)}
                          className="flex items-center gap-1 px-3 py-1 rounded-full bg-green-500/20 text-green-400 hover:bg-green-500/30 transition-colors"
                        >
                          👍 Yes
                        </button>
                        <button
                          onClick={() => handleHelpfulVote(faq.id, false)}
                          className="flex items-center gap-1 px-3 py-1 rounded-full bg-red-500/20 text-red-400 hover:bg-red-500/30 transition-colors"
                        >
                          👎 No
                        </button>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </div>
    </div>
  );
}

// FAQ Analytics Dashboard Component
export function FAQAnalyticsDashboard({
  analytics,
  className
}: {
  analytics: FAQAnalytics;
  className?: string;
}) {
  const topQuestions = Object.entries(analytics.questionViews)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  const topSearches = analytics.searchQueries
    .slice(-10)
    .reduce((acc, search) => {
      acc[search.query] = (acc[search.query] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

  return (
    <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10', className)}>
      <h3 className="text-lg font-semibold text-white mb-6">FAQ Analytics</h3>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Top Questions */}
        <div>
          <h4 className="font-medium text-white mb-3">Most Viewed Questions</h4>
          <div className="space-y-2">
            {topQuestions.map(([questionId, views]) => {
              const question = mockFAQs.find(faq => faq.id === questionId);
              return (
                <div key={questionId} className="flex items-center justify-between p-2 bg-white/5 rounded">
                  <span className="text-gray-300 text-sm truncate">
                    {question?.question || 'Unknown Question'}
                  </span>
                  <span className="text-blue-400 text-sm font-medium">{views} views</span>
                </div>
              );
            })}
          </div>
        </div>

        {/* Top Searches */}
        <div>
          <h4 className="font-medium text-white mb-3">Popular Searches</h4>
          <div className="space-y-2">
            {Object.entries(topSearches)
              .sort(([,a], [,b]) => b - a)
              .slice(0, 5)
              .map(([query, count]) => (
                <div key={query} className="flex items-center justify-between p-2 bg-white/5 rounded">
                  <span className="text-gray-300 text-sm truncate">"{query}"</span>
                  <span className="text-green-400 text-sm font-medium">{count}x</span>
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* Category Performance */}
      <div className="mt-6">
        <h4 className="font-medium text-white mb-3">Category Performance</h4>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
          {Object.entries(analytics.categoryViews).map(([category, views]) => {
            const categoryInfo = faqCategories[category as keyof typeof faqCategories];
            if (!categoryInfo) return null;

            const Icon = categoryInfo.icon;
            return (
              <div key={category} className="text-center p-3 bg-white/5 rounded-lg">
                <Icon className="w-6 h-6 mx-auto mb-2 text-gray-400" />
                <div className="text-lg font-bold text-white">{views}</div>
                <div className="text-xs text-gray-400">{categoryInfo.label}</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}

// Hook for FAQ analytics
export function useFAQAnalytics() {
  const [analytics, setAnalytics] = React.useState<FAQAnalytics>({
    questionViews: {},
    searchQueries: [],
    helpfulRatings: {},
    categoryViews: {}
  });

  React.useEffect(() => {
    // Load analytics from localStorage
    const saved = localStorage.getItem('faq_analytics');
    if (saved) {
      try {
        const parsed = JSON.parse(saved);
        setAnalytics({
          ...parsed,
          searchQueries: parsed.searchQueries.map((q: any) => ({
            ...q,
            timestamp: new Date(q.timestamp)
          }))
        });
      } catch (error) {
        console.error('Failed to load FAQ analytics:', error);
      }
    }
  }, []);

  React.useEffect(() => {
    // Save analytics to localStorage
    localStorage.setItem('faq_analytics', JSON.stringify(analytics));
  }, [analytics]);

  const trackQuestionView = (questionId: string) => {
    setAnalytics(prev => ({
      ...prev,
      questionViews: {
        ...prev.questionViews,
        [questionId]: (prev.questionViews[questionId] || 0) + 1
      }
    }));
  };

  const trackSearch = (query: string, resultsCount: number) => {
    setAnalytics(prev => ({
      ...prev,
      searchQueries: [
        ...prev.searchQueries.slice(-99), // Keep last 100 searches
        {
          query,
          timestamp: new Date(),
          resultsCount
        }
      ]
    }));
  };

  const trackHelpfulRating = (questionId: string, isHelpful: boolean) => {
    setAnalytics(prev => ({
      ...prev,
      helpfulRatings: {
        ...prev.helpfulRatings,
        [questionId]: {
          helpful: (prev.helpfulRatings[questionId]?.helpful || 0) + (isHelpful ? 1 : 0),
          notHelpful: (prev.helpfulRatings[questionId]?.notHelpful || 0) + (!isHelpful ? 1 : 0)
        }
      }
    }));
  };

  const trackCategoryView = (category: string) => {
    setAnalytics(prev => ({
      ...prev,
      categoryViews: {
        ...prev.categoryViews,
        [category]: (prev.categoryViews[category] || 0) + 1
      }
    }));
  };

  return {
    analytics,
    trackQuestionView,
    trackSearch,
    trackHelpfulRating,
    trackCategoryView
  };
}
      <motion.div
        className="mb-8 space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        {/* Search Bar */}
        <div className="relative">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <input
            type="text"
            placeholder="Search FAQs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-12 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          )}
        </div>

        {/* Category Filters */}
        <div className="flex flex-wrap gap-3">
          <button
            onClick={() => handleCategorySelect(null)}
            className={cn(
              'px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200',
              !selectedCategory
                ? 'bg-blue-600 text-white'
                : 'bg-white/10 text-gray-300 hover:bg-white/20'
            )}
          >
            All Categories
          </button>
          {Object.entries(faqCategories).map(([key, category]) => {
            const Icon = category.icon;
            return (
              <button
                key={key}
                onClick={() => handleCategorySelect(key)}
                className={cn(
                  'flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border',
                  selectedCategory === key
                    ? category.color
                    : 'bg-white/10 text-gray-300 hover:bg-white/20 border-white/20'
                )}
              >
                <Icon className="w-4 h-4" />
                <span>{category.label}</span>
              </button>
            );
          })}
        </div>

        {/* Results Count */}
        {(searchQuery || selectedCategory) && (
          <div className="text-gray-400 text-sm">
            {filteredFAQs.length} result{filteredFAQs.length !== 1 ? 's' : ''} found
            {selectedCategory && ` in ${faqCategories[selectedCategory as keyof typeof faqCategories].label}`}
            {searchQuery && ` for "${searchQuery}"`}
          </div>
        )}
      </motion.div>

      {/* FAQ Items */}
      <div className="space-y-4">
        <AnimatePresence>
          {filteredFAQs.map((faq, index) => {
            const isExpanded = expandedItems.has(faq.id);
            const categoryInfo = faqCategories[faq.category];
            const Icon = categoryInfo.icon;

            return (
              <motion.div
                key={faq.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden"
              >
                {/* Question Header */}
                <button
                  onClick={() => toggleFAQ(faq.id)}
                  className="w-full p-6 text-left hover:bg-white/5 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset"
                  aria-expanded={isExpanded}
                  aria-controls={`faq-answer-${faq.id}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className={cn('p-1 rounded border', categoryInfo.color)}>
                          <Icon className="w-3 h-3" />
                        </div>
                        <span className="text-xs text-gray-400 uppercase tracking-wide">
                          {categoryInfo.label}
                        </span>
                      </div>
                      <h3 className="text-lg font-semibold text-white leading-relaxed">
                        {faq.question}
                      </h3>
                    </div>
                    <motion.div
                      animate={{ rotate: isExpanded ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                      className="ml-4 flex-shrink-0"
                    >
                      <ChevronDown className="w-5 h-5 text-gray-400" />
                    </motion.div>
                  </div>
                </button>

                {/* Answer Content */}
                <AnimatePresence>
                  {isExpanded && (
                    <motion.div
                      id={`faq-answer-${faq.id}`}
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="border-t border-white/10"
                    >
                      <div className="p-6 pt-4">
                        {/* Answer Text */}
                        <p className="text-gray-300 leading-relaxed mb-4">
                          {faq.answer}
                        </p>

                        {/* Related Links */}
                        {faq.relatedLinks && faq.relatedLinks.length > 0 && (
                          <div className="mb-4">
                            <h4 className="text-sm font-medium text-white mb-2">Related Resources:</h4>
                            <div className="flex flex-wrap gap-2">
                              {faq.relatedLinks.map((link, linkIndex) => (
                                <a
                                  key={linkIndex}
                                  href={link.url}
                                  className="inline-flex items-center space-x-1 text-blue-400 hover:text-blue-300 text-sm transition-colors"
                                  target={link.type === 'external' ? '_blank' : '_self'}
                                  rel={link.type === 'external' ? 'noopener noreferrer' : undefined}
                                >
                                  <span>{link.title}</span>
                                  {link.type === 'external' && <ExternalLink className="w-3 h-3" />}
                                </a>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Helpful Rating */}
                        <div className="flex items-center justify-between pt-4 border-t border-white/10">
                          <div className="flex items-center space-x-4">
                            <span className="text-sm text-gray-400">Was this helpful?</span>
                            <div className="flex items-center space-x-2">
                              <button
                                onClick={() => handleHelpfulRating(faq.id, true)}
                                className="flex items-center space-x-1 text-green-400 hover:text-green-300 transition-colors"
                                aria-label="Mark as helpful"
                              >
                                <ThumbsUp className="w-4 h-4" />
                                <span className="text-sm">{faq.helpful + (analytics.helpfulRatings[faq.id]?.helpful || 0)}</span>
                              </button>
                              <button
                                onClick={() => handleHelpfulRating(faq.id, false)}
                                className="flex items-center space-x-1 text-red-400 hover:text-red-300 transition-colors"
                                aria-label="Mark as not helpful"
                              >
                                <ThumbsDown className="w-4 h-4" />
                                <span className="text-sm">{faq.notHelpful + (analytics.helpfulRatings[faq.id]?.notHelpful || 0)}</span>
                              </button>
                            </div>
                          </div>
                          <div className="text-xs text-gray-500">
                            Updated {faq.lastUpdated.toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* No Results */}
      {filteredFAQs.length === 0 && (
        <motion.div
          className="text-center py-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <HelpCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">No FAQs Found</h3>
          <p className="text-gray-400 mb-6">
            We couldn't find any FAQs matching your search criteria.
          </p>
          <div className="space-y-3">
            <EnhancedButton
              onClick={() => {
                setSearchQuery('');
                setSelectedCategory(null);
              }}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Clear Filters
            </EnhancedButton>
            <div className="text-sm text-gray-400">
              Still need help?{' '}
              <a href="/support" className="text-blue-400 hover:text-blue-300 transition-colors">
                Contact Support
              </a>
            </div>
          </div>
        </motion.div>
      )}

      {/* Contact Support CTA */}
      <motion.div
        className="mt-12 text-center p-6 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg border border-blue-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.8 }}
      >
        <MessageCircle className="w-8 h-8 text-blue-400 mx-auto mb-3" />
        <h3 className="text-lg font-semibold text-white mb-2">Still have questions?</h3>
        <p className="text-gray-300 mb-4">
          Our support team is here to help you succeed in your Solidity learning journey.
        </p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <EnhancedButton
            className="bg-blue-600 hover:bg-blue-700 text-white"
            touchTarget
          >
            <MessageCircle className="w-4 h-4 mr-2" />
            Contact Support
          </EnhancedButton>
          <EnhancedButton
            variant="ghost"
            className="text-blue-400 hover:text-blue-300 border-blue-400/30"
            touchTarget
          >
            Join Community Discord
          </EnhancedButton>
        </div>
      </motion.div>
    </div>
  );
}
