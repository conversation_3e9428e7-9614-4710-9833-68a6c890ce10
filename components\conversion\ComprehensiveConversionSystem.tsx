'use client';

import React, { useState, useEffect, useCallback, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

// Import conversion components
import { ExitIntentSystem } from './ExitIntentSystem';
import { ScrollTriggeredCTAs } from './ScrollTriggeredCTAs';
import { UrgencyScarcitySystem } from './UrgencyScarcitySystem';
import { SocialProofDisplay } from './SocialProofDisplay';

// Import analytics
import { useComprehensiveAnalytics } from '@/components/analytics/ComprehensiveAnalyticsSystem';

interface ConversionConfig {
  // Exit intent
  enableExitIntent: boolean;
  exitIntentSensitivity: number;
  exitIntentDelay: number;
  maxExitIntentTriggers: number;
  
  // Scroll triggers
  enableScrollTriggers: boolean;
  scrollMilestones: number[];
  ctaVariations: string[];
  
  // Urgency & scarcity
  enableUrgencyTimers: boolean;
  enableScarcityIndicators: boolean;
  urgencyDuration: number;
  scarcityThreshold: number;
  
  // Social proof
  enableSocialProof: boolean;
  socialProofTypes: ('numbers' | 'testimonials' | 'activity' | 'achievements')[];
  socialProofFrequency: number;
  
  // Personalization
  enablePersonalization: boolean;
  segmentationRules: SegmentationRule[];
  
  // A/B testing
  enableABTesting: boolean;
  activeTests: string[];
}

interface SegmentationRule {
  id: string;
  name: string;
  conditions: {
    pageViews?: number;
    timeOnSite?: number;
    referrer?: string;
    device?: 'mobile' | 'tablet' | 'desktop';
    location?: string;
    previousVisitor?: boolean;
  };
  personalizations: {
    heroMessage?: string;
    ctaText?: string;
    urgencyLevel?: 'low' | 'medium' | 'high';
    socialProofType?: 'numbers' | 'testimonials' | 'activity';
    offerType?: 'discount' | 'trial' | 'bonus';
  };
}

interface ConversionEvent {
  id: string;
  type: 'view' | 'click' | 'conversion' | 'exit_intent' | 'scroll_milestone';
  element?: string;
  value?: number;
  timestamp: Date;
  userSegment?: string;
  testVariant?: string;
  metadata?: any;
}

interface ConversionContextType {
  config: ConversionConfig;
  events: ConversionEvent[];
  currentSegment: string | null;
  conversionProbability: number;
  
  // Methods
  trackConversionEvent: (event: Omit<ConversionEvent, 'id' | 'timestamp'>) => void;
  updateConfig: (updates: Partial<ConversionConfig>) => void;
  triggerConversionAction: (action: string, context?: any) => void;
  getPersonalizedContent: () => any;
}

const ConversionContext = createContext<ConversionContextType | null>(null);

const defaultConfig: ConversionConfig = {
  enableExitIntent: true,
  exitIntentSensitivity: 20,
  exitIntentDelay: 1000,
  maxExitIntentTriggers: 1,
  
  enableScrollTriggers: true,
  scrollMilestones: [25, 50, 75, 90],
  ctaVariations: ['Start Free Trial', 'Begin Learning', 'Get Started Now', 'Try It Free'],
  
  enableUrgencyTimers: true,
  enableScarcityIndicators: true,
  urgencyDuration: 24 * 60 * 60 * 1000, // 24 hours
  scarcityThreshold: 50,
  
  enableSocialProof: true,
  socialProofTypes: ['numbers', 'testimonials', 'activity'],
  socialProofFrequency: 8000,
  
  enablePersonalization: true,
  segmentationRules: [
    {
      id: 'new_visitor',
      name: 'New Visitor',
      conditions: { previousVisitor: false },
      personalizations: {
        heroMessage: 'Start Your Blockchain Journey Today',
        ctaText: 'Begin Free Trial',
        urgencyLevel: 'medium',
        socialProofType: 'numbers',
        offerType: 'trial'
      }
    },
    {
      id: 'returning_visitor',
      name: 'Returning Visitor',
      conditions: { previousVisitor: true, pageViews: 3 },
      personalizations: {
        heroMessage: 'Welcome Back! Continue Your Learning',
        ctaText: 'Continue Learning',
        urgencyLevel: 'high',
        socialProofType: 'activity',
        offerType: 'discount'
      }
    },
    {
      id: 'mobile_user',
      name: 'Mobile User',
      conditions: { device: 'mobile' },
      personalizations: {
        heroMessage: 'Learn Solidity On-the-Go',
        ctaText: 'Start Mobile Learning',
        urgencyLevel: 'low',
        socialProofType: 'testimonials',
        offerType: 'trial'
      }
    }
  ],
  
  enableABTesting: true,
  activeTests: ['hero_cta_test', 'urgency_timer_test', 'social_proof_test']
};

interface ComprehensiveConversionSystemProps {
  children: React.ReactNode;
  config?: Partial<ConversionConfig>;
  userId?: string;
  userTraits?: any;
}

export function ComprehensiveConversionSystem({
  children,
  config: configOverrides = {},
  userId,
  userTraits
}: ComprehensiveConversionSystemProps) {
  const router = useRouter();
  const [config, setConfig] = useState<ConversionConfig>({ ...defaultConfig, ...configOverrides });
  const [events, setEvents] = useState<ConversionEvent[]>([]);
  const [currentSegment, setCurrentSegment] = useState<string | null>(null);
  const [conversionProbability, setConversionProbability] = useState(0.3);
  const [sessionData, setSessionData] = useState({
    startTime: Date.now(),
    pageViews: 1,
    timeOnSite: 0,
    scrollDepth: 0,
    interactions: 0
  });

  const { trackEvent, trackConversion } = useComprehensiveAnalytics();

  // Initialize user segmentation
  useEffect(() => {
    const determineSegment = () => {
      const userAgent = navigator.userAgent;
      const isReturning = localStorage.getItem('previous_visit') !== null;
      const device = getDeviceType(userAgent);
      
      const context = {
        pageViews: sessionData.pageViews,
        timeOnSite: sessionData.timeOnSite,
        device,
        previousVisitor: isReturning,
        referrer: document.referrer
      };

      // Find matching segment
      const matchingSegment = config.segmentationRules.find(rule => {
        return Object.entries(rule.conditions).every(([key, value]) => {
          const contextValue = context[key as keyof typeof context];
          return contextValue === value || (typeof value === 'number' && contextValue >= value);
        });
      });

      if (matchingSegment) {
        setCurrentSegment(matchingSegment.id);
        
        // Track segmentation
        trackEvent('user_segmented', {
          category: 'conversion',
          action: 'segment_assigned',
          segment: matchingSegment.id,
          conditions: matchingSegment.conditions
        });
      }

      // Mark as returning visitor
      localStorage.setItem('previous_visit', Date.now().toString());
    };

    determineSegment();
  }, [config.segmentationRules, sessionData, trackEvent]);

  // Update session data
  useEffect(() => {
    const interval = setInterval(() => {
      setSessionData(prev => ({
        ...prev,
        timeOnSite: Date.now() - prev.startTime
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Calculate conversion probability
  useEffect(() => {
    const calculateProbability = () => {
      let probability = 0.3; // Base probability

      // Time on site factor
      const timeMinutes = sessionData.timeOnSite / (1000 * 60);
      if (timeMinutes > 5) probability += 0.2;
      if (timeMinutes > 10) probability += 0.1;

      // Interaction factor
      if (sessionData.interactions > 3) probability += 0.15;
      if (sessionData.interactions > 6) probability += 0.1;

      // Scroll depth factor
      if (sessionData.scrollDepth > 50) probability += 0.1;
      if (sessionData.scrollDepth > 80) probability += 0.1;

      // Segment factor
      if (currentSegment === 'returning_visitor') probability += 0.2;
      if (currentSegment === 'mobile_user') probability -= 0.1;

      setConversionProbability(Math.min(0.95, probability));
    };

    calculateProbability();
  }, [sessionData, currentSegment]);

  const trackConversionEvent = useCallback((eventData: Omit<ConversionEvent, 'id' | 'timestamp'>) => {
    const event: ConversionEvent = {
      ...eventData,
      id: `event-${Date.now()}-${Math.random()}`,
      timestamp: new Date(),
      userSegment: currentSegment || undefined
    };

    setEvents(prev => [event, ...prev.slice(0, 99)]); // Keep last 100 events

    // Update session data
    if (event.type === 'click') {
      setSessionData(prev => ({ ...prev, interactions: prev.interactions + 1 }));
    }

    if (event.type === 'scroll_milestone' && event.value) {
      setSessionData(prev => ({ 
        ...prev, 
        scrollDepth: Math.max(prev.scrollDepth, event.value || 0) 
      }));
    }

    // Track with analytics
    trackEvent('conversion_event', {
      category: 'conversion',
      action: event.type,
      element: event.element,
      value: event.value,
      userSegment: currentSegment,
      conversionProbability
    });

    // Track actual conversions
    if (event.type === 'conversion') {
      trackConversion('conversion_system', event.value || 0);
    }
  }, [currentSegment, trackEvent, trackConversion, conversionProbability]);

  const updateConfig = useCallback((updates: Partial<ConversionConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  }, []);

  const triggerConversionAction = useCallback((action: string, context: any = {}) => {
    trackConversionEvent({
      type: 'click',
      element: action,
      metadata: context
    });

    // Handle specific conversion actions
    switch (action) {
      case 'start_trial':
        router.push('/signup?source=conversion_system');
        break;
      case 'view_pricing':
        router.push('/pricing?source=conversion_system');
        break;
      case 'contact_sales':
        router.push('/contact?source=conversion_system');
        break;
      case 'download_guide':
        // Trigger download
        break;
      default:
        console.log('Unknown conversion action:', action);
    }
  }, [trackConversionEvent, router]);

  const getPersonalizedContent = useCallback(() => {
    if (!currentSegment || !config.enablePersonalization) {
      return {
        heroMessage: 'Learn Solidity Development',
        ctaText: 'Start Free Trial',
        urgencyLevel: 'medium',
        socialProofType: 'numbers',
        offerType: 'trial'
      };
    }

    const segment = config.segmentationRules.find(rule => rule.id === currentSegment);
    return segment?.personalizations || {};
  }, [currentSegment, config.enablePersonalization, config.segmentationRules]);

  const contextValue: ConversionContextType = {
    config,
    events,
    currentSegment,
    conversionProbability,
    trackConversionEvent,
    updateConfig,
    triggerConversionAction,
    getPersonalizedContent
  };

  return (
    <ConversionContext.Provider value={contextValue}>
      <div className="relative">
        {/* Exit Intent System */}
        {config.enableExitIntent && (
          <ExitIntentSystem
            config={{
              enabled: true,
              sensitivity: config.exitIntentSensitivity,
              delay: config.exitIntentDelay,
              maxTriggers: config.maxExitIntentTriggers,
              onExitIntent: () => {
                trackConversionEvent({
                  type: 'exit_intent',
                  element: 'exit_intent_modal'
                });
              }
            }}
          />
        )}

        {/* Scroll-triggered CTAs */}
        {config.enableScrollTriggers && (
          <ScrollTriggeredCTAs
            milestones={config.scrollMilestones}
            ctaVariations={config.ctaVariations}
            onScrollMilestone={(percentage) => {
              trackConversionEvent({
                type: 'scroll_milestone',
                value: percentage
              });
            }}
            onCTAClick={(cta, context) => {
              trackConversionEvent({
                type: 'click',
                element: 'scroll_cta',
                metadata: { cta, context }
              });
            }}
          />
        )}

        {/* Urgency & Scarcity */}
        {(config.enableUrgencyTimers || config.enableScarcityIndicators) && (
          <UrgencyScarcitySystem
            enableUrgency={config.enableUrgencyTimers}
            enableScarcity={config.enableScarcityIndicators}
            urgencyDuration={config.urgencyDuration}
            scarcityThreshold={config.scarcityThreshold}
            onInteraction={(type, data) => {
              trackConversionEvent({
                type: 'click',
                element: `urgency_${type}`,
                metadata: data
              });
            }}
          />
        )}

        {/* Social Proof */}
        {config.enableSocialProof && (
          <SocialProofDisplay
            types={config.socialProofTypes}
            frequency={config.socialProofFrequency}
            personalizedType={getPersonalizedContent().socialProofType}
            onInteraction={(type, data) => {
              trackConversionEvent({
                type: 'click',
                element: `social_proof_${type}`,
                metadata: data
              });
            }}
          />
        )}

        {children}

        {/* Conversion Analytics Overlay (Development) */}
        {process.env.NODE_ENV === 'development' && (
          <div className="fixed bottom-4 left-4 z-50 p-3 bg-black/80 text-white rounded text-xs max-w-xs">
            <div>Segment: {currentSegment || 'None'}</div>
            <div>Conversion Prob: {(conversionProbability * 100).toFixed(1)}%</div>
            <div>Events: {events.length}</div>
            <div>Time: {Math.floor(sessionData.timeOnSite / 1000)}s</div>
            <div>Interactions: {sessionData.interactions}</div>
            <div>Scroll: {sessionData.scrollDepth}%</div>
          </div>
        )}
      </div>
    </ConversionContext.Provider>
  );
}

// Hook to use conversion context
export function useConversionSystem() {
  const context = useContext(ConversionContext);
  if (!context) {
    throw new Error('useConversionSystem must be used within ComprehensiveConversionSystem');
  }
  return context;
}

// Utility functions
function getDeviceType(userAgent: string): 'mobile' | 'tablet' | 'desktop' {
  if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
    return 'tablet';
  }
  if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
    return 'mobile';
  }
  return 'desktop';
}

// Conversion optimization hooks
export function useConversionOptimization() {
  const { 
    config, 
    events, 
    conversionProbability, 
    getPersonalizedContent,
    triggerConversionAction 
  } = useConversionSystem();

  const getOptimalCTA = useCallback(() => {
    const personalizedContent = getPersonalizedContent();
    const baseVariations = config.ctaVariations;
    
    if (personalizedContent.ctaText) {
      return personalizedContent.ctaText;
    }

    // Return variation based on conversion probability
    if (conversionProbability > 0.7) {
      return baseVariations[0]; // Most direct
    } else if (conversionProbability > 0.4) {
      return baseVariations[1]; // Moderate
    } else {
      return baseVariations[2]; // Softer approach
    }
  }, [config.ctaVariations, conversionProbability, getPersonalizedContent]);

  const getOptimalUrgencyLevel = useCallback(() => {
    const personalizedContent = getPersonalizedContent();
    
    if (personalizedContent.urgencyLevel) {
      return personalizedContent.urgencyLevel;
    }

    // Determine urgency based on user behavior
    const recentEvents = events.slice(0, 10);
    const hasHighEngagement = recentEvents.filter(e => e.type === 'click').length > 3;
    
    if (conversionProbability > 0.8 || hasHighEngagement) {
      return 'high';
    } else if (conversionProbability > 0.5) {
      return 'medium';
    } else {
      return 'low';
    }
  }, [events, conversionProbability, getPersonalizedContent]);

  const shouldShowOffer = useCallback(() => {
    const personalizedContent = getPersonalizedContent();
    const timeOnSite = Date.now() - (events[events.length - 1]?.timestamp.getTime() || Date.now());
    
    // Show offer based on engagement and segment
    return (
      conversionProbability > 0.6 ||
      timeOnSite > 5 * 60 * 1000 || // 5 minutes
      personalizedContent.offerType === 'discount'
    );
  }, [events, conversionProbability, getPersonalizedContent]);

  return {
    getOptimalCTA,
    getOptimalUrgencyLevel,
    shouldShowOffer,
    conversionProbability,
    triggerConversionAction
  };
}

export default ComprehensiveConversionSystem;
