// Types barrel export
// Centralized access to all TypeScript type definitions

// Core types
export * from './auth';
export * from './user';
export * from './api';
export * from './database';

// Learning types
export * from './learning';
export * from './course';
export * from './lesson';
export * from './achievement';
export * from './progress';

// Analytics types
export * from './analytics';
export * from './performance';
export * from './ab-testing';
export * from './conversion';

// Social types
export * from './social';
export * from './community';
export * from './gamification';

// Collaboration types
export * from './collaboration';
export * from './real-time';

// UI types
export * from './ui';
export * from './components';
export * from './forms';

// Blockchain types
export * from './blockchain';
export * from './smart-contracts';

// Admin types
export * from './admin';
export * from './management';

// Navigation types
export * from './navigation';
export * from './routing';

// Content types
export * from './content';
export * from './curriculum';

// Notification types
export * from './notifications';

// Settings types
export * from './settings';
export * from './preferences';

// Error types
export * from './errors';

// Utility types
export * from './utils';

// Version info
export const TYPES_VERSION = '2.0.0';
export const LAST_UPDATED = '2024-12-26';

// Type categories for organization
export const TYPE_CATEGORIES = {
  CORE: ['Auth', 'User', 'Api', 'Database'],
  LEARNING: ['Learning', 'Course', 'Lesson', 'Achievement', 'Progress'],
  ANALYTICS: ['Analytics', 'Performance', 'ABTesting', 'Conversion'],
  SOCIAL: ['Social', 'Community', 'Gamification'],
  COLLABORATION: ['Collaboration', 'RealTime'],
  UI: ['UI', 'Components', 'Forms'],
  BLOCKCHAIN: ['Blockchain', 'SmartContracts'],
  ADMIN: ['Admin', 'Management'],
  NAVIGATION: ['Navigation', 'Routing'],
  CONTENT: ['Content', 'Curriculum'],
  NOTIFICATIONS: ['Notifications'],
  SETTINGS: ['Settings', 'Preferences'],
  ERRORS: ['Errors'],
  UTILS: ['Utils']
} as const;

// Common utility types used across the application
export type ID = string | number;
export type Timestamp = string | Date;
export type Optional<T> = T | undefined;
export type Nullable<T> = T | null;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// API response wrapper types
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    hasMore?: boolean;
  };
}

export interface ApiError {
  code: string;
  message: string;
  details?: any;
  timestamp: Timestamp;
}

// Pagination types
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Filter and search types
export interface FilterParams {
  [key: string]: any;
}

export interface SearchParams {
  query?: string;
  filters?: FilterParams;
  pagination?: PaginationParams;
}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  id?: string;
  'data-testid'?: string;
}

export interface LoadingProps {
  isLoading?: boolean;
  loadingText?: string;
  skeleton?: React.ReactNode;
}

export interface ErrorProps {
  error?: Error | string | null;
  onRetry?: () => void;
  fallback?: React.ReactNode;
}

// Event handler types
export type EventHandler<T = any> = (event: T) => void;
export type AsyncEventHandler<T = any> = (event: T) => Promise<void>;
export type ChangeHandler<T = any> = (value: T) => void;

// Configuration types
export interface AppConfig {
  version: string;
  environment: 'development' | 'staging' | 'production';
  features: Record<string, boolean>;
  api: {
    baseUrl: string;
    timeout: number;
    retries: number;
  };
  analytics: {
    enabled: boolean;
    providers: string[];
  };
  performance: {
    monitoring: boolean;
    thresholds: Record<string, number>;
  };
}

// Feature flag types
export interface FeatureFlags {
  [key: string]: boolean;
}

// Theme types
export interface Theme {
  colors: Record<string, string>;
  fonts: Record<string, string>;
  spacing: Record<string, string>;
  breakpoints: Record<string, string>;
}

// Default export
export default {
  version: TYPES_VERSION,
  lastUpdated: LAST_UPDATED,
  categories: TYPE_CATEGORIES
};
