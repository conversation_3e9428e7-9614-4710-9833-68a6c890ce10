/**
 * Analytics Testing Setup
 * Configures mocks and utilities for testing analytics components
 */

// Mock Google Analytics
global.gtag = jest.fn();
global.dataLayer = [];

// Mock Hotjar
global.hj = jest.fn();

// Mock window.performance for performance monitoring tests
Object.defineProperty(window, 'performance', {
  value: {
    now: jest.fn(() => Date.now()),
    mark: jest.fn(),
    measure: jest.fn(),
    getEntriesByType: jest.fn(() => []),
    getEntriesByName: jest.fn(() => []),
    navigation: {
      type: 0
    },
    timing: {
      navigationStart: Date.now() - 1000,
      loadEventEnd: Date.now(),
      domContentLoadedEventEnd: Date.now() - 500,
      responseStart: Date.now() - 800,
      requestStart: Date.now() - 900
    }
  },
  writable: true
});

// Mock PerformanceObserver
global.PerformanceObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  takeRecords: jest.fn(() => [])
}));

// Mock IntersectionObserver for lazy loading tests
global.IntersectionObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}));

// Mock localStorage for analytics storage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock navigator for connection information
Object.defineProperty(navigator, 'connection', {
  value: {
    effectiveType: '4g',
    downlink: 10,
    rtt: 50,
    type: 'wifi'
  },
  writable: true
});

// Mock navigator.onLine
Object.defineProperty(navigator, 'onLine', {
  value: true,
  writable: true
});

// Mock fetch for API calls
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  })
);

// Mock console methods to avoid noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Analytics test utilities
global.analyticsTestUtils = {
  // Reset all analytics mocks
  resetMocks: () => {
    global.gtag.mockClear();
    global.hj.mockClear();
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    sessionStorageMock.getItem.mockClear();
    sessionStorageMock.setItem.mockClear();
    global.fetch.mockClear();
  },

  // Mock analytics events
  mockAnalyticsEvent: (eventName, parameters = {}) => {
    global.gtag.mockImplementation((command, event, params) => {
      if (command === 'event' && event === eventName) {
        return params;
      }
    });
  },

  // Mock Hotjar events
  mockHotjarEvent: (eventName, attributes = {}) => {
    global.hj.mockImplementation((command, event, attrs) => {
      if (command === 'event' && event === eventName) {
        return attrs;
      }
    });
  },

  // Mock performance entries
  mockPerformanceEntries: (entries) => {
    window.performance.getEntriesByType.mockReturnValue(entries);
  },

  // Mock user consent
  mockUserConsent: (consent = {}) => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'user_consent') {
        return JSON.stringify({
          analytics: true,
          heatmaps: true,
          sessionRecordings: false,
          marketing: false,
          hasConsented: true,
          ...consent
        });
      }
      return null;
    });
  },

  // Mock A/B test variants
  mockABTestVariants: (variants = {}) => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'ab_test_variants') {
        return JSON.stringify(variants);
      }
      return null;
    });
  },

  // Mock user profile for personalization
  mockUserProfile: (profile = {}) => {
    localStorageMock.getItem.mockImplementation((key) => {
      if (key === 'user_profile') {
        return JSON.stringify({
          segment: 'beginner',
          experience: 'beginner',
          interests: ['blockchain', 'programming'],
          behavior: {
            preferredLearningStyle: 'visual',
            sessionDuration: 300,
            completionRate: 75,
            engagementScore: 80
          },
          demographics: {
            role: 'student',
            industry: 'technology',
            goals: ['learn-blockchain', 'career-change']
          },
          ...profile
        });
      }
      return null;
    });
  },

  // Mock network conditions
  mockNetworkConditions: (conditions = {}) => {
    Object.defineProperty(navigator, 'connection', {
      value: {
        effectiveType: '4g',
        downlink: 10,
        rtt: 50,
        type: 'wifi',
        ...conditions
      },
      writable: true
    });
  },

  // Mock offline/online status
  mockOnlineStatus: (isOnline = true) => {
    Object.defineProperty(navigator, 'onLine', {
      value: isOnline,
      writable: true
    });
  },

  // Wait for async operations
  waitForAsync: () => new Promise(resolve => setTimeout(resolve, 0)),

  // Mock date for consistent testing
  mockDate: (date = new Date('2024-01-01T00:00:00.000Z')) => {
    jest.useFakeTimers();
    jest.setSystemTime(date);
  },

  // Restore real timers
  restoreTimers: () => {
    jest.useRealTimers();
  }
};

// Setup before each test
beforeEach(() => {
  // Reset all mocks
  global.analyticsTestUtils.resetMocks();
  
  // Reset DOM
  document.body.innerHTML = '';
  
  // Reset window location
  delete window.location;
  window.location = {
    href: 'http://localhost:3000',
    pathname: '/',
    search: '',
    hash: '',
    origin: 'http://localhost:3000'
  };
});

// Cleanup after each test
afterEach(() => {
  // Clear all timers
  jest.clearAllTimers();
  
  // Clear all mocks
  jest.clearAllMocks();
  
  // Restore real timers if fake timers were used
  if (jest.isMockFunction(setTimeout)) {
    jest.useRealTimers();
  }
});

// Global error handler for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Suppress specific warnings in tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning: ReactDOM.render is deprecated') ||
       args[0].includes('Warning: componentWillReceiveProps has been renamed'))
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});
