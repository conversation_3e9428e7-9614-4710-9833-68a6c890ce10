'use client';

import React, { Suspense } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

// Import all performance and analytics components
import { 
  LoadingWrapper,
  HeroSkeleton,
  LearningPathSkeleton,
  FAQSkeleton,
  ComparisonTableSkeleton,
  ConversionSkeleton
} from '@/components/performance/LoadingStates';

import { PWAProvider } from '@/components/performance/PWAUtils';
import { LazyComponents, preloadCriticalComponents } from '@/components/performance/LazyComponents';

import { 
  useGoogleAnalytics,
  useHotjar,
  useUserConsent,
  useEnhancedTracking,
  ConsentBanner
} from '@/components/analytics/AnalyticsIntegration';

import { RealTimeDashboard } from '@/components/analytics/RealTimeDashboard';

import { 
  ABTestingProvider,
  ABTestDashboard,
  useABTestingContext
} from '@/components/analytics/ABTestingFramework';

import { 
  FunnelVisualization,
  useFeedbackWidget
} from '@/components/analytics/ConversionFunnelSystem';

import { FeedbackWidget, NPSSurvey, QuickFeedback } from '@/components/analytics/FeedbackWidgets';

import { 
  PerformanceDashboard,
  CoreWebVitals,
  BundleAnalysisView,
  LighthouseScores
} from '@/components/analytics/PerformanceMonitoring';

interface PerformanceAnalyticsConfig {
  enableAnalytics: boolean;
  enableHeatmaps: boolean;
  enableABTesting: boolean;
  enablePerformanceMonitoring: boolean;
  enableFeedbackWidgets: boolean;
  enablePWA: boolean;
  enableAdvancedLoading: boolean;
  googleAnalyticsId?: string;
  hotjarId?: string;
  environment: 'development' | 'staging' | 'production';
}

interface PerformanceAnalyticsProps {
  config: PerformanceAnalyticsConfig;
  children: React.ReactNode;
  className?: string;
}

// Main Performance & Analytics Integration Component
export function PerformanceAnalyticsIntegration({
  config,
  children,
  className
}: PerformanceAnalyticsProps) {
  // Initialize analytics
  const analytics = useEnhancedTracking({
    googleAnalyticsId: config.googleAnalyticsId,
    hotjarId: config.hotjarId,
    enableHeatmaps: config.enableHeatmaps,
    enableSessionRecordings: config.enableHeatmaps,
    enableUserConsent: true,
    privacyCompliant: true,
    trackingLevel: config.environment === 'production' ? 'enhanced' : 'standard'
  });

  // Preload critical components
  React.useEffect(() => {
    if (config.enableAdvancedLoading) {
      preloadCriticalComponents();
    }
  }, [config.enableAdvancedLoading]);

  // Track page performance
  React.useEffect(() => {
    if (config.enablePerformanceMonitoring && analytics.isAnalyticsReady) {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            analytics.trackEvent({
              event: 'page_performance',
              category: 'performance',
              action: 'page_load',
              value: navEntry.loadEventEnd - navEntry.navigationStart,
              customParameters: {
                dom_content_loaded: navEntry.domContentLoadedEventEnd - navEntry.navigationStart,
                first_contentful_paint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
                time_to_first_byte: navEntry.responseStart - navEntry.requestStart
              }
            });
          }
        }
      });

      observer.observe({ entryTypes: ['navigation'] });
      return () => observer.disconnect();
    }
  }, [config.enablePerformanceMonitoring, analytics]);

  const providers = (
    <PWAProvider>
      <ABTestingProvider>
        <div className={cn('min-h-screen', className)}>
          {children}
          
          {/* Analytics & Feedback Widgets */}
          {config.enableFeedbackWidgets && <FeedbackWidget />}
          {config.enableAnalytics && <ConsentBanner />}
        </div>
      </ABTestingProvider>
    </PWAProvider>
  );

  return config.enablePWA ? providers : (
    <ABTestingProvider>
      <div className={cn('min-h-screen', className)}>
        {children}
        
        {/* Analytics & Feedback Widgets */}
        {config.enableFeedbackWidgets && <FeedbackWidget />}
        {config.enableAnalytics && <ConsentBanner />}
      </div>
    </ABTestingProvider>
  );
}

// Performance Analytics Dashboard (for admin/development)
export function PerformanceAnalyticsDashboard({ 
  className,
  showRealTime = true,
  showABTests = true,
  showFunnel = true,
  showPerformance = true
}: { 
  className?: string;
  showRealTime?: boolean;
  showABTests?: boolean;
  showFunnel?: boolean;
  showPerformance?: boolean;
}) {
  return (
    <div className={cn('space-y-8', className)}>
      {/* Real-time Analytics */}
      {showRealTime && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <RealTimeDashboard />
        </motion.section>
      )}

      {/* A/B Testing Results */}
      {showABTests && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <ABTestDashboard />
        </motion.section>
      )}

      {/* Conversion Funnel */}
      {showFunnel && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <FunnelVisualization />
        </motion.section>
      )}

      {/* Performance Monitoring */}
      {showPerformance && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <PerformanceDashboard />
        </motion.section>
      )}

      {/* Bundle Analysis & Lighthouse */}
      {showPerformance && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <BundleAnalysisView />
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
          >
            <LighthouseScores />
          </motion.div>
        </div>
      )}
    </div>
  );
}

// Enhanced Loading States Provider
export function EnhancedLoadingProvider({ 
  children,
  enableSkeletons = true 
}: { 
  children: React.ReactNode;
  enableSkeletons?: boolean;
}) {
  if (!enableSkeletons) {
    return <>{children}</>;
  }

  return (
    <Suspense fallback={<HeroSkeleton />}>
      {children}
    </Suspense>
  );
}

// A/B Testing Component Wrapper
export function ABTestComponent({ 
  testId, 
  variants,
  children 
}: { 
  testId: string;
  variants: Record<string, React.ReactNode>;
  children?: React.ReactNode;
}) {
  const { assignVariant, getVariantConfig } = useABTestingContext();
  const variantId = assignVariant(testId);
  const config = getVariantConfig(testId);

  // Return the appropriate variant or fallback to children
  return variants[variantId] || children || variants.control || null;
}

// Performance-optimized section wrapper
export function PerformanceSection({
  children,
  skeleton,
  isLoading = false,
  lazy = false,
  threshold = 0.1,
  className
}: {
  children: React.ReactNode;
  skeleton?: React.ReactNode;
  isLoading?: boolean;
  lazy?: boolean;
  threshold?: number;
  className?: string;
}) {
  const [isVisible, setIsVisible] = React.useState(!lazy);
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    if (!lazy) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [lazy, threshold]);

  return (
    <div ref={ref} className={className}>
      <LoadingWrapper
        isLoading={isLoading || (lazy && !isVisible)}
        skeleton={skeleton}
      >
        {children}
      </LoadingWrapper>
    </div>
  );
}

// Hook for comprehensive analytics tracking
export function useComprehensiveAnalytics() {
  const analytics = useEnhancedTracking({
    googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
    hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID,
    enableHeatmaps: true,
    enableSessionRecordings: true,
    enableUserConsent: true,
    privacyCompliant: true,
    trackingLevel: 'enhanced'
  });

  const { triggerWidget } = useFeedbackWidget();

  const trackUserJourney = React.useCallback((step: string, metadata?: any) => {
    analytics.trackEvent({
      event: 'user_journey',
      category: 'engagement',
      action: step,
      customParameters: {
        timestamp: Date.now(),
        page: window.location.pathname,
        ...metadata
      }
    });
  }, [analytics]);

  const trackConversionEvent = React.useCallback((event: string, value?: number, metadata?: any) => {
    analytics.trackConversionTouchpoint(event, 'comprehensive_system', value);
    
    // Trigger appropriate feedback widget
    if (event === 'trial_signup') {
      setTimeout(() => triggerWidget('trial_extension_offered', { engagement_score: 75 }), 5000);
    } else if (event === 'lesson_completed') {
      setTimeout(() => triggerWidget('lesson_completed'), 2000);
    }
  }, [analytics, triggerWidget]);

  const trackPerformanceIssue = React.useCallback((issue: string, severity: 'low' | 'medium' | 'high') => {
    analytics.trackEvent({
      event: 'performance_issue',
      category: 'technical',
      action: issue,
      customParameters: {
        severity,
        user_agent: navigator.userAgent,
        timestamp: Date.now()
      }
    });
  }, [analytics]);

  return {
    ...analytics,
    trackUserJourney,
    trackConversionEvent,
    trackPerformanceIssue
  };
}

// Export all components for easy access
export {
  // Loading States
  LoadingWrapper,
  HeroSkeleton,
  LearningPathSkeleton,
  FAQSkeleton,
  ComparisonTableSkeleton,
  ConversionSkeleton,
  
  // PWA
  PWAProvider,
  
  // Lazy Components
  LazyComponents,
  
  // Analytics
  RealTimeDashboard,
  ABTestDashboard,
  FunnelVisualization,
  
  // Feedback
  FeedbackWidget,
  NPSSurvey,
  QuickFeedback,
  
  // Performance
  PerformanceDashboard,
  CoreWebVitals,
  BundleAnalysisView,
  LighthouseScores
};

// Default export
export default PerformanceAnalyticsIntegration;
