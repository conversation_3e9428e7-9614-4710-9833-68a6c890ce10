'use client';

import React, { useState, useRef, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { Skeleton } from './LoadingStates';

interface ProgressiveImageProps {
  src: string;
  alt: string;
  className?: string;
  width?: number;
  height?: number;
  placeholder?: string;
  blurDataURL?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  onLoad?: () => void;
  onError?: (error: Event) => void;
  lazy?: boolean;
  webpSrc?: string;
  avifSrc?: string;
  fallbackSrc?: string;
}

// Progressive image component with modern format support
export function ProgressiveImage({
  src,
  alt,
  className,
  width,
  height,
  placeholder,
  blurDataURL,
  priority = false,
  quality = 75,
  sizes,
  onLoad,
  onError,
  lazy = true,
  webpSrc,
  avifSrc,
  fallbackSrc
}: ProgressiveImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [isInView, setIsInView] = useState(!lazy || priority);
  const [currentSrc, setCurrentSrc] = useState<string>('');
  
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || priority || isInView) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsInView(true);
            observerRef.current?.disconnect();
          }
        });
      },
      {
        rootMargin: '50px' // Start loading 50px before image comes into view
      }
    );

    if (imgRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => {
      observerRef.current?.disconnect();
    };
  }, [lazy, priority, isInView]);

  // Determine best image format to use
  const getBestImageSrc = useCallback(() => {
    // Check browser support for modern formats
    const supportsAvif = typeof window !== 'undefined' && 
      document.createElement('canvas').toDataURL('image/avif').indexOf('data:image/avif') === 0;
    
    const supportsWebp = typeof window !== 'undefined' && 
      document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0;

    if (avifSrc && supportsAvif) return avifSrc;
    if (webpSrc && supportsWebp) return webpSrc;
    return src;
  }, [src, webpSrc, avifSrc]);

  // Load image when in view
  useEffect(() => {
    if (!isInView) return;

    const bestSrc = getBestImageSrc();
    setCurrentSrc(bestSrc);

    const img = new Image();
    
    img.onload = () => {
      setIsLoading(false);
      onLoad?.();
    };

    img.onerror = (error) => {
      // Try fallback sources
      if (bestSrc === avifSrc && webpSrc) {
        setCurrentSrc(webpSrc);
        return;
      }
      if ((bestSrc === avifSrc || bestSrc === webpSrc) && src !== bestSrc) {
        setCurrentSrc(src);
        return;
      }
      if (fallbackSrc && bestSrc !== fallbackSrc) {
        setCurrentSrc(fallbackSrc);
        return;
      }
      
      setHasError(true);
      setIsLoading(false);
      onError?.(error);
    };

    img.src = bestSrc;
  }, [isInView, getBestImageSrc, onLoad, onError, avifSrc, webpSrc, src, fallbackSrc]);

  // Generate responsive srcSet
  const generateSrcSet = useCallback((baseSrc: string) => {
    if (!width || !height) return undefined;
    
    const sizes = [0.5, 1, 1.5, 2];
    return sizes
      .map(scale => {
        const scaledWidth = Math.round(width * scale);
        const scaledHeight = Math.round(height * scale);
        return `${baseSrc}?w=${scaledWidth}&h=${scaledHeight}&q=${quality} ${scale}x`;
      })
      .join(', ');
  }, [width, height, quality]);

  if (hasError) {
    return (
      <div 
        className={cn(
          'flex items-center justify-center bg-gray-800 text-gray-400 text-sm',
          className
        )}
        style={{ width, height }}
        role="img"
        aria-label={`Failed to load image: ${alt}`}
      >
        <span>Image failed to load</span>
      </div>
    );
  }

  return (
    <div 
      ref={imgRef}
      className={cn('relative overflow-hidden', className)}
      style={{ width, height }}
    >
      <AnimatePresence>
        {isLoading && (
          <motion.div
            className="absolute inset-0"
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {blurDataURL ? (
              <img
                src={blurDataURL}
                alt=""
                className="w-full h-full object-cover filter blur-sm scale-110"
                aria-hidden="true"
              />
            ) : placeholder ? (
              <img
                src={placeholder}
                alt=""
                className="w-full h-full object-cover opacity-50"
                aria-hidden="true"
              />
            ) : (
              <Skeleton 
                variant="rectangular" 
                className="w-full h-full" 
                animation="wave"
              />
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {isInView && currentSrc && (
        <motion.img
          src={currentSrc}
          alt={alt}
          className={cn(
            'w-full h-full object-cover',
            isLoading ? 'opacity-0' : 'opacity-100'
          )}
          style={{ width, height }}
          srcSet={generateSrcSet(currentSrc)}
          sizes={sizes}
          loading={priority ? 'eager' : 'lazy'}
          decoding="async"
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoading ? 0 : 1 }}
          transition={{ duration: 0.3 }}
        />
      )}
    </div>
  );
}

// Hook for image format detection
export function useImageFormatSupport() {
  const [support, setSupport] = useState({
    webp: false,
    avif: false,
    heic: false
  });

  useEffect(() => {
    const checkSupport = async () => {
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      
      const webpSupport = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      
      // Check AVIF support
      const avifSupport = await new Promise<boolean>((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
      });
      
      setSupport({
        webp: webpSupport,
        avif: await avifSupport,
        heic: false // HEIC support is limited and complex to detect
      });
    };

    checkSupport();
  }, []);

  return support;
}

// Image optimization utility
export function optimizeImageUrl(
  src: string, 
  options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'avif' | 'jpeg' | 'png';
    fit?: 'cover' | 'contain' | 'fill';
  } = {}
) {
  const { width, height, quality = 75, format, fit = 'cover' } = options;
  
  // If using a CDN like Cloudinary, Vercel, or similar
  const params = new URLSearchParams();
  
  if (width) params.set('w', width.toString());
  if (height) params.set('h', height.toString());
  if (quality) params.set('q', quality.toString());
  if (format) params.set('f', format);
  if (fit) params.set('fit', fit);
  
  const queryString = params.toString();
  const separator = src.includes('?') ? '&' : '?';
  
  return queryString ? `${src}${separator}${queryString}` : src;
}

// Responsive image component with automatic optimization
export function ResponsiveImage({
  src,
  alt,
  className,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  priority = false,
  quality = 75,
  ...props
}: ProgressiveImageProps & {
  sizes?: string;
}) {
  const formatSupport = useImageFormatSupport();
  
  // Generate optimized sources
  const generateSources = useCallback(() => {
    const sources = [];
    
    // AVIF source (best compression)
    if (formatSupport.avif) {
      sources.push({
        srcSet: generateResponsiveSrcSet(src, 'avif', quality),
        type: 'image/avif'
      });
    }
    
    // WebP source (good compression, wide support)
    if (formatSupport.webp) {
      sources.push({
        srcSet: generateResponsiveSrcSet(src, 'webp', quality),
        type: 'image/webp'
      });
    }
    
    // Fallback JPEG/PNG
    sources.push({
      srcSet: generateResponsiveSrcSet(src, undefined, quality),
      type: src.includes('.png') ? 'image/png' : 'image/jpeg'
    });
    
    return sources;
  }, [src, formatSupport, quality]);

  const sources = generateSources();

  return (
    <picture className={className}>
      {sources.map((source, index) => (
        <source
          key={index}
          srcSet={source.srcSet}
          type={source.type}
          sizes={sizes}
        />
      ))}
      <ProgressiveImage
        src={src}
        alt={alt}
        priority={priority}
        quality={quality}
        sizes={sizes}
        {...props}
      />
    </picture>
  );
}

// Generate responsive srcSet for different screen sizes
function generateResponsiveSrcSet(
  src: string, 
  format?: string, 
  quality = 75
): string {
  const breakpoints = [320, 640, 768, 1024, 1280, 1536];
  
  return breakpoints
    .map(width => {
      const optimizedSrc = optimizeImageUrl(src, {
        width,
        quality,
        format: format as any
      });
      return `${optimizedSrc} ${width}w`;
    })
    .join(', ');
}

// Image preloader utility
export function preloadImage(src: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = src;
  });
}

// Batch image preloader
export function preloadImages(sources: string[]): Promise<void[]> {
  return Promise.all(sources.map(preloadImage));
}
