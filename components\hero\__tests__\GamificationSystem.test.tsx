/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { GamificationSystem } from '../GamificationSystem';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <div>{children}</div>,
}));

describe('GamificationSystem', () => {
  const mockProgress = {
    currentStep: 2,
    completedSteps: new Set([0, 1]),
    totalSteps: 5,
    totalXP: 150,
    achievements: [
      {
        id: 'first-step',
        title: 'Getting Started',
        description: 'Complete your first step',
        icon: () => <div>Icon</div>,
        xpReward: 25,
        unlocked: true,
        rarity: 'common' as const,
      },
      {
        id: 'halfway',
        title: 'Halfway Hero',
        description: 'Reach the halfway point',
        icon: () => <div>Icon</div>,
        xpReward: 50,
        unlocked: false,
        rarity: 'rare' as const,
      },
    ],
    startTime: new Date(),
    lastActiveTime: new Date(),
    completionPercentage: 40,
    estimatedTimeRemaining: 120,
    streak: 3,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders progress overview correctly', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    expect(screen.getByText('Your Progress')).toBeInTheDocument();
    expect(screen.getByText('150 XP')).toBeInTheDocument();
    expect(screen.getByText('Level 2')).toBeInTheDocument();
    expect(screen.getByText('Step 3 of 5')).toBeInTheDocument();
    expect(screen.getByText('40% Complete')).toBeInTheDocument();
  });

  it('displays progress bar with correct percentage', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    const progressBar = screen.getByText('40% Complete').previousElementSibling;
    expect(progressBar).toBeInTheDocument();
  });

  it('shows XP level progress correctly', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    expect(screen.getByText('50/100 XP')).toBeInTheDocument();
  });

  it('displays achievement statistics', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    expect(screen.getByText('1')).toBeInTheDocument(); // Unlocked achievements
    expect(screen.getByText('3')).toBeInTheDocument(); // Streak
    expect(screen.getByText('2:00')).toBeInTheDocument(); // Time remaining
  });

  it('renders achievements with correct unlock status', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    expect(screen.getByText('Getting Started')).toBeInTheDocument();
    expect(screen.getByText('Halfway Hero')).toBeInTheDocument();
    
    // Check achievement count display
    expect(screen.getByText('1/2')).toBeInTheDocument();
  });

  it('shows different styling for unlocked vs locked achievements', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    const unlockedAchievement = screen.getByText('Getting Started').closest('div');
    const lockedAchievement = screen.getByText('Halfway Hero').closest('div');
    
    expect(unlockedAchievement).not.toHaveClass('opacity-60');
    expect(lockedAchievement).toHaveClass('opacity-60');
  });

  it('displays leaderboard when enabled', () => {
    render(<GamificationSystem progress={mockProgress} showLeaderboard={true} />);
    
    expect(screen.getByText('Leaderboard')).toBeInTheDocument();
    expect(screen.getByText('Top Learners')).toBeInTheDocument();
  });

  it('hides leaderboard when disabled', () => {
    render(<GamificationSystem progress={mockProgress} showLeaderboard={false} />);
    
    expect(screen.queryByText('Leaderboard')).not.toBeInTheDocument();
  });

  it('highlights current user in leaderboard', async () => {
    render(<GamificationSystem progress={mockProgress} showLeaderboard={true} />);
    
    await waitFor(() => {
      const userEntry = screen.getByText('You');
      expect(userEntry.closest('div')).toHaveClass('bg-blue-500/20');
    });
  });

  it('calls onAchievementUnlocked when achievement is unlocked', () => {
    const onAchievementUnlocked = jest.fn();
    
    render(
      <GamificationSystem 
        progress={mockProgress} 
        onAchievementUnlocked={onAchievementUnlocked}
      />
    );
    
    // This would be triggered by the parent component when achievements change
    // The test verifies the callback is properly passed
    expect(onAchievementUnlocked).toBeDefined();
  });

  it('calls onXPEarned when XP is earned', () => {
    const onXPEarned = jest.fn();
    
    render(
      <GamificationSystem 
        progress={mockProgress} 
        onXPEarned={onXPEarned}
      />
    );
    
    expect(onXPEarned).toBeDefined();
  });

  it('formats time correctly', () => {
    const progressWithDifferentTime = {
      ...mockProgress,
      estimatedTimeRemaining: 75, // 1:15
    };
    
    render(<GamificationSystem progress={progressWithDifferentTime} />);
    
    expect(screen.getByText('1:15')).toBeInTheDocument();
  });

  it('calculates XP level correctly', () => {
    const progressWithHighXP = {
      ...mockProgress,
      totalXP: 250, // Level 3
    };
    
    render(<GamificationSystem progress={progressWithHighXP} />);
    
    expect(screen.getByText('Level 3')).toBeInTheDocument();
    expect(screen.getByText('50/100 XP')).toBeInTheDocument(); // 250 % 100 = 50
  });

  it('shows achievement rarity correctly', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    expect(screen.getByText('COMMON')).toBeInTheDocument();
    expect(screen.getByText('RARE')).toBeInTheDocument();
  });

  it('displays XP rewards for achievements', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    expect(screen.getByText('+25 XP')).toBeInTheDocument();
    expect(screen.getByText('+50 XP')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<GamificationSystem progress={mockProgress} className="custom-gamification" />);
    
    const container = screen.getByText('Your Progress').closest('.custom-gamification');
    expect(container).toBeInTheDocument();
  });

  it('handles zero XP correctly', () => {
    const progressWithZeroXP = {
      ...mockProgress,
      totalXP: 0,
    };
    
    render(<GamificationSystem progress={progressWithZeroXP} />);
    
    expect(screen.getByText('0 XP')).toBeInTheDocument();
    expect(screen.getByText('Level 1')).toBeInTheDocument();
    expect(screen.getByText('0/100 XP')).toBeInTheDocument();
  });

  it('handles empty achievements array', () => {
    const progressWithNoAchievements = {
      ...mockProgress,
      achievements: [],
    };
    
    render(<GamificationSystem progress={progressWithNoAchievements} />);
    
    expect(screen.getByText('0/0')).toBeInTheDocument();
  });

  it('shows learning objectives completion status', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    // The component should show learning objectives if they exist
    expect(screen.getByText('Your Progress')).toBeInTheDocument();
  });

  it('handles high XP values correctly', () => {
    const progressWithHighXP = {
      ...mockProgress,
      totalXP: 9999,
    };
    
    render(<GamificationSystem progress={progressWithHighXP} />);
    
    expect(screen.getByText('9999 XP')).toBeInTheDocument();
    expect(screen.getByText('Level 100')).toBeInTheDocument();
  });

  it('shows correct rank in leaderboard', async () => {
    render(<GamificationSystem progress={mockProgress} showLeaderboard={true} />);
    
    await waitFor(() => {
      // Should show rank based on XP
      expect(screen.getByText('#4')).toBeInTheDocument();
    });
  });

  it('handles achievement hover effects', async () => {
    const user = userEvent.setup();
    render(<GamificationSystem progress={mockProgress} />);
    
    const unlockedAchievement = screen.getByText('Getting Started').closest('div');
    
    if (unlockedAchievement) {
      await user.hover(unlockedAchievement);
      // Achievement should have hover effects (tested through CSS classes)
      expect(unlockedAchievement).toBeInTheDocument();
    }
  });

  it('displays streak information correctly', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    expect(screen.getByText('3')).toBeInTheDocument();
    expect(screen.getByText('Day Streak')).toBeInTheDocument();
  });

  it('shows completion percentage in stats', () => {
    render(<GamificationSystem progress={mockProgress} />);
    
    expect(screen.getByText('40%')).toBeInTheDocument();
    expect(screen.getByText('Complete')).toBeInTheDocument();
  });
});
