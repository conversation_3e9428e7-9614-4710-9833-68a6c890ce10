# =============================================================================
# SOLIDITY LEARNING PLATFORM - ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env.local for development
# For production, set these variables in your deployment platform
# =============================================================================

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="Solidity Learning Platform"
NEXT_PUBLIC_APP_VERSION=1.0.0

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database URL for Prisma ORM
DATABASE_URL="postgresql://username:password@localhost:5432/solidity_learning_dev?schema=public"

# Database Connection Pool Settings
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10
DATABASE_POOL_TIMEOUT=30000

# Redis Configuration for Session Storage and Caching
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""
REDIS_DB=0

# =============================================================================
# AUTHENTICATION & SESSION MANAGEMENT
# =============================================================================
# NextAuth.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET="your-nextauth-secret-key-min-32-chars"

# Session Configuration
SESSION_TIMEOUT=86400
SESSION_UPDATE_AGE=3600
CSRF_TOKEN_SECRET="your-csrf-secret-key"

# GitHub OAuth Provider
GITHUB_CLIENT_ID="your-github-client-id"
GITHUB_CLIENT_SECRET="your-github-client-secret"

# Google OAuth Provider
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Discord OAuth Provider (Optional)
DISCORD_CLIENT_ID="your-discord-client-id"
DISCORD_CLIENT_SECRET="your-discord-client-secret"

# =============================================================================
# AI SERVICES CONFIGURATION
# =============================================================================
# OpenAI API Configuration
OPENAI_API_KEY="sk-your-openai-api-key"
OPENAI_MODEL="gpt-4"
OPENAI_MAX_TOKENS=2048
OPENAI_TEMPERATURE=0.7

# Google Generative AI Configuration
GOOGLE_GENERATIVE_AI_API_KEY="your-google-ai-api-key"
GOOGLE_AI_MODEL="gemini-pro"
GEMINI_API_KEY=your_gemini_api_key_here

# AI Rate Limiting
AI_REQUESTS_PER_MINUTE=60
AI_REQUESTS_PER_HOUR=1000
AI_REQUESTS_PER_DAY=10000

# =============================================================================
# SOCKET.IO CONFIGURATION
# =============================================================================
# Socket.io Server Configuration
SOCKET_IO_PORT=3001
SOCKET_IO_CORS_ORIGINS="http://localhost:3000,https://yourdomain.com"
SOCKET_IO_MAX_CONNECTIONS=1000
SOCKET_IO_CONNECTION_TIMEOUT=60000

# Collaboration Session Limits
MAX_COLLABORATION_SESSIONS=100
MAX_PARTICIPANTS_PER_SESSION=10
SESSION_IDLE_TIMEOUT=1800000

# =============================================================================
# EMAIL SERVICE CONFIGURATION
# =============================================================================
# SMTP Configuration for Email Notifications
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"
SMTP_FROM_NAME="Solidity Learning Platform"
SMTP_FROM_EMAIL="<EMAIL>"

# SendGrid Configuration (Alternative)
SENDGRID_API_KEY="your-sendgrid-api-key"
SENDGRID_FROM_EMAIL="<EMAIL>"

# Email Templates
EMAIL_VERIFICATION_TEMPLATE_ID="d-your-template-id"
PASSWORD_RESET_TEMPLATE_ID="d-your-template-id"
WELCOME_EMAIL_TEMPLATE_ID="d-your-template-id"

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# AWS S3 Configuration for File Uploads
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="solidity-learning-uploads"
AWS_S3_BUCKET_URL="https://your-bucket.s3.amazonaws.com"

# Cloudinary Configuration (Alternative)
CLOUDINARY_CLOUD_NAME="your-cloud-name"
CLOUDINARY_API_KEY="your-api-key"
CLOUDINARY_API_SECRET="your-api-secret"

# File Upload Limits
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,text/plain,application/json"

# =============================================================================
# BLOCKCHAIN INTEGRATION
# =============================================================================
# Ethereum Network Configuration
ETHEREUM_RPC_URL="https://mainnet.infura.io/v3/your-project-id"
ETHEREUM_TESTNET_RPC_URL="https://sepolia.infura.io/v3/your-project-id"
INFURA_PROJECT_ID="your-infura-project-id"
INFURA_PROJECT_SECRET="your-infura-project-secret"

# Alchemy Configuration (Alternative)
ALCHEMY_API_KEY="your-alchemy-api-key"
ALCHEMY_NETWORK="eth-mainnet"

# Contract Deployment
DEPLOYER_PRIVATE_KEY="your-deployer-private-key"
CONTRACT_VERIFICATION_API_KEY="your-etherscan-api-key"

# =============================================================================
# MONITORING & ANALYTICS
# =============================================================================
# Sentry Error Tracking
SENTRY_DSN="https://<EMAIL>/project-id"
SENTRY_ORG="your-org"
SENTRY_PROJECT="solidity-learning-platform"
SENTRY_AUTH_TOKEN="your-sentry-auth-token"

# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY="phc_your-posthog-key"
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"

# Mixpanel Analytics
NEXT_PUBLIC_MIXPANEL_TOKEN="your-mixpanel-token"

# Plausible Analytics (Privacy-focused alternative)
NEXT_PUBLIC_PLAUSIBLE_DOMAIN="your-domain.com"
NEXT_PUBLIC_PLAUSIBLE_API_HOST="https://plausible.io"

# Performance Monitoring
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true
NEXT_PUBLIC_PERFORMANCE_SAMPLE_RATE=0.1
LIGHTHOUSE_CI_TOKEN="your-lighthouse-ci-token"

# Core Web Vitals Tracking
NEXT_PUBLIC_TRACK_WEB_VITALS=true
NEXT_PUBLIC_WEB_VITALS_ENDPOINT="/api/analytics/web-vitals"

# =============================================================================
# RATE LIMITING & SECURITY
# =============================================================================
# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false

# API Rate Limits
API_RATE_LIMIT_PER_MINUTE=60
AUTH_RATE_LIMIT_PER_MINUTE=5
COLLABORATION_RATE_LIMIT_PER_MINUTE=30

# Security Headers
CONTENT_SECURITY_POLICY_REPORT_URI="https://your-domain.report-uri.com/r/d/csp/enforce"
HSTS_MAX_AGE=31536000

# =============================================================================
# PWA & SERVICE WORKER CONFIGURATION
# =============================================================================
# Progressive Web App Configuration
NEXT_PUBLIC_PWA_ENABLED=true
NEXT_PUBLIC_PWA_CACHE_STRATEGY="CacheFirst"
NEXT_PUBLIC_PWA_OFFLINE_FALLBACK="/offline"

# Service Worker Configuration
NEXT_PUBLIC_SW_ENABLED=true
NEXT_PUBLIC_SW_CACHE_NAME="solidity-learn-v1"
NEXT_PUBLIC_SW_PRECACHE_URLS="/,/learn,/code,/dashboard"

# Cache Configuration
NEXT_PUBLIC_CACHE_STATIC_TTL=86400      # 24 hours
NEXT_PUBLIC_CACHE_DYNAMIC_TTL=3600      # 1 hour
NEXT_PUBLIC_CACHE_API_TTL=300           # 5 minutes

# Background Sync
NEXT_PUBLIC_BACKGROUND_SYNC_ENABLED=true
NEXT_PUBLIC_SYNC_TAG_PREFIX="solidity-learn"

# =============================================================================
# ACCESSIBILITY CONFIGURATION
# =============================================================================
# Accessibility Features
NEXT_PUBLIC_A11Y_ENABLED=true
NEXT_PUBLIC_A11Y_TESTING_ENABLED=true
NEXT_PUBLIC_HIGH_CONTRAST_SUPPORT=true
NEXT_PUBLIC_REDUCED_MOTION_SUPPORT=true

# Screen Reader Support
NEXT_PUBLIC_SCREEN_READER_ANNOUNCEMENTS=true
NEXT_PUBLIC_LIVE_REGION_POLITENESS="polite"

# Keyboard Navigation
NEXT_PUBLIC_KEYBOARD_SHORTCUTS_ENABLED=true
NEXT_PUBLIC_FOCUS_VISIBLE_ENABLED=true

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Feature Toggle Configuration
FEATURE_AI_TUTORING=true
FEATURE_COLLABORATION=true
FEATURE_CODE_COMPILATION=true
FEATURE_BLOCKCHAIN_INTEGRATION=true
FEATURE_GAMIFICATION=true
FEATURE_SOCIAL_FEATURES=true
FEATURE_ADVANCED_ANALYTICS=false

# Performance Features
FEATURE_LAZY_LOADING=true
FEATURE_IMAGE_OPTIMIZATION=true
FEATURE_BUNDLE_ANALYSIS=true
FEATURE_PERFORMANCE_MONITORING=true

# Accessibility Features
FEATURE_ACCESSIBILITY_TESTING=true
FEATURE_SCREEN_READER_SUPPORT=true
FEATURE_KEYBOARD_NAVIGATION=true
FEATURE_HIGH_CONTRAST_MODE=true

# Beta Features
BETA_VOICE_CHAT=false
BETA_VIDEO_COLLABORATION=false
BETA_AI_CODE_REVIEW=false
BETA_OFFLINE_MODE=true
BETA_PUSH_NOTIFICATIONS=false

# =============================================================================
# THIRD-PARTY INTEGRATIONS
# =============================================================================
# GitHub Integration for Code Repositories
GITHUB_APP_ID="your-github-app-id"
GITHUB_APP_PRIVATE_KEY="your-github-app-private-key"
GITHUB_WEBHOOK_SECRET="your-webhook-secret"

# Stripe Payment Processing (for Premium Features)
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_WEBHOOK_SECRET="whsec_your-webhook-secret"

# Discord Bot Integration
DISCORD_BOT_TOKEN="your-discord-bot-token"
DISCORD_GUILD_ID="your-discord-server-id"

# Slack Integration
SLACK_BOT_TOKEN="xoxb-your-slack-bot-token"
SLACK_SIGNING_SECRET="your-slack-signing-secret"

# =============================================================================
# USER EXPERIENCE & ONBOARDING
# =============================================================================
# Help System Configuration
NEXT_PUBLIC_HELP_SYSTEM_ENABLED=true
NEXT_PUBLIC_CONTEXTUAL_HELP_ENABLED=true
NEXT_PUBLIC_KEYBOARD_SHORTCUTS_HELP=true

# Onboarding Configuration
NEXT_PUBLIC_ONBOARDING_ENABLED=true
NEXT_PUBLIC_INTERACTIVE_TUTORIALS=true
NEXT_PUBLIC_FEATURE_DISCOVERY=true
NEXT_PUBLIC_SMART_TOOLTIPS=true

# User Behavior Tracking (for adaptive UX)
NEXT_PUBLIC_BEHAVIOR_TRACKING=true
NEXT_PUBLIC_ADAPTIVE_UI=true
NEXT_PUBLIC_PERSONALIZATION=true

# =============================================================================
# DEVELOPMENT & DEBUGGING
# =============================================================================
# Development Configuration
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_QUERY_LOGGING=false
ENABLE_PERFORMANCE_MONITORING=true

# Testing Configuration
TEST_DATABASE_URL="postgresql://username:password@localhost:5432/solidity_learning_test"
TEST_REDIS_URL="redis://localhost:6379/1"

# Accessibility Testing
ENABLE_A11Y_TESTING=true
A11Y_TEST_RUNNER="axe-core"
A11Y_COMPLIANCE_LEVEL="AA"

# Performance Testing
ENABLE_PERFORMANCE_TESTING=true
LIGHTHOUSE_CI_ENABLED=true
PERFORMANCE_BUDGET_ENABLED=true

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================
# Build Configuration
BUILD_STANDALONE=false
OUTPUT_EXPORT=false
ANALYZE_BUNDLE=false

# Health Check Configuration
HEALTH_CHECK_ENDPOINT="/api/health"
HEALTH_CHECK_TIMEOUT=5000

# Backup Configuration
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET="solidity-learning-backups"

# =============================================================================
# ENVIRONMENT-SPECIFIC OVERRIDES
# =============================================================================
# These will be overridden in staging/production
LOG_LEVEL=info
CACHE_TTL=3600
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_SAME_SITE=lax

# Redis (for caching and sessions)
REDIS_URL="redis://localhost:6379"

# Socket.io Server
NEXT_PUBLIC_SOCKET_URL="http://localhost:3001"

# Blockchain Networks
SEPOLIA_RPC_URL="https://sepolia.infura.io/v3/your-project-id"
GOERLI_RPC_URL="https://goerli.infura.io/v3/your-project-id"
MUMBAI_RPC_URL="https://polygon-mumbai.infura.io/v3/your-project-id"

# Etherscan API (for contract verification)
ETHERSCAN_API_KEY="your-etherscan-api-key"
POLYGONSCAN_API_KEY="your-polygonscan-api-key"

# Development
NODE_ENV="development"
LOG_LEVEL="debug"
