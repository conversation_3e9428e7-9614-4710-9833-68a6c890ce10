# Component Documentation

## Overview

This document provides comprehensive documentation for all components in the Solidity Learning Platform. Components are organized by feature area and include usage examples, props documentation, and best practices.

## Component Architecture

### Design Principles

- **Composition over Inheritance**: Components are designed to be composable
- **Single Responsibility**: Each component has a clear, focused purpose
- **Accessibility First**: All components follow WCAG 2.1 AA guidelines
- **Performance Optimized**: Lazy loading and memoization where appropriate
- **Type Safe**: Full TypeScript support with strict typing

### Component Categories

1. [Core UI Components](#core-ui-components)
2. [Analytics Components](#analytics-components)
3. [Learning Platform Components](#learning-platform-components)
4. [Authentication Components](#authentication-components)
5. [Performance Components](#performance-components)
6. [Conversion Optimization Components](#conversion-optimization-components)
7. [Social & Gamification Components](#social--gamification-components)

## Core UI Components

### EnhancedButton

A highly optimized button component with accessibility features and performance optimizations.

```typescript
interface EnhancedButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  disabled?: boolean;
  onClick?: (event: React.MouseEvent) => void | Promise<void>;
  className?: string;
  'data-testid'?: string;
}
```

**Usage:**

```tsx
import { EnhancedButton } from '@/components/ui/EnhancedButton';

function MyComponent() {
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = async () => {
    setIsLoading(true);
    await someAsyncOperation();
    setIsLoading(false);
  };

  return (
    <EnhancedButton
      variant="primary"
      size="lg"
      isLoading={isLoading}
      onClick={handleClick}
      data-testid="submit-button"
    >
      Submit
    </EnhancedButton>
  );
}
```

**Features:**
- Automatic debouncing for async operations
- Loading states with spinner
- Keyboard navigation support
- Touch-friendly 44px minimum target size
- Automatic analytics tracking

### LoadingWrapper

Provides skeleton loading states for better perceived performance.

```typescript
interface LoadingWrapperProps {
  isLoading: boolean;
  skeleton?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}
```

**Usage:**

```tsx
import { LoadingWrapper, HeroSkeleton } from '@/components/performance/LoadingStates';

function HeroSection() {
  const { data, isLoading } = useHeroData();

  return (
    <LoadingWrapper
      isLoading={isLoading}
      skeleton={<HeroSkeleton />}
    >
      <HeroContent data={data} />
    </LoadingWrapper>
  );
}
```

## Analytics Components

### PerformanceAnalyticsIntegration

Main wrapper component that provides comprehensive analytics and performance monitoring.

```typescript
interface PerformanceAnalyticsConfig {
  enableAnalytics: boolean;
  enableHeatmaps: boolean;
  enableABTesting: boolean;
  enablePerformanceMonitoring: boolean;
  enableFeedbackWidgets: boolean;
  enablePWA: boolean;
  enableAdvancedLoading: boolean;
  googleAnalyticsId?: string;
  hotjarId?: string;
  environment: 'development' | 'staging' | 'production';
}
```

**Usage:**

```tsx
import { PerformanceAnalyticsIntegration } from '@/components/analytics/PerformanceAnalyticsIntegration';

function App({ children }) {
  const config = {
    enableAnalytics: true,
    enableHeatmaps: true,
    enableABTesting: true,
    enablePerformanceMonitoring: true,
    enableFeedbackWidgets: true,
    enablePWA: true,
    enableAdvancedLoading: true,
    googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
    hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID,
    environment: 'production'
  };

  return (
    <PerformanceAnalyticsIntegration config={config}>
      {children}
    </PerformanceAnalyticsIntegration>
  );
}
```

### ABTestingProvider

Provides A/B testing functionality with statistical significance tracking.

```typescript
interface ABTestingContextType {
  tests: ABTest[];
  assignVariant: (testId: string) => string;
  trackConversion: (testId: string, variantId: string) => void;
  getVariantConfig: (testId: string) => any;
  getTestResults: (testId: string) => ABTestResult[];
}
```

**Usage:**

```tsx
import { ABTestingProvider, useABTestingContext } from '@/components/analytics/ABTestingFramework';

function MyComponent() {
  const { assignVariant, trackConversion } = useABTestingContext();
  
  const variant = assignVariant('hero-cta-test');
  
  const handleClick = () => {
    trackConversion('hero-cta-test', variant);
    // Handle click
  };

  return (
    <button onClick={handleClick}>
      {variant === 'variant-a' ? 'Start Learning' : 'Begin Journey'}
    </button>
  );
}

// Wrap your app
function App() {
  return (
    <ABTestingProvider>
      <MyComponent />
    </ABTestingProvider>
  );
}
```

### RealTimeDashboard

Displays real-time analytics data with live updates.

```typescript
interface RealTimeDashboardProps {
  className?: string;
  refreshInterval?: number;
  showMetrics?: string[];
}
```

**Usage:**

```tsx
import { RealTimeDashboard } from '@/components/analytics/RealTimeDashboard';

function AdminPage() {
  return (
    <RealTimeDashboard
      refreshInterval={5000}
      showMetrics={['activeUsers', 'conversions', 'performance']}
      className="dashboard-container"
    />
  );
}
```

## Learning Platform Components

### ComprehensiveLearningPlatform

Main learning platform component with course management and progress tracking.

```typescript
interface LearningPlatformProps {
  userId: string;
  initialCourse?: string;
  enableGamification?: boolean;
  enableCollaboration?: boolean;
  className?: string;
}
```

**Usage:**

```tsx
import { ComprehensiveLearningPlatform } from '@/components/learning/ComprehensiveLearningPlatform';

function LearningPage({ user }) {
  return (
    <ComprehensiveLearningPlatform
      userId={user.id}
      initialCourse="solidity-basics"
      enableGamification={true}
      enableCollaboration={true}
    />
  );
}
```

### InteractiveCodeEditor

Monaco-based code editor with Solidity syntax highlighting and real-time compilation.

```typescript
interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language?: string;
  theme?: 'vs-dark' | 'vs-light';
  readOnly?: boolean;
  enableAutoComplete?: boolean;
  enableLinting?: boolean;
  onCompile?: (result: CompilationResult) => void;
  className?: string;
}
```

**Usage:**

```tsx
import { InteractiveCodeEditor } from '@/components/learning/InteractiveCodeEditor';

function CodeLesson() {
  const [code, setCode] = useState('// Write your Solidity code here');
  
  const handleCompile = (result) => {
    if (result.success) {
      console.log('Compilation successful');
    } else {
      console.error('Compilation errors:', result.errors);
    }
  };

  return (
    <InteractiveCodeEditor
      value={code}
      onChange={setCode}
      language="solidity"
      theme="vs-dark"
      enableAutoComplete={true}
      enableLinting={true}
      onCompile={handleCompile}
    />
  );
}
```

## Authentication Components

### EnhancedAuthProvider

Comprehensive authentication provider with session management and role-based access.

```typescript
interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signIn: (credentials: SignInCredentials) => Promise<void>;
  signOut: () => Promise<void>;
  signUp: (userData: SignUpData) => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
}
```

**Usage:**

```tsx
import { EnhancedAuthProvider, useAuth } from '@/components/auth/EnhancedAuthProvider';

function LoginForm() {
  const { signIn, isLoading } = useAuth();
  
  const handleSubmit = async (data) => {
    await signIn(data);
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Form fields */}
    </form>
  );
}

// Wrap your app
function App() {
  return (
    <EnhancedAuthProvider>
      <LoginForm />
    </EnhancedAuthProvider>
  );
}
```

### ProtectedRoute

Route protection component with role-based access control.

```typescript
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string;
  fallback?: React.ReactNode;
  redirectTo?: string;
}
```

**Usage:**

```tsx
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

function AdminPage() {
  return (
    <ProtectedRoute
      requiredRole="admin"
      fallback={<div>Access Denied</div>}
      redirectTo="/login"
    >
      <AdminDashboard />
    </ProtectedRoute>
  );
}
```

## Performance Components

### LazyComponents

Provides lazy loading for heavy components with error boundaries.

```typescript
interface LazyComponentProps {
  componentName: string;
  fallback?: React.ReactNode;
  errorFallback?: React.ReactNode;
  preload?: boolean;
}
```

**Usage:**

```tsx
import { LazyComponents } from '@/components/performance/LazyComponents';

function App() {
  return (
    <LazyComponents
      componentName="InteractiveCodeEditor"
      fallback={<div>Loading editor...</div>}
      errorFallback={<div>Failed to load editor</div>}
      preload={true}
    />
  );
}
```

### PWAProvider

Service worker integration with offline capabilities.

```typescript
interface PWAContextType {
  isOnline: boolean;
  isInstallable: boolean;
  installApp: () => Promise<void>;
  updateAvailable: boolean;
  updateApp: () => Promise<void>;
}
```

**Usage:**

```tsx
import { PWAProvider, usePWA } from '@/components/performance/PWAUtils';

function InstallPrompt() {
  const { isInstallable, installApp } = usePWA();
  
  if (!isInstallable) return null;

  return (
    <button onClick={installApp}>
      Install App
    </button>
  );
}
```

## Conversion Optimization Components

### ExitIntentSystem

Detects exit intent and triggers conversion actions.

```typescript
interface ExitIntentConfig {
  enabled: boolean;
  sensitivity: number;
  delay: number;
  maxTriggers: number;
  onExitIntent: () => void;
}
```

**Usage:**

```tsx
import { ExitIntentSystem } from '@/components/conversion/ExitIntentSystem';

function App() {
  const handleExitIntent = () => {
    // Show exit intent modal
    setShowExitModal(true);
  };

  return (
    <ExitIntentSystem
      config={{
        enabled: true,
        sensitivity: 20,
        delay: 1000,
        maxTriggers: 1,
        onExitIntent: handleExitIntent
      }}
    >
      <YourContent />
    </ExitIntentSystem>
  );
}
```

### UrgencyScarcitySystem

Creates urgency and scarcity to drive conversions.

```typescript
interface UrgencyTimerProps {
  endTime: Date;
  onExpire?: () => void;
  showDays?: boolean;
  className?: string;
}
```

**Usage:**

```tsx
import { UrgencyTimer } from '@/components/conversion/UrgencyScarcitySystem';

function SpecialOffer() {
  const offerEndTime = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  return (
    <div>
      <h2>Limited Time Offer!</h2>
      <UrgencyTimer
        endTime={offerEndTime}
        onExpire={() => console.log('Offer expired')}
        showDays={false}
      />
    </div>
  );
}
```

## Social & Gamification Components

### AchievementCelebration

Displays achievement celebrations with animations.

```typescript
interface AchievementCelebrationProps {
  achievement: Achievement;
  isVisible: boolean;
  onClose: () => void;
  enableConfetti?: boolean;
  enableSound?: boolean;
}
```

**Usage:**

```tsx
import { AchievementCelebration } from '@/components/gamification/AchievementCelebration';

function LearningPage() {
  const [newAchievement, setNewAchievement] = useState(null);

  return (
    <>
      <LearningContent />
      <AchievementCelebration
        achievement={newAchievement}
        isVisible={!!newAchievement}
        onClose={() => setNewAchievement(null)}
        enableConfetti={true}
        enableSound={true}
      />
    </>
  );
}
```

### SocialSharing

Social media sharing component with analytics tracking.

```typescript
interface SocialSharingProps {
  content: ShareContent;
  platforms?: string[];
  onShare?: (platform: string) => void;
  className?: string;
}
```

**Usage:**

```tsx
import { SocialSharing } from '@/components/social/SocialSharing';

function AchievementPage({ achievement }) {
  const shareContent = {
    title: `I just earned the ${achievement.name} badge!`,
    description: achievement.description,
    url: window.location.href,
    image: achievement.imageUrl
  };

  return (
    <SocialSharing
      content={shareContent}
      platforms={['twitter', 'linkedin', 'facebook']}
      onShare={(platform) => console.log(`Shared on ${platform}`)}
    />
  );
}
```

## Best Practices

### Component Development

1. **Use TypeScript**: All components should have proper type definitions
2. **Accessibility**: Follow WCAG 2.1 AA guidelines
3. **Performance**: Use React.memo for expensive components
4. **Testing**: Write comprehensive tests for all components
5. **Documentation**: Include JSDoc comments for all props

### Example Component Template

```tsx
import React, { memo } from 'react';
import { cn } from '@/lib/utils';

/**
 * Example component with proper TypeScript and documentation
 */
interface ExampleComponentProps {
  /** The main content to display */
  children: React.ReactNode;
  /** Optional CSS class name */
  className?: string;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Callback fired when clicked */
  onClick?: () => void;
  /** Test identifier for testing */
  'data-testid'?: string;
}

/**
 * ExampleComponent provides a template for creating new components
 * 
 * @example
 * ```tsx
 * <ExampleComponent onClick={() => console.log('clicked')}>
 *   Hello World
 * </ExampleComponent>
 * ```
 */
export const ExampleComponent = memo<ExampleComponentProps>(({
  children,
  className,
  disabled = false,
  onClick,
  'data-testid': testId
}) => {
  return (
    <div
      className={cn(
        'example-component',
        disabled && 'opacity-50 cursor-not-allowed',
        className
      )}
      onClick={disabled ? undefined : onClick}
      data-testid={testId}
      role="button"
      tabIndex={disabled ? -1 : 0}
      aria-disabled={disabled}
    >
      {children}
    </div>
  );
});

ExampleComponent.displayName = 'ExampleComponent';
```

### Testing Components

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ExampleComponent } from './ExampleComponent';

describe('ExampleComponent', () => {
  it('renders children correctly', () => {
    render(<ExampleComponent>Test Content</ExampleComponent>);
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('handles click events', () => {
    const handleClick = jest.fn();
    render(
      <ExampleComponent onClick={handleClick}>
        Click me
      </ExampleComponent>
    );
    
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('respects disabled state', () => {
    const handleClick = jest.fn();
    render(
      <ExampleComponent disabled onClick={handleClick}>
        Disabled
      </ExampleComponent>
    );
    
    fireEvent.click(screen.getByText('Disabled'));
    expect(handleClick).not.toHaveBeenCalled();
  });
});
```

## Component Index

For a complete list of all available components, see the [component index](./component-index.md) or check the `/components/index.ts` file for barrel exports.
