@tailwind base;
@tailwind components;
@tailwind utilities;

/* Critical CSS - Loaded inline for performance */

@layer base {
  :root {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-slate-800/50;
}

::-webkit-scrollbar-thumb {
  @apply bg-slate-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-slate-500;
}

/* Enhanced Focus Indicators for Accessibility */
.focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* High contrast focus indicators */
@media (prefers-contrast: high) {
  .focus-visible {
    outline: 3px solid #ffffff;
    outline-offset: 3px;
    background-color: rgba(59, 130, 246, 0.2);
  }
}

/* Focus indicators for interactive elements */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
}

/* Enhanced focus for glassmorphism elements */
.glass:focus-visible {
  outline: 2px solid #60a5fa;
  outline-offset: 2px;
  box-shadow:
    0 0 0 4px rgba(96, 165, 250, 0.3),
    0 8px 32px rgba(31, 38, 135, 0.37);
}

/* Skip link styling */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 9999;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Glassmorphism utilities */
.glass {
  @apply backdrop-blur-md bg-white/10 border border-white/20;
}

.glass-dark {
  @apply backdrop-blur-md bg-black/20 border border-white/10;
}

/* Neumorphism utilities */
.neomorphic {
  background: linear-gradient(145deg, #1e293b, #0f172a);
  box-shadow: 
    20px 20px 60px #0a0f1a,
    -20px -20px 60px #2a3441;
}

.neomorphic-inset {
  background: linear-gradient(145deg, #0f172a, #1e293b);
  box-shadow: 
    inset 20px 20px 60px #0a0f1a,
    inset -20px -20px 60px #2a3441;
}

/* Animation utilities */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
  }
  to {
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.8);
  }
}

/* Code editor styles */
.monaco-editor {
  @apply rounded-lg overflow-hidden;
}

.monaco-editor .margin {
  @apply bg-slate-800;
}

.monaco-editor .monaco-editor-background {
  @apply bg-slate-900;
}

/* Loading states */
.skeleton {
  @apply animate-pulse bg-slate-700/50 rounded;
}

/* Interactive elements */
.interactive-hover {
  @apply transition-all duration-300 hover:scale-105 hover:shadow-xl;
}

.interactive-press {
  @apply transition-all duration-150 active:scale-95;
}

/* Gradient text */
.gradient-text {
  @apply bg-gradient-to-r from-blue-400 via-purple-500 to-pink-500 bg-clip-text text-transparent;
}

/* Custom focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900;
}

/* Progress indicators */
.progress-glow {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.5);
}

/* Chat bubble styles */
.chat-bubble-user {
  @apply bg-blue-600 text-white rounded-2xl rounded-br-md;
}

.chat-bubble-ai {
  @apply bg-slate-700 text-white rounded-2xl rounded-bl-md;
}

.chat-bubble-system {
  @apply bg-yellow-600/20 text-yellow-200 rounded-lg border border-yellow-600/30;
}

/* Code syntax highlighting overrides */
.hljs {
  @apply bg-slate-900 text-slate-100;
}

.hljs-keyword {
  @apply text-purple-400;
}

.hljs-string {
  @apply text-green-400;
}

.hljs-number {
  @apply text-blue-400;
}

.hljs-comment {
  @apply text-slate-500;
}

.hljs-function {
  @apply text-yellow-400;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    @apply hidden;
  }
  
  .mobile-full {
    @apply w-full;
  }
}

/* Print styles */
@media print {
  .no-print {
    @apply hidden;
  }
}
