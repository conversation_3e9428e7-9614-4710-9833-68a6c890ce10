'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MessageCircle,
  X,
  Send,
  Sparkles,
  Bot,
  Minimize2,
  Maximize2,
  HelpCircle,
  Zap,
  BookOpen,
  Code,
  Lightbulb,
  Target,
  ChevronDown
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useXPNotifications } from '@/components/xp/XPNotification';
import { useMicroAchievements } from '@/components/gamification/MicroAchievements';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  isTyping?: boolean;
  suggestions?: string[];
  xpReward?: number;
}

interface ContextualHelp {
  id: string;
  trigger: 'scroll' | 'time' | 'interaction' | 'error';
  section: string;
  title: string;
  content: string;
  priority: 'low' | 'medium' | 'high';
  icon: React.ComponentType<{ className?: string }>;
  action?: string;
}

interface AIAssistantWidgetProps {
  className?: string;
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  initialMessage?: string;
  brandColor?: string;
  enableSound?: boolean;
  respectReducedMotion?: boolean;
  inactivityDelay?: number;
  enableContextualHelp?: boolean;
  enableGamification?: boolean;
}

/**
 * AI Assistant Chat Widget Integration
 * 
 * Floating chat widget with AI assistant branding, expandable interface, and accessibility support.
 * Features pulsing animation, keyboard navigation, and proper focus management.
 * 
 * @component
 * @example
 * ```tsx
 * <AIAssistantWidget 
 *   position="bottom-right"
 *   initialMessage="Hi! I'm your AI Solidity tutor. How can I help you today?"
 *   brandColor="purple"
 *   enableSound={true}
 * />
 * ```
 */
export function AIAssistantWidget({
  className = '',
  position = 'bottom-right',
  initialMessage = "Hi! I'm your AI Solidity tutor. How can I help you today?",
  brandColor = 'purple',
  enableSound = false,
  respectReducedMotion = true,
  inactivityDelay = 10000,
  enableContextualHelp = true,
  enableGamification = true
}: AIAssistantWidgetProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);
  const [showInactivityPrompt, setShowInactivityPrompt] = useState(false);
  const [contextualHelp, setContextualHelp] = useState<ContextualHelp | null>(null);
  const [userActivity, setUserActivity] = useState({
    lastInteraction: Date.now(),
    scrollPosition: 0,
    timeOnPage: 0
  });

  const inputRef = useRef<HTMLInputElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const widgetRef = useRef<HTMLDivElement>(null);
  const inactivityTimerRef = useRef<NodeJS.Timeout>();

  // Gamification hooks
  const { triggerXPGain } = useXPNotifications();
  const { triggerMicroAchievement } = useMicroAchievements();

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined'
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches
    : false;

  const shouldAnimate = respectReducedMotion ? !prefersReducedMotion : true;

  // Contextual help data
  const contextualHelpData: ContextualHelp[] = [
    {
      id: 'hero-scroll',
      trigger: 'scroll',
      section: 'hero',
      title: 'Getting Started with Solidity',
      content: 'New to Solidity? I can help you understand smart contracts and guide you through your first steps!',
      priority: 'high',
      icon: BookOpen,
      action: 'Start Learning'
    },
    {
      id: 'demo-interaction',
      trigger: 'interaction',
      section: 'demo',
      title: 'Interactive Learning',
      content: 'Try the interactive demo! I can explain each step and answer questions as you go.',
      priority: 'medium',
      icon: Code,
      action: 'Explain Demo'
    },
    {
      id: 'features-exploration',
      trigger: 'time',
      section: 'features',
      title: 'Explore Platform Features',
      content: 'Discover our comprehensive learning tools. What type of development interests you most?',
      priority: 'medium',
      icon: Lightbulb,
      action: 'Show Features'
    },
    {
      id: 'inactivity-help',
      trigger: 'time',
      section: 'general',
      title: 'Need Help Getting Started?',
      content: 'I notice you\'ve been here a while. Can I help you find the perfect learning path?',
      priority: 'low',
      icon: HelpCircle,
      action: 'Get Help'
    }
  ];

  // Initialize with welcome message
  useEffect(() => {
    const welcomeMessage: ChatMessage = {
      id: 'welcome-1',
      type: 'assistant',
      content: initialMessage,
      timestamp: new Date(),
      suggestions: ['What is Solidity?', 'Show me a demo', 'Help me get started', 'Explain smart contracts']
    };
    setMessages([welcomeMessage]);
    setUnreadCount(1);
  }, [initialMessage]);

  // Handle user activity tracking
  useEffect(() => {
    const handleActivity = () => {
      setUserActivity(prev => ({
        ...prev,
        lastInteraction: Date.now()
      }));
      setShowInactivityPrompt(false);
    };

    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setUserActivity(prev => ({
        ...prev,
        scrollPosition,
        lastInteraction: Date.now()
      }));

      // Trigger contextual help based on scroll position
      if (enableContextualHelp && scrollPosition > 100 && scrollPosition < 500) {
        const heroHelp = contextualHelpData.find(h => h.id === 'hero-scroll');
        if (heroHelp && !contextualHelp && !isExpanded) {
          setTimeout(() => setContextualHelp(heroHelp), 2000);
        }
      }
    };

    document.addEventListener('mousemove', handleActivity);
    document.addEventListener('keypress', handleActivity);
    document.addEventListener('click', handleActivity);
    document.addEventListener('scroll', handleScroll);

    return () => {
      document.removeEventListener('mousemove', handleActivity);
      document.removeEventListener('keypress', handleActivity);
      document.removeEventListener('click', handleActivity);
      document.removeEventListener('scroll', handleScroll);
    };
  }, [enableContextualHelp, contextualHelp, isExpanded, contextualHelpData]);

  // Inactivity detection
  useEffect(() => {
    const checkInactivity = () => {
      const timeSinceLastActivity = Date.now() - userActivity.lastInteraction;

      if (timeSinceLastActivity > inactivityDelay && !isExpanded && !showInactivityPrompt) {
        setShowInactivityPrompt(true);
        const inactivityHelp = contextualHelpData.find(h => h.id === 'inactivity-help');
        if (inactivityHelp) {
          setContextualHelp(inactivityHelp);
        }
      }
    };

    inactivityTimerRef.current = setInterval(checkInactivity, 2000);

    return () => {
      if (inactivityTimerRef.current) {
        clearInterval(inactivityTimerRef.current);
      }
    };
  }, [userActivity.lastInteraction, inactivityDelay, isExpanded, showInactivityPrompt, contextualHelpData]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current && isExpanded) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, isExpanded]);

  // Focus input when expanded
  useEffect(() => {
    if (isExpanded && inputRef.current) {
      setTimeout(() => {
        inputRef.current?.focus();
      }, 300);
    }
  }, [isExpanded]);

  // Quick response suggestions
  const quickResponses = [
    "How do I start learning Solidity?",
    "Explain smart contracts",
    "Show me a code example",
    "What's gas optimization?"
  ];

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'top-left':
        return 'top-4 left-4';
      default:
        return 'bottom-4 right-4';
    }
  };

  const getBrandColorClasses = () => {
    switch (brandColor) {
      case 'blue':
        return {
          gradient: 'from-blue-600 to-cyan-600',
          text: 'text-blue-400',
          bg: 'bg-blue-500/20'
        };
      case 'green':
        return {
          gradient: 'from-green-600 to-emerald-600',
          text: 'text-green-400',
          bg: 'bg-green-500/20'
        };
      default:
        return {
          gradient: 'from-purple-600 to-pink-600',
          text: 'text-purple-400',
          bg: 'bg-purple-500/20'
        };
    }
  };

  const colorClasses = getBrandColorClasses();

  const handleToggleExpanded = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      setUnreadCount(0);
      setIsMinimized(false);
    }
  };

  // Enhanced AI response generation
  const getAIResponse = (userMessage: string): ChatMessage => {
    const lowerMessage = userMessage.toLowerCase();

    let content = '';
    let suggestions: string[] = [];
    let xpReward = 0;

    if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
      content = "Hello! I'm your AI Solidity tutor. I'm here to help you master smart contract development. What would you like to learn today?";
      suggestions = ['What is Solidity?', 'Show me a demo', 'Explain smart contracts', 'Help me choose a course'];
      xpReward = 10;
    } else if (lowerMessage.includes('smart contract')) {
      content = "Smart contracts are self-executing contracts with terms directly written into code. They run on blockchain networks like Ethereum and automatically execute when conditions are met. Would you like to see an example?";
      suggestions = ['Show me an example', 'Explain gas fees', 'Security best practices', 'Deploy to testnet'];
      xpReward = 15;
    } else if (lowerMessage.includes('solidity')) {
      content = "Solidity is a programming language for writing smart contracts on Ethereum. It's statically typed and supports inheritance, libraries, and complex user-defined types. Ready to start coding?";
      suggestions = ['Basic syntax', 'Data types', 'Functions', 'Start tutorial'];
      xpReward = 15;
    } else if (lowerMessage.includes('gas')) {
      content = "Gas fees are payments for computational work on Ethereum. I can show you optimization techniques to reduce costs!";
      suggestions = ['Gas optimization', 'Estimate costs', 'Best practices', 'Tools for testing'];
      xpReward = 20;
    } else if (lowerMessage.includes('help') || lowerMessage.includes('stuck')) {
      content = "I'm here to help! What specific topic are you struggling with? I can explain concepts, show examples, or guide you through tutorials.";
      suggestions = ['Debugging tips', 'Common errors', 'Best practices', 'Find tutorials'];
      xpReward = 10;
    } else {
      content = "That's a great question! Let me help you explore that topic. What specific aspect interests you most?";
      suggestions = ['Browse tutorials', 'See examples', 'Join community', 'Get more help'];
      xpReward = 5;
    }

    return {
      id: `ai-${Date.now()}`,
      type: 'assistant',
      content,
      timestamp: new Date(),
      suggestions,
      xpReward
    };
  };

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: content.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI thinking time
    setTimeout(() => {
      const aiResponse = getAIResponse(content);
      setMessages(prev => [...prev, aiResponse]);
      setIsTyping(false);

      // Award XP for asking questions
      if (enableGamification && aiResponse.xpReward) {
        triggerXPGain(aiResponse.xpReward, 'ai_interaction', 'Asked AI assistant a question');
        triggerMicroAchievement('curious-learner');
      }

      if (!isExpanded) {
        setUnreadCount(prev => prev + 1);
      }
    }, 1000 + Math.random() * 2000);
  };

  const handleQuickResponse = (response: string) => {
    handleSendMessage(response);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage(inputValue);
    }
  };

  return (
    <>
      {/* Contextual Help Bubble */}
      <AnimatePresence>
        {contextualHelp && !isExpanded && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            className={cn(
              'fixed z-40 max-w-sm',
              position.includes('right') ? 'right-4' : 'left-4',
              position.includes('bottom') ? 'bottom-20' : 'top-20'
            )}
          >
            <div className="bg-gradient-to-br from-blue-600 to-purple-600 p-4 rounded-lg shadow-lg border border-white/20 backdrop-blur-sm">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                  <contextualHelp.icon className="w-4 h-4 text-white" />
                </div>
                <div className="flex-1">
                  <h4 className="font-semibold text-white text-sm mb-1">
                    {contextualHelp.title}
                  </h4>
                  <p className="text-white/90 text-xs leading-relaxed">
                    {contextualHelp.content}
                  </p>
                </div>
                <button
                  onClick={() => setContextualHelp(null)}
                  className="text-white/60 hover:text-white transition-colors"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              <div className="flex space-x-2 mt-3">
                <button
                  onClick={() => {
                    setIsExpanded(true);
                    setContextualHelp(null);
                    if (enableGamification) {
                      triggerMicroAchievement('help-seeker');
                    }
                  }}
                  className="px-3 py-1 bg-white/20 hover:bg-white/30 rounded text-white text-xs font-medium transition-colors"
                >
                  {contextualHelp.action || 'Ask AI'}
                </button>
                <button
                  onClick={() => setContextualHelp(null)}
                  className="px-3 py-1 text-white/80 hover:text-white text-xs transition-colors"
                >
                  Dismiss
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <div
        ref={widgetRef}
        className={cn(
          'fixed z-50 flex flex-col',
          getPositionClasses(),
          className
        )}
      >
      <AnimatePresence mode="wait">
        {isExpanded ? (
          <motion.div
            key="expanded"
            initial={shouldAnimate ? { opacity: 0, scale: 0.8, y: 20 } : {}}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={shouldAnimate ? { opacity: 0, scale: 0.8, y: 20 } : {}}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className={cn(
              'glass w-80 h-96 rounded-lg border border-white/20 backdrop-blur-md bg-white/10',
              'shadow-xl flex flex-col overflow-hidden',
              isMinimized && 'h-12'
            )}
            role="dialog"
            aria-label="AI Assistant Chat"
            aria-expanded={isExpanded}
          >
            {/* Header */}
            <div className={cn(
              'flex items-center justify-between p-4 border-b border-white/10',
              `bg-gradient-to-r ${colorClasses.gradient}`
            )}>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 rounded-full bg-white/20 flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-white">AI Assistant</h3>
                  <p className="text-xs text-white/80">
                    {isTyping ? 'Typing...' : 'Online'}
                  </p>
                </div>
              </div>
              
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="p-1 text-white/80 hover:text-white transition-colors"
                  aria-label={isMinimized ? "Restore chat" : "Minimize chat"}
                >
                  {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
                </button>
                <button
                  onClick={handleToggleExpanded}
                  className="p-1 text-white/80 hover:text-white transition-colors"
                  aria-label="Close chat"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {!isMinimized && (
              <>
                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-3">
                  {messages.map((message) => (
                    <MessageBubble
                      key={message.id}
                      message={message}
                      brandColor={brandColor}
                      onSuggestionClick={handleSendMessage}
                    />
                  ))}
                  
                  {isTyping && (
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 rounded-full bg-white/10 flex items-center justify-center">
                        <Bot className="w-3 h-3 text-gray-400" />
                      </div>
                      <div className="flex space-x-1">
                        {[0, 1, 2].map((i) => (
                          <motion.div
                            key={i}
                            className="w-2 h-2 bg-gray-400 rounded-full"
                            animate={{ scale: [1, 1.2, 1], opacity: [0.5, 1, 0.5] }}
                            transition={{ 
                              duration: 1, 
                              repeat: Infinity, 
                              delay: i * 0.2 
                            }}
                          />
                        ))}
                      </div>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>

                {/* Quick Responses */}
                {messages.length === 1 && (
                  <div className="px-4 pb-2">
                    <div className="flex flex-wrap gap-1">
                      {quickResponses.slice(0, 2).map((response, index) => (
                        <button
                          key={index}
                          onClick={() => handleQuickResponse(response)}
                          className="text-xs px-2 py-1 rounded-full bg-white/10 hover:bg-white/20 text-gray-300 hover:text-white transition-colors"
                        >
                          {response}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Input */}
                <div className="p-4 border-t border-white/10">
                  <div className="flex items-center space-x-2">
                    <input
                      ref={inputRef}
                      type="text"
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Ask me anything about Solidity..."
                      className="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-sm text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                      aria-label="Type your message"
                    />
                    <button
                      onClick={() => handleSendMessage(inputValue)}
                      disabled={!inputValue.trim()}
                      className={cn(
                        'p-2 rounded-lg transition-colors',
                        `bg-gradient-to-r ${colorClasses.gradient}`,
                        'hover:opacity-90 disabled:opacity-50 disabled:cursor-not-allowed'
                      )}
                      aria-label="Send message"
                    >
                      <Send className="w-4 h-4 text-white" />
                    </button>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        ) : (
          <motion.button
            key="collapsed"
            onClick={handleToggleExpanded}
            initial={shouldAnimate ? { opacity: 0, scale: 0.8 } : {}}
            animate={{ opacity: 1, scale: 1 }}
            exit={shouldAnimate ? { opacity: 0, scale: 0.8 } : {}}
            whileHover={shouldAnimate ? { scale: 1.05 } : {}}
            whileTap={shouldAnimate ? { scale: 0.95 } : {}}
            className={cn(
              'relative w-14 h-14 rounded-full shadow-lg',
              `bg-gradient-to-r ${colorClasses.gradient}`,
              'flex items-center justify-center group',
              'focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:ring-offset-transparent',
              showInactivityPrompt && 'animate-pulse ring-4 ring-blue-400/50'
            )}
            aria-label="Open AI Assistant Chat"
            aria-expanded={isExpanded}
          >
            <motion.div
              animate={showInactivityPrompt ? { scale: [1, 1.2, 1] } : {}}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <MessageCircle className="w-6 h-6 text-white" />
            </motion.div>
            
            {/* Pulsing animation */}
            {shouldAnimate && (
              <motion.div
                className={cn(
                  'absolute inset-0 rounded-full',
                  `bg-gradient-to-r ${colorClasses.gradient}`,
                  'opacity-75'
                )}
                animate={{ scale: [1, 1.2, 1], opacity: [0.75, 0.3, 0.75] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            )}

            {/* Unread count badge */}
            {unreadCount > 0 && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center"
              >
                <span className="text-xs font-bold text-white">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              </motion.div>
            )}

            {/* Help text tooltip */}
            <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
              <div className="bg-gray-900 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                Need help? Chat with our AI assistant
                <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
              </div>
            </div>
          </motion.button>
        )}
      </AnimatePresence>
    </div>
    </>
  );
}

/**
 * Individual Message Bubble Component
 */
interface MessageBubbleProps {
  message: ChatMessage;
  brandColor: string;
  onSuggestionClick?: (suggestion: string) => void;
}

function MessageBubble({ message, brandColor, onSuggestionClick }: MessageBubbleProps) {
  const isUser = message.type === 'user';
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        'flex',
        isUser ? 'justify-end' : 'justify-start'
      )}
    >
      <div className={cn(
        'max-w-[80%] px-3 py-2 rounded-lg text-sm',
        isUser 
          ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white ml-4'
          : 'bg-white/10 text-gray-100 mr-4'
      )}>
        {!isUser && (
          <div className="flex items-center space-x-1 mb-1">
            <Sparkles className="w-3 h-3 text-purple-400" />
            <span className="text-xs text-purple-400 font-medium">AI Assistant</span>
          </div>
        )}
        <div className="whitespace-pre-wrap">{message.content}</div>

        {/* Suggestions */}
        {message.suggestions && message.suggestions.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {message.suggestions.map((suggestion, index) => (
              <button
                key={index}
                onClick={() => onSuggestionClick?.(suggestion)}
                className="px-2 py-1 bg-white/20 hover:bg-white/30 rounded text-xs transition-colors border border-white/20 hover:border-white/40"
              >
                {suggestion}
              </button>
            ))}
          </div>
        )}

        <div className={cn(
          'text-xs mt-1 opacity-70',
          isUser ? 'text-right' : 'text-left'
        )}>
          {message.timestamp.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
          })}
        </div>
      </div>
    </motion.div>
  );
}
