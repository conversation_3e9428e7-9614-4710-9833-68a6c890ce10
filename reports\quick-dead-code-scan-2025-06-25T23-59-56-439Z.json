{"timestamp": "2025-06-25T23:59:56.439Z", "summary": {"totalFiles": 441, "unusedFiles": 92, "structureIssues": 3, "recommendations": 4}, "unusedFiles": {"source": [{"path": "components\\achievements\\AchievementNotificationSystem.tsx", "size": 15015}, {"path": "components\\achievements\\AchievementsPage.tsx", "size": 10586}, {"path": "components\\AchievementsPage.tsx", "size": 5544}, {"path": "components\\admin\\CommunityControls.tsx", "size": 18305}, {"path": "components\\admin\\ContentVersionControl.tsx", "size": 20877}, {"path": "components\\admin\\PerformanceDashboard.tsx", "size": 9650}, {"path": "components\\admin\\SafetyConfirmation.tsx", "size": 28648}, {"path": "components\\admin\\UserAnalytics.tsx", "size": 11710}, {"path": "components\\ai\\AICodeAnalyzer.tsx", "size": 19938}, {"path": "components\\ai\\AIContractGenerator.tsx", "size": 30417}, {"path": "components\\ai\\AILearningPath.tsx", "size": 20955}, {"path": "components\\ai\\EnhancedAIAssistant.tsx", "size": 18979}, {"path": "components\\auth\\EnhancedLoginModal.tsx", "size": 16576}, {"path": "components\\auth\\PasswordResetModal.tsx", "size": 18300}, {"path": "components\\auth\\PasswordStrengthIndicator.tsx", "size": 9026}, {"path": "components\\blockchain\\BlockchainIntegration.tsx", "size": 17051}, {"path": "components\\collaboration\\ComprehensiveCollaborationDashboard.tsx", "size": 19019}, {"path": "components\\ConfirmationModal.tsx", "size": 2044}, {"path": "components\\curriculum\\PrerequisiteDisplay.tsx", "size": 10687}, {"path": "components\\discovery\\SmartTooltip.tsx", "size": 11336}, {"path": "components\\editor\\AdvancedIDEInterface.tsx", "size": 27366}, {"path": "components\\error\\ErrorBoundaryFallback.tsx", "size": 2452}, {"path": "components\\error-handling\\AsyncErrorBoundary.tsx", "size": 10683}, {"path": "components\\forms\\ContactForm.tsx", "size": 9446}, {"path": "components\\GeminiChat.tsx", "size": 5837}, {"path": "components\\help\\ContextualTooltip.tsx", "size": 10267}, {"path": "components\\LandingPage.tsx", "size": 10332}, {"path": "components\\lazy\\LazyComponents.tsx", "size": 9571}, {"path": "components\\lazy\\LazyMonacoEditor.tsx", "size": 4853}, {"path": "components\\learning\\ComprehensiveLearningPlatform.tsx", "size": 27503}, {"path": "components\\MobileNavigation.tsx", "size": 5964}, {"path": "components\\ModuleContent.tsx", "size": 11718}, {"path": "components\\navigation\\AuthenticatedNavbar.tsx", "size": 9109}, {"path": "components\\navigation\\GuidedOnboarding.tsx", "size": 16762}, {"path": "components\\navigation\\NavigationFlowOptimizer.tsx", "size": 15871}, {"path": "components\\navigation\\SmartNavigation.tsx", "size": 13459}, {"path": "components\\onboarding\\InteractiveTutorial.tsx", "size": 10416}, {"path": "components\\progress\\ProgressDashboard.tsx", "size": 33012}, {"path": "components\\providers\\FallbackProvider.tsx", "size": 9366}, {"path": "components\\providers\\SessionProvider.tsx", "size": 510}, {"path": "components\\settings\\__tests__\\integration.test.tsx", "size": 12977}, {"path": "components\\settings\\__tests__\\ProfileSection.test.tsx", "size": 6698}, {"path": "components\\settings\\__tests__\\SecuritySection.test.tsx", "size": 10604}, {"path": "components\\settings\\__tests__\\SettingsPage.test.tsx", "size": 9316}, {"path": "components\\testing\\FeedbackCollectionSystem.tsx", "size": 18824}, {"path": "components\\testing\\NotificationTestingPage.tsx", "size": 16552}, {"path": "components\\testing\\UATDashboard.tsx", "size": 20107}, {"path": "components\\ui\\AdvancedAnimations.tsx", "size": 11013}, {"path": "components\\ui\\AnimatedButton.tsx", "size": 3155}, {"path": "components\\ui\\AnimationShowcase.tsx", "size": 11689}, {"path": "components\\ui\\Branding.tsx", "size": 10267}, {"path": "components\\ui\\EnhancedButton.stories.tsx", "size": 10888}, {"path": "components\\ui\\EnhancedCard.tsx", "size": 1609}, {"path": "components\\ui\\EnhancedLoadingStates.tsx", "size": 13385}, {"path": "components\\ui\\EnhancedProgress.tsx", "size": 10325}, {"path": "components\\ui\\ErrorHandling.tsx", "size": 9437}, {"path": "components\\ui\\FeatureState.tsx", "size": 12427}, {"path": "components\\ui\\GlassCard.stories.tsx", "size": 7524}, {"path": "components\\ui\\GlassmorphismModal.tsx", "size": 3387}, {"path": "components\\ui\\GlassNeumorphDemo.tsx", "size": 11797}, {"path": "components\\ui\\index.ts", "size": 2684}, {"path": "components\\ui\\LazyLoadingComponents.tsx", "size": 10119}, {"path": "components\\ui\\Neumorphism.tsx", "size": 11574}, {"path": "components\\ui\\NotificationCenter.tsx", "size": 16256}, {"path": "components\\ui\\OptimizedImage.tsx", "size": 7622}, {"path": "components\\ui\\PageTransition.tsx", "size": 5747}, {"path": "components\\ui\\SkeletonLoader.tsx", "size": 12240}, {"path": "components\\ui\\SVGAnimations.tsx", "size": 12396}, {"path": "components\\ui\\Typography.tsx", "size": 11898}, {"path": "components\\ui\\VisualFeedbackSystem.tsx", "size": 12988}, {"path": "lib\\analysis\\SolidityCodeAnalyzer.ts", "size": 18687}, {"path": "lib\\api\\documentation.ts", "size": 17143}, {"path": "lib\\api\\optimizedApiClient.ts", "size": 8873}, {"path": "lib\\auth\\navigationGuard.ts", "size": 10832}, {"path": "lib\\config\\secrets.ts", "size": 10458}, {"path": "lib\\editor\\SolidityLanguageDefinition.ts", "size": 17743}, {"path": "lib\\hooks\\useEnhancedKeyboardNavigation.ts", "size": 8448}, {"path": "lib\\hooks\\useKeyboardNavigation.ts", "size": 8068}, {"path": "lib\\hooks\\useNotificationIntegrations.ts", "size": 11263}, {"path": "lib\\hooks\\useSolidityAnalyzer.ts", "size": 11431}, {"path": "lib\\hooks\\__tests__\\useSettings.test.ts", "size": 9920}, {"path": "lib\\security\\headers.ts", "size": 12547}, {"path": "lib\\testing\\ux-testing.ts", "size": 20925}, {"path": "lib\\theme\\ThemeProvider.tsx", "size": 13823}, {"path": "lib\\utils\\assetOptimization.ts", "size": 7786}, {"path": "lib\\utils\\cssOptimization.ts", "size": 9276}, {"path": "hooks\\useLoadingState.ts", "size": 5942}, {"path": "hooks\\usePerformance.ts", "size": 9204}, {"path": "hooks\\useProgress.ts", "size": 1856}, {"path": "types\\global.d.ts", "size": 1585}, {"path": "types\\next-auth.d.ts", "size": 484}], "assets": [{"path": "public\\favicon.svg", "size": 227}]}, "structure": {"issues": ["Route api missing page file", "Route auth missing page file", "Route ping missing page file"], "recommendations": ["Add index.ts to components for cleaner imports", "Add index.ts to lib for cleaner imports", "Add index.ts to hooks for cleaner imports", "Add index.ts to utils for cleaner imports"]}, "potentialSavings": {"files": 92, "sizeKB": 1066}}