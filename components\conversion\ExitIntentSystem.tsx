'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Star, 
  Users, 
  Trophy, 
  Clock, 
  CheckCircle, 
  ArrowRight,
  Gift,
  Zap,
  Shield,
  TrendingUp,
  Play
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import { useProgressTracking } from '@/components/progress/ProgressTrackingSystem';

interface ExitIntentOffer {
  id: string;
  type: 'discount' | 'trial_extension' | 'bonus_content' | 'achievement_unlock';
  title: string;
  subtitle: string;
  description: string;
  value: string;
  urgency: string;
  ctaText: string;
  ctaSecondary?: string;
  conditions?: string[];
  validUntil?: Date;
}

interface UserBehaviorData {
  timeOnSite: number;
  pagesVisited: number;
  scrollDepth: number;
  engagementScore: number;
  lastActivity: Date;
  hasStartedAssessment: boolean;
  hasViewedPricing: boolean;
  hasWatchedDemo: boolean;
  currentLearningPath?: string;
  completionPercentage: number;
  achievementsEarned: number;
  isReturningUser: boolean;
  previousExitIntents: number;
}

interface SocialProofData {
  totalUsers: number;
  recentSignups: number;
  averageRating: number;
  successStories: number;
  liveUserCount: number;
  recentAchievements: Array<{
    user: string;
    achievement: string;
    timestamp: Date;
  }>;
}

export function ExitIntentSystem({ 
  className,
  onConversion,
  onDismiss 
}: { 
  className?: string;
  onConversion?: (offerId: string) => void;
  onDismiss?: () => void;
}) {
  const [isActive, setIsActive] = useState(false);
  const [currentOffer, setCurrentOffer] = useState<ExitIntentOffer | null>(null);
  const [userBehavior, setUserBehavior] = useState<UserBehaviorData | null>(null);
  const [socialProof, setSocialProof] = useState<SocialProofData | null>(null);
  const [hasTriggered, setHasTriggered] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [liveCounter, setLiveCounter] = useState(12547);
  const exitIntentRef = useRef<boolean>(false);
  const { userProgress } = useProgressTracking();

  // Mock social proof data
  const mockSocialProof: SocialProofData = {
    totalUsers: 12547,
    recentSignups: 23,
    averageRating: 4.9,
    successStories: 1834,
    liveUserCount: 156,
    recentAchievements: [
      { user: 'Sarah K.', achievement: 'Smart Contract Master', timestamp: new Date(Date.now() - 2 * 60 * 1000) },
      { user: 'Alex M.', achievement: 'DeFi Expert', timestamp: new Date(Date.now() - 5 * 60 * 1000) },
      { user: 'Maria R.', achievement: 'Security Specialist', timestamp: new Date(Date.now() - 8 * 60 * 1000) }
    ]
  };

  // Generate personalized offer based on user behavior
  const generatePersonalizedOffer = useCallback((behavior: UserBehaviorData): ExitIntentOffer => {
    const offers: ExitIntentOffer[] = [
      {
        id: 'first-time-discount',
        type: 'discount',
        title: 'Wait! Don\'t Miss Out on Your Blockchain Future',
        subtitle: 'Complete your Solidity assessment and get 50% off',
        description: 'Join 12,547+ developers who chose our platform to launch their blockchain careers. Limited time offer for new learners.',
        value: '50% OFF',
        urgency: 'Offer expires in 15 minutes',
        ctaText: 'Claim 50% Discount',
        ctaSecondary: 'Continue Assessment',
        conditions: ['Valid for first-time users', 'Must complete skill assessment', 'Cannot be combined with other offers']
      },
      {
        id: 'achievement-unlock',
        type: 'achievement_unlock',
        title: 'You\'re So Close to Your First Achievement!',
        subtitle: `You're ${behavior.completionPercentage > 0 ? '1 lesson' : '1 assessment'} away from earning your first Smart Contract badge`,
        description: 'Don\'t lose your progress! Complete your current milestone and unlock exclusive developer resources.',
        value: 'FREE BADGE',
        urgency: 'Progress expires in 24 hours',
        ctaText: 'Complete & Earn Badge',
        ctaSecondary: 'Save Progress',
        conditions: ['Complete current lesson', 'Unlock exclusive resources', 'Join developer community']
      },
      {
        id: 'trial-extension',
        type: 'trial_extension',
        title: 'Extend Your Learning Journey',
        subtitle: 'Get 7 extra days to explore our complete curriculum',
        description: 'You\'ve made great progress! Continue learning with full access to all courses, mentoring, and job placement support.',
        value: '+7 DAYS FREE',
        urgency: 'Limited time extension offer',
        ctaText: 'Extend Free Trial',
        ctaSecondary: 'Continue Learning',
        conditions: ['No credit card required', 'Full platform access', 'Cancel anytime']
      },
      {
        id: 'bonus-content',
        type: 'bonus_content',
        title: 'Unlock Exclusive Blockchain Developer Kit',
        subtitle: 'Get premium resources worth $297 absolutely free',
        description: 'Access our complete developer toolkit: smart contract templates, security checklists, and direct hiring partner connections.',
        value: '$297 VALUE',
        urgency: 'Available for next 50 users only',
        ctaText: 'Get Free Developer Kit',
        ctaSecondary: 'Learn More',
        conditions: ['Instant download', 'Lifetime access', 'Regular updates included']
      }
    ];

    // Select offer based on user behavior
    if (behavior.hasStartedAssessment && behavior.completionPercentage < 100) {
      return offers[1]; // Achievement unlock
    } else if (behavior.isReturningUser && behavior.timeOnSite > 300) {
      return offers[2]; // Trial extension
    } else if (behavior.hasViewedPricing) {
      return offers[0]; // Discount
    } else {
      return offers[3]; // Bonus content
    }
  }, []);

  // Track user behavior
  useEffect(() => {
    const trackBehavior = () => {
      const behavior: UserBehaviorData = {
        timeOnSite: Date.now() - (parseInt(sessionStorage.getItem('session_start') || '0') || Date.now()),
        pagesVisited: parseInt(sessionStorage.getItem('pages_visited') || '1'),
        scrollDepth: Math.max(0, window.scrollY / (document.body.scrollHeight - window.innerHeight) * 100),
        engagementScore: 0, // Calculate based on interactions
        lastActivity: new Date(),
        hasStartedAssessment: localStorage.getItem('assessment_started') === 'true',
        hasViewedPricing: sessionStorage.getItem('viewed_pricing') === 'true',
        hasWatchedDemo: sessionStorage.getItem('watched_demo') === 'true',
        currentLearningPath: localStorage.getItem('current_learning_path') || undefined,
        completionPercentage: userProgress?.overallProgress || 0,
        achievementsEarned: userProgress?.achievements?.length || 0,
        isReturningUser: localStorage.getItem('returning_user') === 'true',
        previousExitIntents: parseInt(localStorage.getItem('exit_intents') || '0')
      };

      setUserBehavior(behavior);
    };

    // Track session start
    if (!sessionStorage.getItem('session_start')) {
      sessionStorage.setItem('session_start', Date.now().toString());
    }

    // Track page visit
    const currentPages = parseInt(sessionStorage.getItem('pages_visited') || '0');
    sessionStorage.setItem('pages_visited', (currentPages + 1).toString());

    // Set returning user flag
    if (localStorage.getItem('first_visit')) {
      localStorage.setItem('returning_user', 'true');
    } else {
      localStorage.setItem('first_visit', 'true');
    }

    trackBehavior();
    setSocialProof(mockSocialProof);

    // Update behavior periodically
    const interval = setInterval(trackBehavior, 5000);
    return () => clearInterval(interval);
  }, [userProgress]);

  // Exit intent detection
  useEffect(() => {
    let exitIntentTimeout: NodeJS.Timeout;

    const handleMouseLeave = (e: MouseEvent) => {
      // Only trigger if mouse leaves from top of viewport (indicating tab close/navigation)
      if (e.clientY <= 0 && !exitIntentRef.current && !hasTriggered && userBehavior) {
        // Don't show if user has seen too many exit intents
        if (userBehavior.previousExitIntents >= 3) return;

        // Don't show immediately - wait for user to be engaged
        if (userBehavior.timeOnSite < 30000) return; // 30 seconds minimum

        exitIntentRef.current = true;
        setHasTriggered(true);

        // Generate and show personalized offer
        const offer = generatePersonalizedOffer(userBehavior);
        setCurrentOffer(offer);
        setIsActive(true);

        // Track exit intent
        const exitIntents = userBehavior.previousExitIntents + 1;
        localStorage.setItem('exit_intents', exitIntents.toString());

        // Reset after delay to allow multiple triggers per session (but limited)
        exitIntentTimeout = setTimeout(() => {
          exitIntentRef.current = false;
        }, 60000); // 1 minute cooldown
      }
    };

    // Mobile exit intent detection (less reliable)
    const handleVisibilityChange = () => {
      if (document.hidden && !exitIntentRef.current && !hasTriggered && userBehavior) {
        if (userBehavior.previousExitIntents >= 3) return;
        if (userBehavior.timeOnSite < 30000) return;

        // Only trigger on mobile
        if (window.innerWidth <= 768) {
          exitIntentRef.current = true;
          setHasTriggered(true);

          const offer = generatePersonalizedOffer(userBehavior);
          setCurrentOffer(offer);
          setIsActive(true);

          const exitIntents = userBehavior.previousExitIntents + 1;
          localStorage.setItem('exit_intents', exitIntents.toString());
        }
      }
    };

    document.addEventListener('mouseleave', handleMouseLeave);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('mouseleave', handleMouseLeave);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (exitIntentTimeout) clearTimeout(exitIntentTimeout);
    };
  }, [hasTriggered, userBehavior, generatePersonalizedOffer]);

  // Live counter animation
  useEffect(() => {
    if (!isActive) return;

    const interval = setInterval(() => {
      setLiveCounter(prev => prev + Math.floor(Math.random() * 3));
    }, 8000);

    return () => clearInterval(interval);
  }, [isActive]);

  // Show modal with delay for animation
  useEffect(() => {
    if (isActive) {
      setTimeout(() => setIsVisible(true), 100);
    }
  }, [isActive]);

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(() => {
      setIsActive(false);
      onDismiss?.();
    }, 300);
  };

  const handleConversion = (offerId: string) => {
    // Track conversion
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exit_intent_conversion', {
        offer_id: offerId,
        user_behavior: userBehavior,
        timestamp: new Date().toISOString()
      });
    }

    onConversion?.(offerId);
    handleClose();
  };

  const getOfferIcon = (type: string) => {
    switch (type) {
      case 'discount':
        return Gift;
      case 'trial_extension':
        return Clock;
      case 'bonus_content':
        return Star;
      case 'achievement_unlock':
        return Trophy;
      default:
        return Gift;
    }
  };

  const getOfferColor = (type: string) => {
    switch (type) {
      case 'discount':
        return 'from-green-600 to-emerald-600 border-green-500';
      case 'trial_extension':
        return 'from-blue-600 to-indigo-600 border-blue-500';
      case 'bonus_content':
        return 'from-purple-600 to-pink-600 border-purple-500';
      case 'achievement_unlock':
        return 'from-yellow-600 to-orange-600 border-yellow-500';
      default:
        return 'from-gray-600 to-gray-800 border-gray-500';
    }
  };

  if (!isActive || !currentOffer || !socialProof) return null;

  const OfferIcon = getOfferIcon(currentOffer.type);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed inset-0 z-[9999] flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          role="dialog"
          aria-modal="true"
          aria-labelledby="exit-intent-title"
          aria-describedby="exit-intent-description"
        >
          <motion.div
            className={cn(
              'relative max-w-2xl w-full bg-gradient-to-br rounded-2xl shadow-2xl overflow-hidden border-2',
              getOfferColor(currentOffer.type),
              className
            )}
            initial={{ scale: 0.8, opacity: 0, y: 50 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.8, opacity: 0, y: 50 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            {/* Close Button */}
            <button
              onClick={handleClose}
              className="absolute top-4 right-4 z-10 p-2 bg-black/20 hover:bg-black/40 rounded-full text-white transition-colors focus:outline-none focus:ring-2 focus:ring-white/50"
              aria-label="Close offer"
            >
              <X className="w-5 h-5" />
            </button>

            {/* Header with Icon */}
            <div className="relative p-8 pb-6">
              <div className="flex items-center justify-center mb-6">
                <div className="p-4 bg-white/20 rounded-full">
                  <OfferIcon className="w-12 h-12 text-white" />
                </div>
              </div>

              <h2 id="exit-intent-title" className="text-3xl font-bold text-white text-center mb-3">
                {currentOffer.title}
              </h2>
              <p className="text-xl text-white/90 text-center mb-4">
                {currentOffer.subtitle}
              </p>
              <p id="exit-intent-description" className="text-white/80 text-center leading-relaxed">
                {currentOffer.description}
              </p>
            </div>

            {/* Value Proposition */}
            <div className="px-8 pb-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-center">
                <div className="text-4xl font-bold text-white mb-2">{currentOffer.value}</div>
                <div className="text-white/80 text-sm">{currentOffer.urgency}</div>
              </div>
            </div>

            {/* Social Proof */}
            <div className="px-8 pb-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="flex items-center justify-center space-x-1 mb-1">
                    <Users className="w-4 h-4 text-white" />
                    <span className="text-2xl font-bold text-white">{liveCounter.toLocaleString()}+</span>
                  </div>
                  <div className="text-white/80 text-sm">Developers Trust Us</div>
                </div>
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="flex items-center justify-center space-x-1 mb-1">
                    <Star className="w-4 h-4 text-yellow-400 fill-current" />
                    <span className="text-2xl font-bold text-white">{socialProof.averageRating}</span>
                  </div>
                  <div className="text-white/80 text-sm">Average Rating</div>
                </div>
                <div className="bg-white/10 rounded-lg p-4">
                  <div className="flex items-center justify-center space-x-1 mb-1">
                    <TrendingUp className="w-4 h-4 text-green-400" />
                    <span className="text-2xl font-bold text-white">{socialProof.successStories}</span>
                  </div>
                  <div className="text-white/80 text-sm">Success Stories</div>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="px-8 pb-6">
              <div className="bg-white/10 rounded-lg p-4">
                <h3 className="text-white font-medium mb-3 text-center">Recent Activity</h3>
                <div className="space-y-2">
                  {socialProof.recentAchievements.slice(0, 2).map((activity, index) => (
                    <motion.div
                      key={index}
                      className="flex items-center space-x-3 text-sm"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.2 }}
                    >
                      <CheckCircle className="w-4 h-4 text-green-400 flex-shrink-0" />
                      <span className="text-white/90">
                        <strong>{activity.user}</strong> earned "{activity.achievement}" 
                        <span className="text-white/60 ml-1">
                          {Math.floor((Date.now() - activity.timestamp.getTime()) / 60000)}m ago
                        </span>
                      </span>
                    </motion.div>
                  ))}
                </div>
                <div className="text-center mt-3">
                  <span className="text-white/60 text-xs">
                    {socialProof.recentSignups} developers joined in the last hour
                  </span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="px-8 pb-8">
              <div className="space-y-3">
                <EnhancedButton
                  onClick={() => handleConversion(currentOffer.id)}
                  className="w-full bg-white text-gray-900 hover:bg-gray-100 font-semibold text-lg py-4"
                  touchTarget
                >
                  <OfferIcon className="w-5 h-5 mr-2" />
                  {currentOffer.ctaText}
                  <ArrowRight className="w-5 h-5 ml-2" />
                </EnhancedButton>
                
                {currentOffer.ctaSecondary && (
                  <EnhancedButton
                    onClick={() => handleConversion(`${currentOffer.id}_secondary`)}
                    variant="ghost"
                    className="w-full text-white border-white/30 hover:bg-white/10"
                    touchTarget
                  >
                    {currentOffer.ctaSecondary}
                  </EnhancedButton>
                )}
              </div>

              {/* Conditions */}
              {currentOffer.conditions && (
                <div className="mt-4 text-center">
                  <div className="text-white/60 text-xs space-y-1">
                    {currentOffer.conditions.map((condition, index) => (
                      <div key={index}>• {condition}</div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Trust Indicators */}
            <div className="bg-black/20 px-8 py-4">
              <div className="flex items-center justify-center space-x-6 text-white/60 text-xs">
                <div className="flex items-center space-x-1">
                  <Shield className="w-3 h-3" />
                  <span>Secure Checkout</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-3 h-3" />
                  <span>Instant Access</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CheckCircle className="w-3 h-3" />
                  <span>30-Day Guarantee</span>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
