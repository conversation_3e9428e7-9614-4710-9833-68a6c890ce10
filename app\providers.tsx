'use client';

import { SessionProvider } from 'next-auth/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { SocketProvider } from '@/lib/socket/SocketProvider';
import { LearningProvider } from '@/lib/context/LearningContext';
import { CollaborationProvider } from '@/lib/context/CollaborationContext';
import { Web3Provider } from '@/lib/blockchain/Web3Provider';
import dynamic from 'next/dynamic';

// Dynamically import React Query Devtools only in development
const ReactQueryDevtools = dynamic(
  () => import('@tanstack/react-query-devtools').then(mod => ({ default: mod.ReactQueryDevtools })),
  { ssr: false }
);

// Create QueryClient outside of component for static export compatibility
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 60 * 1000, // 1 minute
      retry: 1,
    },
  },
});

export function Providers({ children }: { children: React.ReactNode }) {

  return (
    <QueryClientProvider client={queryClient}>
      <SessionProvider>
        <Web3Provider>
          <SocketProvider>
            <LearningProvider>
              <CollaborationProvider>
                {children}
              </CollaborationProvider>
            </LearningProvider>
          </SocketProvider>
        </Web3Provider>
      </SessionProvider>
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  );
}
