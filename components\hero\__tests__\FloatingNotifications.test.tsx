/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { FloatingNotifications } from '../FloatingNotifications';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => <div>{children}</div>,
}));

// Mock reduced motion
const mockMatchMedia = jest.fn();
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: mockMatchMedia,
});

describe('FloatingNotifications', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    mockMatchMedia.mockReturnValue({
      matches: false,
      media: '',
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    });
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders with default props', () => {
    render(<FloatingNotifications />);
    
    const container = screen.getByRole('region', { name: 'Live notifications' });
    expect(container).toBeInTheDocument();
    expect(container).toHaveClass('top-4', 'right-4');
  });

  it('applies correct position classes', () => {
    const { rerender } = render(<FloatingNotifications position="top-left" />);
    let container = screen.getByRole('region');
    expect(container).toHaveClass('top-4', 'left-4');

    rerender(<FloatingNotifications position="bottom-right" />);
    container = screen.getByRole('region');
    expect(container).toHaveClass('bottom-4', 'right-4');

    rerender(<FloatingNotifications position="bottom-left" />);
    container = screen.getByRole('region');
    expect(container).toHaveClass('bottom-4', 'left-4');
  });

  it('displays initial notifications', async () => {
    render(<FloatingNotifications />);
    
    await waitFor(() => {
      expect(screen.getByText(/people started learning/)).toBeInTheDocument();
    });
  });

  it('auto-rotates notifications', async () => {
    render(<FloatingNotifications autoRotateInterval={1000} />);
    
    // Wait for initial notifications to load
    await waitFor(() => {
      expect(screen.getByText(/people started learning/)).toBeInTheDocument();
    });

    // Fast-forward time to trigger rotation
    act(() => {
      jest.advanceTimersByTime(1000);
    });

    await waitFor(() => {
      expect(screen.getByText(/Sarah just deployed/)).toBeInTheDocument();
    });
  });

  it('pauses rotation on mouse enter', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<FloatingNotifications autoRotateInterval={1000} />);
    
    const container = screen.getByRole('region');
    
    // Hover over container
    await user.hover(container);
    
    // Advance time - rotation should be paused
    act(() => {
      jest.advanceTimersByTime(2000);
    });
    
    // Should still show initial notification
    expect(screen.getByText(/people started learning/)).toBeInTheDocument();
  });

  it('resumes rotation on mouse leave', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    render(<FloatingNotifications autoRotateInterval={1000} />);
    
    const container = screen.getByRole('region');
    
    // Hover and unhover
    await user.hover(container);
    await user.unhover(container);
    
    // Advance time - rotation should resume
    act(() => {
      jest.advanceTimersByTime(1000);
    });
    
    await waitFor(() => {
      expect(screen.getByText(/Sarah just deployed/)).toBeInTheDocument();
    });
  });

  it('dismisses notifications when clicked', async () => {
    const user = userEvent.setup();
    
    render(<FloatingNotifications />);
    
    await waitFor(() => {
      expect(screen.getByText(/people started learning/)).toBeInTheDocument();
    });

    const notification = screen.getByRole('alert');
    await user.click(notification);
    
    await waitFor(() => {
      expect(screen.queryByText(/people started learning/)).not.toBeInTheDocument();
    });
  });

  it('dismisses notifications with close button', async () => {
    const user = userEvent.setup();
    
    render(<FloatingNotifications />);
    
    await waitFor(() => {
      expect(screen.getByText(/people started learning/)).toBeInTheDocument();
    });

    const closeButton = screen.getByLabelText('Dismiss notification');
    await user.click(closeButton);
    
    await waitFor(() => {
      expect(screen.queryByText(/people started learning/)).not.toBeInTheDocument();
    });
  });

  it('respects maxVisible prop', async () => {
    render(<FloatingNotifications maxVisible={2} />);
    
    await waitFor(() => {
      const notifications = screen.getAllByRole('alert');
      expect(notifications).toHaveLength(2);
    });
  });

  it('handles reduced motion preference', () => {
    mockMatchMedia.mockReturnValue({
      matches: true, // prefers-reduced-motion: reduce
      media: '(prefers-reduced-motion: reduce)',
      onchange: null,
      addListener: jest.fn(),
      removeListener: jest.fn(),
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    });

    render(<FloatingNotifications respectReducedMotion={true} />);
    
    // Should still render but without animations
    const container = screen.getByRole('region');
    expect(container).toBeInTheDocument();
  });

  it('displays priority indicators for high priority notifications', async () => {
    render(<FloatingNotifications />);
    
    await waitFor(() => {
      // Look for high priority notification (should have red indicator)
      const highPriorityNotification = screen.getByText(/people started learning/);
      expect(highPriorityNotification).toBeInTheDocument();
    });
  });

  it('shows timestamps for notifications', async () => {
    render(<FloatingNotifications />);
    
    await waitFor(() => {
      // Check for timestamp format (HH:MM)
      const timestamp = screen.getByText(/\d{1,2}:\d{2}/);
      expect(timestamp).toBeInTheDocument();
    });
  });

  it('adds new notifications over time', async () => {
    render(<FloatingNotifications />);
    
    // Wait for initial notifications
    await waitFor(() => {
      expect(screen.getByText(/people started learning/)).toBeInTheDocument();
    });

    // Fast-forward to trigger new notification
    act(() => {
      jest.advanceTimersByTime(20000); // Trigger random notification
    });

    // Should have new notifications
    await waitFor(() => {
      const notifications = screen.getAllByRole('alert');
      expect(notifications.length).toBeGreaterThan(0);
    });
  });

  it('has proper accessibility attributes', async () => {
    render(<FloatingNotifications />);
    
    const container = screen.getByRole('region', { name: 'Live notifications' });
    expect(container).toHaveAttribute('aria-live', 'polite');

    await waitFor(() => {
      const notification = screen.getByRole('alert');
      expect(notification).toHaveAttribute('aria-live', 'assertive');
    });
  });

  it('applies custom className', () => {
    render(<FloatingNotifications className="custom-notifications" />);
    
    const container = screen.getByRole('region');
    expect(container).toHaveClass('custom-notifications');
  });

  it('handles notification auto-dismiss', async () => {
    render(<FloatingNotifications />);
    
    await waitFor(() => {
      expect(screen.getByText(/people started learning/)).toBeInTheDocument();
    });

    // Fast-forward past notification duration
    act(() => {
      jest.advanceTimersByTime(6000); // Longer than notification duration
    });

    await waitFor(() => {
      // Notification should be auto-dismissed
      expect(screen.queryByText(/people started learning/)).not.toBeInTheDocument();
    });
  });

  it('prevents event bubbling on close button click', async () => {
    const user = userEvent.setup();
    const mockClick = jest.fn();
    
    render(
      <div onClick={mockClick}>
        <FloatingNotifications />
      </div>
    );
    
    await waitFor(() => {
      expect(screen.getByText(/people started learning/)).toBeInTheDocument();
    });

    const closeButton = screen.getByLabelText('Dismiss notification');
    await user.click(closeButton);
    
    // Parent click handler should not be called
    expect(mockClick).not.toHaveBeenCalled();
  });

  it('cleans up timers on unmount', () => {
    const { unmount } = render(<FloatingNotifications />);
    
    // Should not throw errors when unmounting
    expect(() => unmount()).not.toThrow();
  });

  it('handles empty notification state gracefully', () => {
    render(<FloatingNotifications maxVisible={0} />);
    
    const container = screen.getByRole('region');
    expect(container).toBeInTheDocument();
    expect(container).toBeEmptyDOMElement();
  });
});
