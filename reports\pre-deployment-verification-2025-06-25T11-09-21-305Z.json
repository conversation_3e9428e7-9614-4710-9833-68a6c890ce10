{"timestamp": "2025-06-25T11:09:21.324Z", "results": {"filesExist": true, "packageValid": true, "envValid": true, "buildChecks": false, "readinessConfirmed": false, "checklistComplete": false}, "success": false, "config": {"requiredFiles": ["package.json", "next.config.js", "tsconfig.json", "tailwind.config.js", "docs/production-readiness-assessment.md", "DEPLOYMENT_CHECKLIST.md"], "requiredEnvVars": ["DATABASE_URL", "NEXTAUTH_SECRET", "NEXTAUTH_URL"], "testCommands": [{"name": "TypeScript Check", "command": "npx tsc --noEmit"}, {"name": "ESLint Check", "command": "npx eslint . --ext .ts,.tsx --max-warnings 0"}, {"name": "Build Test", "command": "npm run build"}], "performanceTargets": {"buildTime": 300000, "bundleSize": 5242880, "dependencies": 1000}}}