/* Eye-Friendly UI Design System */

:root {
  /* Base font settings for readability */
  --base-font-size: 16px;
  --base-line-height: 1.5;
  --base-letter-spacing: 0;
  
  /* Animation durations (can be overridden for reduced motion) */
  --animation-duration: 0.3s;
  --transition-duration: 0.2s;
  
  /* Eye-friendly color palette */
  --warm-white: #fefefe;
  --soft-black: #1a1a1a;
  --gentle-gray: #f8f9fa;
  --muted-blue: #4a90e2;
  --calm-green: #27ae60;
  --warm-yellow: #f39c12;
  --soft-red: #e74c3c;
  
  /* High contrast alternatives */
  --hc-background: #000000;
  --hc-foreground: #ffffff;
  --hc-primary: #ffff00;
  --hc-secondary: #00ffff;
  --hc-accent: #ff00ff;
}

/* Base typography for eye comfort */
body {
  font-size: var(--base-font-size);
  line-height: var(--base-line-height);
  letter-spacing: var(--base-letter-spacing);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Smooth transitions for theme changes */
* {
  transition: 
    background-color var(--transition-duration) ease,
    border-color var(--transition-duration) ease,
    color var(--transition-duration) ease,
    box-shadow var(--transition-duration) ease;
}

/* Light theme optimizations */
.light {
  --background: 0 0% 100%;
  --foreground: 0 0% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 3.9%;
  --primary: 221.2 83.2% 53.3%;
  --primary-foreground: 210 40% 98%;
  --secondary: 210 40% 96%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 96%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 96%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
  
  /* Light theme specific optimizations */
  background-color: #fefefe;
  color: #2c3e50;
}

.light .glass {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.light .text-shadow {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Dark theme optimizations */
.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 217.2 91.2% 59.8%;
  --primary-foreground: 222.2 84% 4.9%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 224.3 76.3% 94.1%;
  
  /* Dark theme specific optimizations */
  background-color: #0f1419;
  color: #e6e6e6;
}

.dark .glass {
  background: rgba(15, 20, 25, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
}

.dark .text-shadow {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* High contrast mode */
.high-contrast {
  --background: var(--hc-background);
  --foreground: var(--hc-foreground);
  --primary: var(--hc-primary);
  --secondary: var(--hc-secondary);
  --accent: var(--hc-accent);
}

.high-contrast * {
  border-color: var(--hc-foreground) !important;
  outline: 2px solid var(--hc-foreground) !important;
  outline-offset: 2px !important;
}

.high-contrast .glass {
  background: var(--hc-background) !important;
  border: 2px solid var(--hc-foreground) !important;
  backdrop-filter: none !important;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus indicators for accessibility */
*:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

.focus-visible:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Enhanced readability for code blocks */
pre, code {
  font-family: 'JetBrains Mono', 'Fira Code', 'Cascadia Code', Consolas, monospace;
  font-feature-settings: 'liga' 1, 'calt' 1;
  line-height: 1.6;
  letter-spacing: 0.025em;
}

/* Improved form elements */
input, textarea, select {
  font-size: var(--base-font-size);
  line-height: var(--base-line-height);
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--border));
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  transition: all var(--transition-duration) ease;
}

input:focus, textarea:focus, select:focus {
  border-color: hsl(var(--ring));
  box-shadow: 0 0 0 3px hsla(var(--ring), 0.1);
}

/* Button improvements */
button {
  font-size: var(--base-font-size);
  line-height: var(--base-line-height);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all var(--transition-duration) ease;
  cursor: pointer;
  border: none;
  position: relative;
  overflow: hidden;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:active {
  transform: translateY(0);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Card and container improvements */
.card {
  border-radius: 0.75rem;
  border: 1px solid hsl(var(--border));
  background-color: hsl(var(--card));
  color: hsl(var(--card-foreground));
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-duration) ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Improved spacing for readability */
.prose {
  max-width: 65ch;
  line-height: 1.7;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  line-height: 1.3;
  margin-top: 2em;
  margin-bottom: 0.5em;
  font-weight: 600;
}

.prose p {
  margin-bottom: 1.25em;
}

.prose ul, .prose ol {
  margin-bottom: 1.25em;
  padding-left: 1.5em;
}

.prose li {
  margin-bottom: 0.5em;
}

/* Loading states */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: loading-shimmer 1.5s infinite;
}

@keyframes loading-shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* Error states */
.error {
  border-color: hsl(var(--destructive));
  background-color: hsla(var(--destructive), 0.1);
}

.error:focus {
  border-color: hsl(var(--destructive));
  box-shadow: 0 0 0 3px hsla(var(--destructive), 0.1);
}

/* Success states */
.success {
  border-color: hsl(var(--calm-green));
  background-color: hsla(var(--calm-green), 0.1);
}

.success:focus {
  border-color: hsl(var(--calm-green));
  box-shadow: 0 0 0 3px hsla(var(--calm-green), 0.1);
}

/* Utility classes for eye comfort */
.text-comfortable {
  font-size: calc(var(--base-font-size) * 1.1);
  line-height: calc(var(--base-line-height) * 1.1);
  letter-spacing: 0.025em;
}

.text-large {
  font-size: calc(var(--base-font-size) * 1.25);
  line-height: calc(var(--base-line-height) * 1.1);
}

.text-extra-large {
  font-size: calc(var(--base-font-size) * 1.5);
  line-height: calc(var(--base-line-height) * 1.1);
}

.spacing-comfortable {
  padding: 1.5rem;
  margin: 1rem 0;
}

.spacing-relaxed {
  padding: 2rem;
  margin: 1.5rem 0;
}

/* Print styles for better readability */
@media print {
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .no-print {
    display: none !important;
  }
  
  body {
    font-size: 12pt;
    line-height: 1.5;
  }
  
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid;
  }
  
  p, li {
    orphans: 3;
    widows: 3;
  }
}
