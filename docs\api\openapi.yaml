openapi: 3.0.3
info:
  title: Solidity Learning Platform API
  description: |
    Comprehensive API for the Solidity Learning Platform providing endpoints for user management, 
    lesson delivery, progress tracking, community features, and administrative functions.
    
    ## Authentication
    This API uses JWT Bearer token authentication. Include the token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Rate Limiting
    API requests are rate limited based on user authentication status:
    - Authenticated users: 1000 requests per 15 minutes
    - Anonymous users: 100 requests per 15 minutes
    - Authentication endpoints: 10 requests per 15 minutes
    
    ## Error Handling
    All endpoints return standardized error responses with appropriate HTTP status codes:
    - 400: Bad Request (validation errors)
    - 401: Unauthorized (authentication required)
    - 403: Forbidden (insufficient permissions)
    - 404: Not Found
    - 429: Too Many Requests (rate limit exceeded)
    - 500: Internal Server Error
    
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://api.solidity-learning.com/v1
    description: Production server
  - url: https://staging-api.solidity-learning.com/v1
    description: Staging server
  - url: http://localhost:3000/api/v1
    description: Development server

security:
  - BearerAuth: []

paths:
  /health:
    get:
      tags:
        - System
      summary: Health check
      description: Check the health status of the API and its dependencies
      security: []
      responses:
        '200':
          description: System is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheck'
        '503':
          description: System is unhealthy or degraded
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user and return access tokens
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

  /auth/register:
    post:
      tags:
        - Authentication
      summary: User registration
      description: Register a new user account
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: Registration successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RegisterResponse'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

  /auth/refresh:
    post:
      tags:
        - Authentication
      summary: Refresh access token
      description: Get a new access token using refresh token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RefreshTokenRequest'
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TokenResponse'
        '401':
          description: Invalid refresh token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

  /users:
    get:
      tags:
        - Users
      summary: List users
      description: Get a paginated list of users (admin only)
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Search'
        - $ref: '#/components/parameters/SortBy'
        - $ref: '#/components/parameters/SortOrder'
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsersResponse'
        '403':
          description: Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

    post:
      tags:
        - Users
      summary: Create user
      description: Create a new user (admin only)
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '409':
          description: User already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

  /users/{id}:
    get:
      tags:
        - Users
      summary: Get user by ID
      description: Retrieve user information by ID
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: User ID
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

    put:
      tags:
        - Users
      summary: Update user
      description: Update user information
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: User ID
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

    delete:
      tags:
        - Users
      summary: Delete user
      description: Delete a user (admin only)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          description: User ID
      responses:
        '204':
          description: User deleted successfully
        '404':
          description: User not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

  /lessons:
    get:
      tags:
        - Lessons
      summary: List lessons
      description: Get a paginated list of lessons
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Search'
        - $ref: '#/components/parameters/SortBy'
        - $ref: '#/components/parameters/SortOrder'
        - name: difficulty
          in: query
          schema:
            $ref: '#/components/schemas/DifficultyLevel'
          description: Filter by difficulty level
        - name: type
          in: query
          schema:
            $ref: '#/components/schemas/LessonType'
          description: Filter by lesson type
      responses:
        '200':
          description: Lessons retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LessonsResponse'

    post:
      tags:
        - Lessons
      summary: Create lesson
      description: Create a new lesson (instructors and admins only)
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateLessonRequest'
      responses:
        '201':
          description: Lesson created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LessonResponse'
        '403':
          description: Insufficient permissions
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiError'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    Page:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number for pagination

    Limit:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20
      description: Number of items per page

    Search:
      name: search
      in: query
      schema:
        type: string
        maxLength: 100
      description: Search query

    SortBy:
      name: sortBy
      in: query
      schema:
        type: string
      description: Field to sort by

    SortOrder:
      name: sortOrder
      in: query
      schema:
        type: string
        enum: [asc, desc]
        default: asc
      description: Sort order

  schemas:
    ApiResponse:
      type: object
      required:
        - success
        - timestamp
        - requestId
      properties:
        success:
          type: boolean
        data:
          type: object
        error:
          $ref: '#/components/schemas/ApiError'
        meta:
          $ref: '#/components/schemas/ResponseMeta'
        timestamp:
          type: string
          format: date-time
        requestId:
          type: string
          format: uuid

    ApiError:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: string
          enum:
            - UNAUTHORIZED
            - FORBIDDEN
            - VALIDATION_ERROR
            - RESOURCE_NOT_FOUND
            - RATE_LIMIT_EXCEEDED
            - INTERNAL_SERVER_ERROR
        message:
          type: string
        details:
          type: array
          items:
            $ref: '#/components/schemas/ValidationError'

    ValidationError:
      type: object
      required:
        - field
        - message
        - code
      properties:
        field:
          type: string
        message:
          type: string
        code:
          type: string
        value:
          type: string

    ResponseMeta:
      type: object
      properties:
        pagination:
          $ref: '#/components/schemas/PaginationMeta'
        total:
          type: integer
        page:
          type: integer
        limit:
          type: integer

    PaginationMeta:
      type: object
      required:
        - currentPage
        - totalPages
        - totalItems
        - itemsPerPage
        - hasNextPage
        - hasPreviousPage
      properties:
        currentPage:
          type: integer
        totalPages:
          type: integer
        totalItems:
          type: integer
        itemsPerPage:
          type: integer
        hasNextPage:
          type: boolean
        hasPreviousPage:
          type: boolean

    HealthCheck:
      type: object
      required:
        - status
        - timestamp
        - uptime
        - version
        - environment
        - services
      properties:
        status:
          type: string
          enum: [healthy, unhealthy, degraded]
        timestamp:
          type: string
          format: date-time
        uptime:
          type: integer
          description: Uptime in milliseconds
        version:
          type: string
        environment:
          type: string
        services:
          type: object
          properties:
            database:
              $ref: '#/components/schemas/ServiceHealth'
            redis:
              $ref: '#/components/schemas/ServiceHealth'
            external:
              type: object
              additionalProperties:
                $ref: '#/components/schemas/ServiceHealth'

    ServiceHealth:
      type: object
      required:
        - status
        - lastCheck
      properties:
        status:
          type: string
          enum: [healthy, unhealthy]
        responseTime:
          type: integer
          description: Response time in milliseconds
        error:
          type: string
        lastCheck:
          type: string
          format: date-time

    UserRole:
      type: string
      enum: [STUDENT, INSTRUCTOR, ADMIN, SUPER_ADMIN]

    UserStatus:
      type: string
      enum: [ACTIVE, INACTIVE, SUSPENDED, PENDING_VERIFICATION]

    DifficultyLevel:
      type: string
      enum: [BEGINNER, INTERMEDIATE, ADVANCED, EXPERT]

    LessonType:
      type: string
      enum: [THEORY, PRACTICAL, QUIZ, PROJECT, CHALLENGE]

    LoginRequest:
      type: object
      required:
        - email
        - password
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
        rememberMe:
          type: boolean
          default: false

    LoginResponse:
      type: object
      required:
        - user
        - tokens
        - session
      properties:
        user:
          $ref: '#/components/schemas/User'
        tokens:
          $ref: '#/components/schemas/TokenResponse'
        session:
          $ref: '#/components/schemas/SessionInfo'

    RegisterRequest:
      type: object
      required:
        - email
        - password
        - confirmPassword
        - name
        - acceptTerms
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
        confirmPassword:
          type: string
        name:
          type: string
          minLength: 2
          maxLength: 50
        acceptTerms:
          type: boolean

    RegisterResponse:
      type: object
      required:
        - user
        - tokens
        - session
        - message
      properties:
        user:
          $ref: '#/components/schemas/User'
        tokens:
          $ref: '#/components/schemas/TokenResponse'
        session:
          $ref: '#/components/schemas/SessionInfo'
        message:
          type: string

    RefreshTokenRequest:
      type: object
      required:
        - refreshToken
      properties:
        refreshToken:
          type: string

    TokenResponse:
      type: object
      required:
        - accessToken
        - refreshToken
        - expiresIn
        - tokenType
      properties:
        accessToken:
          type: string
        refreshToken:
          type: string
        expiresIn:
          type: integer
          description: Token expiration time in seconds
        tokenType:
          type: string
          default: Bearer

    SessionInfo:
      type: object
      required:
        - id
        - expiresAt
        - rememberMe
      properties:
        id:
          type: string
        expiresAt:
          type: string
          format: date-time
        rememberMe:
          type: boolean

    User:
      type: object
      required:
        - id
        - email
        - name
        - role
        - status
        - profile
        - preferences
        - createdAt
        - updatedAt
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        name:
          type: string
        role:
          $ref: '#/components/schemas/UserRole'
        status:
          $ref: '#/components/schemas/UserStatus'
        profile:
          $ref: '#/components/schemas/UserProfile'
        preferences:
          $ref: '#/components/schemas/UserPreferences'
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        lastLoginAt:
          type: string
          format: date-time

    UserProfile:
      type: object
      required:
        - xpTotal
        - level
        - lessonsCompleted
        - coursesCompleted
        - achievementsCount
        - currentStreak
        - longestStreak
      properties:
        avatar:
          type: string
          format: uri
        bio:
          type: string
          maxLength: 500
        location:
          type: string
          maxLength: 100
        website:
          type: string
          format: uri
        github:
          type: string
          maxLength: 100
        twitter:
          type: string
          maxLength: 100
        linkedin:
          type: string
          maxLength: 100
        xpTotal:
          type: integer
          minimum: 0
        level:
          type: integer
          minimum: 1
        lessonsCompleted:
          type: integer
          minimum: 0
        coursesCompleted:
          type: integer
          minimum: 0
        achievementsCount:
          type: integer
          minimum: 0
        currentStreak:
          type: integer
          minimum: 0
        longestStreak:
          type: integer
          minimum: 0

    UserPreferences:
      type: object
      required:
        - theme
        - language
        - timezone
        - emailNotifications
        - pushNotifications
        - weeklyDigest
        - achievementNotifications
      properties:
        theme:
          type: string
          enum: [light, dark, system]
        language:
          type: string
          minLength: 2
          maxLength: 2
        timezone:
          type: string
        emailNotifications:
          type: boolean
        pushNotifications:
          type: boolean
        weeklyDigest:
          type: boolean
        achievementNotifications:
          type: boolean

    CreateUserRequest:
      type: object
      required:
        - email
        - password
        - name
      properties:
        email:
          type: string
          format: email
        password:
          type: string
          minLength: 8
        name:
          type: string
          minLength: 2
          maxLength: 50
        role:
          $ref: '#/components/schemas/UserRole'

    UpdateUserRequest:
      type: object
      properties:
        name:
          type: string
          minLength: 2
          maxLength: 50
        email:
          type: string
          format: email
        role:
          $ref: '#/components/schemas/UserRole'
        status:
          $ref: '#/components/schemas/UserStatus'

    UserResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/User'

    UsersResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/User'

    Lesson:
      type: object
      required:
        - id
        - title
        - description
        - content
        - type
        - difficulty
        - estimatedDuration
        - xpReward
        - prerequisites
        - tags
        - courseId
        - instructorId
        - status
        - isPublished
        - createdAt
        - updatedAt
        - completionCount
        - averageRating
        - ratingCount
      properties:
        id:
          type: string
          format: uuid
        title:
          type: string
          minLength: 3
          maxLength: 200
        description:
          type: string
          minLength: 10
          maxLength: 1000
        content:
          type: string
          minLength: 50
        type:
          $ref: '#/components/schemas/LessonType'
        difficulty:
          $ref: '#/components/schemas/DifficultyLevel'
        estimatedDuration:
          type: integer
          minimum: 1
          maximum: 480
          description: Duration in minutes
        xpReward:
          type: integer
          minimum: 10
          maximum: 1000
        prerequisites:
          type: array
          items:
            type: string
            format: uuid
        tags:
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 50
          maxItems: 10
        courseId:
          type: string
          format: uuid
        instructorId:
          type: string
          format: uuid
        status:
          type: string
          enum: [DRAFT, REVIEW, PUBLISHED, ARCHIVED]
        isPublished:
          type: boolean
        publishedAt:
          type: string
          format: date-time
        createdAt:
          type: string
          format: date-time
        updatedAt:
          type: string
          format: date-time
        completionCount:
          type: integer
          minimum: 0
        averageRating:
          type: number
          minimum: 0
          maximum: 5
        ratingCount:
          type: integer
          minimum: 0

    CreateLessonRequest:
      type: object
      required:
        - title
        - description
        - content
        - type
        - difficulty
        - estimatedDuration
        - xpReward
        - courseId
      properties:
        title:
          type: string
          minLength: 3
          maxLength: 200
        description:
          type: string
          minLength: 10
          maxLength: 1000
        content:
          type: string
          minLength: 50
        type:
          $ref: '#/components/schemas/LessonType'
        difficulty:
          $ref: '#/components/schemas/DifficultyLevel'
        estimatedDuration:
          type: integer
          minimum: 1
          maximum: 480
        xpReward:
          type: integer
          minimum: 10
          maximum: 1000
        prerequisites:
          type: array
          items:
            type: string
            format: uuid
          default: []
        tags:
          type: array
          items:
            type: string
            minLength: 1
            maxLength: 50
          maxItems: 10
          default: []
        courseId:
          type: string
          format: uuid

    LessonResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              $ref: '#/components/schemas/Lesson'

    LessonsResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/Lesson'

tags:
  - name: System
    description: System health and monitoring endpoints
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management operations
  - name: Lessons
    description: Lesson management and delivery
  - name: Courses
    description: Course management operations
  - name: Progress
    description: Learning progress tracking
  - name: Community
    description: Community features and leaderboards
  - name: Achievements
    description: Achievement and badge system
