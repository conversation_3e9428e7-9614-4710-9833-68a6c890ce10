#!/usr/bin/env node

/**
 * ESLint Configuration Optimizer
 * Creates optimized ESLint configuration for better code quality and performance
 */

const fs = require('fs');
const path = require('path');

class ESLintOptimizer {
  constructor() {
    this.configPath = '.eslintrc.json';
    this.backupPath = '.eslintrc.backup.json';
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
  }

  createBackup() {
    if (fs.existsSync(this.configPath)) {
      fs.copyFileSync(this.configPath, this.backupPath);
      this.log('Created backup of .eslintrc.json');
    }
  }

  getOptimizedConfig() {
    return {
      extends: [
        "next/core-web-vitals",
        "@typescript-eslint/recommended",
        "plugin:react/recommended",
        "plugin:react-hooks/recommended",
        "plugin:jsx-a11y/recommended",
        "plugin:import/recommended",
        "plugin:import/typescript"
      ],
      
      parser: "@typescript-eslint/parser",
      
      parserOptions: {
        ecmaVersion: "latest",
        sourceType: "module",
        ecmaFeatures: {
          jsx: true
        },
        project: "./tsconfig.json"
      },
      
      plugins: [
        "@typescript-eslint",
        "react",
        "react-hooks",
        "jsx-a11y",
        "import",
        "unused-imports",
        "prefer-arrow"
      ],
      
      settings: {
        react: {
          version: "detect"
        },
        "import/resolver": {
          typescript: {
            alwaysTryTypes: true,
            project: "./tsconfig.json"
          },
          node: {
            extensions: [".js", ".jsx", ".ts", ".tsx"]
          }
        }
      },
      
      rules: {
        // TypeScript specific rules
        "@typescript-eslint/no-unused-vars": ["error", { 
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_"
        }],
        "@typescript-eslint/no-explicit-any": "warn",
        "@typescript-eslint/explicit-function-return-type": "off",
        "@typescript-eslint/explicit-module-boundary-types": "off",
        "@typescript-eslint/no-non-null-assertion": "warn",
        "@typescript-eslint/prefer-const": "error",
        "@typescript-eslint/no-var-requires": "error",
        
        // React specific rules
        "react/react-in-jsx-scope": "off", // Not needed in Next.js
        "react/prop-types": "off", // Using TypeScript
        "react/display-name": "warn",
        "react/no-unescaped-entities": "warn",
        "react/jsx-key": "error",
        "react/jsx-no-duplicate-props": "error",
        "react/jsx-no-undef": "error",
        "react/jsx-uses-react": "off", // Not needed in Next.js
        "react/jsx-uses-vars": "error",
        
        // React Hooks rules
        "react-hooks/rules-of-hooks": "error",
        "react-hooks/exhaustive-deps": "warn",
        
        // Accessibility rules
        "jsx-a11y/alt-text": "error",
        "jsx-a11y/anchor-has-content": "error",
        "jsx-a11y/anchor-is-valid": "error",
        "jsx-a11y/aria-props": "error",
        "jsx-a11y/aria-proptypes": "error",
        "jsx-a11y/aria-unsupported-elements": "error",
        "jsx-a11y/click-events-have-key-events": "warn",
        "jsx-a11y/interactive-supports-focus": "warn",
        "jsx-a11y/no-noninteractive-element-interactions": "warn",
        
        // Import rules
        "import/order": ["error", {
          groups: [
            "builtin",
            "external",
            "internal",
            "parent",
            "sibling",
            "index"
          ],
          "newlines-between": "always",
          alphabetize: {
            order: "asc",
            caseInsensitive: true
          }
        }],
        "import/no-unresolved": "error",
        "import/no-duplicates": "error",
        "import/no-unused-modules": "warn",
        "import/no-cycle": "error",
        
        // Unused imports
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": ["warn", {
          vars: "all",
          varsIgnorePattern: "^_",
          args: "after-used",
          argsIgnorePattern: "^_"
        }],
        
        // General code quality rules
        "no-console": ["warn", { allow: ["warn", "error"] }],
        "no-debugger": "error",
        "no-alert": "error",
        "no-var": "error",
        "prefer-const": "error",
        "prefer-arrow-callback": "error",
        "arrow-spacing": "error",
        "no-multiple-empty-lines": ["error", { max: 2, maxEOF: 1 }],
        "eol-last": "error",
        "comma-dangle": ["error", "never"],
        "semi": ["error", "always"],
        "quotes": ["error", "single", { avoidEscape: true }],
        "indent": ["error", 2, { SwitchCase: 1 }],
        
        // Performance rules
        "prefer-arrow/prefer-arrow-functions": ["error", {
          disallowPrototype: true,
          singleReturnOnly: false,
          classPropertiesAllowed: false
        }],
        
        // Security rules
        "no-eval": "error",
        "no-implied-eval": "error",
        "no-new-func": "error",
        "no-script-url": "error"
      },
      
      overrides: [
        {
          files: ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"],
          env: {
            jest: true
          },
          rules: {
            "@typescript-eslint/no-explicit-any": "off",
            "no-console": "off"
          }
        },
        {
          files: ["scripts/**/*.js"],
          env: {
            node: true
          },
          rules: {
            "@typescript-eslint/no-var-requires": "off",
            "no-console": "off"
          }
        },
        {
          files: ["**/*.config.js", "**/*.config.ts"],
          env: {
            node: true
          },
          rules: {
            "@typescript-eslint/no-var-requires": "off"
          }
        }
      ],
      
      env: {
        browser: true,
        es2022: true,
        node: true
      },
      
      ignorePatterns: [
        "node_modules/",
        ".next/",
        "out/",
        "dist/",
        "build/",
        "coverage/",
        "public/",
        "*.min.js",
        "cleanup-backup/"
      ]
    };
  }

  createESLintIgnore() {
    const ignoreContent = `# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output

# Next.js
.next/
out/

# Production builds
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Cleanup backup
cleanup-backup/

# Generated files
*.min.js
*.min.css

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
`;

    fs.writeFileSync('.eslintignore', ignoreContent);
    this.log('Created .eslintignore file');
  }

  run() {
    this.log('Starting ESLint configuration optimization...');

    // Create backup
    this.createBackup();

    // Get optimized configuration
    const optimizedConfig = this.getOptimizedConfig();

    // Write optimized configuration
    fs.writeFileSync(this.configPath, JSON.stringify(optimizedConfig, null, 2));
    this.log('Updated .eslintrc.json with optimized settings');

    // Create .eslintignore file
    this.createESLintIgnore();

    // Generate package.json scripts
    const packageJsonPath = 'package.json';
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      
      packageJson.scripts = {
        ...packageJson.scripts,
        "lint": "eslint . --ext .ts,.tsx,.js,.jsx",
        "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix",
        "lint:check": "eslint . --ext .ts,.tsx,.js,.jsx --max-warnings 0",
        "type-check": "tsc --noEmit",
        "code-quality": "npm run lint:check && npm run type-check"
      };

      fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
      this.log('Updated package.json with lint scripts');
    }

    // Display summary
    console.log('\n=== ESLINT OPTIMIZATION REPORT ===');
    console.log('✓ ESLint configuration optimized');
    console.log('✓ TypeScript integration configured');
    console.log('✓ React and Next.js rules applied');
    console.log('✓ Accessibility rules enabled');
    console.log('✓ Import organization rules added');
    console.log('✓ Code quality rules enforced');
    console.log('✓ Performance rules included');
    console.log('✓ Security rules enabled');
    
    console.log('\nNew npm scripts available:');
    console.log('  - npm run lint: Check for linting errors');
    console.log('  - npm run lint:fix: Fix auto-fixable linting errors');
    console.log('  - npm run lint:check: Strict linting with no warnings');
    console.log('  - npm run type-check: Check TypeScript types');
    console.log('  - npm run code-quality: Run both linting and type checking');
    
    console.log('\nRecommendations:');
    console.log('  - Run "npm run lint:fix" to fix auto-fixable issues');
    console.log('  - Run "npm run code-quality" to check overall code quality');
    console.log('  - Configure your IDE to use the workspace ESLint configuration');
    console.log('  - Consider adding pre-commit hooks with husky and lint-staged');

    console.log(`\nBackup saved as: ${this.backupPath}`);

    this.log('ESLint optimization completed successfully');
  }
}

// Run the optimizer
if (require.main === module) {
  const optimizer = new ESLintOptimizer();
  optimizer.run();
}

module.exports = ESLintOptimizer;
