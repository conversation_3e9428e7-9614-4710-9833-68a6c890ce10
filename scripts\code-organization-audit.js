#!/usr/bin/env node

/**
 * Code Organization and Dead Code Audit
 * Comprehensive analysis of the Solidity Learning Platform codebase
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  sourceDirectories: ['app', 'components', 'lib', 'hooks', 'utils', 'types', 'services', 'stores'],
  assetDirectories: ['public'],
  excludePatterns: ['node_modules', '.next', '.git', 'dist', 'build', 'coverage', 'test-results', 'reports', 'logs'],
  fileExtensions: ['.ts', '.tsx', '.js', '.jsx'],
  assetExtensions: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot']
};

// Colors for output
const colors = {
  reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m',
  yellow: '\x1b[33m', blue: '\x1b[34m', cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

// Get all files recursively
function getAllFiles(dir, extensions = [], excludePatterns = []) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item);
        const relativePath = path.relative(process.cwd(), fullPath);
        
        if (excludePatterns.some(pattern => relativePath.includes(pattern))) {
          return;
        }
        
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          traverse(fullPath);
        } else if (stat.isFile()) {
          if (extensions.length === 0 || extensions.some(ext => item.endsWith(ext))) {
            files.push({
              path: fullPath,
              relativePath: relativePath.replace(/\\/g, '/'),
              name: item,
              size: stat.size,
              lastModified: stat.mtime,
              extension: path.extname(item)
            });
          }
        }
      });
    } catch (error) {
      // Skip directories that can't be read
    }
  }
  
  if (fs.existsSync(dir)) {
    traverse(dir);
  }
  
  return files;
}

// Analyze file for imports and exports
function analyzeFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // Extract imports
    const imports = [];
    const importRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"`]([^'"`]+)['"`]/g;
    let match;
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    // Extract exports
    const exports = [];
    const exportRegex = /export\s+(?:const|let|var|function|class|interface|type|enum)\s+(\w+)/g;
    while ((match = exportRegex.exec(content)) !== null) {
      exports.push({ name: match[1], type: 'named' });
    }
    
    // Extract default exports
    const defaultExportRegex = /export\s+default\s+/g;
    if (defaultExportRegex.test(content)) {
      exports.push({ name: 'default', type: 'default' });
    }
    
    // Check for dead code patterns
    const deadCodePatterns = {
      unreachableCode: /return\s*[^;]*;\s*\n\s*[^}]/g.test(content),
      commentedCode: /\/\*[\s\S]*?\*\/|\/\/.*$/gm.test(content) && content.includes('// TODO') || content.includes('// FIXME'),
      unusedVariables: /const\s+\w+\s*=.*?;(?!\s*\w+)/g.test(content),
      debugStatements: /console\.(log|debug|info|warn|error)/g.test(content)
    };
    
    return { imports, exports, deadCodePatterns, lineCount: content.split('\n').length };
  } catch (error) {
    return { imports: [], exports: [], deadCodePatterns: {}, lineCount: 0 };
  }
}

// Find unused files
function findUnusedFiles() {
  logHeader('Analyzing Unused Files');
  
  const allSourceFiles = [];
  const allAssetFiles = [];
  
  // Get all source files
  CONFIG.sourceDirectories.forEach(dir => {
    const files = getAllFiles(dir, CONFIG.fileExtensions, CONFIG.excludePatterns);
    allSourceFiles.push(...files);
  });
  
  // Get all asset files
  CONFIG.assetDirectories.forEach(dir => {
    const files = getAllFiles(dir, CONFIG.assetExtensions, CONFIG.excludePatterns);
    allAssetFiles.push(...files);
  });
  
  log(`📁 Found ${allSourceFiles.length} source files`, 'blue');
  log(`🖼️  Found ${allAssetFiles.length} asset files`, 'blue');
  
  // Build import graph
  const importGraph = new Map();
  const allImports = new Set();
  
  allSourceFiles.forEach(file => {
    const analysis = analyzeFile(file.path);
    importGraph.set(file.relativePath, analysis);
    analysis.imports.forEach(imp => allImports.add(imp));
  });
  
  // Find potentially unused source files
  const unusedSourceFiles = allSourceFiles.filter(file => {
    const relativePath = file.relativePath;
    const withoutExt = relativePath.replace(/\.(ts|tsx|js|jsx)$/, '');
    
    // Check if file is imported
    const isImported = Array.from(allImports).some(imp => {
      return imp.includes(file.name.replace(/\.(ts|tsx|js|jsx)$/, '')) ||
             imp.includes(relativePath) ||
             imp.includes(withoutExt);
    });
    
    // Check if it's a Next.js special file
    const isNextJSFile = ['page.tsx', 'layout.tsx', 'loading.tsx', 'error.tsx', 'not-found.tsx', 'middleware.ts'].includes(file.name) ||
                        file.relativePath.includes('api/') ||
                        file.name === 'globals.css' ||
                        file.name === 'providers.tsx';
    
    // Check if it's a config or entry file
    const isConfigFile = ['next.config.js', 'tailwind.config.js', 'tsconfig.json', 'package.json'].includes(file.name) ||
                        file.relativePath.includes('scripts/') ||
                        file.name.includes('config') ||
                        file.name.includes('setup');
    
    return !isImported && !isNextJSFile && !isConfigFile;
  });
  
  // Find potentially unused asset files
  const unusedAssetFiles = allAssetFiles.filter(file => {
    const isReferenced = allSourceFiles.some(sourceFile => {
      try {
        const content = fs.readFileSync(sourceFile.path, 'utf8');
        return content.includes(file.name) || content.includes(file.relativePath);
      } catch {
        return false;
      }
    });
    
    // Exclude common files
    const isCommonFile = ['favicon.svg', 'manifest.json', 'sw.js'].includes(file.name);
    
    return !isReferenced && !isCommonFile;
  });
  
  // Display results
  if (unusedSourceFiles.length > 0) {
    log(`⚠️  Found ${unusedSourceFiles.length} potentially unused source files:`, 'yellow');
    unusedSourceFiles.slice(0, 10).forEach(file => {
      log(`   ${file.relativePath} (${(file.size / 1024).toFixed(1)}KB)`, 'yellow');
    });
    if (unusedSourceFiles.length > 10) {
      log(`   ... and ${unusedSourceFiles.length - 10} more`, 'yellow');
    }
  } else {
    log('✅ No unused source files found', 'green');
  }
  
  if (unusedAssetFiles.length > 0) {
    log(`⚠️  Found ${unusedAssetFiles.length} potentially unused asset files:`, 'yellow');
    unusedAssetFiles.slice(0, 5).forEach(file => {
      log(`   ${file.relativePath} (${(file.size / 1024).toFixed(1)}KB)`, 'yellow');
    });
  } else {
    log('✅ No unused asset files found', 'green');
  }
  
  return { unusedSourceFiles, unusedAssetFiles, importGraph, allSourceFiles, allAssetFiles };
}

// Analyze exports usage
function analyzeExportsUsage(importGraph) {
  logHeader('Analyzing Unused Exports');
  
  const allExports = [];
  const exportUsage = new Map();
  
  // Collect all exports
  for (const [filePath, analysis] of importGraph) {
    analysis.exports.forEach(exp => {
      const exportKey = `${filePath}:${exp.name}`;
      allExports.push({ file: filePath, name: exp.name, type: exp.type });
      exportUsage.set(exportKey, false);
    });
  }
  
  // Simple usage detection (this could be more sophisticated)
  for (const [filePath, analysis] of importGraph) {
    analysis.imports.forEach(importPath => {
      // Mark exports as potentially used if the file is imported
      for (const [exportKey] of exportUsage) {
        if (exportKey.includes(importPath)) {
          exportUsage.set(exportKey, true);
        }
      }
    });
  }
  
  const unusedExports = [];
  for (const [exportKey, isUsed] of exportUsage) {
    if (!isUsed) {
      const [file, name] = exportKey.split(':');
      unusedExports.push({ file, name });
    }
  }
  
  if (unusedExports.length > 0) {
    log(`⚠️  Found ${unusedExports.length} potentially unused exports:`, 'yellow');
    unusedExports.slice(0, 10).forEach(exp => {
      log(`   ${exp.file}: ${exp.name}`, 'yellow');
    });
    if (unusedExports.length > 10) {
      log(`   ... and ${unusedExports.length - 10} more`, 'yellow');
    }
  } else {
    log('✅ No obviously unused exports found', 'green');
  }
  
  return { totalExports: allExports.length, unusedExports };
}

// Analyze dead code patterns
function analyzeDeadCodePatterns(importGraph) {
  logHeader('Analyzing Dead Code Patterns');
  
  const deadCodeIssues = {
    filesWithDeadCode: 0,
    unreachableCode: 0,
    commentedCode: 0,
    debugStatements: 0,
    unusedVariables: 0
  };
  
  const issueFiles = [];
  
  for (const [filePath, analysis] of importGraph) {
    const patterns = analysis.deadCodePatterns;
    let hasIssues = false;
    
    if (patterns.unreachableCode) {
      deadCodeIssues.unreachableCode++;
      hasIssues = true;
    }
    if (patterns.commentedCode) {
      deadCodeIssues.commentedCode++;
      hasIssues = true;
    }
    if (patterns.debugStatements) {
      deadCodeIssues.debugStatements++;
      hasIssues = true;
    }
    if (patterns.unusedVariables) {
      deadCodeIssues.unusedVariables++;
      hasIssues = true;
    }
    
    if (hasIssues) {
      deadCodeIssues.filesWithDeadCode++;
      issueFiles.push({ file: filePath, patterns });
    }
  }
  
  if (deadCodeIssues.filesWithDeadCode > 0) {
    log(`⚠️  Found dead code patterns in ${deadCodeIssues.filesWithDeadCode} files:`, 'yellow');
    log(`   Debug statements: ${deadCodeIssues.debugStatements} files`, 'yellow');
    log(`   Commented code: ${deadCodeIssues.commentedCode} files`, 'yellow');
    log(`   Unreachable code: ${deadCodeIssues.unreachableCode} files`, 'yellow');
    log(`   Unused variables: ${deadCodeIssues.unusedVariables} files`, 'yellow');
  } else {
    log('✅ No obvious dead code patterns found', 'green');
  }
  
  return { deadCodeIssues, issueFiles };
}

// Analyze directory structure
function analyzeDirectoryStructure() {
  logHeader('Analyzing Directory Structure');
  
  const issues = [];
  const recommendations = [];
  
  // Check app directory structure
  const appDir = 'app';
  if (fs.existsSync(appDir)) {
    const routes = fs.readdirSync(appDir).filter(item => {
      const itemPath = path.join(appDir, item);
      return fs.existsSync(itemPath) && fs.statSync(itemPath).isDirectory();
    });
    
    routes.forEach(route => {
      const routePath = path.join(appDir, route);
      if (fs.existsSync(routePath)) {
        const files = fs.readdirSync(routePath);
        const hasPage = files.some(f => f.startsWith('page.'));
        
        if (!hasPage && route !== 'api') {
          issues.push(`Route ${route} missing page file`);
        }
      }
    });
  }
  
  // Check for index files in major directories
  const dirsToCheck = ['components', 'lib', 'hooks', 'utils', 'types'];
  dirsToCheck.forEach(dir => {
    if (fs.existsSync(dir)) {
      const hasIndex = fs.existsSync(path.join(dir, 'index.ts')) || 
                     fs.existsSync(path.join(dir, 'index.tsx'));
      
      if (!hasIndex) {
        recommendations.push(`Consider adding index.ts to ${dir} for cleaner imports`);
      }
    }
  });
  
  if (issues.length > 0) {
    log('⚠️  Directory structure issues:', 'yellow');
    issues.forEach(issue => log(`   ${issue}`, 'yellow'));
  }
  
  if (recommendations.length > 0) {
    log('💡 Directory structure recommendations:', 'blue');
    recommendations.forEach(rec => log(`   ${rec}`, 'blue'));
  }
  
  if (issues.length === 0 && recommendations.length === 0) {
    log('✅ Directory structure looks good', 'green');
  }
  
  return { issues, recommendations };
}

// Generate comprehensive report
function generateReport(results) {
  logHeader('Generating Comprehensive Report');
  
  const { unusedFiles, exportsAnalysis, deadCodeAnalysis, structureAnalysis } = results;
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalSourceFiles: unusedFiles.allSourceFiles.length,
      totalAssetFiles: unusedFiles.allAssetFiles.length,
      unusedSourceFiles: unusedFiles.unusedSourceFiles.length,
      unusedAssetFiles: unusedFiles.unusedAssetFiles.length,
      totalExports: exportsAnalysis.totalExports,
      unusedExports: exportsAnalysis.unusedExports.length,
      filesWithDeadCode: deadCodeAnalysis.deadCodeIssues.filesWithDeadCode,
      structureIssues: structureAnalysis.issues.length
    },
    findings: {
      unusedFiles: {
        source: unusedFiles.unusedSourceFiles.map(f => ({
          path: f.relativePath,
          size: f.size,
          lastModified: f.lastModified,
          confidence: 'medium'
        })),
        assets: unusedFiles.unusedAssetFiles.map(f => ({
          path: f.relativePath,
          size: f.size,
          confidence: 'medium'
        }))
      },
      unusedExports: exportsAnalysis.unusedExports.map(exp => ({
        file: exp.file,
        name: exp.name,
        confidence: 'low'
      })),
      deadCodePatterns: deadCodeAnalysis.deadCodeIssues,
      organizationIssues: {
        issues: structureAnalysis.issues,
        recommendations: structureAnalysis.recommendations
      }
    },
    potentialSavings: {
      files: unusedFiles.unusedSourceFiles.length + unusedFiles.unusedAssetFiles.length,
      estimatedSizeKB: Math.round(
        (unusedFiles.unusedSourceFiles.reduce((sum, f) => sum + f.size, 0) +
         unusedFiles.unusedAssetFiles.reduce((sum, f) => sum + f.size, 0)) / 1024
      )
    }
  };
  
  // Save report
  const reportDir = 'reports';
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(reportDir, `code-organization-audit-${timestamp}.json`);
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`✅ Report saved to: ${reportPath}`, 'green');
  
  // Display summary
  logHeader('Analysis Summary');
  log(`📁 Total files analyzed: ${report.summary.totalSourceFiles + report.summary.totalAssetFiles}`, 'blue');
  log(`🗑️  Potentially unused files: ${report.summary.unusedSourceFiles + report.summary.unusedAssetFiles}`, 'yellow');
  log(`📤 Potentially unused exports: ${report.summary.unusedExports}`, 'yellow');
  log(`💾 Potential size savings: ${report.potentialSavings.estimatedSizeKB}KB`, 'green');
  
  return report;
}

// Main execution
function main() {
  logHeader('Solidity Learning Platform - Code Organization Audit');
  
  try {
    const unusedFiles = findUnusedFiles();
    const exportsAnalysis = analyzeExportsUsage(unusedFiles.importGraph);
    const deadCodeAnalysis = analyzeDeadCodePatterns(unusedFiles.importGraph);
    const structureAnalysis = analyzeDirectoryStructure();
    
    const report = generateReport({
      unusedFiles,
      exportsAnalysis,
      deadCodeAnalysis,
      structureAnalysis
    });
    
    const hasIssues = report.summary.unusedSourceFiles > 0 || 
                     report.summary.unusedAssetFiles > 0 || 
                     report.summary.structureIssues > 0;
    
    log('\n' + '='.repeat(60), 'cyan');
    if (hasIssues) {
      log('⚠️  Code organization issues found. See report for details.', 'yellow');
    } else {
      log('🎉 No significant code organization issues found!', 'green');
    }
    log('='.repeat(60), 'cyan');
    
    process.exit(0);
    
  } catch (error) {
    log(`❌ Analysis failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
