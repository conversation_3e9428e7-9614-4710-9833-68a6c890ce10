#!/usr/bin/env node

/**
 * Import/Export Consistency Analysis
 * Analyzes import patterns, barrel exports, and TypeScript path alias usage
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  sourceDirectories: ['app', 'components', 'lib', 'hooks', 'utils', 'types', 'services', 'stores'],
  excludePatterns: ['node_modules', '.next', '.git', 'dist', 'build', 'coverage', 'test-results', 'reports', 'logs'],
  fileExtensions: ['.ts', '.tsx', '.js', '.jsx']
};

// Colors for output
const colors = {
  reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m',
  yellow: '\x1b[33m', blue: '\x1b[34m', cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

// Get all files recursively
function getAllFiles(dir, extensions = [], excludePatterns = []) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item);
        const relativePath = path.relative(process.cwd(), fullPath);
        
        if (excludePatterns.some(pattern => relativePath.includes(pattern))) {
          return;
        }
        
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          traverse(fullPath);
        } else if (stat.isFile()) {
          if (extensions.length === 0 || extensions.some(ext => item.endsWith(ext))) {
            files.push({
              path: fullPath,
              relativePath: relativePath.replace(/\\/g, '/'),
              name: item,
              directory: path.dirname(relativePath).replace(/\\/g, '/')
            });
          }
        }
      });
    } catch (error) {
      // Skip directories that can't be read
    }
  }
  
  if (fs.existsSync(dir)) {
    traverse(dir);
  }
  
  return files;
}

// Analyze import patterns in a file
function analyzeImportPatterns(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    const imports = {
      relative: [],
      absolute: [],
      aliased: [],
      external: []
    };
    
    // Extract all import statements
    const importRegex = /import\s+(?:(?:\{[^}]*\}|\*\s+as\s+\w+|\w+)(?:\s*,\s*(?:\{[^}]*\}|\*\s+as\s+\w+|\w+))*\s+from\s+)?['"`]([^'"`]+)['"`]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1];
      
      if (importPath.startsWith('./') || importPath.startsWith('../')) {
        imports.relative.push(importPath);
      } else if (importPath.startsWith('@/')) {
        imports.aliased.push(importPath);
      } else if (importPath.startsWith('@') || !importPath.includes('/')) {
        imports.external.push(importPath);
      } else {
        imports.absolute.push(importPath);
      }
    }
    
    return imports;
  } catch (error) {
    return { relative: [], absolute: [], aliased: [], external: [] };
  }
}

// Check for index files and barrel exports
function analyzeBarrelExports() {
  logHeader('Analyzing Barrel Exports and Index Files');
  
  const indexFiles = [];
  const missingIndexDirs = [];
  const barrelExportIssues = [];
  
  // Check each source directory
  CONFIG.sourceDirectories.forEach(dir => {
    if (fs.existsSync(dir)) {
      const indexPath = path.join(dir, 'index.ts');
      const indexTsxPath = path.join(dir, 'index.tsx');
      
      if (fs.existsSync(indexPath) || fs.existsSync(indexTsxPath)) {
        const actualIndexPath = fs.existsSync(indexPath) ? indexPath : indexTsxPath;
        indexFiles.push({
          directory: dir,
          path: actualIndexPath,
          hasBarrelExports: false
        });
        
        // Check if it's a proper barrel export
        try {
          const content = fs.readFileSync(actualIndexPath, 'utf8');
          const hasExports = /export\s+(?:\{[^}]*\}|\*)\s+from/.test(content);
          
          if (hasExports) {
            indexFiles[indexFiles.length - 1].hasBarrelExports = true;
          } else {
            barrelExportIssues.push(`${dir}/index.ts exists but doesn't contain barrel exports`);
          }
        } catch (error) {
          barrelExportIssues.push(`${dir}/index.ts exists but couldn't be analyzed`);
        }
      } else {
        missingIndexDirs.push(dir);
      }
      
      // Check subdirectories
      try {
        const items = fs.readdirSync(dir);
        items.forEach(item => {
          const itemPath = path.join(dir, item);
          if (fs.statSync(itemPath).isDirectory()) {
            const subIndexPath = path.join(itemPath, 'index.ts');
            const subIndexTsxPath = path.join(itemPath, 'index.tsx');
            
            if (fs.existsSync(subIndexPath) || fs.existsSync(subIndexTsxPath)) {
              const actualSubIndexPath = fs.existsSync(subIndexPath) ? subIndexPath : subIndexTsxPath;
              indexFiles.push({
                directory: `${dir}/${item}`,
                path: actualSubIndexPath,
                hasBarrelExports: false
              });
              
              try {
                const content = fs.readFileSync(actualSubIndexPath, 'utf8');
                const hasExports = /export\s+(?:\{[^}]*\}|\*)\s+from/.test(content);
                
                if (hasExports) {
                  indexFiles[indexFiles.length - 1].hasBarrelExports = true;
                }
              } catch (error) {
                // Skip analysis errors
              }
            }
          }
        });
      } catch (error) {
        // Skip directory read errors
      }
    }
  });
  
  // Display results
  log(`📁 Found ${indexFiles.length} index files`, 'blue');
  log(`📤 ${indexFiles.filter(f => f.hasBarrelExports).length} have proper barrel exports`, 'green');
  
  if (missingIndexDirs.length > 0) {
    log(`⚠️  Directories missing index files:`, 'yellow');
    missingIndexDirs.forEach(dir => log(`   ${dir}`, 'yellow'));
  }
  
  if (barrelExportIssues.length > 0) {
    log(`⚠️  Barrel export issues:`, 'yellow');
    barrelExportIssues.forEach(issue => log(`   ${issue}`, 'yellow'));
  }
  
  return { indexFiles, missingIndexDirs, barrelExportIssues };
}

// Analyze import consistency across the codebase
function analyzeImportConsistency() {
  logHeader('Analyzing Import Consistency');
  
  const allFiles = [];
  CONFIG.sourceDirectories.forEach(dir => {
    const files = getAllFiles(dir, CONFIG.fileExtensions, CONFIG.excludePatterns);
    allFiles.push(...files);
  });
  
  const importStats = {
    totalFiles: allFiles.length,
    relativeImports: 0,
    absoluteImports: 0,
    aliasedImports: 0,
    externalImports: 0,
    inconsistentPatterns: []
  };
  
  const importPatternsByFile = new Map();
  
  // Analyze each file
  allFiles.forEach(file => {
    const patterns = analyzeImportPatterns(file.path);
    importPatternsByFile.set(file.relativePath, patterns);
    
    importStats.relativeImports += patterns.relative.length;
    importStats.absoluteImports += patterns.absolute.length;
    importStats.aliasedImports += patterns.aliased.length;
    importStats.externalImports += patterns.external.length;
  });
  
  // Check for inconsistent patterns
  for (const [filePath, patterns] of importPatternsByFile) {
    const hasRelative = patterns.relative.length > 0;
    const hasAliased = patterns.aliased.length > 0;
    
    // Flag files that mix relative and aliased imports for internal modules
    if (hasRelative && hasAliased) {
      const relativeInternal = patterns.relative.filter(imp => 
        !imp.includes('node_modules') && (imp.includes('../') || imp.includes('./'))
      );
      
      if (relativeInternal.length > 0) {
        importStats.inconsistentPatterns.push({
          file: filePath,
          issue: 'Mixes relative and aliased imports',
          relative: relativeInternal.length,
          aliased: patterns.aliased.length
        });
      }
    }
  }
  
  // Display results
  log(`📊 Import Statistics:`, 'blue');
  log(`   Total files analyzed: ${importStats.totalFiles}`, 'blue');
  log(`   Relative imports: ${importStats.relativeImports}`, 'blue');
  log(`   Aliased imports (@/): ${importStats.aliasedImports}`, 'blue');
  log(`   External imports: ${importStats.externalImports}`, 'blue');
  
  if (importStats.inconsistentPatterns.length > 0) {
    log(`⚠️  Found ${importStats.inconsistentPatterns.length} files with inconsistent import patterns:`, 'yellow');
    importStats.inconsistentPatterns.slice(0, 10).forEach(pattern => {
      log(`   ${pattern.file}: ${pattern.issue}`, 'yellow');
    });
    if (importStats.inconsistentPatterns.length > 10) {
      log(`   ... and ${importStats.inconsistentPatterns.length - 10} more`, 'yellow');
    }
  } else {
    log('✅ Import patterns are consistent', 'green');
  }
  
  return importStats;
}

// Check TypeScript path alias usage
function analyzePathAliasUsage() {
  logHeader('Analyzing TypeScript Path Alias Usage');
  
  let tsconfigPaths = {};
  
  // Read tsconfig.json
  try {
    const tsconfigPath = 'tsconfig.json';
    if (fs.existsSync(tsconfigPath)) {
      const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));
      tsconfigPaths = tsconfig.compilerOptions?.paths || {};
    }
  } catch (error) {
    log('⚠️  Could not read tsconfig.json', 'yellow');
  }
  
  const aliasUsage = {
    definedAliases: Object.keys(tsconfigPaths),
    usedAliases: new Set(),
    unusedAliases: [],
    invalidAliases: []
  };
  
  // Analyze usage in all files
  const allFiles = [];
  CONFIG.sourceDirectories.forEach(dir => {
    const files = getAllFiles(dir, CONFIG.fileExtensions, CONFIG.excludePatterns);
    allFiles.push(...files);
  });
  
  allFiles.forEach(file => {
    const patterns = analyzeImportPatterns(file.path);
    
    patterns.aliased.forEach(aliasedImport => {
      const aliasPrefix = aliasedImport.split('/')[0] + '/*';
      if (aliasUsage.definedAliases.includes(aliasPrefix)) {
        aliasUsage.usedAliases.add(aliasPrefix);
      } else {
        aliasUsage.invalidAliases.push({
          file: file.relativePath,
          alias: aliasedImport
        });
      }
    });
  });
  
  // Find unused aliases
  aliasUsage.unusedAliases = aliasUsage.definedAliases.filter(alias => 
    !aliasUsage.usedAliases.has(alias)
  );
  
  // Display results
  log(`🔗 Path Alias Analysis:`, 'blue');
  log(`   Defined aliases: ${aliasUsage.definedAliases.length}`, 'blue');
  log(`   Used aliases: ${aliasUsage.usedAliases.size}`, 'blue');
  
  if (aliasUsage.unusedAliases.length > 0) {
    log(`⚠️  Unused aliases:`, 'yellow');
    aliasUsage.unusedAliases.forEach(alias => log(`   ${alias}`, 'yellow'));
  }
  
  if (aliasUsage.invalidAliases.length > 0) {
    log(`⚠️  Invalid alias usage:`, 'yellow');
    aliasUsage.invalidAliases.slice(0, 5).forEach(invalid => {
      log(`   ${invalid.file}: ${invalid.alias}`, 'yellow');
    });
    if (aliasUsage.invalidAliases.length > 5) {
      log(`   ... and ${aliasUsage.invalidAliases.length - 5} more`, 'yellow');
    }
  }
  
  if (aliasUsage.unusedAliases.length === 0 && aliasUsage.invalidAliases.length === 0) {
    log('✅ Path alias usage is consistent', 'green');
  }
  
  return aliasUsage;
}

// Generate comprehensive report
function generateReport(barrelAnalysis, importConsistency, pathAliasAnalysis) {
  logHeader('Generating Import/Export Analysis Report');
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      indexFiles: barrelAnalysis.indexFiles.length,
      missingIndexDirs: barrelAnalysis.missingIndexDirs.length,
      barrelExportIssues: barrelAnalysis.barrelExportIssues.length,
      totalFiles: importConsistency.totalFiles,
      inconsistentImportPatterns: importConsistency.inconsistentPatterns.length,
      definedAliases: pathAliasAnalysis.definedAliases.length,
      usedAliases: pathAliasAnalysis.usedAliases.size,
      unusedAliases: pathAliasAnalysis.unusedAliases.length
    },
    findings: {
      barrelExports: barrelAnalysis,
      importConsistency: importConsistency,
      pathAliases: pathAliasAnalysis
    },
    recommendations: [
      ...(barrelAnalysis.missingIndexDirs.length > 0 ? 
        [`Add index.ts files to: ${barrelAnalysis.missingIndexDirs.join(', ')}`] : []),
      ...(importConsistency.inconsistentPatterns.length > 0 ? 
        ['Standardize import patterns (prefer aliased imports over relative)'] : []),
      ...(pathAliasAnalysis.unusedAliases.length > 0 ? 
        ['Remove unused path aliases from tsconfig.json'] : [])
    ]
  };
  
  // Save report
  const reportDir = 'reports';
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(reportDir, `import-export-analysis-${timestamp}.json`);
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`✅ Report saved to: ${reportPath}`, 'green');
  
  // Display summary
  logHeader('Import/Export Analysis Summary');
  log(`📁 Index files found: ${report.summary.indexFiles}`, 'blue');
  log(`📤 Barrel export issues: ${report.summary.barrelExportIssues}`, 'yellow');
  log(`🔄 Inconsistent import patterns: ${report.summary.inconsistentImportPatterns}`, 'yellow');
  log(`🔗 Unused path aliases: ${report.summary.unusedAliases}`, 'yellow');
  
  if (report.recommendations.length > 0) {
    log(`💡 Recommendations:`, 'blue');
    report.recommendations.forEach(rec => log(`   ${rec}`, 'blue'));
  }
  
  return report;
}

// Main execution
function main() {
  logHeader('Import/Export Consistency Analysis');
  
  try {
    const barrelAnalysis = analyzeBarrelExports();
    const importConsistency = analyzeImportConsistency();
    const pathAliasAnalysis = analyzePathAliasUsage();
    
    const report = generateReport(barrelAnalysis, importConsistency, pathAliasAnalysis);
    
    const hasIssues = report.summary.barrelExportIssues > 0 || 
                     report.summary.inconsistentImportPatterns > 0 || 
                     report.summary.unusedAliases > 0;
    
    log('\n' + '='.repeat(60), 'cyan');
    if (hasIssues) {
      log('⚠️  Import/export consistency issues found.', 'yellow');
    } else {
      log('✅ Import/export patterns are consistent!', 'green');
    }
    log('='.repeat(60), 'cyan');
    
    process.exit(0);
    
  } catch (error) {
    log(`❌ Analysis failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
