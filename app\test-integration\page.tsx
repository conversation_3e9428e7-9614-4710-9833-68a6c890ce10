'use client';

import React from 'react';
import { ComprehensiveIntegrationSystem } from '@/components/integration/ComprehensiveIntegrationSystem';

export default function TestIntegrationPage() {
  const integrationConfig = {
    enableEngagement: true,
    enableAnalytics: true,
    enableConversion: true,
    enableContent: true,
    enablePerformanceMonitoring: true,
    enablePWA: true,
    enableCrossSystemAnalytics: true,
    enableUnifiedUserTracking: true,
    enableRealTimeSync: true,
    enableErrorBoundaries: true,
    environment: 'development' as const,
    debugMode: true,
    enableDevTools: true,
    enableLazyLoading: true,
    enableCodeSplitting: true,
    enableServiceWorker: true,
    analyticsConfig: {
      googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
      hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID,
      sentryDsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
      enableHeatmaps: true,
      enableSessionRecordings: true,
      enableUserConsent: true,
      enableABTesting: true,
      enablePerformanceMonitoring: true,
      enableFeedbackWidgets: true,
      privacyCompliant: true,
      trackingLevel: 'enhanced' as const
    },
    engagementConfig: {
      enableNotifications: true,
      enableStatsCounter: true,
      enableAIAssistant: true,
      enableFAQ: true,
      enableExitIntent: true,
      enableScrollCTAs: true,
      enableUrgencyTimers: true,
      enableSocialSharing: true,
      enableAchievements: true,
      enableAnalytics: true,
      trackEngagement: true,
      theme: 'auto' as const,
      position: 'bottom-right' as const,
      animationLevel: 'standard' as const
    },
    conversionConfig: {
      enableExitIntent: true,
      enableScrollTriggers: true,
      enableUrgencyTimers: true,
      enableScarcityIndicators: true,
      enableSocialProof: true,
      enablePersonalization: true,
      enableABTesting: true
    }
  };

  const userContext = {
    id: 'test-user-123',
    name: 'Test User',
    email: '<EMAIL>',
    currentPage: '/test-integration',
    sessionId: `session-${Date.now()}`,
    progress: {
      currentLesson: 'solidity-basics-1',
      completedLessons: 5,
      currentCourse: 'solidity-fundamentals',
      totalXP: 250,
      level: 3,
      streak: 7
    },
    preferences: {
      reducedMotion: false,
      notifications: true,
      sounds: true,
      theme: 'auto' as const
    },
    metadata: {
      signupDate: new Date('2024-01-01'),
      lastLoginDate: new Date(),
      deviceType: 'desktop' as const,
      browser: 'Chrome',
      location: 'US'
    }
  };

  return (
    <ComprehensiveIntegrationSystem
      config={integrationConfig}
      userContext={userContext}
      onSystemEvent={(event, data) => {
        console.log('System Event:', event, data);
      }}
      onError={(error, context) => {
        console.error('System Error:', error, context);
      }}
    >
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
        <div className="container mx-auto px-4 py-16">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              🚀 Integration Test Page
            </h1>
            <p className="text-xl text-gray-300 mb-8">
              Testing all comprehensive systems integration
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {/* Analytics System Test */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-4">📊 Analytics System</h3>
              <ul className="text-gray-300 space-y-2">
                <li>✅ Google Analytics 4 Integration</li>
                <li>✅ Hotjar Heatmaps & Recordings</li>
                <li>✅ A/B Testing Framework</li>
                <li>✅ User Segmentation</li>
                <li>✅ Performance Monitoring</li>
              </ul>
            </div>

            {/* Engagement System Test */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-4">🎮 Engagement System</h3>
              <ul className="text-gray-300 space-y-2">
                <li>✅ Real-time Notifications</li>
                <li>✅ Animated Stats Counter</li>
                <li>✅ AI Assistant Widget</li>
                <li>✅ Interactive FAQ</li>
                <li>✅ Social Sharing</li>
              </ul>
            </div>

            {/* Conversion System Test */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-4">🔄 Conversion System</h3>
              <ul className="text-gray-300 space-y-2">
                <li>✅ Exit Intent Detection</li>
                <li>✅ Scroll-triggered CTAs</li>
                <li>✅ Social Proof Display</li>
                <li>✅ Urgency Timers</li>
                <li>✅ Personalization</li>
              </ul>
            </div>

            {/* Performance System Test */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-4">⚡ Performance System</h3>
              <ul className="text-gray-300 space-y-2">
                <li>✅ Core Web Vitals Tracking</li>
                <li>✅ Error Monitoring</li>
                <li>✅ Performance Alerts</li>
                <li>✅ Bundle Optimization</li>
                <li>✅ Skeleton Loading</li>
              </ul>
            </div>

            {/* PWA System Test */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-4">📱 PWA System</h3>
              <ul className="text-gray-300 space-y-2">
                <li>✅ Service Worker</li>
                <li>✅ Offline Functionality</li>
                <li>✅ Push Notifications</li>
                <li>✅ App Installation</li>
                <li>✅ Background Sync</li>
              </ul>
            </div>

            {/* Integration System Test */}
            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-6 border border-white/20">
              <h3 className="text-xl font-semibold text-white mb-4">🔧 Integration System</h3>
              <ul className="text-gray-300 space-y-2">
                <li>✅ Cross-system Events</li>
                <li>✅ Error Boundaries</li>
                <li>✅ Health Monitoring</li>
                <li>✅ Dev Tools</li>
                <li>✅ Configuration</li>
              </ul>
            </div>
          </div>

          <div className="text-center">
            <div className="bg-green-500/20 border border-green-500/30 rounded-lg p-6 inline-block">
              <h2 className="text-2xl font-bold text-green-400 mb-2">
                🎉 All Systems Operational!
              </h2>
              <p className="text-green-300">
                The comprehensive Solidity Learning Platform is ready for production deployment.
              </p>
            </div>
          </div>

          {/* Test Buttons */}
          <div className="mt-12 flex flex-wrap gap-4 justify-center">
            <button
              onClick={() => {
                // Test analytics tracking
                if (typeof window !== 'undefined' && window.gtag) {
                  window.gtag('event', 'test_button_click', {
                    button_name: 'analytics_test',
                    page: '/test-integration'
                  });
                }
              }}
              className="px-6 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              Test Analytics
            </button>
            
            <button
              onClick={() => {
                // Test performance tracking
                const startTime = performance.now();
                setTimeout(() => {
                  const endTime = performance.now();
                  console.log('Performance test completed in:', endTime - startTime, 'ms');
                }, 100);
              }}
              className="px-6 py-3 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors"
            >
              Test Performance
            </button>
            
            <button
              onClick={() => {
                // Test error reporting
                try {
                  throw new Error('Test error for monitoring system');
                } catch (error) {
                  console.error('Test error caught:', error);
                }
              }}
              className="px-6 py-3 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
            >
              Test Error Handling
            </button>
          </div>
        </div>
      </div>
    </ComprehensiveIntegrationSystem>
  );
}
