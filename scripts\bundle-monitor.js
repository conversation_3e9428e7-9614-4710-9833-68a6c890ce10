#!/usr/bin/env node

/**
 * Bundle Size Monitoring and Performance Regression Detection
 * Tracks bundle size changes and alerts on performance regressions
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CONFIG = {
  bundleDirectory: '.next/static/chunks',
  reportDirectory: 'reports/bundle-monitoring',
  thresholds: {
    javascript: 400, // KB
    css: 100, // KB
    total: 500, // KB
    regressionPercent: 10 // % increase that triggers alert
  },
  alertChannels: {
    console: true,
    file: true,
    github: process.env.CI === 'true' // Only in CI
  }
};

// Colors for output
const colors = {
  reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m',
  yellow: '\x1b[33m', blue: '\x1b[34m', cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

// Ensure report directory exists
function ensureReportDirectory() {
  if (!fs.existsSync(CONFIG.reportDirectory)) {
    fs.mkdirSync(CONFIG.reportDirectory, { recursive: true });
  }
}

// Get bundle size information
function analyzeBundleSize() {
  const bundleInfo = {
    timestamp: new Date().toISOString(),
    javascript: 0,
    css: 0,
    chunks: [],
    total: 0
  };

  if (!fs.existsSync(CONFIG.bundleDirectory)) {
    logWarning('Bundle directory not found. Run build first.');
    return bundleInfo;
  }

  const files = fs.readdirSync(CONFIG.bundleDirectory);
  
  files.forEach(file => {
    const filePath = path.join(CONFIG.bundleDirectory, file);
    const stats = fs.statSync(filePath);
    const sizeKB = Math.round(stats.size / 1024);
    
    const chunkInfo = {
      name: file,
      size: sizeKB,
      type: file.endsWith('.css') ? 'css' : 'javascript'
    };
    
    bundleInfo.chunks.push(chunkInfo);
    
    if (file.endsWith('.css')) {
      bundleInfo.css += sizeKB;
    } else {
      bundleInfo.javascript += sizeKB;
    }
  });

  bundleInfo.total = bundleInfo.javascript + bundleInfo.css;
  
  return bundleInfo;
}

// Load previous bundle report
function loadPreviousReport() {
  const reportPath = path.join(CONFIG.reportDirectory, 'latest-bundle-report.json');
  
  if (fs.existsSync(reportPath)) {
    try {
      return JSON.parse(fs.readFileSync(reportPath, 'utf8'));
    } catch (error) {
      logWarning(`Could not load previous report: ${error.message}`);
    }
  }
  
  return null;
}

// Save bundle report
function saveBundleReport(bundleInfo) {
  ensureReportDirectory();
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(CONFIG.reportDirectory, `bundle-report-${timestamp}.json`);
  const latestPath = path.join(CONFIG.reportDirectory, 'latest-bundle-report.json');
  
  fs.writeFileSync(reportPath, JSON.stringify(bundleInfo, null, 2));
  fs.writeFileSync(latestPath, JSON.stringify(bundleInfo, null, 2));
  
  return reportPath;
}

// Compare with previous report
function compareWithPrevious(current, previous) {
  if (!previous) {
    return {
      isRegression: false,
      changes: {
        javascript: { absolute: current.javascript, percent: 0 },
        css: { absolute: current.css, percent: 0 },
        total: { absolute: current.total, percent: 0 }
      }
    };
  }

  const changes = {
    javascript: {
      absolute: current.javascript - previous.javascript,
      percent: previous.javascript > 0 ? ((current.javascript - previous.javascript) / previous.javascript) * 100 : 0
    },
    css: {
      absolute: current.css - previous.css,
      percent: previous.css > 0 ? ((current.css - previous.css) / previous.css) * 100 : 0
    },
    total: {
      absolute: current.total - previous.total,
      percent: previous.total > 0 ? ((current.total - previous.total) / previous.total) * 100 : 0
    }
  };

  const isRegression = changes.total.percent > CONFIG.thresholds.regressionPercent;

  return { isRegression, changes };
}

// Check against thresholds
function checkThresholds(bundleInfo) {
  const violations = [];

  if (bundleInfo.javascript > CONFIG.thresholds.javascript) {
    violations.push({
      type: 'javascript',
      actual: bundleInfo.javascript,
      threshold: CONFIG.thresholds.javascript,
      severity: 'high'
    });
  }

  if (bundleInfo.css > CONFIG.thresholds.css) {
    violations.push({
      type: 'css',
      actual: bundleInfo.css,
      threshold: CONFIG.thresholds.css,
      severity: 'medium'
    });
  }

  if (bundleInfo.total > CONFIG.thresholds.total) {
    violations.push({
      type: 'total',
      actual: bundleInfo.total,
      threshold: CONFIG.thresholds.total,
      severity: 'high'
    });
  }

  return violations;
}

// Generate monitoring report
function generateMonitoringReport(bundleInfo, comparison, violations) {
  const report = {
    timestamp: bundleInfo.timestamp,
    bundleSize: {
      javascript: bundleInfo.javascript,
      css: bundleInfo.css,
      total: bundleInfo.total
    },
    thresholds: CONFIG.thresholds,
    comparison,
    violations,
    status: violations.length === 0 && !comparison.isRegression ? 'pass' : 'fail',
    recommendations: []
  };

  // Generate recommendations
  if (violations.length > 0) {
    violations.forEach(violation => {
      switch (violation.type) {
        case 'javascript':
          report.recommendations.push('Consider implementing code splitting and removing unused dependencies');
          break;
        case 'css':
          report.recommendations.push('Optimize CSS by removing unused styles and using CSS-in-JS');
          break;
        case 'total':
          report.recommendations.push('Overall bundle size is too large. Review all optimization strategies');
          break;
      }
    });
  }

  if (comparison.isRegression) {
    report.recommendations.push('Bundle size has increased significantly. Review recent changes');
  }

  return report;
}

// Alert functions
function alertConsole(report) {
  logHeader('Bundle Size Monitoring Report');
  
  log(`📊 Current Bundle Size:`, 'blue');
  log(`   JavaScript: ${report.bundleSize.javascript}KB`, 'blue');
  log(`   CSS: ${report.bundleSize.css}KB`, 'blue');
  log(`   Total: ${report.bundleSize.total}KB`, 'blue');

  if (report.comparison.changes) {
    log(`\n📈 Changes from Previous Build:`, 'blue');
    const { changes } = report.comparison;
    
    Object.entries(changes).forEach(([type, change]) => {
      const sign = change.absolute >= 0 ? '+' : '';
      const color = change.absolute > 0 ? 'yellow' : 'green';
      log(`   ${type}: ${sign}${change.absolute}KB (${sign}${change.percent.toFixed(1)}%)`, color);
    });
  }

  if (report.violations.length > 0) {
    log(`\n🚨 Threshold Violations:`, 'red');
    report.violations.forEach(violation => {
      log(`   ${violation.type}: ${violation.actual}KB > ${violation.threshold}KB`, 'red');
    });
  }

  if (report.comparison.isRegression) {
    logError('Performance regression detected!');
  }

  if (report.recommendations.length > 0) {
    log(`\n💡 Recommendations:`, 'yellow');
    report.recommendations.forEach(rec => {
      log(`   • ${rec}`, 'yellow');
    });
  }

  if (report.status === 'pass') {
    logSuccess('Bundle size monitoring: PASSED');
  } else {
    logError('Bundle size monitoring: FAILED');
  }
}

// Main monitoring function
function runBundleMonitoring() {
  logHeader('Bundle Size Monitoring');

  try {
    // Analyze current bundle
    const currentBundle = analyzeBundleSize();
    
    // Load previous report for comparison
    const previousBundle = loadPreviousReport();
    
    // Compare with previous
    const comparison = compareWithPrevious(currentBundle, previousBundle);
    
    // Check thresholds
    const violations = checkThresholds(currentBundle);
    
    // Generate report
    const report = generateMonitoringReport(currentBundle, comparison, violations);
    
    // Save report
    const reportPath = saveBundleReport(currentBundle);
    log(`📄 Report saved to: ${reportPath}`, 'blue');
    
    // Alert
    if (CONFIG.alertChannels.console) {
      alertConsole(report);
    }
    
    // Exit with appropriate code
    process.exit(report.status === 'pass' ? 0 : 1);
    
  } catch (error) {
    logError(`Bundle monitoring failed: ${error.message}`);
    process.exit(1);
  }
}

// CLI handling
if (require.main === module) {
  runBundleMonitoring();
}

module.exports = {
  analyzeBundleSize,
  runBundleMonitoring,
  CONFIG
};
