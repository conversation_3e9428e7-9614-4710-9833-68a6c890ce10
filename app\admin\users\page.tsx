'use client';

import React, { Suspense, lazy } from 'react';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { AdminGuard } from '@/components/admin/AdminGuard';
import { ADMIN_PERMISSIONS } from '@/lib/admin/auth';

// Dynamically import UserManagement for better code splitting
const UserManagement = lazy(() => import('@/components/admin/UserManagement').then(mod => ({ default: mod.UserManagement })));

export default function AdminUsersPage() {
  return (
    <AdminGuard requiredPermission={ADMIN_PERMISSIONS.USERS_READ}>
      <AdminLayout currentPage="users">
        <Suspense fallback={
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-gray-400">Loading User Management...</p>
            </div>
          </div>
        }>
          <UserManagement />
        </Suspense>
      </AdminLayout>
    </AdminGuard>
  );
}
