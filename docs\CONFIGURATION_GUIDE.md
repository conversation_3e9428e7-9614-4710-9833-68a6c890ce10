# Configuration Guide - Solidity Learning Platform

## Table of Contents

1. [Environment Variables](#environment-variables)
2. [Analytics Configuration](#analytics-configuration)
3. [Performance Monitoring Setup](#performance-monitoring-setup)
4. [A/B Testing Configuration](#ab-testing-configuration)
5. [PWA Service Worker Configuration](#pwa-service-worker-configuration)
6. [Integration System Configuration](#integration-system-configuration)
7. [Troubleshooting](#troubleshooting)

## Environment Variables

### Required Variables

```bash
# Analytics Services
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX                    # Google Analytics 4 Measurement ID
NEXT_PUBLIC_HOTJAR_ID=1234567                     # Hotjar Site ID
SENTRY_DSN=https://<EMAIL>/xxx   # Sentry DSN for error tracking

# Performance Monitoring
NEXT_PUBLIC_PERFORMANCE_API_KEY=your-api-key      # Performance monitoring API key
LIGHTHOUSE_API_KEY=your-lighthouse-key            # Lighthouse CI API key

# A/B Testing
NEXT_PUBLIC_AB_TESTING_KEY=your-ab-key            # A/B testing service key
OPTIMIZELY_SDK_KEY=your-optimizely-key            # Optimizely SDK key (optional)

# PWA Configuration
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your-vapid-key       # VAPID public key for push notifications
VAPID_PRIVATE_KEY=your-vapid-private-key          # VAPID private key (server-side)

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true                 # Enable/disable analytics
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true   # Enable/disable performance monitoring
NEXT_PUBLIC_ENABLE_PWA=true                       # Enable/disable PWA features
NEXT_PUBLIC_DEBUG_MODE=false                      # Enable debug logging
```

### Optional Variables

```bash
# Advanced Analytics
NEXT_PUBLIC_MIXPANEL_TOKEN=your-mixpanel-token    # Mixpanel token (optional)
AMPLITUDE_API_KEY=your-amplitude-key              # Amplitude API key (optional)

# Performance Thresholds
PERFORMANCE_LCP_THRESHOLD=2500                    # Largest Contentful Paint threshold (ms)
PERFORMANCE_FID_THRESHOLD=100                     # First Input Delay threshold (ms)
PERFORMANCE_CLS_THRESHOLD=0.1                     # Cumulative Layout Shift threshold

# Privacy & Compliance
NEXT_PUBLIC_PRIVACY_MODE=strict                   # Privacy mode: strict, standard, minimal
NEXT_PUBLIC_COOKIE_CONSENT_REQUIRED=true          # Require cookie consent
GDPR_COMPLIANCE_MODE=true                         # Enable GDPR compliance features
```

## Analytics Configuration

### ComprehensiveAnalyticsSystem Configuration

```typescript
interface AnalyticsConfig {
  // Service Integration
  googleAnalyticsId?: string;
  hotjarId?: string;
  sentryDsn?: string;
  mixpanelToken?: string;
  amplitudeApiKey?: string;
  
  // Feature Toggles
  enableHeatmaps: boolean;
  enableSessionRecordings: boolean;
  enableUserConsent: boolean;
  enableABTesting: boolean;
  enablePerformanceMonitoring: boolean;
  enableFeedbackWidgets: boolean;
  
  // Privacy & Compliance
  privacyCompliant: boolean;
  cookieConsentRequired: boolean;
  gdprCompliance: boolean;
  ccpaCompliance: boolean;
  
  // Tracking Configuration
  trackingLevel: 'minimal' | 'standard' | 'enhanced';
  anonymizeIp: boolean;
  respectDoNotTrack: boolean;
  
  // Performance Settings
  sampleRate: number;              // 0.0 to 1.0
  sessionTimeout: number;          // in minutes
  pageviewTimeout: number;         // in milliseconds
  
  // Custom Dimensions
  customDimensions?: {
    userType?: string;
    subscriptionPlan?: string;
    experimentVariant?: string;
    userSegment?: string;
  };
}
```

### Example Configuration

```typescript
// Basic Configuration
const basicAnalyticsConfig: AnalyticsConfig = {
  googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
  enableHeatmaps: false,
  enableSessionRecordings: false,
  enableUserConsent: true,
  privacyCompliant: true,
  trackingLevel: 'minimal',
  sampleRate: 0.1
};

// Advanced Configuration
const advancedAnalyticsConfig: AnalyticsConfig = {
  googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
  hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID,
  sentryDsn: process.env.SENTRY_DSN,
  enableHeatmaps: true,
  enableSessionRecordings: true,
  enableUserConsent: true,
  enableABTesting: true,
  enablePerformanceMonitoring: true,
  enableFeedbackWidgets: true,
  privacyCompliant: true,
  trackingLevel: 'enhanced',
  sampleRate: 1.0,
  customDimensions: {
    userType: 'student',
    subscriptionPlan: 'premium'
  }
};
```

## Performance Monitoring Setup

### Performance Configuration

```typescript
interface PerformanceConfig {
  // Core Web Vitals Thresholds
  thresholds: {
    lcp: number;        // Largest Contentful Paint (ms)
    fid: number;        // First Input Delay (ms)
    cls: number;        // Cumulative Layout Shift
    ttfb: number;       // Time to First Byte (ms)
    fcp: number;        // First Contentful Paint (ms)
  };
  
  // Monitoring Settings
  enableRealTimeMonitoring: boolean;
  enableErrorTracking: boolean;
  enableResourceTiming: boolean;
  enableNavigationTiming: boolean;
  enableUserTiming: boolean;
  
  // Alert Configuration
  alertThresholds: {
    errorRate: number;           // Error rate threshold (%)
    responseTime: number;        // Response time threshold (ms)
    memoryUsage: number;         // Memory usage threshold (MB)
  };
  
  // Sampling
  performanceSampleRate: number;  // 0.0 to 1.0
  errorSampleRate: number;        // 0.0 to 1.0
}
```

### Example Performance Setup

```typescript
const performanceConfig: PerformanceConfig = {
  thresholds: {
    lcp: 2500,
    fid: 100,
    cls: 0.1,
    ttfb: 600,
    fcp: 1800
  },
  enableRealTimeMonitoring: true,
  enableErrorTracking: true,
  enableResourceTiming: true,
  enableNavigationTiming: true,
  enableUserTiming: true,
  alertThresholds: {
    errorRate: 5,
    responseTime: 3000,
    memoryUsage: 100
  },
  performanceSampleRate: 0.1,
  errorSampleRate: 1.0
};
```

## A/B Testing Configuration

### A/B Testing Framework Setup

```typescript
interface ABTestingConfig {
  // Service Configuration
  provider: 'internal' | 'optimizely' | 'google-optimize';
  apiKey?: string;
  projectId?: string;
  
  // Test Configuration
  defaultVariation: string;
  enableAutoAssignment: boolean;
  enableStatisticalSignificance: boolean;
  confidenceLevel: number;        // 0.90, 0.95, 0.99
  minimumSampleSize: number;
  
  // Targeting
  enableUserSegmentation: boolean;
  segmentationRules: SegmentationRule[];
  
  // Tracking
  trackConversions: boolean;
  conversionEvents: string[];
  revenueTracking: boolean;
}

interface SegmentationRule {
  id: string;
  name: string;
  conditions: {
    attribute: string;
    operator: 'equals' | 'contains' | 'greater_than' | 'less_than';
    value: any;
  }[];
}
```

### Example A/B Testing Setup

```typescript
const abTestingConfig: ABTestingConfig = {
  provider: 'internal',
  defaultVariation: 'control',
  enableAutoAssignment: true,
  enableStatisticalSignificance: true,
  confidenceLevel: 0.95,
  minimumSampleSize: 1000,
  enableUserSegmentation: true,
  segmentationRules: [
    {
      id: 'new_users',
      name: 'New Users',
      conditions: [
        {
          attribute: 'user.isNew',
          operator: 'equals',
          value: true
        }
      ]
    }
  ],
  trackConversions: true,
  conversionEvents: ['trial_signup', 'lesson_completed', 'payment_success'],
  revenueTracking: true
};
```

## PWA Service Worker Configuration

### Service Worker Setup

```typescript
// public/sw.js configuration
const SW_CONFIG = {
  // Cache Configuration
  cacheNames: {
    static: 'static-cache-v1',
    dynamic: 'dynamic-cache-v1',
    lessons: 'lessons-cache-v1',
    images: 'images-cache-v1'
  },
  
  // Cache Strategies
  strategies: {
    static: 'CacheFirst',
    api: 'NetworkFirst',
    images: 'CacheFirst',
    lessons: 'StaleWhileRevalidate'
  },
  
  // Cache Expiration
  expiration: {
    static: 30 * 24 * 60 * 60,      // 30 days
    dynamic: 7 * 24 * 60 * 60,      // 7 days
    lessons: 24 * 60 * 60,          // 1 day
    images: 30 * 24 * 60 * 60       // 30 days
  },
  
  // Background Sync
  backgroundSync: {
    enabled: true,
    queueName: 'analytics-queue',
    maxRetentionTime: 24 * 60 * 60 * 1000  // 24 hours
  },
  
  // Push Notifications
  pushNotifications: {
    enabled: true,
    vapidPublicKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
    defaultIcon: '/icons/icon-192x192.png',
    defaultBadge: '/icons/badge-72x72.png'
  }
};
```

### PWA Manifest Configuration

```json
{
  "name": "Solidity Learning Platform",
  "short_name": "SolidityLearn",
  "description": "Learn Solidity and blockchain development",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#3b82f6",
  "orientation": "portrait-primary",
  "categories": ["education", "developer", "blockchain"],
  "lang": "en",
  "dir": "ltr",
  "icons": [
    {
      "src": "/icons/icon-72x72.png",
      "sizes": "72x72",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "maskable any"
    },
    {
      "src": "/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "maskable any"
    }
  ],
  "shortcuts": [
    {
      "name": "Start Learning",
      "short_name": "Learn",
      "description": "Jump into a lesson",
      "url": "/learn",
      "icons": [
        {
          "src": "/icons/shortcut-learn.png",
          "sizes": "96x96"
        }
      ]
    },
    {
      "name": "My Progress",
      "short_name": "Progress",
      "description": "View your learning progress",
      "url": "/progress",
      "icons": [
        {
          "src": "/icons/shortcut-progress.png",
          "sizes": "96x96"
        }
      ]
    }
  ]
}
```

## Integration System Configuration

### Comprehensive Integration Setup

```typescript
interface IntegrationConfig {
  // System Enablement
  enableEngagement: boolean;
  enableAnalytics: boolean;
  enableConversion: boolean;
  enableContent: boolean;
  enablePerformanceMonitoring: boolean;
  enablePWA: boolean;
  
  // Cross-system Features
  enableCrossSystemAnalytics: boolean;
  enableUnifiedUserTracking: boolean;
  enableRealTimeSync: boolean;
  enableErrorBoundaries: boolean;
  
  // Environment Settings
  environment: 'development' | 'staging' | 'production';
  debugMode: boolean;
  enableDevTools: boolean;
  
  // Performance Settings
  enableLazyLoading: boolean;
  enableCodeSplitting: boolean;
  enableServiceWorker: boolean;
  
  // Nested Configurations
  analyticsConfig: AnalyticsConfig;
  engagementConfig: EngagementConfig;
  conversionConfig: ConversionConfig;
}
```

### Production Configuration Example

```typescript
const productionConfig: IntegrationConfig = {
  enableEngagement: true,
  enableAnalytics: true,
  enableConversion: true,
  enableContent: true,
  enablePerformanceMonitoring: true,
  enablePWA: true,
  enableCrossSystemAnalytics: true,
  enableUnifiedUserTracking: true,
  enableRealTimeSync: true,
  enableErrorBoundaries: true,
  environment: 'production',
  debugMode: false,
  enableDevTools: false,
  enableLazyLoading: true,
  enableCodeSplitting: true,
  enableServiceWorker: true,
  analyticsConfig: {
    googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
    hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID,
    sentryDsn: process.env.SENTRY_DSN,
    enableHeatmaps: true,
    enableSessionRecordings: true,
    enableUserConsent: true,
    privacyCompliant: true,
    trackingLevel: 'enhanced'
  },
  engagementConfig: {
    enableNotifications: true,
    enableStatsCounter: true,
    enableAIAssistant: true,
    enableFAQ: true,
    theme: 'auto',
    position: 'bottom-right',
    animationLevel: 'standard'
  },
  conversionConfig: {
    enableExitIntent: true,
    enableScrollTriggers: true,
    enableUrgencyTimers: true,
    enableSocialProof: true,
    enablePersonalization: true,
    enableABTesting: true
  }
};
```

## Troubleshooting

### Common Configuration Issues

#### 1. Analytics Not Tracking

**Problem**: Events not appearing in Google Analytics

**Solutions**:
```typescript
// Check environment variables
console.log('GA ID:', process.env.NEXT_PUBLIC_GA_ID);

// Verify initialization
const { isAnalyticsReady } = useComprehensiveAnalytics();
console.log('Analytics Ready:', isAnalyticsReady);

// Enable debug mode
const config = {
  debugMode: true,
  trackingLevel: 'enhanced'
};
```

#### 2. Performance Monitoring Issues

**Problem**: Performance metrics not being collected

**Solutions**:
```typescript
// Check browser support
if ('performance' in window) {
  console.log('Performance API supported');
}

// Verify configuration
const performanceConfig = {
  enableRealTimeMonitoring: true,
  performanceSampleRate: 1.0  // Increase for testing
};
```

#### 3. PWA Installation Issues

**Problem**: Install prompt not appearing

**Solutions**:
```typescript
// Check manifest
fetch('/manifest.json')
  .then(response => response.json())
  .then(manifest => console.log('Manifest:', manifest));

// Verify service worker
navigator.serviceWorker.getRegistration()
  .then(registration => console.log('SW Registration:', registration));
```

#### 4. A/B Testing Not Working

**Problem**: Users not being assigned to test variations

**Solutions**:
```typescript
// Check test configuration
const { activeTests, userVariant } = useABTesting();
console.log('Active Tests:', activeTests);
console.log('User Variant:', userVariant);

// Verify user segmentation
const { userSegment } = useComprehensiveAnalytics();
console.log('User Segment:', userSegment);
```

### Debug Mode Configuration

```typescript
// Enable comprehensive debugging
const debugConfig = {
  debugMode: true,
  enableDevTools: true,
  environment: 'development',
  analyticsConfig: {
    trackingLevel: 'enhanced',
    sampleRate: 1.0
  },
  performanceConfig: {
    enableRealTimeMonitoring: true,
    performanceSampleRate: 1.0
  }
};
```

### Health Check Endpoints

```typescript
// API health check endpoints
GET /api/health/analytics     // Analytics system status
GET /api/health/performance   // Performance monitoring status
GET /api/health/pwa          // PWA features status
GET /api/health/system       // Overall system health
```

### Configuration Validation

```typescript
// Validate configuration on startup
function validateConfig(config: IntegrationConfig): boolean {
  const errors: string[] = [];
  
  if (config.enableAnalytics && !config.analyticsConfig.googleAnalyticsId) {
    errors.push('Google Analytics ID required when analytics enabled');
  }
  
  if (config.enablePerformanceMonitoring && !config.analyticsConfig.sentryDsn) {
    errors.push('Sentry DSN required for error tracking');
  }
  
  if (errors.length > 0) {
    console.error('Configuration Errors:', errors);
    return false;
  }
  
  return true;
}
```
