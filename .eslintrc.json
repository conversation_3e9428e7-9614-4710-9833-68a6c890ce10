{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"@typescript-eslint/no-unused-vars": "warn", "@typescript-eslint/no-explicit-any": "warn", "react-hooks/exhaustive-deps": "warn", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "off", "prefer-const": "warn", "no-console": "warn"}, "ignorePatterns": ["node_modules/", ".next/", "dist/", "build/", "*.config.js", "*.config.ts"]}