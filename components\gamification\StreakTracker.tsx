'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Flame, 
  Calendar, 
  TrendingUp, 
  Award, 
  Clock,
  Zap,
  Target,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { GlassCard } from '@/components/ui/Glassmorphism';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import { useXPNotifications } from '@/components/xp/XPNotification';

export interface StreakData {
  currentStreak: number;
  longestStreak: number;
  lastActivity: Date;
  streakGoal: number;
  todayCompleted: boolean;
  streakHistory: Date[];
  milestones: StreakMilestone[];
}

export interface StreakMilestone {
  days: number;
  title: string;
  description: string;
  xpReward: number;
  unlocked: boolean;
  icon: React.ComponentType<{ className?: string }>;
}

interface StreakTrackerProps {
  streakData: StreakData;
  onStreakUpdate?: (newStreak: number) => void;
  onMilestoneUnlocked?: (milestone: StreakMilestone) => void;
  className?: string;
  compact?: boolean;
}

export function StreakTracker({
  streakData,
  onStreakUpdate,
  onMilestoneUnlocked,
  className,
  compact = false
}: StreakTrackerProps) {
  const [showRecovery, setShowRecovery] = useState(false);
  const [timeUntilReset, setTimeUntilReset] = useState<string>('');
  const { triggerStreakXP } = useXPNotifications();

  // Calculate time until streak resets (midnight)
  useEffect(() => {
    const updateTimer = () => {
      const now = new Date();
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(0, 0, 0, 0);
      
      const diff = tomorrow.getTime() - now.getTime();
      const hours = Math.floor(diff / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      
      setTimeUntilReset(`${hours}h ${minutes}m`);
    };

    updateTimer();
    const interval = setInterval(updateTimer, 60000); // Update every minute
    
    return () => clearInterval(interval);
  }, []);

  // Check if streak is at risk (last activity > 20 hours ago)
  const isStreakAtRisk = () => {
    if (streakData.todayCompleted) return false;
    const now = new Date();
    const lastActivity = new Date(streakData.lastActivity);
    const hoursSinceActivity = (now.getTime() - lastActivity.getTime()) / (1000 * 60 * 60);
    return hoursSinceActivity > 20;
  };

  // Get flame intensity based on streak length
  const getFlameIntensity = () => {
    if (streakData.currentStreak === 0) return 'text-gray-400';
    if (streakData.currentStreak < 3) return 'text-orange-400';
    if (streakData.currentStreak < 7) return 'text-orange-500';
    if (streakData.currentStreak < 14) return 'text-red-500';
    if (streakData.currentStreak < 30) return 'text-red-600';
    return 'text-purple-500';
  };

  // Get next milestone
  const getNextMilestone = () => {
    return streakData.milestones.find(m => !m.unlocked && m.days > streakData.currentStreak);
  };

  const handleStreakRecovery = async () => {
    // Simulate streak recovery (would normally call API)
    const newStreak = Math.max(1, streakData.currentStreak - 1);
    onStreakUpdate?.(newStreak);
    setShowRecovery(false);
    
    // Trigger XP notification for recovery
    triggerStreakXP(50, newStreak, undefined);
  };

  if (compact) {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <motion.div
          animate={streakData.currentStreak > 0 ? { 
            scale: [1, 1.1, 1],
            rotate: [0, 5, -5, 0]
          } : {}}
          transition={{ duration: 2, repeat: Infinity }}
          className="relative"
        >
          <Flame className={cn('w-5 h-5', getFlameIntensity())} />
          {streakData.currentStreak > 0 && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute -top-1 -right-1 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center text-xs font-bold text-white"
            >
              {streakData.currentStreak}
            </motion.div>
          )}
        </motion.div>
        <div className="text-sm">
          <div className="font-semibold text-white">{streakData.currentStreak} days</div>
          <div className="text-xs text-gray-400">streak</div>
        </div>
      </div>
    );
  }

  return (
    <GlassCard className={cn('p-6', className)}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <motion.div
              animate={streakData.currentStreak > 0 ? { 
                scale: [1, 1.2, 1],
                rotate: [0, 10, -10, 0]
              } : {}}
              transition={{ duration: 3, repeat: Infinity }}
              className="relative"
            >
              <Flame className={cn('w-8 h-8', getFlameIntensity())} />
              {/* Flame particles */}
              {streakData.currentStreak > 0 && (
                <>
                  {[...Array(3)].map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0, y: 0, scale: 0 }}
                      animate={{ 
                        opacity: [0, 1, 0],
                        y: -20,
                        scale: [0, 1, 0]
                      }}
                      transition={{
                        duration: 2,
                        delay: i * 0.3,
                        repeat: Infinity
                      }}
                      className="absolute w-1 h-1 bg-orange-400 rounded-full"
                      style={{
                        left: `${20 + i * 10}%`,
                        top: '10%'
                      }}
                    />
                  ))}
                </>
              )}
            </motion.div>
            <div>
              <h3 className="text-xl font-bold text-white">Learning Streak</h3>
              <p className="text-sm text-gray-400">Keep the momentum going!</p>
            </div>
          </div>
          
          {/* Streak at risk warning */}
          {isStreakAtRisk() && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              className="flex items-center space-x-2 px-3 py-2 bg-red-500/20 border border-red-500/30 rounded-lg"
            >
              <AlertCircle className="w-4 h-4 text-red-400" />
              <span className="text-sm text-red-300 font-medium">Streak at risk!</span>
            </motion.div>
          )}
        </div>

        {/* Current Streak Display */}
        <div className="text-center space-y-2">
          <motion.div
            key={streakData.currentStreak}
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ type: 'spring', stiffness: 300 }}
            className="text-6xl font-bold text-white"
          >
            {streakData.currentStreak}
          </motion.div>
          <div className="text-lg text-gray-300">
            {streakData.currentStreak === 1 ? 'day' : 'days'} in a row
          </div>
          
          {/* Today's status */}
          <div className="flex items-center justify-center space-x-2">
            {streakData.todayCompleted ? (
              <>
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span className="text-green-400 font-medium">Today completed!</span>
              </>
            ) : (
              <>
                <Clock className="w-5 h-5 text-yellow-400" />
                <span className="text-yellow-400 font-medium">
                  {timeUntilReset} left today
                </span>
              </>
            )}
          </div>
        </div>

        {/* Progress to next milestone */}
        {(() => {
          const nextMilestone = getNextMilestone();
          if (!nextMilestone) return null;
          
          const progress = (streakData.currentStreak / nextMilestone.days) * 100;
          
          return (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-300">
                  Next milestone: {nextMilestone.title}
                </span>
                <span className="text-sm text-gray-400">
                  {nextMilestone.days - streakData.currentStreak} days to go
                </span>
              </div>
              
              <div className="w-full bg-gray-700 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${progress}%` }}
                  transition={{ duration: 1, ease: 'easeOut' }}
                  className="bg-gradient-to-r from-orange-500 to-red-500 h-2 rounded-full"
                />
              </div>
              
              <div className="text-xs text-gray-400">
                +{nextMilestone.xpReward} XP reward
              </div>
            </div>
          );
        })()}

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center p-3 bg-white/5 rounded-lg">
            <div className="text-2xl font-bold text-white">{streakData.longestStreak}</div>
            <div className="text-sm text-gray-400">Longest streak</div>
          </div>
          <div className="text-center p-3 bg-white/5 rounded-lg">
            <div className="text-2xl font-bold text-white">{streakData.streakGoal}</div>
            <div className="text-sm text-gray-400">Goal</div>
          </div>
        </div>

        {/* Streak recovery option */}
        {isStreakAtRisk() && !streakData.todayCompleted && (
          <div className="space-y-3">
            <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-yellow-400 mt-0.5" />
                <div className="flex-1">
                  <h4 className="font-medium text-yellow-300">Streak Recovery Available</h4>
                  <p className="text-sm text-yellow-200/80 mt-1">
                    Complete 2 lessons today to maintain your {streakData.currentStreak}-day streak!
                  </p>
                </div>
              </div>
            </div>
            
            <EnhancedButton
              onClick={() => setShowRecovery(true)}
              className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600"
              touchTarget
            >
              <Zap className="w-4 h-4 mr-2" />
              Use Streak Recovery
            </EnhancedButton>
          </div>
        )}
      </div>

      {/* Streak Recovery Modal */}
      <AnimatePresence>
        {showRecovery && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowRecovery(false)}
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
              className="bg-gray-900 border border-gray-700 rounded-xl p-6 max-w-md w-full"
            >
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto">
                  <Zap className="w-8 h-8 text-yellow-400" />
                </div>
                
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">Streak Recovery</h3>
                  <p className="text-gray-300">
                    Use streak recovery to maintain your {streakData.currentStreak}-day streak? 
                    You'll need to complete 2 lessons today.
                  </p>
                </div>
                
                <div className="flex space-x-3">
                  <EnhancedButton
                    onClick={() => setShowRecovery(false)}
                    variant="outline"
                    className="flex-1"
                  >
                    Cancel
                  </EnhancedButton>
                  <EnhancedButton
                    onClick={handleStreakRecovery}
                    className="flex-1 bg-gradient-to-r from-yellow-500 to-orange-500"
                  >
                    Use Recovery
                  </EnhancedButton>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </GlassCard>
  );
}

// Default streak milestones
export const defaultStreakMilestones: StreakMilestone[] = [
  {
    days: 3,
    title: 'Getting Started',
    description: 'Complete 3 days in a row',
    xpReward: 100,
    unlocked: false,
    icon: Target
  },
  {
    days: 7,
    title: 'Week Warrior',
    description: 'Complete a full week',
    xpReward: 250,
    unlocked: false,
    icon: Award
  },
  {
    days: 14,
    title: 'Two Week Champion',
    description: 'Two weeks of consistent learning',
    xpReward: 500,
    unlocked: false,
    icon: TrendingUp
  },
  {
    days: 30,
    title: 'Monthly Master',
    description: 'A full month of dedication',
    xpReward: 1000,
    unlocked: false,
    icon: Calendar
  },
  {
    days: 50,
    title: 'Consistency King',
    description: 'Fifty days of unwavering dedication',
    xpReward: 2000,
    unlocked: false,
    icon: Crown
  },
  {
    days: 100,
    title: 'Century Achiever',
    description: 'One hundred days of learning excellence',
    xpReward: 5000,
    unlocked: false,
    icon: Trophy
  }
];

// Streak Manager Hook
export function useStreakManager() {
  const [streakData, setStreakData] = useState<StreakData | null>(null);
  const { triggerStreakXP } = useXPNotifications();

  // Load streak data
  const loadStreakData = async (userId: string): Promise<StreakData> => {
    try {
      // In a real app, this would fetch from an API
      const mockData: StreakData = {
        currentStreak: 5,
        longestStreak: 12,
        lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        streakGoal: 30,
        todayCompleted: false,
        streakHistory: [
          new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
          new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
          new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
          new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
          new Date(Date.now() - 0 * 24 * 60 * 60 * 1000)
        ],
        milestones: defaultStreakMilestones.map(m => ({
          ...m,
          unlocked: m.days <= 5 // Mock: unlock milestones up to current streak
        }))
      };

      setStreakData(mockData);
      return mockData;
    } catch (error) {
      console.error('Failed to load streak data:', error);
      throw error;
    }
  };

  // Update streak
  const updateStreak = async (newStreakCount: number) => {
    if (!streakData) return;

    const updatedData = {
      ...streakData,
      currentStreak: newStreakCount,
      longestStreak: Math.max(streakData.longestStreak, newStreakCount),
      lastActivity: new Date(),
      todayCompleted: true
    };

    // Check for milestone unlocks
    const newMilestones = updatedData.milestones.map(milestone => {
      if (!milestone.unlocked && newStreakCount >= milestone.days) {
        // Trigger milestone achievement
        triggerStreakXP(milestone.xpReward, newStreakCount, undefined);
        return { ...milestone, unlocked: true };
      }
      return milestone;
    });

    updatedData.milestones = newMilestones;
    setStreakData(updatedData);

    // In a real app, this would save to an API
    return updatedData;
  };

  // Break streak
  const breakStreak = async () => {
    if (!streakData) return;

    const updatedData = {
      ...streakData,
      currentStreak: 0,
      lastActivity: new Date(),
      todayCompleted: false
    };

    setStreakData(updatedData);
    return updatedData;
  };

  // Extend streak
  const extendStreak = async () => {
    if (!streakData) return;

    return updateStreak(streakData.currentStreak + 1);
  };

  return {
    streakData,
    loadStreakData,
    updateStreak,
    breakStreak,
    extendStreak
  };
}
