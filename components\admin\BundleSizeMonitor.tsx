'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle,
  Package,
  Zap,
  Clock,
  Download
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

interface BundleData {
  timestamp: string;
  javascript: number;
  css: number;
  total: number;
  chunks: Array<{
    name: string;
    size: number;
    type: 'javascript' | 'css';
  }>;
}

interface BundleReport {
  bundleSize: BundleData;
  thresholds: {
    javascript: number;
    css: number;
    total: number;
  };
  violations: Array<{
    type: string;
    actual: number;
    threshold: number;
    severity: 'high' | 'medium' | 'low';
  }>;
  status: 'pass' | 'fail';
  recommendations: string[];
}

export function BundleSizeMonitor() {
  const [bundleReport, setBundleReport] = useState<BundleReport | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadBundleReport();
  }, []);

  const loadBundleReport = async () => {
    try {
      setLoading(true);
      // In a real implementation, this would fetch from an API
      // For now, we'll simulate the data
      const mockReport: BundleReport = {
        bundleSize: {
          timestamp: new Date().toISOString(),
          javascript: 380,
          css: 65,
          total: 445,
          chunks: [
            { name: 'main.js', size: 120, type: 'javascript' },
            { name: 'vendors.js', size: 180, type: 'javascript' },
            { name: 'monaco.js', size: 80, type: 'javascript' },
            { name: 'styles.css', size: 45, type: 'css' },
            { name: 'components.css', size: 20, type: 'css' }
          ]
        },
        thresholds: {
          javascript: 400,
          css: 100,
          total: 500
        },
        violations: [],
        status: 'pass',
        recommendations: [
          'Consider implementing tree-shaking for unused code',
          'Optimize image assets for better compression'
        ]
      };

      setBundleReport(mockReport);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load bundle report');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pass': return 'text-green-500';
      case 'fail': return 'text-red-500';
      default: return 'text-yellow-500';
    }
  };

  const getProgressColor = (value: number, threshold: number) => {
    const percentage = (value / threshold) * 100;
    if (percentage <= 70) return 'bg-green-500';
    if (percentage <= 90) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Bundle Size Monitor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Bundle Size Monitor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-red-500">
            <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
            <p>{error}</p>
            <Button onClick={loadBundleReport} className="mt-4">
              Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!bundleReport) return null;

  const { bundleSize, thresholds, violations, status, recommendations } = bundleReport;

  return (
    <div className="space-y-6">
      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Bundle Size Monitor
            </div>
            <Badge variant={status === 'pass' ? 'default' : 'destructive'}>
              {status === 'pass' ? (
                <CheckCircle className="h-4 w-4 mr-1" />
              ) : (
                <AlertTriangle className="h-4 w-4 mr-1" />
              )}
              {status.toUpperCase()}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* JavaScript Bundle */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">JavaScript</span>
                <span className="text-sm text-gray-500">
                  {bundleSize.javascript}KB / {thresholds.javascript}KB
                </span>
              </div>
              <Progress 
                value={(bundleSize.javascript / thresholds.javascript) * 100} 
                className="h-2"
              />
              <div className="flex items-center gap-1 text-xs">
                {bundleSize.javascript <= thresholds.javascript ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <AlertTriangle className="h-3 w-3 text-red-500" />
                )}
                <span className={bundleSize.javascript <= thresholds.javascript ? 'text-green-500' : 'text-red-500'}>
                  {((bundleSize.javascript / thresholds.javascript) * 100).toFixed(1)}% of limit
                </span>
              </div>
            </div>

            {/* CSS Bundle */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">CSS</span>
                <span className="text-sm text-gray-500">
                  {bundleSize.css}KB / {thresholds.css}KB
                </span>
              </div>
              <Progress 
                value={(bundleSize.css / thresholds.css) * 100} 
                className="h-2"
              />
              <div className="flex items-center gap-1 text-xs">
                {bundleSize.css <= thresholds.css ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <AlertTriangle className="h-3 w-3 text-red-500" />
                )}
                <span className={bundleSize.css <= thresholds.css ? 'text-green-500' : 'text-red-500'}>
                  {((bundleSize.css / thresholds.css) * 100).toFixed(1)}% of limit
                </span>
              </div>
            </div>

            {/* Total Bundle */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Total</span>
                <span className="text-sm text-gray-500">
                  {bundleSize.total}KB / {thresholds.total}KB
                </span>
              </div>
              <Progress 
                value={(bundleSize.total / thresholds.total) * 100} 
                className="h-2"
              />
              <div className="flex items-center gap-1 text-xs">
                {bundleSize.total <= thresholds.total ? (
                  <CheckCircle className="h-3 w-3 text-green-500" />
                ) : (
                  <AlertTriangle className="h-3 w-3 text-red-500" />
                )}
                <span className={bundleSize.total <= thresholds.total ? 'text-green-500' : 'text-red-500'}>
                  {((bundleSize.total / thresholds.total) * 100).toFixed(1)}% of limit
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chunk Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Chunk Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {bundleSize.chunks
              .sort((a, b) => b.size - a.size)
              .map((chunk, index) => (
                <motion.div
                  key={chunk.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${chunk.type === 'javascript' ? 'bg-blue-500' : 'bg-green-500'}`} />
                    <span className="font-medium">{chunk.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {chunk.type}
                    </Badge>
                  </div>
                  <span className="text-sm font-mono">{chunk.size}KB</span>
                </motion.div>
              ))}
          </div>
        </CardContent>
      </Card>

      {/* Violations & Recommendations */}
      {(violations.length > 0 || recommendations.length > 0) && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Violations */}
          {violations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-red-500">
                  <AlertTriangle className="h-5 w-5" />
                  Threshold Violations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {violations.map((violation, index) => (
                    <div key={index} className="p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                      <div className="flex items-center justify-between">
                        <span className="font-medium capitalize">{violation.type}</span>
                        <Badge variant="destructive">{violation.severity}</Badge>
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {violation.actual}KB exceeds {violation.threshold}KB limit
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Optimization Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recommendations.map((recommendation, index) => (
                    <div key={index} className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-sm">{recommendation}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Button onClick={loadBundleReport} variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Refresh Report
            </Button>
            <Button variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              View History
            </Button>
            <Button variant="outline">
              <TrendingUp className="h-4 w-4 mr-2" />
              Performance Trends
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default BundleSizeMonitor;
