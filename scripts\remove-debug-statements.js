#!/usr/bin/env node

/**
 * Debug Statement Removal <PERSON>t
 * Removes console.log, console.debug, and other debug statements from source files
 * Estimated bundle reduction: 5-10KB
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  sourceDirectories: ['app', 'components', 'lib', 'hooks', 'utils', 'types', 'services', 'stores'],
  excludePatterns: ['node_modules', '.next', '.git', 'dist', 'build', 'coverage', 'test-results', 'reports', 'logs'],
  fileExtensions: ['.ts', '.tsx', '.js', '.jsx'],
  debugPatterns: [
    /console\.(log|debug|info|warn|error)\s*\([^)]*\)\s*;?/g,
    /console\.(trace|time|timeEnd|count|group|groupEnd)\s*\([^)]*\)\s*;?/g,
    /debugger\s*;?/g,
    /\/\*\s*DEBUG:[\s\S]*?\*\//g,
    /\/\/\s*DEBUG:.*$/gm,
    /\/\/\s*TODO:.*$/gm,
    /\/\/\s*FIXME:.*$/gm,
    /\/\/\s*XXX:.*$/gm
  ],
  preservePatterns: [
    /console\.(error|warn)\s*\([^)]*production[^)]*\)/g, // Keep production error logging
    /console\.(error|warn)\s*\([^)]*process\.env\.NODE_ENV[^)]*\)/g // Keep environment-based logging
  ]
};

// Colors for output
const colors = {
  reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m',
  yellow: '\x1b[33m', blue: '\x1b[34m', cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

// Get all files recursively
function getAllFiles(dir, extensions = [], excludePatterns = []) {
  const files = [];
  
  function traverse(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      items.forEach(item => {
        const fullPath = path.join(currentDir, item);
        const relativePath = path.relative(process.cwd(), fullPath);
        
        if (excludePatterns.some(pattern => relativePath.includes(pattern))) {
          return;
        }
        
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          traverse(fullPath);
        } else if (stat.isFile()) {
          if (extensions.length === 0 || extensions.some(ext => item.endsWith(ext))) {
            files.push({
              path: fullPath,
              relativePath: relativePath.replace(/\\/g, '/'),
              name: item,
              size: stat.size
            });
          }
        }
      });
    } catch (error) {
      // Skip directories that can't be read
    }
  }
  
  if (fs.existsSync(dir)) {
    traverse(dir);
  }
  
  return files;
}

// Create backup of file
function createBackup(filePath) {
  const backupDir = path.join(process.cwd(), 'backups', 'debug-cleanup');
  const backupPath = path.join(backupDir, filePath);
  
  // Create backup directory
  const backupDirPath = path.dirname(backupPath);
  if (!fs.existsSync(backupDirPath)) {
    fs.mkdirSync(backupDirPath, { recursive: true });
  }
  
  // Copy original file to backup
  fs.copyFileSync(filePath, backupPath);
  return backupPath;
}

// Remove debug statements from content
function removeDebugStatements(content, filePath) {
  let modifiedContent = content;
  const removedStatements = [];
  
  // Check for preserved patterns first
  const preservedRanges = [];
  CONFIG.preservePatterns.forEach(pattern => {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      preservedRanges.push({
        start: match.index,
        end: match.index + match[0].length
      });
    }
  });
  
  // Remove debug patterns
  CONFIG.debugPatterns.forEach((pattern, patternIndex) => {
    let match;
    const patternRemovals = [];
    
    while ((match = pattern.exec(content)) !== null) {
      const matchStart = match.index;
      const matchEnd = match.index + match[0].length;
      
      // Check if this match overlaps with preserved ranges
      const isPreserved = preservedRanges.some(range => 
        (matchStart >= range.start && matchStart < range.end) ||
        (matchEnd > range.start && matchEnd <= range.end)
      );
      
      if (!isPreserved) {
        patternRemovals.push({
          match: match[0],
          start: matchStart,
          end: matchEnd,
          line: content.substring(0, matchStart).split('\n').length
        });
      }
    }
    
    // Remove matches in reverse order to maintain indices
    patternRemovals.reverse().forEach(removal => {
      modifiedContent = modifiedContent.substring(0, removal.start) + 
                       modifiedContent.substring(removal.end);
      
      removedStatements.push({
        statement: removal.match.trim(),
        line: removal.line,
        pattern: patternIndex
      });
    });
  });
  
  // Clean up empty lines left by removals
  modifiedContent = modifiedContent.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  return {
    content: modifiedContent,
    removedStatements,
    hasChanges: removedStatements.length > 0
  };
}

// Process a single file
function processFile(file, dryRun = false) {
  try {
    const originalContent = fs.readFileSync(file.path, 'utf8');
    const result = removeDebugStatements(originalContent, file.path);
    
    if (result.hasChanges) {
      if (!dryRun) {
        // Create backup
        createBackup(file.path);
        
        // Write modified content
        fs.writeFileSync(file.path, result.content, 'utf8');
      }
      
      return {
        file: file.relativePath,
        removedCount: result.removedStatements.length,
        removedStatements: result.removedStatements,
        originalSize: originalContent.length,
        newSize: result.content.length,
        sizeDiff: originalContent.length - result.content.length
      };
    }
    
    return null;
  } catch (error) {
    log(`❌ Error processing ${file.relativePath}: ${error.message}`, 'red');
    return null;
  }
}

// Main processing function
function processFiles(dryRun = false) {
  logHeader(`Debug Statement Removal ${dryRun ? '(Dry Run)' : ''}`);
  
  const allFiles = [];
  CONFIG.sourceDirectories.forEach(dir => {
    const files = getAllFiles(dir, CONFIG.fileExtensions, CONFIG.excludePatterns);
    allFiles.push(...files);
  });
  
  log(`📁 Found ${allFiles.length} source files to analyze`, 'blue');
  
  const results = [];
  let totalFilesModified = 0;
  let totalStatementsRemoved = 0;
  let totalSizeReduction = 0;
  
  allFiles.forEach(file => {
    const result = processFile(file, dryRun);
    if (result) {
      results.push(result);
      totalFilesModified++;
      totalStatementsRemoved += result.removedCount;
      totalSizeReduction += result.sizeDiff;
    }
  });
  
  // Display results
  if (totalFilesModified > 0) {
    log(`\n🧹 ${dryRun ? 'Would modify' : 'Modified'} ${totalFilesModified} files`, 'green');
    log(`🗑️  ${dryRun ? 'Would remove' : 'Removed'} ${totalStatementsRemoved} debug statements`, 'green');
    log(`💾 ${dryRun ? 'Would reduce' : 'Reduced'} size by ${(totalSizeReduction / 1024).toFixed(1)}KB`, 'green');
    
    if (results.length <= 10) {
      log('\n📋 Modified files:', 'blue');
      results.forEach(result => {
        log(`   ${result.file}: ${result.removedCount} statements (-${result.sizeDiff} bytes)`, 'blue');
      });
    } else {
      log('\n📋 Top 10 modified files:', 'blue');
      results
        .sort((a, b) => b.removedCount - a.removedCount)
        .slice(0, 10)
        .forEach(result => {
          log(`   ${result.file}: ${result.removedCount} statements (-${result.sizeDiff} bytes)`, 'blue');
        });
      log(`   ... and ${results.length - 10} more files`, 'blue');
    }
  } else {
    log('✅ No debug statements found to remove', 'green');
  }
  
  return {
    filesModified: totalFilesModified,
    statementsRemoved: totalStatementsRemoved,
    sizeReduction: totalSizeReduction,
    results
  };
}

// Generate report
function generateReport(results, dryRun) {
  const report = {
    timestamp: new Date().toISOString(),
    type: dryRun ? 'dry-run' : 'cleanup',
    summary: {
      filesAnalyzed: CONFIG.sourceDirectories.length,
      filesModified: results.filesModified,
      statementsRemoved: results.statementsRemoved,
      sizeReductionBytes: results.sizeReduction,
      sizeReductionKB: Math.round(results.sizeReduction / 1024 * 100) / 100
    },
    modifiedFiles: results.results.map(result => ({
      file: result.file,
      removedCount: result.removedCount,
      sizeDiff: result.sizeDiff,
      statements: result.removedStatements.map(stmt => ({
        statement: stmt.statement,
        line: stmt.line
      }))
    }))
  };
  
  // Save report
  const reportDir = path.join(process.cwd(), 'reports');
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(reportDir, `debug-cleanup-${dryRun ? 'dry-run-' : ''}${timestamp}.json`);
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`📊 Report saved to: ${reportPath}`, 'green');
  
  return report;
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  const dryRun = args.includes('--dry-run') || args.includes('-d');
  const help = args.includes('--help') || args.includes('-h');
  
  if (help) {
    console.log(`
Debug Statement Removal Script

Usage: node remove-debug-statements.js [options]

Options:
  --dry-run, -d    Show what would be removed without making changes
  --help, -h       Show this help message

Examples:
  node remove-debug-statements.js --dry-run    # Preview changes
  node remove-debug-statements.js             # Remove debug statements
`);
    return;
  }
  
  try {
    const results = processFiles(dryRun);
    const report = generateReport(results, dryRun);
    
    logHeader('Summary');
    if (dryRun) {
      log('🔍 Dry run completed - no files were modified', 'yellow');
      log('💡 Run without --dry-run to apply changes', 'blue');
    } else {
      log('✅ Debug statement cleanup completed', 'green');
      log('💾 Backups created in ./backups/debug-cleanup/', 'blue');
      log('🔧 Run tests to ensure nothing is broken: npm test', 'blue');
    }
    
    process.exit(0);
    
  } catch (error) {
    log(`❌ Script failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { processFiles, removeDebugStatements };
