'use client';

import React, { useEffect, useState, useRef } from 'react';
import { motion } from 'framer-motion';

interface PerformanceMetrics {
  fcp: number | null;
  lcp: number | null;
  cls: number | null;
  fid: number | null;
  ttfb: number | null;
}

interface PerformanceOptimizerProps {
  children: React.ReactNode;
  enableLazyLoading?: boolean;
  enablePreloading?: boolean;
  enableResourceHints?: boolean;
  targetFCP?: number;
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void;
}

/**
 * Performance Optimizer Component
 * 
 * Optimizes Hero Section performance to achieve <1.5s First Contentful Paint.
 * Implements lazy loading, resource preloading, and performance monitoring.
 * 
 * @component
 * @example
 * ```tsx
 * <PerformanceOptimizer 
 *   targetFCP={1500}
 *   enableLazyLoading={true}
 *   enablePreloading={true}
 *   onMetricsUpdate={(metrics) => console.log(metrics)}
 * >
 *   <HeroContent />
 * </PerformanceOptimizer>
 * ```
 */
export function PerformanceOptimizer({
  children,
  enableLazyLoading = true,
  enablePreloading = true,
  enableResourceHints = true,
  targetFCP = 1500,
  onMetricsUpdate
}: PerformanceOptimizerProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    fcp: null,
    lcp: null,
    cls: null,
    fid: null,
    ttfb: null
  });
  const [isVisible, setIsVisible] = useState(false);
  const [shouldRenderHeavyComponents, setShouldRenderHeavyComponents] = useState(false);
  
  const observerRef = useRef<IntersectionObserver | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Performance monitoring
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const measurePerformance = () => {
      try {
        // First Contentful Paint
        const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0] as PerformanceEntry;
        const fcp = fcpEntry ? fcpEntry.startTime : null;

        // Largest Contentful Paint
        const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
        const lcp = lcpEntries.length > 0 ? lcpEntries[lcpEntries.length - 1].startTime : null;

        // Time to First Byte
        const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const ttfb = navigationEntry ? navigationEntry.responseStart - navigationEntry.requestStart : null;

        const newMetrics = {
          fcp,
          lcp,
          cls: null, // CLS requires layout shift observer
          fid: null, // FID requires first input delay observer
          ttfb
        };

        setMetrics(newMetrics);
        onMetricsUpdate?.(newMetrics);

        // Log performance warnings
        if (fcp && fcp > targetFCP) {
          console.warn(`FCP (${Math.round(fcp)}ms) exceeds target (${targetFCP}ms)`);
        }
      } catch (error) {
        console.warn('Performance measurement failed:', error);
      }
    };

    // Measure after page load
    if (document.readyState === 'complete') {
      measurePerformance();
    } else {
      window.addEventListener('load', measurePerformance);
    }

    // Observe CLS
    if ('LayoutShift' in window) {
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        setMetrics(prev => ({ ...prev, cls: clsValue }));
      });
      clsObserver.observe({ type: 'layout-shift', buffered: true });
    }

    // Observe FID
    if ('FirstInputDelay' in window) {
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          setMetrics(prev => ({ ...prev, fid: entry.processingStart - entry.startTime }));
        }
      });
      fidObserver.observe({ type: 'first-input', buffered: true });
    }

    return () => {
      window.removeEventListener('load', measurePerformance);
    };
  }, [targetFCP, onMetricsUpdate]);

  // Lazy loading setup
  useEffect(() => {
    if (!enableLazyLoading || typeof window === 'undefined') {
      setShouldRenderHeavyComponents(true);
      return;
    }

    observerRef.current = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Delay heavy components rendering slightly to prioritize critical content
          setTimeout(() => {
            setShouldRenderHeavyComponents(true);
          }, 100);
        }
      },
      { 
        threshold: 0.1,
        rootMargin: '50px'
      }
    );

    if (containerRef.current) {
      observerRef.current.observe(containerRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [enableLazyLoading]);

  // Resource preloading
  useEffect(() => {
    if (!enablePreloading || typeof window === 'undefined') return;

    const preloadResources = () => {
      // Preload critical fonts
      const fontLink = document.createElement('link');
      fontLink.rel = 'preload';
      fontLink.href = '/fonts/inter-var.woff2';
      fontLink.as = 'font';
      fontLink.type = 'font/woff2';
      fontLink.crossOrigin = 'anonymous';
      document.head.appendChild(fontLink);

      // Preload critical images
      const heroImageLink = document.createElement('link');
      heroImageLink.rel = 'preload';
      heroImageLink.href = '/images/hero-bg.webp';
      heroImageLink.as = 'image';
      document.head.appendChild(heroImageLink);

      // Preload critical CSS
      const criticalCSSLink = document.createElement('link');
      criticalCSSLink.rel = 'preload';
      criticalCSSLink.href = '/styles/critical.css';
      criticalCSSLink.as = 'style';
      document.head.appendChild(criticalCSSLink);
    };

    // Preload after initial render
    requestIdleCallback(preloadResources, { timeout: 1000 });
  }, [enablePreloading]);

  // Resource hints
  useEffect(() => {
    if (!enableResourceHints || typeof window === 'undefined') return;

    // DNS prefetch for external resources
    const dnsPrefetchDomains = [
      'fonts.googleapis.com',
      'fonts.gstatic.com',
      'api.github.com'
    ];

    dnsPrefetchDomains.forEach(domain => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = `//${domain}`;
      document.head.appendChild(link);
    });

    // Preconnect to critical origins
    const preconnectOrigins = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com'
    ];

    preconnectOrigins.forEach(origin => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = origin;
      link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });
  }, [enableResourceHints]);

  // Critical CSS injection
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const criticalCSS = `
      /* Critical Hero Section styles */
      .hero-critical {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #1e293b 0%, #7c3aed 50%, #1e40af 100%);
      }
      
      .hero-title {
        font-size: clamp(2rem, 8vw, 4rem);
        font-weight: 700;
        line-height: 1.1;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #ffffff 0%, #a855f7 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .hero-subtitle {
        font-size: clamp(1rem, 3vw, 1.25rem);
        color: #d1d5db;
        margin-bottom: 2rem;
        max-width: 48rem;
      }
      
      .hero-cta {
        display: inline-flex;
        align-items: center;
        padding: 0.75rem 2rem;
        background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
        color: white;
        border-radius: 0.5rem;
        font-weight: 600;
        text-decoration: none;
        transition: transform 0.2s ease;
      }
      
      .hero-cta:hover {
        transform: translateY(-2px);
      }
    `;

    const style = document.createElement('style');
    style.textContent = criticalCSS;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <div ref={containerRef} className="performance-optimized-hero">
      {/* Critical content renders immediately */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        {children}
      </motion.div>

      {/* Performance metrics display (development only) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed bottom-4 left-4 z-50 bg-black/80 text-white p-2 rounded text-xs font-mono">
          <div>FCP: {metrics.fcp ? `${Math.round(metrics.fcp)}ms` : 'N/A'}</div>
          <div>LCP: {metrics.lcp ? `${Math.round(metrics.lcp)}ms` : 'N/A'}</div>
          <div>CLS: {metrics.cls ? metrics.cls.toFixed(3) : 'N/A'}</div>
          <div>TTFB: {metrics.ttfb ? `${Math.round(metrics.ttfb)}ms` : 'N/A'}</div>
          <div className={metrics.fcp && metrics.fcp <= targetFCP ? 'text-green-400' : 'text-red-400'}>
            Target FCP: {targetFCP}ms
          </div>
        </div>
      )}
    </div>
  );
}

/**
 * Lazy Loading Wrapper for Heavy Components
 */
interface LazyComponentWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  delay?: number;
  threshold?: number;
}

export function LazyComponentWrapper({
  children,
  fallback = <div className="animate-pulse bg-gray-700 rounded h-32" />,
  delay = 0,
  threshold = 0.1
}: LazyComponentWrapperProps) {
  const [shouldRender, setShouldRender] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => {
            setShouldRender(true);
          }, delay);
        }
      },
      { threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [delay, threshold]);

  return (
    <div ref={ref}>
      {shouldRender ? children : fallback}
    </div>
  );
}

/**
 * Critical Resource Preloader
 */
export function CriticalResourcePreloader() {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Preload critical resources
    const resources = [
      { href: '/fonts/inter-var.woff2', as: 'font', type: 'font/woff2' },
      { href: '/images/hero-bg.webp', as: 'image' },
      { href: '/api/metrics', as: 'fetch' }
    ];

    resources.forEach(resource => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = resource.href;
      link.as = resource.as;
      if (resource.type) link.type = resource.type;
      if (resource.as === 'font') link.crossOrigin = 'anonymous';
      document.head.appendChild(link);
    });
  }, []);

  return null;
}
