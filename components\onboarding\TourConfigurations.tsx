import { 
  Code, 
  Play, 
  Zap, 
  Trophy, 
  BookOpen, 
  Users, 
  Sparkles, 
  Target,
  MessageCircle,
  BarChart3,
  Settings,
  Rocket
} from 'lucide-react';

export const platformTourSteps = [
  {
    id: 'welcome',
    title: 'Welcome to Solidity Learning Platform!',
    description: 'Let\'s take a quick tour to help you get started with smart contract development. This tour will show you all the key features.',
    target: 'body',
    position: 'center' as const,
    icon: Rocket,
    xpReward: 10,
    achievementId: 'tour-starter',
    highlightStyle: 'glow' as const,
    customContent: (
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg">
        <div className="flex items-center space-x-2 mb-2">
          <Sparkles className="w-5 h-5 text-purple-600" />
          <span className="font-medium text-purple-800">What you'll learn:</span>
        </div>
        <ul className="text-sm text-purple-700 space-y-1">
          <li>• How to use the interactive code editor</li>
          <li>• Compilation and deployment process</li>
          <li>• Gamification and progress tracking</li>
          <li>• AI assistance and help features</li>
        </ul>
      </div>
    )
  },
  {
    id: 'code-editor',
    title: 'Interactive Code Editor',
    description: 'This is where the magic happens! Write, edit, and experiment with Solidity smart contracts in our feature-rich editor.',
    target: '[data-tour="code-editor"]',
    position: 'right' as const,
    icon: Code,
    action: 'click' as const,
    actionText: 'Click in the editor to try typing some code',
    isInteractive: true,
    xpReward: 15,
    achievementId: 'code-explorer',
    highlightStyle: 'pulse' as const,
    beforeShow: () => {
      // Ensure code editor is visible
      const editor = document.querySelector('[data-tour="code-editor"]');
      if (editor) {
        editor.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    }
  },
  {
    id: 'compile-button',
    title: 'Compile Your Code',
    description: 'Click the compile button to check your code for errors and prepare it for deployment. You\'ll see real-time feedback here.',
    target: '[data-tour="compile-button"]',
    position: 'bottom' as const,
    icon: Zap,
    action: 'click' as const,
    actionText: 'Try clicking the compile button',
    isInteractive: true,
    xpReward: 20,
    achievementId: 'first-compiler',
    highlightStyle: 'glow' as const
  },
  {
    id: 'deploy-section',
    title: 'Deploy to Blockchain',
    description: 'Once compiled successfully, deploy your smart contract to a test network. This is where your code becomes a real blockchain application!',
    target: '[data-tour="deploy-section"]',
    position: 'top' as const,
    icon: Rocket,
    xpReward: 25,
    achievementId: 'deployment-ready',
    highlightStyle: 'border' as const
  },
  {
    id: 'gamification-panel',
    title: 'Track Your Progress',
    description: 'Earn XP, unlock achievements, and track your learning journey. Every action you take helps you level up your Solidity skills!',
    target: '[data-tour="gamification-panel"]',
    position: 'left' as const,
    icon: Trophy,
    xpReward: 15,
    achievementId: 'progress-tracker',
    highlightStyle: 'pulse' as const,
    customContent: (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
        <div className="flex items-center space-x-2 mb-2">
          <Trophy className="w-4 h-4 text-yellow-600" />
          <span className="text-yellow-800 font-medium text-sm">Gamification Features:</span>
        </div>
        <ul className="text-xs text-yellow-700 space-y-1">
          <li>• XP points for every action</li>
          <li>• Achievement badges</li>
          <li>• Learning streaks</li>
          <li>• Leaderboards</li>
        </ul>
      </div>
    )
  },
  {
    id: 'ai-assistant',
    title: 'AI-Powered Help',
    description: 'Stuck on something? Our AI assistant is here to help! Get instant answers, code suggestions, and learning guidance.',
    target: '[data-tour="ai-assistant"]',
    position: 'left' as const,
    icon: MessageCircle,
    action: 'click' as const,
    actionText: 'Click to open the AI assistant',
    isInteractive: true,
    xpReward: 20,
    achievementId: 'ai-helper',
    highlightStyle: 'glow' as const
  },
  {
    id: 'learning-resources',
    title: 'Learning Resources',
    description: 'Access tutorials, documentation, and interactive lessons. Everything you need to master Solidity development.',
    target: '[data-tour="learning-resources"]',
    position: 'top' as const,
    icon: BookOpen,
    xpReward: 15,
    achievementId: 'resource-explorer',
    highlightStyle: 'border' as const
  },
  {
    id: 'community-features',
    title: 'Join the Community',
    description: 'Connect with other developers, share your projects, and collaborate on smart contracts. Learning is better together!',
    target: '[data-tour="community-features"]',
    position: 'bottom' as const,
    icon: Users,
    xpReward: 15,
    achievementId: 'community-member',
    highlightStyle: 'pulse' as const
  },
  {
    id: 'tour-complete',
    title: 'You\'re All Set!',
    description: 'Congratulations! You\'ve completed the platform tour. You\'re now ready to start building amazing smart contracts. Happy coding!',
    target: 'body',
    position: 'center' as const,
    icon: Target,
    xpReward: 50,
    achievementId: 'tour-master',
    highlightStyle: 'spotlight' as const,
    customContent: (
      <div className="text-center">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
          <div className="flex items-center justify-center space-x-2 mb-2">
            <Trophy className="w-6 h-6 text-green-600" />
            <span className="font-bold text-green-800">Tour Completed!</span>
          </div>
          <p className="text-green-700 text-sm">
            You've earned <strong>+50 XP</strong> and the <strong>"Tour Master"</strong> achievement!
          </p>
        </div>
        <div className="space-y-2 text-sm text-gray-600">
          <p>🚀 Start with a simple "Hello World" contract</p>
          <p>📚 Explore our interactive tutorials</p>
          <p>💬 Ask the AI assistant for help anytime</p>
          <p>🏆 Track your progress and earn achievements</p>
        </div>
      </div>
    )
  }
];

export const quickStartTourSteps = [
  {
    id: 'quick-welcome',
    title: 'Quick Start Guide',
    description: 'Let\'s get you coding in 3 simple steps!',
    target: 'body',
    position: 'center' as const,
    icon: Zap,
    xpReward: 5,
    highlightStyle: 'glow' as const
  },
  {
    id: 'quick-editor',
    title: 'Step 1: Code Editor',
    description: 'Write your Solidity code here. Try modifying the example contract!',
    target: '[data-tour="code-editor"]',
    position: 'right' as const,
    icon: Code,
    isInteractive: true,
    actionText: 'Make a small edit to the code',
    xpReward: 10,
    highlightStyle: 'pulse' as const
  },
  {
    id: 'quick-compile',
    title: 'Step 2: Compile',
    description: 'Click compile to check your code and prepare for deployment.',
    target: '[data-tour="compile-button"]',
    position: 'bottom' as const,
    icon: Zap,
    isInteractive: true,
    actionText: 'Click the compile button',
    xpReward: 15,
    highlightStyle: 'glow' as const
  },
  {
    id: 'quick-deploy',
    title: 'Step 3: Deploy',
    description: 'Deploy your contract to see it in action on the blockchain!',
    target: '[data-tour="deploy-section"]',
    position: 'top' as const,
    icon: Rocket,
    xpReward: 20,
    achievementId: 'quick-starter',
    highlightStyle: 'border' as const
  }
];

export const advancedFeaturesTour = [
  {
    id: 'advanced-welcome',
    title: 'Advanced Features Tour',
    description: 'Discover powerful features for experienced developers.',
    target: 'body',
    position: 'center' as const,
    icon: Settings,
    xpReward: 10,
    highlightStyle: 'glow' as const
  },
  {
    id: 'gas-optimization',
    title: 'Gas Optimization Tools',
    description: 'Analyze and optimize your contract\'s gas usage with our advanced tools.',
    target: '[data-tour="gas-analyzer"]',
    position: 'right' as const,
    icon: BarChart3,
    xpReward: 25,
    achievementId: 'gas-optimizer',
    highlightStyle: 'pulse' as const
  },
  {
    id: 'debugging-tools',
    title: 'Advanced Debugging',
    description: 'Use breakpoints, step-through debugging, and state inspection.',
    target: '[data-tour="debugger"]',
    position: 'bottom' as const,
    icon: Target,
    xpReward: 30,
    achievementId: 'debug-master',
    highlightStyle: 'glow' as const
  },
  {
    id: 'collaboration',
    title: 'Real-time Collaboration',
    description: 'Code together with other developers in real-time.',
    target: '[data-tour="collaboration"]',
    position: 'left' as const,
    icon: Users,
    xpReward: 20,
    achievementId: 'collaborator',
    highlightStyle: 'border' as const
  }
];

// Tour configuration helper
export function getTourConfiguration(tourType: 'platform' | 'quickstart' | 'advanced') {
  switch (tourType) {
    case 'platform':
      return {
        steps: platformTourSteps,
        tourId: 'platform-tour',
        autoStart: true
      };
    case 'quickstart':
      return {
        steps: quickStartTourSteps,
        tourId: 'quickstart-tour',
        autoStart: false
      };
    case 'advanced':
      return {
        steps: advancedFeaturesTour,
        tourId: 'advanced-tour',
        autoStart: false
      };
    default:
      return {
        steps: platformTourSteps,
        tourId: 'platform-tour',
        autoStart: true
      };
  }
}

// Tour progress tracking
export function getTourProgress(tourId: string) {
  if (typeof window === 'undefined') return null;
  
  const completed = localStorage.getItem(`tour-${tourId}-completed`);
  const skipped = localStorage.getItem(`tour-${tourId}-skipped`);
  const currentStep = localStorage.getItem(`tour-${tourId}-current-step`);
  
  return {
    completed: completed === 'true',
    skipped: skipped === 'true',
    currentStep: currentStep ? parseInt(currentStep, 10) : 0,
    hasStarted: completed === 'true' || skipped === 'true' || currentStep !== null
  };
}

// Reset tour progress
export function resetTourProgress(tourId: string) {
  if (typeof window === 'undefined') return;
  
  localStorage.removeItem(`tour-${tourId}-completed`);
  localStorage.removeItem(`tour-${tourId}-skipped`);
  localStorage.removeItem(`tour-${tourId}-current-step`);
}

// Check if user should see tour
export function shouldShowTour(tourId: string, userVisitCount: number = 1) {
  const progress = getTourProgress(tourId);
  
  // Don't show if already completed or skipped
  if (progress?.completed || progress?.skipped) {
    return false;
  }
  
  // Show platform tour for first-time users
  if (tourId === 'platform-tour' && userVisitCount === 1) {
    return true;
  }
  
  // Show other tours only if explicitly requested
  return false;
}
