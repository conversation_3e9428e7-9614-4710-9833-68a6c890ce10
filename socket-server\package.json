{"name": "solanalearn-socket-server", "version": "1.0.0", "description": "Real-time collaboration server for SolanaLearn", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'No build step required'"}, "dependencies": {"socket.io": "^4.7.0", "express": "^4.18.0", "cors": "^2.8.5", "dotenv": "^16.3.0", "redis": "^4.6.0", "jsonwebtoken": "^9.0.0", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=18.0.0"}}