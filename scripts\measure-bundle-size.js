#!/usr/bin/env node

/**
 * Bundle Size Measurement Script
 * Measures current bundle sizes and compares against targets
 */

const fs = require('fs');
const path = require('path');

// Colors for output
const colors = {
  reset: '\x1b[0m', bright: '\x1b[1m', red: '\x1b[31m', green: '\x1b[32m',
  yellow: '\x1b[33m', blue: '\x1b[34m', cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

// Get file size in KB
function getFileSizeKB(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return Math.round(stats.size / 1024 * 100) / 100;
  } catch (error) {
    return 0;
  }
}

// Get all files in directory with extension
function getFilesInDirectory(dir, extension) {
  const files = [];
  
  try {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isFile() && item.endsWith(extension)) {
        files.push({
          name: item,
          path: fullPath,
          size: getFileSizeKB(fullPath)
        });
      }
    });
  } catch (error) {
    // Directory doesn't exist or can't be read
  }
  
  return files;
}

// Measure bundle sizes
function measureBundleSizes() {
  logHeader('Bundle Size Measurement');
  
  const results = {
    javascript: { files: [], total: 0 },
    css: { files: [], total: 0 },
    chunks: { files: [], total: 0 },
    timestamp: new Date().toISOString()
  };
  
  // Check if build exists
  const buildDir = '.next/static';
  if (!fs.existsSync(buildDir)) {
    log('❌ No build found. Run "npm run build" first.', 'red');
    return results;
  }
  
  // Measure JavaScript chunks
  const chunksDir = path.join(buildDir, 'chunks');
  if (fs.existsSync(chunksDir)) {
    const jsFiles = getFilesInDirectory(chunksDir, '.js');
    results.javascript.files = jsFiles;
    results.javascript.total = jsFiles.reduce((sum, file) => sum + file.size, 0);
    
    log(`📦 JavaScript Files: ${jsFiles.length}`, 'blue');
    log(`📊 Total JavaScript: ${results.javascript.total} KB`, 'blue');
    
    // Show largest JS files
    const largestJS = jsFiles.sort((a, b) => b.size - a.size).slice(0, 5);
    if (largestJS.length > 0) {
      log('\n🔍 Largest JavaScript Files:', 'yellow');
      largestJS.forEach(file => {
        log(`   ${file.name}: ${file.size} KB`, 'yellow');
      });
    }
  }
  
  // Measure CSS files
  const cssFiles = getFilesInDirectory(chunksDir, '.css');
  results.css.files = cssFiles;
  results.css.total = cssFiles.reduce((sum, file) => sum + file.size, 0);
  
  log(`\n🎨 CSS Files: ${cssFiles.length}`, 'blue');
  log(`📊 Total CSS: ${results.css.total} KB`, 'blue');
  
  // Show largest CSS files
  const largestCSS = cssFiles.sort((a, b) => b.size - a.size).slice(0, 3);
  if (largestCSS.length > 0) {
    log('\n🔍 Largest CSS Files:', 'yellow');
    largestCSS.forEach(file => {
      log(`   ${file.name}: ${file.size} KB`, 'yellow');
    });
  }
  
  // Total bundle size
  const totalSize = results.javascript.total + results.css.total;
  log(`\n📦 Total Bundle Size: ${totalSize} KB`, 'bright');
  
  return results;
}

// Compare against targets
function compareAgainstTargets(results) {
  logHeader('Bundle Size Analysis');
  
  const targets = {
    javascript: 400, // KB
    css: 100,        // KB
    total: 500       // KB
  };
  
  const totalSize = results.javascript.total + results.css.total;
  
  // JavaScript analysis
  const jsStatus = results.javascript.total <= targets.javascript ? '✅' : '❌';
  const jsColor = results.javascript.total <= targets.javascript ? 'green' : 'red';
  log(`${jsStatus} JavaScript: ${results.javascript.total} KB / ${targets.javascript} KB target`, jsColor);
  
  if (results.javascript.total > targets.javascript) {
    const excess = results.javascript.total - targets.javascript;
    log(`   Exceeds target by ${excess} KB (${Math.round(excess / targets.javascript * 100)}%)`, 'red');
  }
  
  // CSS analysis
  const cssStatus = results.css.total <= targets.css ? '✅' : '❌';
  const cssColor = results.css.total <= targets.css ? 'green' : 'red';
  log(`${cssStatus} CSS: ${results.css.total} KB / ${targets.css} KB target`, cssColor);
  
  if (results.css.total > targets.css) {
    const excess = results.css.total - targets.css;
    log(`   Exceeds target by ${excess} KB (${Math.round(excess / targets.css * 100)}%)`, 'red');
  }
  
  // Total analysis
  const totalStatus = totalSize <= targets.total ? '✅' : '❌';
  const totalColor = totalSize <= targets.total ? 'green' : 'red';
  log(`${totalStatus} Total: ${totalSize} KB / ${targets.total} KB target`, totalColor);
  
  if (totalSize > targets.total) {
    const excess = totalSize - targets.total;
    log(`   Exceeds target by ${excess} KB (${Math.round(excess / targets.total * 100)}%)`, 'red');
  }
  
  return {
    javascript: {
      actual: results.javascript.total,
      target: targets.javascript,
      withinTarget: results.javascript.total <= targets.javascript,
      excess: Math.max(0, results.javascript.total - targets.javascript)
    },
    css: {
      actual: results.css.total,
      target: targets.css,
      withinTarget: results.css.total <= targets.css,
      excess: Math.max(0, results.css.total - targets.css)
    },
    total: {
      actual: totalSize,
      target: targets.total,
      withinTarget: totalSize <= targets.total,
      excess: Math.max(0, totalSize - targets.total)
    }
  };
}

// Generate report
function generateReport(results, analysis) {
  const report = {
    timestamp: results.timestamp,
    bundleSizes: results,
    analysis: analysis,
    recommendations: []
  };
  
  // Generate recommendations
  if (!analysis.javascript.withinTarget) {
    report.recommendations.push({
      type: 'javascript',
      priority: 'high',
      message: `JavaScript bundle exceeds target by ${analysis.javascript.excess} KB`,
      suggestions: [
        'Implement code splitting for large components',
        'Use dynamic imports for non-critical features',
        'Remove unused dependencies',
        'Optimize third-party library usage'
      ]
    });
  }
  
  if (!analysis.css.withinTarget) {
    report.recommendations.push({
      type: 'css',
      priority: 'medium',
      message: `CSS bundle exceeds target by ${analysis.css.excess} KB`,
      suggestions: [
        'Remove unused CSS classes',
        'Optimize Tailwind CSS purging',
        'Consolidate duplicate styles',
        'Use CSS-in-JS for component-specific styles'
      ]
    });
  }
  
  // Save report
  const reportDir = 'reports';
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(reportDir, `bundle-size-measurement-${timestamp}.json`);
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  log(`\n📊 Report saved to: ${reportPath}`, 'green');
  
  return report;
}

// Main execution
function main() {
  logHeader('Bundle Size Measurement Tool');
  
  try {
    const results = measureBundleSizes();
    
    if (results.javascript.total === 0 && results.css.total === 0) {
      log('\n⚠️  No bundle files found. Make sure to build the project first.', 'yellow');
      log('💡 Run: npm run build', 'blue');
      return;
    }
    
    const analysis = compareAgainstTargets(results);
    const report = generateReport(results, analysis);
    
    // Summary
    logHeader('Summary');
    const totalWithinTarget = analysis.total.withinTarget;
    
    if (totalWithinTarget) {
      log('🎉 Bundle size is within targets!', 'green');
    } else {
      log('⚠️  Bundle size exceeds targets. See recommendations above.', 'yellow');
    }
    
    if (report.recommendations.length > 0) {
      log('\n💡 Key Recommendations:', 'blue');
      report.recommendations.forEach((rec, index) => {
        log(`${index + 1}. ${rec.message}`, 'cyan');
      });
    }
    
    process.exit(totalWithinTarget ? 0 : 1);
    
  } catch (error) {
    log(`❌ Bundle measurement failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { measureBundleSizes, compareAgainstTargets };
