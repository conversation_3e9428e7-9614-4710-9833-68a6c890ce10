const fs = require('fs');
const path = require('path');

// Simple bundle size calculation
function calculateBundleSize() {
  const chunksDir = '.next/static/chunks';
  
  if (!fs.existsSync(chunksDir)) {
    console.log('No build found');
    return;
  }
  
  const files = fs.readdirSync(chunksDir);
  let totalJS = 0;
  let totalCSS = 0;
  let jsFiles = 0;
  let cssFiles = 0;
  
  files.forEach(file => {
    const filePath = path.join(chunksDir, file);
    const stats = fs.statSync(filePath);
    const sizeKB = Math.round(stats.size / 1024);
    
    if (file.endsWith('.js')) {
      totalJS += sizeKB;
      jsFiles++;
    } else if (file.endsWith('.css')) {
      totalCSS += sizeKB;
      cssFiles++;
    }
  });
  
  console.log('='.repeat(50));
  console.log('  Bundle Size Analysis');
  console.log('='.repeat(50));
  console.log(`JavaScript: ${totalJS} KB (${jsFiles} files)`);
  console.log(`CSS: ${totalCSS} KB (${cssFiles} files)`);
  console.log(`Total: ${totalJS + totalCSS} KB`);
  console.log('='.repeat(50));
  
  // Compare against targets
  const jsTarget = 400;
  const cssTarget = 100;
  const totalTarget = 500;
  
  console.log('Target Analysis:');
  console.log(`JS: ${totalJS <= jsTarget ? '✅' : '❌'} ${totalJS}/${jsTarget} KB`);
  console.log(`CSS: ${totalCSS <= cssTarget ? '✅' : '❌'} ${totalCSS}/${cssTarget} KB`);
  console.log(`Total: ${(totalJS + totalCSS) <= totalTarget ? '✅' : '❌'} ${totalJS + totalCSS}/${totalTarget} KB`);
  
  return {
    javascript: totalJS,
    css: totalCSS,
    total: totalJS + totalCSS,
    filesRemoved: 9 // We removed 9 files in Phase 1
  };
}

calculateBundleSize();
