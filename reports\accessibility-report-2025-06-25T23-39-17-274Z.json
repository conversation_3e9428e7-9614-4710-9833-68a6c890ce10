{"timestamp": "2025-06-25T23:39:17.275Z", "summary": {"success": true, "totalViolations": 0, "totalPasses": 0, "criticalIssues": [], "allIssues": [{"url": "Homepage", "type": "screen-reader", "issue": "Heading level 3 follows level 1 (skipped levels)"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Homepage", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "screen-reader", "issue": "Form control missing label"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "screen-reader", "issue": "Form control missing label"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Registration Page", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "screen-reader", "issue": "Form control missing label"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Dashboard", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "screen-reader", "issue": "Heading level 3 follows level 1 (skipped levels)"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "<PERSON><PERSON>", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "screen-reader", "issue": "Form control missing label"}, {"url": "Code Editor", "type": "screen-reader", "issue": "Heading level 3 follows level 1 (skipped levels)"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Code Editor", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "screen-reader", "issue": "Heading level 2 follows level 0 (skipped levels)"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Collaboration", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Achievements", "type": "keyboard", "issue": "No focusable elements found or tab navigation not working"}, {"url": "Settings", "type": "screen-reader", "issue": "Form control missing label"}, {"url": "Settings", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Animation not disabled with reduced motion preference"}, {"url": "Settings", "type": "reduced-motion", "issue": "Transition not disabled with reduced motion preference"}]}, "results": [{"url": "http://localhost:3000", "name": "Homepage", "axeResults": null, "keyboardResults": {"focusableElements": [{"index": 0, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to main content", "visible": true}, {"index": 1, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to navigation", "visible": true}, {"index": 2, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": "SolanaLearn home page", "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 3, "tagName": "DIV", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 4, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 5, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Learn - Interactive lessons", "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 6, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 7, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Code Lab - Code playground", "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 8, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 9, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Collaborate - Real-time collaboration", "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 10, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 11, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Achievements - View achievements", "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 12, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 13, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Test - View achievements", "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 14, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 15, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Demo - View achievements", "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 16, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 17, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Admin - View achievements", "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 18, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Sign In", "visible": true}, {"index": 19, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Get Started", "visible": true}, {"index": 20, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent h-9 rounded-md md:hidden text-gray-300 hover:text-white p-2 min-h-[44px] min-w-[44px] touch-manipulation", "ariaLabel": "Open navigation menu", "tabIndex": 0, "text": null, "visible": true}, {"index": 21, "tagName": "A", "type": null, "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary hover:bg-primary/90 h-11 rounded-md bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg min-h-[44px]", "ariaLabel": "Start learning Solidity for free - Begin your blockchain development journey", "tabIndex": 0, "text": "Start Learning Free", "visible": true}, {"index": 22, "tagName": "A", "type": null, "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-11 rounded-md border-white/20 text-white hover:bg-white/10 px-8 py-4 text-lg min-h-[44px]", "ariaLabel": "Watch platform demo - See SolanaLearn features in action", "tabIndex": 0, "text": "Watch Demo", "visible": true}, {"index": 23, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 rounded-lg border transition-all duration-300 bg-white/20 border-white/30 text-white", "ariaLabel": null, "tabIndex": 0, "text": "Interactive Code Editor", "visible": true}, {"index": 24, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 rounded-lg border transition-all duration-300 bg-white/5 border-white/10 text-gray-400 hover:bg-white/10 hover:text-white", "ariaLabel": null, "tabIndex": 0, "text": "Real-time Collaboration", "visible": true}, {"index": 25, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 rounded-lg border transition-all duration-300 bg-white/5 border-white/10 text-gray-400 hover:bg-white/10 hover:text-white", "ariaLabel": null, "tabIndex": 0, "text": "Gamified Learning", "visible": true}, {"index": 26, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 rounded-lg border transition-all duration-300 bg-white/5 border-white/10 text-gray-400 hover:bg-white/10 hover:text-white", "ariaLabel": null, "tabIndex": 0, "text": "AI-Powered Assistant", "visible": true}, {"index": 27, "tagName": "A", "type": null, "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600", "ariaLabel": null, "tabIndex": 0, "text": "Try It Now", "visible": true}, {"index": 28, "tagName": "A", "type": null, "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-10 px-4 py-2 border-white/20 text-white hover:bg-white/10", "ariaLabel": null, "tabIndex": 0, "text": "Learn More", "visible": true}, {"index": 29, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600", "ariaLabel": null, "tabIndex": 0, "text": "Run Step", "visible": true}, {"index": 30, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-10 px-4 py-2 w-full border-gray-500/30 text-gray-300 hover:bg-gray-500/10", "ariaLabel": null, "tabIndex": 0, "text": "Reset Demo", "visible": true}, {"index": 31, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 border-gray-500/30 text-gray-300 hover:bg-gray-500/10", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 32, "tagName": "A", "type": null, "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600", "ariaLabel": null, "tabIndex": 0, "text": "Start Full Course", "visible": true}, {"index": 33, "tagName": "A", "type": null, "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-10 px-4 py-2 border-white/20 text-white hover:bg-white/10", "ariaLabel": null, "tabIndex": 0, "text": "Open Playground", "visible": true}, {"index": 34, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-10 px-4 py-2 border-gray-500/30 text-gray-300 hover:bg-gray-500/10", "ariaLabel": null, "tabIndex": 0, "text": "Download Code", "visible": true}, {"index": 35, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600", "ariaLabel": null, "tabIndex": 0, "text": "Complete Challenge (+250 XP)", "visible": true}, {"index": 36, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-10 px-4 py-2 w-full border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10", "ariaLabel": null, "tabIndex": 0, "text": "Unlock Achievement", "visible": true}, {"index": 37, "tagName": "A", "type": null, "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 w-full mt-6", "ariaLabel": null, "tabIndex": 0, "text": "View Full Leaderboard", "visible": true}, {"index": 38, "tagName": "A", "type": null, "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-11 rounded-md px-8 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600", "ariaLabel": null, "tabIndex": 0, "text": "Start Your Journey", "visible": true}, {"index": 39, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl", "ariaLabel": null, "tabIndex": 0, "text": "Start Learning Today", "visible": true}, {"index": 40, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl", "ariaLabel": null, "tabIndex": 0, "text": "Start Learning for Free", "visible": true}, {"index": 41, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-8 py-4 bg-gradient-to-r from-green-600 to-blue-600 text-white font-semibold rounded-xl hover:from-green-700 hover:to-blue-700 transition-all duration-300 shadow-lg hover:shadow-xl", "ariaLabel": null, "tabIndex": 0, "text": "Start Your Success Story", "visible": true}, {"index": 42, "tagName": "BUTTON", "type": "submit", "id": null, "className": "group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300", "ariaLabel": null, "tabIndex": 0, "text": "Start Learning Free", "visible": true}, {"index": 43, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-8 py-4 border border-white/20 text-white font-semibold rounded-xl hover:bg-white/5 transition-all duration-300", "ariaLabel": null, "tabIndex": 0, "text": "View Demo", "visible": true}, {"index": 44, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 45, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 46, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 47, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 48, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Learn Solidity", "visible": true}, {"index": 49, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 50, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 51, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Documentation", "visible": true}, {"index": 52, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Tutorials", "visible": true}, {"index": 53, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Examples", "visible": true}, {"index": 54, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Best Practices", "visible": true}, {"index": 55, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Security Guide", "visible": true}, {"index": 56, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Discord", "visible": true}, {"index": 57, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Forum", "visible": true}, {"index": 58, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Blog", "visible": true}, {"index": 59, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Newsletter", "visible": true}, {"index": 60, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Privacy Policy", "visible": true}, {"index": 61, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Terms of Service", "visible": true}, {"index": 62, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 63, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-primary-foreground px-4 py-2 rounded-full w-12 h-12 bg-blue-600 hover:bg-blue-700 shadow-lg", "ariaLabel": "Open accessibility tester", "tabIndex": 0, "text": null, "visible": true}, {"index": 64, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON>ache", "visible": true}, {"index": 65, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 66, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Send Metrics", "visible": true}, {"index": 67, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Log to Console", "visible": true}, {"index": 68, "tagName": "BUTTON", "type": "submit", "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": "Dismiss feature spotlight", "tabIndex": 0, "text": null, "visible": true}, {"index": 69, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-white/50", "ariaLabel": null, "tabIndex": 0, "text": "Try AI Tutor", "visible": true}, {"index": 70, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-4 py-2 text-gray-400 hover:text-white transition-colors text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Maybe Later", "visible": true}, {"index": 71, "tagName": "BUTTON", "type": "button", "id": null, "className": "tsqd-open-btn", "ariaLabel": "Open Tanstack query devtools", "tabIndex": 0, "text": null, "visible": true}], "tabOrder": [{"tagName": "A", "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "text": "Skip to main content"}, {"tagName": "A", "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "text": "Skip to navigation"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": "flex items-center space-x-2", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": null, "text": "Learn"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Learn"}, {"tagName": "DIV", "id": null, "className": null, "text": "Code Lab"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Code Lab"}, {"tagName": "DIV", "id": null, "className": null, "text": "Collaborate"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Collaborate"}, {"tagName": "DIV", "id": null, "className": null, "text": "Achievements"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Achievements"}, {"tagName": "DIV", "id": null, "className": null, "text": "Auth Test"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Auth Test"}, {"tagName": "DIV", "id": null, "className": null, "text": "<PERSON><PERSON>"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "<PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": null, "text": "Admin"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Admin"}, {"tagName": "BUTTON", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "text": "Sign In"}, {"tagName": "BUTTON", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "text": "Get Started"}], "skipLinks": [{"text": "Skip to main content", "href": "http://localhost:3000/#undefined", "visible": true}, {"text": "Skip to navigation", "href": "http://localhost:3000/#undefined", "visible": true}], "focusTraps": [], "issues": []}, "contrastResults": [{"element": "HTML", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "HEAD", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "TITLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "SCRIPT", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "document.querySelectorAll('bod", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@keyframes go2264125279{from{t", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face {\n      font-displa", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".glass {\n    background: rgba(", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".inter_d3af1398-module__XqzvVq", "isLargeText": false}, {"element": "BODY", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Skip to main contentSkip to na", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to main content", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to navigation", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "NAV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(255, 255, 255, 0.1)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgba(0, 0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "LearnCode LabCollaborateAchiev", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign In", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Get Started", "isLargeText": false}, {"element": "MAIN", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Master SolidityBuild the Futur", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Master SolidityBuild the Futur", "isLargeText": false}, {"element": "SECTION", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Master SolidityBuild the Futur", "isLargeText": false}, {"element": "SECTION", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Master SolidityBuild the Futur", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Master SolidityBuild the Futur", "isLargeText": false}], "screenReaderResults": {"issues": [{"type": "heading-structure", "element": "H3", "message": "Heading level 3 follows level 1 (skipped levels)"}], "landmarks": 5, "headingCount": 44, "imageCount": 0, "formControlCount": 0}, "reducedMotionResults": {"issues": [{"type": "animation-not-disabled", "element": "HTML", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HTML", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "HEAD", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HEAD", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}], "totalAnimatedElements": 3604}}, {"url": "http://localhost:3000/auth/login", "name": "<PERSON><PERSON>", "axeResults": null, "keyboardResults": {"focusableElements": [{"index": 0, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to main content", "visible": true}, {"index": 1, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to navigation", "visible": true}, {"index": 2, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": "SolanaLearn home page", "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 3, "tagName": "DIV", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 4, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 5, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Learn - Interactive lessons", "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 6, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 7, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Code Lab - Code playground", "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 8, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 9, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Collaborate - Real-time collaboration", "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 10, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 11, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Achievements - View achievements", "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 12, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 13, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Test - View achievements", "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 14, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 15, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Demo - View achievements", "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 16, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 17, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Admin - View achievements", "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 18, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Sign In", "visible": true}, {"index": 19, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Get Started", "visible": true}, {"index": 20, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent h-9 rounded-md md:hidden text-gray-300 hover:text-white p-2 min-h-[44px] min-w-[44px] touch-manipulation", "ariaLabel": "Open navigation menu", "tabIndex": 0, "text": null, "visible": true}, {"index": 21, "tagName": "A", "type": null, "id": null, "className": "hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Home", "visible": true}, {"index": 22, "tagName": "INPUT", "type": "text", "id": null, "className": "w-full bg-transparent border-none outline-none text-white placeholder-gray-400 px-4 py-3", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 23, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Solidity FundamentalsbeginnerLearn the basics of S", "visible": true}, {"index": 24, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Smart Contract SecurityBest practices for secure s", "visible": true}, {"index": 25, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Variables and Data Types75% completeUnderstanding ", "visible": true}, {"index": 26, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "mapping in solidity2 hours ago", "visible": true}, {"index": 27, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "function modifiers1 day ago", "visible": true}, {"index": 28, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "DashboardReturn to your learning dashboard", "visible": true}, {"index": 29, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "CoursesBrowse our course catalog", "visible": true}, {"index": 30, "tagName": "BUTTON", "type": "submit", "id": null, "className": "group text-left", "ariaLabel": null, "tabIndex": 0, "text": "Report IssueHelp us fix this broken link", "visible": true}, {"index": 31, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "Home", "visible": true}, {"index": 32, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "courses", "visible": true}, {"index": 33, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "dashboard", "visible": true}, {"index": 34, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Solidity FundamentalsbeginnerLearn the basics of S", "visible": true}, {"index": 35, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Advanced Smart ContractsadvancedMaster complex pat", "visible": true}, {"index": 36, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "DeFi DevelopmentintermediateBuild decentralized fi", "visible": true}, {"index": 37, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Variables and Data TypesSolidity Fundamentals75%", "visible": true}, {"index": 38, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Function ModifiersAdvanced Smart Contracts45%", "visible": true}, {"index": 39, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Go Back", "visible": true}, {"index": 40, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Refresh Page", "visible": true}, {"index": 41, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 42, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 43, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 44, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 45, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Learn Solidity", "visible": true}, {"index": 46, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 47, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 48, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Documentation", "visible": true}, {"index": 49, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Tutorials", "visible": true}, {"index": 50, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Examples", "visible": true}, {"index": 51, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Best Practices", "visible": true}, {"index": 52, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Security Guide", "visible": true}, {"index": 53, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Discord", "visible": true}, {"index": 54, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Forum", "visible": true}, {"index": 55, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Blog", "visible": true}, {"index": 56, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Newsletter", "visible": true}, {"index": 57, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Privacy Policy", "visible": true}, {"index": 58, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Terms of Service", "visible": true}, {"index": 59, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 60, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-primary-foreground px-4 py-2 rounded-full w-12 h-12 bg-blue-600 hover:bg-blue-700 shadow-lg", "ariaLabel": "Open accessibility tester", "tabIndex": 0, "text": null, "visible": true}, {"index": 61, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON>ache", "visible": true}, {"index": 62, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 63, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Send Metrics", "visible": true}, {"index": 64, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Log to Console", "visible": true}, {"index": 65, "tagName": "BUTTON", "type": "submit", "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": "Dismiss feature spotlight", "tabIndex": 0, "text": null, "visible": true}, {"index": 66, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-white/50", "ariaLabel": null, "tabIndex": 0, "text": "Try AI Tutor", "visible": true}, {"index": 67, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-4 py-2 text-gray-400 hover:text-white transition-colors text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Maybe Later", "visible": true}, {"index": 68, "tagName": "BUTTON", "type": "button", "id": null, "className": "tsqd-open-btn", "ariaLabel": "Open Tanstack query devtools", "tabIndex": 0, "text": null, "visible": true}], "tabOrder": [{"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "Solidity FundamentalsbeginnerLearn the basics of S"}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "Smart Contract SecurityBest practices for secure s"}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "Variables and Data Types75% completeUnderstanding "}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "mapping in solidity2 hours ago"}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "function modifiers1 day ago"}, {"tagName": "A", "id": null, "className": "group", "text": "DashboardReturn to your learning dashboard"}, {"tagName": "A", "id": null, "className": "group", "text": "CoursesBrowse our course catalog"}, {"tagName": "BUTTON", "id": null, "className": "group text-left", "text": "Report IssueHelp us fix this broken link"}, {"tagName": "A", "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "text": "Home"}, {"tagName": "A", "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "text": "courses"}, {"tagName": "A", "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "text": "dashboard"}, {"tagName": "A", "id": null, "className": "group", "text": "Solidity FundamentalsbeginnerLearn the basics of S"}, {"tagName": "A", "id": null, "className": "group", "text": "Advanced Smart ContractsadvancedMaster complex pat"}, {"tagName": "A", "id": null, "className": "group", "text": "DeFi DevelopmentintermediateBuild decentralized fi"}, {"tagName": "A", "id": null, "className": "group", "text": "Variables and Data TypesSolidity Fundamentals75%"}, {"tagName": "A", "id": null, "className": "group", "text": "Function ModifiersAdvanced Smart Contracts45%"}, {"tagName": "BUTTON", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors", "text": "Go Back"}, {"tagName": "BUTTON", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors", "text": "Refresh Page"}, {"tagName": "A", "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "text": null}, {"tagName": "A", "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "text": null}], "skipLinks": [{"text": "Skip to main content", "href": "http://localhost:3000/auth/login#undefined", "visible": true}, {"text": "Skip to navigation", "href": "http://localhost:3000/auth/login#undefined", "visible": true}], "focusTraps": [], "issues": []}, "contrastResults": [{"element": "HTML", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "HEAD", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "TITLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "SCRIPT", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "document.querySelectorAll('bod", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@keyframes go2264125279{from{t", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face {\n      font-displa", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".glass {\n    background: rgba(", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".inter_d3af1398-module__XqzvVq", "isLargeText": false}, {"element": "BODY", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Skip to main contentSkip to na", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to main content", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to navigation", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "NAV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(255, 255, 255, 0.1)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgba(0, 0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "LearnCode LabCollaborateAchiev", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign In", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Get Started", "isLargeText": false}, {"element": "MAIN", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauthlogin404404Oops! Page ", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauthlogin404404Oops! Page ", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauthlogin404404Oops! Page ", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauthlogin", "isLargeText": false}, {"element": "DIV", "color": "rgb(156, 163, 175)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauthlogin", "isLargeText": false}], "screenReaderResults": {"issues": [{"type": "text", "element": "INPUT", "message": "Form control missing label"}], "landmarks": 3, "headingCount": 21, "imageCount": 0, "formControlCount": 1}, "reducedMotionResults": {"issues": [{"type": "animation-not-disabled", "element": "HTML", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HTML", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "HEAD", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HEAD", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}], "totalAnimatedElements": 1884}}, {"url": "http://localhost:3000/auth/register", "name": "Registration Page", "axeResults": null, "keyboardResults": {"focusableElements": [{"index": 0, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to main content", "visible": true}, {"index": 1, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to navigation", "visible": true}, {"index": 2, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": "SolanaLearn home page", "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 3, "tagName": "DIV", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 4, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 5, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Learn - Interactive lessons", "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 6, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 7, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Code Lab - Code playground", "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 8, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 9, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Collaborate - Real-time collaboration", "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 10, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 11, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Achievements - View achievements", "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 12, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 13, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Test - View achievements", "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 14, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 15, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Demo - View achievements", "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 16, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 17, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Admin - View achievements", "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 18, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Sign In", "visible": true}, {"index": 19, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Get Started", "visible": true}, {"index": 20, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent h-9 rounded-md md:hidden text-gray-300 hover:text-white p-2 min-h-[44px] min-w-[44px] touch-manipulation", "ariaLabel": "Open navigation menu", "tabIndex": 0, "text": null, "visible": true}, {"index": 21, "tagName": "A", "type": null, "id": null, "className": "hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Home", "visible": true}, {"index": 22, "tagName": "INPUT", "type": "text", "id": null, "className": "w-full bg-transparent border-none outline-none text-white placeholder-gray-400 px-4 py-3", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 23, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Solidity FundamentalsbeginnerLearn the basics of S", "visible": true}, {"index": 24, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Smart Contract SecurityBest practices for secure s", "visible": true}, {"index": 25, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Variables and Data Types75% completeUnderstanding ", "visible": true}, {"index": 26, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "mapping in solidity2 hours ago", "visible": true}, {"index": 27, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "function modifiers1 day ago", "visible": true}, {"index": 28, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "DashboardReturn to your learning dashboard", "visible": true}, {"index": 29, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "CoursesBrowse our course catalog", "visible": true}, {"index": 30, "tagName": "BUTTON", "type": "submit", "id": null, "className": "group text-left", "ariaLabel": null, "tabIndex": 0, "text": "Report IssueHelp us fix this broken link", "visible": true}, {"index": 31, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "Home", "visible": true}, {"index": 32, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "courses", "visible": true}, {"index": 33, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "dashboard", "visible": true}, {"index": 34, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Solidity FundamentalsbeginnerLearn the basics of S", "visible": true}, {"index": 35, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Advanced Smart ContractsadvancedMaster complex pat", "visible": true}, {"index": 36, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "DeFi DevelopmentintermediateBuild decentralized fi", "visible": true}, {"index": 37, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Variables and Data TypesSolidity Fundamentals75%", "visible": true}, {"index": 38, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Function ModifiersAdvanced Smart Contracts45%", "visible": true}, {"index": 39, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Go Back", "visible": true}, {"index": 40, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Refresh Page", "visible": true}, {"index": 41, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 42, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 43, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 44, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 45, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Learn Solidity", "visible": true}, {"index": 46, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 47, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 48, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Documentation", "visible": true}, {"index": 49, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Tutorials", "visible": true}, {"index": 50, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Examples", "visible": true}, {"index": 51, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Best Practices", "visible": true}, {"index": 52, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Security Guide", "visible": true}, {"index": 53, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Discord", "visible": true}, {"index": 54, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Forum", "visible": true}, {"index": 55, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Blog", "visible": true}, {"index": 56, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Newsletter", "visible": true}, {"index": 57, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Privacy Policy", "visible": true}, {"index": 58, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Terms of Service", "visible": true}, {"index": 59, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 60, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-primary-foreground px-4 py-2 rounded-full w-12 h-12 bg-blue-600 hover:bg-blue-700 shadow-lg", "ariaLabel": "Open accessibility tester", "tabIndex": 0, "text": null, "visible": true}, {"index": 61, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON>ache", "visible": true}, {"index": 62, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 63, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Send Metrics", "visible": true}, {"index": 64, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Log to Console", "visible": true}, {"index": 65, "tagName": "BUTTON", "type": "submit", "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": "Dismiss feature spotlight", "tabIndex": 0, "text": null, "visible": true}, {"index": 66, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-white/50", "ariaLabel": null, "tabIndex": 0, "text": "Try AI Tutor", "visible": true}, {"index": 67, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-4 py-2 text-gray-400 hover:text-white transition-colors text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Maybe Later", "visible": true}, {"index": 68, "tagName": "BUTTON", "type": "button", "id": null, "className": "tsqd-open-btn", "ariaLabel": "Open Tanstack query devtools", "tabIndex": 0, "text": null, "visible": true}], "tabOrder": [{"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "Solidity FundamentalsbeginnerLearn the basics of S"}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "Smart Contract SecurityBest practices for secure s"}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "Variables and Data Types75% completeUnderstanding "}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "mapping in solidity2 hours ago"}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "function modifiers1 day ago"}, {"tagName": "A", "id": null, "className": "group", "text": "DashboardReturn to your learning dashboard"}, {"tagName": "A", "id": null, "className": "group", "text": "CoursesBrowse our course catalog"}, {"tagName": "BUTTON", "id": null, "className": "group text-left", "text": "Report IssueHelp us fix this broken link"}, {"tagName": "A", "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "text": "Home"}, {"tagName": "A", "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "text": "courses"}, {"tagName": "A", "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "text": "dashboard"}, {"tagName": "A", "id": null, "className": "group", "text": "Solidity FundamentalsbeginnerLearn the basics of S"}, {"tagName": "A", "id": null, "className": "group", "text": "Advanced Smart ContractsadvancedMaster complex pat"}, {"tagName": "A", "id": null, "className": "group", "text": "DeFi DevelopmentintermediateBuild decentralized fi"}, {"tagName": "A", "id": null, "className": "group", "text": "Variables and Data TypesSolidity Fundamentals75%"}, {"tagName": "A", "id": null, "className": "group", "text": "Function ModifiersAdvanced Smart Contracts45%"}, {"tagName": "BUTTON", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors", "text": "Go Back"}, {"tagName": "BUTTON", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors", "text": "Refresh Page"}, {"tagName": "A", "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "text": null}, {"tagName": "A", "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "text": null}], "skipLinks": [{"text": "Skip to main content", "href": "http://localhost:3000/auth/register#undefined", "visible": true}, {"text": "Skip to navigation", "href": "http://localhost:3000/auth/register#undefined", "visible": true}], "focusTraps": [], "issues": []}, "contrastResults": [{"element": "HTML", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "HEAD", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "TITLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "SCRIPT", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "document.querySelectorAll('bod", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@keyframes go2264125279{from{t", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face {\n      font-displa", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".glass {\n    background: rgba(", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".inter_d3af1398-module__XqzvVq", "isLargeText": false}, {"element": "BODY", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Skip to main contentSkip to na", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to main content", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to navigation", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "NAV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(255, 255, 255, 0.1)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgba(0, 0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "LearnCode LabCollaborateAchiev", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign In", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Get Started", "isLargeText": false}, {"element": "MAIN", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauthregister404404Oops! Pa", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauthregister404404Oops! Pa", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauthregister404404Oops! Pa", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauthregister", "isLargeText": false}, {"element": "DIV", "color": "rgb(156, 163, 175)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauthregister", "isLargeText": false}], "screenReaderResults": {"issues": [{"type": "text", "element": "INPUT", "message": "Form control missing label"}], "landmarks": 3, "headingCount": 21, "imageCount": 0, "formControlCount": 1}, "reducedMotionResults": {"issues": [{"type": "animation-not-disabled", "element": "HTML", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HTML", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "HEAD", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HEAD", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}], "totalAnimatedElements": 1884}}, {"url": "http://localhost:3000/dashboard", "name": "Dashboard", "axeResults": null, "keyboardResults": {"focusableElements": [{"index": 0, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to main content", "visible": true}, {"index": 1, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to navigation", "visible": true}, {"index": 2, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": "SolanaLearn home page", "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 3, "tagName": "DIV", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 4, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 5, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Learn - Interactive lessons", "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 6, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 7, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Code Lab - Code playground", "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 8, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 9, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Collaborate - Real-time collaboration", "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 10, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 11, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Achievements - View achievements", "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 12, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 13, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Test - View achievements", "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 14, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 15, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Demo - View achievements", "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 16, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 17, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Admin - View achievements", "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 18, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Sign In", "visible": true}, {"index": 19, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Get Started", "visible": true}, {"index": 20, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent h-9 rounded-md md:hidden text-gray-300 hover:text-white p-2 min-h-[44px] min-w-[44px] touch-manipulation", "ariaLabel": "Open navigation menu", "tabIndex": 0, "text": null, "visible": true}, {"index": 21, "tagName": "A", "type": null, "id": null, "className": "hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Home", "visible": true}, {"index": 22, "tagName": "INPUT", "type": "text", "id": null, "className": "w-full bg-transparent border-none outline-none text-white placeholder-gray-400 px-4 py-3", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 23, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Solidity FundamentalsbeginnerLearn the basics of S", "visible": true}, {"index": 24, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Smart Contract SecurityBest practices for secure s", "visible": true}, {"index": 25, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Variables and Data Types75% completeUnderstanding ", "visible": true}, {"index": 26, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "mapping in solidity2 hours ago", "visible": true}, {"index": 27, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "function modifiers1 day ago", "visible": true}, {"index": 28, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "DashboardReturn to your learning dashboard", "visible": true}, {"index": 29, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "CoursesBrowse our course catalog", "visible": true}, {"index": 30, "tagName": "BUTTON", "type": "submit", "id": null, "className": "group text-left", "ariaLabel": null, "tabIndex": 0, "text": "Report IssueHelp us fix this broken link", "visible": true}, {"index": 31, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "Home", "visible": true}, {"index": 32, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "courses", "visible": true}, {"index": 33, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "dashboard", "visible": true}, {"index": 34, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Solidity FundamentalsbeginnerLearn the basics of S", "visible": true}, {"index": 35, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Advanced Smart ContractsadvancedMaster complex pat", "visible": true}, {"index": 36, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "DeFi DevelopmentintermediateBuild decentralized fi", "visible": true}, {"index": 37, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Variables and Data TypesSolidity Fundamentals75%", "visible": true}, {"index": 38, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Function ModifiersAdvanced Smart Contracts45%", "visible": true}, {"index": 39, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Go Back", "visible": true}, {"index": 40, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Refresh Page", "visible": true}, {"index": 41, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 42, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 43, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 44, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 45, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Learn Solidity", "visible": true}, {"index": 46, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 47, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 48, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Documentation", "visible": true}, {"index": 49, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Tutorials", "visible": true}, {"index": 50, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Examples", "visible": true}, {"index": 51, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Best Practices", "visible": true}, {"index": 52, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Security Guide", "visible": true}, {"index": 53, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Discord", "visible": true}, {"index": 54, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Forum", "visible": true}, {"index": 55, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Blog", "visible": true}, {"index": 56, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Newsletter", "visible": true}, {"index": 57, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Privacy Policy", "visible": true}, {"index": 58, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Terms of Service", "visible": true}, {"index": 59, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 60, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-primary-foreground px-4 py-2 rounded-full w-12 h-12 bg-blue-600 hover:bg-blue-700 shadow-lg", "ariaLabel": "Open accessibility tester", "tabIndex": 0, "text": null, "visible": true}, {"index": 61, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON>ache", "visible": true}, {"index": 62, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 63, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Send Metrics", "visible": true}, {"index": 64, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Log to Console", "visible": true}, {"index": 65, "tagName": "BUTTON", "type": "submit", "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": "Dismiss feature spotlight", "tabIndex": 0, "text": null, "visible": true}, {"index": 66, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-white/50", "ariaLabel": null, "tabIndex": 0, "text": "Try AI Tutor", "visible": true}, {"index": 67, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-4 py-2 text-gray-400 hover:text-white transition-colors text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Maybe Later", "visible": true}, {"index": 68, "tagName": "BUTTON", "type": "button", "id": null, "className": "tsqd-open-btn", "ariaLabel": "Open Tanstack query devtools", "tabIndex": 0, "text": null, "visible": true}], "tabOrder": [{"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "Solidity FundamentalsbeginnerLearn the basics of S"}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "Smart Contract SecurityBest practices for secure s"}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "Variables and Data Types75% completeUnderstanding "}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "mapping in solidity2 hours ago"}, {"tagName": "BUTTON", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "text": "function modifiers1 day ago"}, {"tagName": "A", "id": null, "className": "group", "text": "DashboardReturn to your learning dashboard"}, {"tagName": "A", "id": null, "className": "group", "text": "CoursesBrowse our course catalog"}, {"tagName": "BUTTON", "id": null, "className": "group text-left", "text": "Report IssueHelp us fix this broken link"}, {"tagName": "A", "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "text": "Home"}, {"tagName": "A", "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "text": "courses"}, {"tagName": "A", "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "text": "dashboard"}, {"tagName": "A", "id": null, "className": "group", "text": "Solidity FundamentalsbeginnerLearn the basics of S"}, {"tagName": "A", "id": null, "className": "group", "text": "Advanced Smart ContractsadvancedMaster complex pat"}, {"tagName": "A", "id": null, "className": "group", "text": "DeFi DevelopmentintermediateBuild decentralized fi"}, {"tagName": "A", "id": null, "className": "group", "text": "Variables and Data TypesSolidity Fundamentals75%"}, {"tagName": "A", "id": null, "className": "group", "text": "Function ModifiersAdvanced Smart Contracts45%"}, {"tagName": "BUTTON", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors", "text": "Go Back"}, {"tagName": "BUTTON", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors", "text": "Refresh Page"}, {"tagName": "A", "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "text": null}, {"tagName": "A", "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "text": null}], "skipLinks": [{"text": "Skip to main content", "href": "http://localhost:3000/auth?returnUrl=%2Fdashboard#undefined", "visible": true}, {"text": "Skip to navigation", "href": "http://localhost:3000/auth?returnUrl=%2Fdashboard#undefined", "visible": true}], "focusTraps": [], "issues": []}, "contrastResults": [{"element": "HTML", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "HEAD", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "TITLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "SCRIPT", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "document.querySelectorAll('bod", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@keyframes go2264125279{from{t", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face {\n      font-displa", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".glass {\n    background: rgba(", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".inter_d3af1398-module__XqzvVq", "isLargeText": false}, {"element": "BODY", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Skip to main contentSkip to na", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to main content", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to navigation", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "NAV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(255, 255, 255, 0.1)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgba(0, 0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "LearnCode LabCollaborateAchiev", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign In", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Get Started", "isLargeText": false}, {"element": "MAIN", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauth404404Oops! Page Not F", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauth404404Oops! Page Not F", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauth404404Oops! Page Not F", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauth", "isLargeText": false}, {"element": "DIV", "color": "rgb(156, 163, 175)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauth", "isLargeText": false}], "screenReaderResults": {"issues": [{"type": "text", "element": "INPUT", "message": "Form control missing label"}], "landmarks": 3, "headingCount": 21, "imageCount": 0, "formControlCount": 1}, "reducedMotionResults": {"issues": [{"type": "animation-not-disabled", "element": "HTML", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HTML", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "HEAD", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HEAD", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}], "totalAnimatedElements": 1878}}, {"url": "http://localhost:3000/learn", "name": "<PERSON><PERSON>", "axeResults": null, "keyboardResults": {"focusableElements": [{"index": 0, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to main content", "visible": true}, {"index": 1, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to navigation", "visible": true}, {"index": 2, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": "SolanaLearn home page", "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 3, "tagName": "DIV", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 4, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 5, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Learn - Interactive lessons", "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 6, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 7, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Code Lab - Code playground", "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 8, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 9, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Collaborate - Real-time collaboration", "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 10, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 11, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Achievements - View achievements", "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 12, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 13, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Test - View achievements", "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 14, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 15, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Demo - View achievements", "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 16, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 17, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Admin - View achievements", "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 18, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Sign In", "visible": true}, {"index": 19, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Get Started", "visible": true}, {"index": 20, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent h-9 rounded-md md:hidden text-gray-300 hover:text-white p-2 min-h-[44px] min-w-[44px] touch-manipulation", "ariaLabel": "Open navigation menu", "tabIndex": 0, "text": null, "visible": true}, {"index": 21, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs border-cyan-500/30 text-cyan-400 hover:bg-cyan-500/10", "ariaLabel": null, "tabIndex": 0, "text": "Complete Challenge +1", "visible": true}, {"index": 22, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border bg-background hover:text-accent-foreground h-9 rounded-md px-3 w-full text-xs border-red-500/30 text-red-400 hover:bg-red-500/10", "ariaLabel": null, "tabIndex": 0, "text": "Complete Goal +1", "visible": true}, {"index": 23, "tagName": "DIV", "type": null, "id": null, "className": "h-10 items-center justify-center rounded-md p-1 text-muted-foreground grid w-full grid-cols-4 glass bg-white/10 border border-white/20", "ariaLabel": null, "tabIndex": 0, "text": "CoursesAchievementsRecent ActivityCommunity", "visible": true}, {"index": 24, "tagName": "BUTTON", "type": "button", "id": "radix-«r1»-trigger-courses", "className": "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=active]:bg-white/20", "ariaLabel": null, "tabIndex": -1, "text": "Courses", "visible": true}, {"index": 25, "tagName": "BUTTON", "type": "button", "id": "radix-«r1»-trigger-achievements", "className": "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=active]:bg-white/20", "ariaLabel": null, "tabIndex": -1, "text": "Achievements", "visible": true}, {"index": 26, "tagName": "BUTTON", "type": "button", "id": "radix-«r1»-trigger-activity", "className": "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=active]:bg-white/20", "ariaLabel": null, "tabIndex": -1, "text": "Recent Activity", "visible": true}, {"index": 27, "tagName": "BUTTON", "type": "button", "id": "radix-«r1»-trigger-community", "className": "inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:text-foreground data-[state=active]:shadow-sm data-[state=active]:bg-white/20", "ariaLabel": null, "tabIndex": -1, "text": "Community", "visible": true}, {"index": 28, "tagName": "DIV", "type": null, "id": "radix-«r1»-content-courses", "className": "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 space-y-6", "ariaLabel": null, "tabIndex": 0, "text": "Continue LearningPick up where you left offAll Cou", "visible": true}, {"index": 29, "tagName": "DIV", "type": null, "id": "radix-«r1»-content-achievements", "className": "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 space-y-6", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": false}, {"index": 30, "tagName": "DIV", "type": null, "id": "radix-«r1»-content-activity", "className": "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 space-y-6", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": false}, {"index": 31, "tagName": "DIV", "type": null, "id": "radix-«r1»-content-community", "className": "mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 space-y-6", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": false}, {"index": 32, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 33, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 34, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 35, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 36, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Learn Solidity", "visible": true}, {"index": 37, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 38, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 39, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Documentation", "visible": true}, {"index": 40, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Tutorials", "visible": true}, {"index": 41, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Examples", "visible": true}, {"index": 42, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Best Practices", "visible": true}, {"index": 43, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Security Guide", "visible": true}, {"index": 44, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Discord", "visible": true}, {"index": 45, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Forum", "visible": true}, {"index": 46, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Blog", "visible": true}, {"index": 47, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Newsletter", "visible": true}, {"index": 48, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Privacy Policy", "visible": true}, {"index": 49, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Terms of Service", "visible": true}, {"index": 50, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 51, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-primary-foreground px-4 py-2 rounded-full w-12 h-12 bg-blue-600 hover:bg-blue-700 shadow-lg", "ariaLabel": "Open accessibility tester", "tabIndex": 0, "text": null, "visible": true}, {"index": 52, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON>ache", "visible": true}, {"index": 53, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 54, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Send Metrics", "visible": true}, {"index": 55, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Log to Console", "visible": true}, {"index": 56, "tagName": "BUTTON", "type": "submit", "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": "Dismiss feature spotlight", "tabIndex": 0, "text": null, "visible": true}, {"index": 57, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-white/50", "ariaLabel": null, "tabIndex": 0, "text": "Try AI Tutor", "visible": true}, {"index": 58, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-4 py-2 text-gray-400 hover:text-white transition-colors text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Maybe Later", "visible": true}, {"index": 59, "tagName": "BUTTON", "type": "button", "id": null, "className": "tsqd-open-btn", "ariaLabel": "Open Tanstack query devtools", "tabIndex": 0, "text": null, "visible": true}], "tabOrder": [{"tagName": "A", "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "text": "Skip to main content"}, {"tagName": "A", "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "text": "Skip to navigation"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": "flex items-center space-x-2", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": null, "text": "Learn"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Learn"}, {"tagName": "DIV", "id": null, "className": null, "text": "Code Lab"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Code Lab"}, {"tagName": "DIV", "id": null, "className": null, "text": "Collaborate"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Collaborate"}, {"tagName": "DIV", "id": null, "className": null, "text": "Achievements"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Achievements"}, {"tagName": "DIV", "id": null, "className": null, "text": "Auth Test"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Auth Test"}, {"tagName": "DIV", "id": null, "className": null, "text": "<PERSON><PERSON>"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "<PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": null, "text": "Admin"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Admin"}, {"tagName": "BUTTON", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "text": "Sign In"}, {"tagName": "BUTTON", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "text": "Get Started"}], "skipLinks": [{"text": "Skip to main content", "href": "http://localhost:3000/learn#undefined", "visible": true}, {"text": "Skip to navigation", "href": "http://localhost:3000/learn#undefined", "visible": true}], "focusTraps": [], "issues": []}, "contrastResults": [{"element": "HTML", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "HEAD", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "TITLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "SCRIPT", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "document.querySelectorAll('bod", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@keyframes go2264125279{from{t", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face {\n      font-displa", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".glass {\n    background: rgba(", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".inter_d3af1398-module__XqzvVq", "isLargeText": false}, {"element": "BODY", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Skip to main contentSkip to na", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to main content", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to navigation", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "NAV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(255, 255, 255, 0.1)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgba(0, 0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "LearnCode LabCollaborateAchiev", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign In", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Get Started", "isLargeText": false}, {"element": "MAIN", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Welcome Back, <PERSON><PERSON><PERSON>!<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Welcome Back, <PERSON><PERSON><PERSON>!<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Welcome Back, <PERSON><PERSON><PERSON>!<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Welcome Back, <PERSON><PERSON><PERSON>!<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Welcome Back, <PERSON><PERSON><PERSON>!<PERSON><PERSON>", "isLargeText": false}], "screenReaderResults": {"issues": [{"type": "heading-structure", "element": "H3", "message": "Heading level 3 follows level 1 (skipped levels)"}], "landmarks": 3, "headingCount": 10, "imageCount": 0, "formControlCount": 0}, "reducedMotionResults": {"issues": [{"type": "animation-not-disabled", "element": "HTML", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HTML", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "HEAD", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HEAD", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}], "totalAnimatedElements": 1658}}, {"url": "http://localhost:3000/code", "name": "Code Editor", "axeResults": null, "keyboardResults": {"focusableElements": [{"index": 0, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to main content", "visible": true}, {"index": 1, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to navigation", "visible": true}, {"index": 2, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": "SolanaLearn home page", "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 3, "tagName": "DIV", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 4, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 5, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Learn - Interactive lessons", "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 6, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 7, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Code Lab - Code playground", "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 8, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 9, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Collaborate - Real-time collaboration", "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 10, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 11, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Achievements - View achievements", "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 12, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 13, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Test - View achievements", "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 14, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 15, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Demo - View achievements", "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 16, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 17, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Admin - View achievements", "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 18, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Sign In", "visible": true}, {"index": 19, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Get Started", "visible": true}, {"index": 20, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent h-9 rounded-md md:hidden text-gray-300 hover:text-white p-2 min-h-[44px] min-w-[44px] touch-manipulation", "ariaLabel": "Open navigation menu", "tabIndex": 0, "text": null, "visible": true}, {"index": 21, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-primary-foreground h-10 px-4 py-2 bg-blue-600 hover:bg-blue-700", "ariaLabel": null, "tabIndex": 0, "text": "Compile", "visible": true}, {"index": 22, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Save", "visible": true}, {"index": 23, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3", "ariaLabel": null, "tabIndex": 0, "text": "Download", "visible": true}, {"index": 24, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 rounded-md px-3", "ariaLabel": null, "tabIndex": 0, "text": "Upload", "visible": true}, {"index": 25, "tagName": "INPUT", "type": "file", "id": null, "className": "hidden", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 26, "tagName": "TEXTAREA", "type": "textarea", "id": null, "className": "inputarea monaco-mouse-cursor-text", "ariaLabel": "Editor content", "tabIndex": 0, "text": null, "visible": true}, {"index": 27, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700", "ariaLabel": null, "tabIndex": 0, "text": "Connect MetaMask", "visible": true}, {"index": 28, "tagName": "A", "type": null, "id": null, "className": "text-blue-400 hover:text-blue-300 inline-flex items-center gap-1", "ariaLabel": null, "tabIndex": 0, "text": "Download here", "visible": true}, {"index": 29, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full text-left p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Simple StorageBasic contract with getter/setter", "visible": true}, {"index": 30, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full text-left p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "ERC-20 TokenStandard token implementation", "visible": true}, {"index": 31, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full text-left p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Multi-Sig WalletAdvanced security pattern", "visible": true}, {"index": 32, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 33, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 34, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 35, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 36, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Learn Solidity", "visible": true}, {"index": 37, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 38, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 39, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Documentation", "visible": true}, {"index": 40, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Tutorials", "visible": true}, {"index": 41, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Examples", "visible": true}, {"index": 42, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Best Practices", "visible": true}, {"index": 43, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Security Guide", "visible": true}, {"index": 44, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Discord", "visible": true}, {"index": 45, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Forum", "visible": true}, {"index": 46, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Blog", "visible": true}, {"index": 47, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Newsletter", "visible": true}, {"index": 48, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Privacy Policy", "visible": true}, {"index": 49, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Terms of Service", "visible": true}, {"index": 50, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 51, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-primary-foreground px-4 py-2 rounded-full w-12 h-12 bg-blue-600 hover:bg-blue-700 shadow-lg", "ariaLabel": "Open accessibility tester", "tabIndex": 0, "text": null, "visible": true}, {"index": 52, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON>ache", "visible": true}, {"index": 53, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 54, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Send Metrics", "visible": true}, {"index": 55, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Log to Console", "visible": true}, {"index": 56, "tagName": "BUTTON", "type": "submit", "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": "Dismiss feature spotlight", "tabIndex": 0, "text": null, "visible": true}, {"index": 57, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-white/50", "ariaLabel": null, "tabIndex": 0, "text": "Try AI Tutor", "visible": true}, {"index": 58, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-4 py-2 text-gray-400 hover:text-white transition-colors text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Maybe Later", "visible": true}, {"index": 59, "tagName": "BUTTON", "type": "button", "id": null, "className": "tsqd-open-btn", "ariaLabel": "Open Tanstack query devtools", "tabIndex": 0, "text": null, "visible": true}], "tabOrder": [{"tagName": "A", "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "text": "Skip to main content"}, {"tagName": "A", "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "text": "Skip to navigation"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": "flex items-center space-x-2", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": null, "text": "Learn"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Learn"}, {"tagName": "DIV", "id": null, "className": null, "text": "Code Lab"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Code Lab"}, {"tagName": "DIV", "id": null, "className": null, "text": "Collaborate"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Collaborate"}, {"tagName": "DIV", "id": null, "className": null, "text": "Achievements"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Achievements"}, {"tagName": "DIV", "id": null, "className": null, "text": "Auth Test"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Auth Test"}, {"tagName": "DIV", "id": null, "className": null, "text": "<PERSON><PERSON>"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "<PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": null, "text": "Admin"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Admin"}, {"tagName": "BUTTON", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "text": "Sign In"}, {"tagName": "BUTTON", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "text": "Get Started"}], "skipLinks": [{"text": "Skip to main content", "href": "http://localhost:3000/code#undefined", "visible": true}, {"text": "Skip to navigation", "href": "http://localhost:3000/code#undefined", "visible": true}], "focusTraps": [], "issues": []}, "contrastResults": [{"element": "HTML", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "HEAD", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "TITLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "SCRIPT", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "document.querySelectorAll('bod", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@keyframes go2264125279{from{t", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face {\n      font-displa", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".glass {\n    background: rgba(", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".inter_d3af1398-module__XqzvVq", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".codicon-add:before { content:", "isLargeText": false}, {"element": "BODY", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Skip to main contentSkip to na", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to main content", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to navigation", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "NAV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(255, 255, 255, 0.1)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgba(0, 0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "LearnCode LabCollaborateAchiev", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign In", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Get Started", "isLargeText": false}, {"element": "MAIN", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code LabWrite, compile, and de", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code LabWrite, compile, and de", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code LabWrite, compile, and de", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code LabWrite, compile, and de", "isLargeText": false}], "screenReaderResults": {"issues": [{"type": "file", "element": "INPUT", "message": "Form control missing label"}, {"type": "heading-structure", "element": "H3", "message": "Heading level 3 follows level 1 (skipped levels)"}], "landmarks": 3, "headingCount": 12, "imageCount": 0, "formControlCount": 2}, "reducedMotionResults": {"issues": [{"type": "animation-not-disabled", "element": "HTML", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HTML", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "HEAD", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HEAD", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}], "totalAnimatedElements": 1732}}, {"url": "http://localhost:3000/collaborate", "name": "Collaboration", "axeResults": null, "keyboardResults": {"focusableElements": [{"index": 0, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to main content", "visible": true}, {"index": 1, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to navigation", "visible": true}, {"index": 2, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": "SolanaLearn home page", "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 3, "tagName": "DIV", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 4, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 5, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Learn - Interactive lessons", "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 6, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 7, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Code Lab - Code playground", "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 8, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 9, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Collaborate - Real-time collaboration", "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 10, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 11, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Achievements - View achievements", "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 12, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 13, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Test - View achievements", "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 14, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 15, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Demo - View achievements", "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 16, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 17, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Admin - View achievements", "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 18, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Sign In", "visible": true}, {"index": 19, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Get Started", "visible": true}, {"index": 20, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent h-9 rounded-md md:hidden text-gray-300 hover:text-white p-2 min-h-[44px] min-w-[44px] touch-manipulation", "ariaLabel": "Open navigation menu", "tabIndex": 0, "text": null, "visible": true}, {"index": 21, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 22, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 23, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 24, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 25, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Learn Solidity", "visible": true}, {"index": 26, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 27, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 28, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Documentation", "visible": true}, {"index": 29, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Tutorials", "visible": true}, {"index": 30, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Examples", "visible": true}, {"index": 31, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Best Practices", "visible": true}, {"index": 32, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Security Guide", "visible": true}, {"index": 33, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Discord", "visible": true}, {"index": 34, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Forum", "visible": true}, {"index": 35, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Blog", "visible": true}, {"index": 36, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Newsletter", "visible": true}, {"index": 37, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Privacy Policy", "visible": true}, {"index": 38, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Terms of Service", "visible": true}, {"index": 39, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 40, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-primary-foreground px-4 py-2 rounded-full w-12 h-12 bg-blue-600 hover:bg-blue-700 shadow-lg", "ariaLabel": "Open accessibility tester", "tabIndex": 0, "text": null, "visible": true}, {"index": 41, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON>ache", "visible": true}, {"index": 42, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 43, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Send Metrics", "visible": true}, {"index": 44, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Log to Console", "visible": true}, {"index": 45, "tagName": "BUTTON", "type": "submit", "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": "Dismiss feature spotlight", "tabIndex": 0, "text": null, "visible": true}, {"index": 46, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-white/50", "ariaLabel": null, "tabIndex": 0, "text": "Try AI Tutor", "visible": true}, {"index": 47, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-4 py-2 text-gray-400 hover:text-white transition-colors text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Maybe Later", "visible": true}, {"index": 48, "tagName": "BUTTON", "type": "button", "id": null, "className": "tsqd-open-btn", "ariaLabel": "Open Tanstack query devtools", "tabIndex": 0, "text": null, "visible": true}], "tabOrder": [{"tagName": "A", "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "text": "Skip to main content"}, {"tagName": "A", "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "text": "Skip to navigation"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": "flex items-center space-x-2", "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": null, "text": "Learn"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Learn"}, {"tagName": "DIV", "id": null, "className": null, "text": "Code Lab"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Code Lab"}, {"tagName": "DIV", "id": null, "className": null, "text": "Collaborate"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Collaborate"}, {"tagName": "DIV", "id": null, "className": null, "text": "Achievements"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Achievements"}, {"tagName": "DIV", "id": null, "className": null, "text": "Auth Test"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Auth Test"}, {"tagName": "DIV", "id": null, "className": null, "text": "<PERSON><PERSON>"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "<PERSON><PERSON>"}, {"tagName": "DIV", "id": null, "className": null, "text": "Admin"}, {"tagName": "A", "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "text": "Admin"}, {"tagName": "BUTTON", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "text": "Sign In"}, {"tagName": "BUTTON", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "text": "Get Started"}], "skipLinks": [{"text": "Skip to main content", "href": "http://localhost:3000/collaborate#undefined", "visible": true}, {"text": "Skip to navigation", "href": "http://localhost:3000/collaborate#undefined", "visible": true}], "focusTraps": [], "issues": []}, "contrastResults": [{"element": "HTML", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "HEAD", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "TITLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON> - Master Solidity ", "isLargeText": false}, {"element": "SCRIPT", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "document.querySelectorAll('bod", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@keyframes go2264125279{from{t", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face {\n      font-displa", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".glass {\n    background: rgba(", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".inter_d3af1398-module__XqzvVq", "isLargeText": false}, {"element": "BODY", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Skip to main contentSkip to na", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to main content", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to navigation", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "NAV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(255, 255, 255, 0.1)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgba(0, 0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "LearnCode LabCollaborateAchiev", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign In", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Get Started", "isLargeText": false}, {"element": "MAIN", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Authentication RequiredPlease ", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Authentication RequiredPlease ", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Authentication RequiredPlease ", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Authentication RequiredPlease ", "isLargeText": false}, {"element": "H2", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Authentication Required", "isLargeText": false}], "screenReaderResults": {"issues": [{"type": "heading-structure", "element": "H2", "message": "Heading level 2 follows level 0 (skipped levels)"}], "landmarks": 3, "headingCount": 8, "imageCount": 0, "formControlCount": 0}, "reducedMotionResults": {"issues": [{"type": "animation-not-disabled", "element": "HTML", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HTML", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "HEAD", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HEAD", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}], "totalAnimatedElements": 1364}}, {"url": "http://localhost:3000/achievements", "name": "Achievements", "axeResults": null, "keyboardResults": {"focusableElements": [], "tabOrder": [], "skipLinks": [], "focusTraps": [], "issues": ["No focusable elements found or tab navigation not working"]}, "contrastResults": [{"element": "HTML", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "HEAD", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "BODY", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "{\"props\":{\"pageProps\":{\"status", "isLargeText": false}, {"element": "SCRIPT", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "{\"props\":{\"pageProps\":{\"status", "isLargeText": false}], "screenReaderResults": {"issues": [], "landmarks": 0, "headingCount": 0, "imageCount": 0, "formControlCount": 0}, "reducedMotionResults": {"issues": [], "totalAnimatedElements": 0}}, {"url": "http://localhost:3000/settings", "name": "Settings", "axeResults": null, "keyboardResults": {"focusableElements": [{"index": 0, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to main content", "visible": true}, {"index": 1, "tagName": "A", "type": null, "id": null, "className": "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 z-50 bg-brand-primary-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200", "ariaLabel": null, "tabIndex": 0, "text": "Skip to navigation", "visible": true}, {"index": 2, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": "SolanaLearn home page", "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 3, "tagName": "DIV", "type": null, "id": null, "className": "flex items-center space-x-2", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "visible": true}, {"index": 4, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 5, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Learn - Interactive lessons", "tabIndex": 0, "text": "Learn", "visible": true}, {"index": 6, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 7, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Code Lab - Code playground", "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 8, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 9, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Collaborate - Real-time collaboration", "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 10, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 11, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Achievements - View achievements", "tabIndex": 0, "text": "Achievements", "visible": true}, {"index": 12, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 13, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Test - View achievements", "tabIndex": 0, "text": "Auth Test", "visible": true}, {"index": 14, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 15, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Auth Demo - View achievements", "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 16, "tagName": "DIV", "type": null, "id": null, "className": null, "ariaLabel": null, "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 17, "tagName": "A", "type": null, "id": null, "className": "flex items-center space-x-2 px-3 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white/10 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:bg-white/10", "ariaLabel": "Admin - View achievements", "tabIndex": 0, "text": "Admin", "visible": true}, {"index": 18, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Sign In", "visible": true}, {"index": 19, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2", "ariaLabel": null, "tabIndex": 0, "text": "Get Started", "visible": true}, {"index": 20, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:bg-accent h-9 rounded-md md:hidden text-gray-300 hover:text-white p-2 min-h-[44px] min-w-[44px] touch-manipulation", "ariaLabel": "Open navigation menu", "tabIndex": 0, "text": null, "visible": true}, {"index": 21, "tagName": "A", "type": null, "id": null, "className": "hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Home", "visible": true}, {"index": 22, "tagName": "INPUT", "type": "text", "id": null, "className": "w-full bg-transparent border-none outline-none text-white placeholder-gray-400 px-4 py-3", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 23, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Solidity FundamentalsbeginnerLearn the basics of S", "visible": true}, {"index": 24, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Smart Contract SecurityBest practices for secure s", "visible": true}, {"index": 25, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Variables and Data Types75% completeUnderstanding ", "visible": true}, {"index": 26, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "mapping in solidity2 hours ago", "visible": true}, {"index": 27, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-white/10 transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "function modifiers1 day ago", "visible": true}, {"index": 28, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "DashboardReturn to your learning dashboard", "visible": true}, {"index": 29, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "CoursesBrowse our course catalog", "visible": true}, {"index": 30, "tagName": "BUTTON", "type": "submit", "id": null, "className": "group text-left", "ariaLabel": null, "tabIndex": 0, "text": "Report IssueHelp us fix this broken link", "visible": true}, {"index": 31, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "Home", "visible": true}, {"index": 32, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "courses", "visible": true}, {"index": 33, "tagName": "A", "type": null, "id": null, "className": "flex items-center justify-between p-3 bg-white/5 hover:bg-white/10 rounded-lg transition-colors border border-white/10", "ariaLabel": null, "tabIndex": 0, "text": "dashboard", "visible": true}, {"index": 34, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Solidity FundamentalsbeginnerLearn the basics of S", "visible": true}, {"index": 35, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Advanced Smart ContractsadvancedMaster complex pat", "visible": true}, {"index": 36, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "DeFi DevelopmentintermediateBuild decentralized fi", "visible": true}, {"index": 37, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Variables and Data TypesSolidity Fundamentals75%", "visible": true}, {"index": 38, "tagName": "A", "type": null, "id": null, "className": "group", "ariaLabel": null, "tabIndex": 0, "text": "Function ModifiersAdvanced Smart Contracts45%", "visible": true}, {"index": 39, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Go Back", "visible": true}, {"index": 40, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Refresh Page", "visible": true}, {"index": 41, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 42, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 43, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 44, "tagName": "A", "type": null, "id": null, "className": "p-2 rounded-lg bg-white/5 hover:bg-white/10 transition-colors group", "ariaLabel": null, "tabIndex": 0, "text": null, "visible": true}, {"index": 45, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Learn Solidity", "visible": true}, {"index": 46, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Code Lab", "visible": true}, {"index": 47, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Collaborate", "visible": true}, {"index": 48, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Documentation", "visible": true}, {"index": 49, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Tutorials", "visible": true}, {"index": 50, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Examples", "visible": true}, {"index": 51, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Best Practices", "visible": true}, {"index": 52, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Security Guide", "visible": true}, {"index": 53, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Discord", "visible": true}, {"index": 54, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Forum", "visible": true}, {"index": 55, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Blog", "visible": true}, {"index": 56, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors duration-200 text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Newsletter", "visible": true}, {"index": 57, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Privacy Policy", "visible": true}, {"index": 58, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "Terms of Service", "visible": true}, {"index": 59, "tagName": "A", "type": null, "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 60, "tagName": "BUTTON", "type": "submit", "id": null, "className": "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 text-primary-foreground px-4 py-2 rounded-full w-12 h-12 bg-blue-600 hover:bg-blue-700 shadow-lg", "ariaLabel": "Open accessibility tester", "tabIndex": 0, "text": null, "visible": true}, {"index": 61, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON>ache", "visible": true}, {"index": 62, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "<PERSON><PERSON>", "visible": true}, {"index": 63, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Send Metrics", "visible": true}, {"index": 64, "tagName": "BUTTON", "type": "submit", "id": null, "className": "w-full px-2 py-1 bg-gray-600 hover:bg-gray-700 text-white text-xs rounded", "ariaLabel": null, "tabIndex": 0, "text": "Log to Console", "visible": true}, {"index": 65, "tagName": "BUTTON", "type": "submit", "id": null, "className": "text-gray-400 hover:text-white transition-colors", "ariaLabel": "Dismiss feature spotlight", "tabIndex": 0, "text": null, "visible": true}, {"index": 66, "tagName": "BUTTON", "type": "submit", "id": null, "className": "flex items-center space-x-2 px-4 py-2 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-colors text-sm focus:outline-none focus:ring-2 focus:ring-white/50", "ariaLabel": null, "tabIndex": 0, "text": "Try AI Tutor", "visible": true}, {"index": 67, "tagName": "BUTTON", "type": "submit", "id": null, "className": "px-4 py-2 text-gray-400 hover:text-white transition-colors text-sm", "ariaLabel": null, "tabIndex": 0, "text": "Maybe Later", "visible": true}, {"index": 68, "tagName": "BUTTON", "type": "button", "id": null, "className": "tsqd-open-btn", "ariaLabel": "Open Tanstack query devtools", "tabIndex": 0, "text": null, "visible": true}], "tabOrder": [{"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}, {"tagName": "NEXTJS-PORTAL", "id": null, "className": null, "text": null}], "skipLinks": [{"text": "Skip to main content", "href": "http://localhost:3000/auth?returnUrl=%2Fsettings#undefined", "visible": true}, {"text": "Skip to navigation", "href": "http://localhost:3000/auth?returnUrl=%2Fsettings#undefined", "visible": true}], "focusTraps": [], "issues": []}, "contrastResults": [{"element": "HTML", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "HEAD", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "TITLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "404 - Page Not Found | Solidit", "isLargeText": false}, {"element": "SCRIPT", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "document.querySelectorAll('bod", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@keyframes go2264125279{from{t", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face{font-family:'__next", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "@font-face {\n      font-displa", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".glass {\n    background: rgba(", "isLargeText": false}, {"element": "STYLE", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": ".inter_d3af1398-module__XqzvVq", "isLargeText": false}, {"element": "BODY", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Skip to main contentSkip to na", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to main content", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgb(124, 58, 237)", "fontSize": 16, "text": "Skip to navigation", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "NAV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(255, 255, 255, 0.1)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "SolanaLearnLearnCode LabCollab", "isLargeText": false}, {"element": "A", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgba(0, 0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "LearnCode LabCollaborateAchiev", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Learn", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Code Lab", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Collaborate", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Achievements", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Auth Test", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "<PERSON><PERSON>", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "A", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "SPAN", "color": "rgb(209, 213, 219)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Admin", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign InGet Started", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Sign In", "isLargeText": false}, {"element": "BUTTON", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Get Started", "isLargeText": false}, {"element": "MAIN", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauth404404Oops! Page Not F", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauth404404Oops! Page Not F", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauth404404Oops! Page Not F", "isLargeText": false}, {"element": "DIV", "color": "rgb(0, 0, 0)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauth", "isLargeText": false}, {"element": "DIV", "color": "rgb(156, 163, 175)", "backgroundColor": "rgba(0, 0, 0, 0)", "fontSize": 16, "text": "Homeauth", "isLargeText": false}], "screenReaderResults": {"issues": [{"type": "text", "element": "INPUT", "message": "Form control missing label"}], "landmarks": 3, "headingCount": 21, "imageCount": 0, "formControlCount": 1}, "reducedMotionResults": {"issues": [{"type": "animation-not-disabled", "element": "HTML", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HTML", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "HEAD", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "HEAD", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "META", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "META", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "LINK", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "LINK", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}, {"type": "animation-not-disabled", "element": "SCRIPT", "className": "", "animationDuration": "1e-05s", "message": "Animation not disabled with reduced motion preference"}, {"type": "transition-not-disabled", "element": "SCRIPT", "className": "", "transitionDuration": "1e-05s", "message": "Transition not disabled with reduced motion preference"}], "totalAnimatedElements": 1878}}], "config": {"urls": [{"url": "http://localhost:3000", "name": "Homepage"}, {"url": "http://localhost:3000/auth/login", "name": "<PERSON><PERSON>"}, {"url": "http://localhost:3000/auth/register", "name": "Registration Page"}, {"url": "http://localhost:3000/dashboard", "name": "Dashboard"}, {"url": "http://localhost:3000/learn", "name": "<PERSON><PERSON>"}, {"url": "http://localhost:3000/code", "name": "Code Editor"}, {"url": "http://localhost:3000/collaborate", "name": "Collaboration"}, {"url": "http://localhost:3000/achievements", "name": "Achievements"}, {"url": "http://localhost:3000/settings", "name": "Settings"}], "wcagLevels": ["wcag2a", "wcag2aa"], "tags": ["wcag2a", "wcag2aa", "wcag21aa", "best-practice"], "rules": {"color-contrast": {"enabled": true, "level": "AA"}, "keyboard-navigation": {"enabled": true, "level": "AA"}, "aria-labels": {"enabled": true, "level": "AA"}, "focus-indicators": {"enabled": true, "level": "AA"}, "heading-structure": {"enabled": true, "level": "AA"}, "alt-text": {"enabled": true, "level": "AA"}, "form-labels": {"enabled": true, "level": "AA"}, "link-purpose": {"enabled": true, "level": "AA"}, "page-title": {"enabled": true, "level": "AA"}, "language": {"enabled": true, "level": "AA"}}}}