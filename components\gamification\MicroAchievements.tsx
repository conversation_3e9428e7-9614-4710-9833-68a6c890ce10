'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Code, 
  Zap, 
  Target, 
  CheckCircle, 
  Clock, 
  TrendingUp,
  Lightbulb,
  Coffee,
  Rocket,
  Star,
  Award,
  BookOpen
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useXPNotifications } from '@/components/xp/XPNotification';

export interface MicroAchievement {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  xpReward: number;
  trigger: string;
  category: 'coding' | 'learning' | 'speed' | 'consistency' | 'exploration';
  unlocked: boolean;
  unlockedAt?: Date;
  progress?: number;
  maxProgress?: number;
}

interface MicroAchievementNotificationProps {
  achievement: MicroAchievement;
  onClose: () => void;
  duration?: number;
}

function MicroAchievementNotification({
  achievement,
  onClose,
  duration = 3000
}: MicroAchievementNotificationProps) {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onClose, 500);
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const getCategoryColor = () => {
    switch (achievement.category) {
      case 'coding':
        return 'from-blue-500 to-cyan-500';
      case 'learning':
        return 'from-green-500 to-emerald-500';
      case 'speed':
        return 'from-yellow-500 to-orange-500';
      case 'consistency':
        return 'from-purple-500 to-pink-500';
      case 'exploration':
        return 'from-indigo-500 to-purple-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const Icon = achievement.icon;

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, scale: 0.5, y: 50 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.5, y: -50 }}
          transition={{ type: 'spring', stiffness: 400, damping: 25 }}
          className="fixed bottom-4 right-4 z-50 max-w-sm w-full"
        >
          <div className={cn(
            'bg-black/90 backdrop-blur-md border-2 rounded-xl p-4',
            'shadow-2xl border-gradient-to-r',
            getCategoryColor().replace('from-', 'border-').replace('to-', '').split(' ')[0] + '/50'
          )}>
            <div className="flex items-start space-x-3">
              {/* Icon */}
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ delay: 0.2, type: 'spring', stiffness: 300 }}
                className={cn(
                  'w-10 h-10 rounded-full flex items-center justify-center',
                  'bg-gradient-to-br',
                  getCategoryColor(),
                  'shadow-lg'
                )}
              >
                <Icon className="w-5 h-5 text-white" />
              </motion.div>

              {/* Content */}
              <div className="flex-1 min-w-0">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.3 }}
                >
                  <div className="flex items-center space-x-2 mb-1">
                    <h4 className="font-semibold text-white text-sm">Micro Achievement!</h4>
                    <div className="flex items-center space-x-1">
                      <Zap className="w-3 h-3 text-yellow-400" />
                      <span className="text-yellow-400 text-xs font-bold">+{achievement.xpReward}</span>
                    </div>
                  </div>
                  <p className="text-white font-medium text-sm">{achievement.title}</p>
                  <p className="text-gray-400 text-xs mt-1">{achievement.description}</p>
                </motion.div>
              </div>
            </div>

            {/* Progress bar animation */}
            <motion.div
              initial={{ width: 0 }}
              animate={{ width: '100%' }}
              transition={{ duration: duration / 1000, ease: 'linear' }}
              className={cn(
                'h-1 rounded-full mt-3 bg-gradient-to-r',
                getCategoryColor()
              )}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

interface MicroAchievementManagerProps {
  className?: string;
  onAchievementUnlocked?: (achievement: MicroAchievement) => void;
}

export function MicroAchievementManager({
  className,
  onAchievementUnlocked
}: MicroAchievementManagerProps) {
  const [notifications, setNotifications] = useState<MicroAchievement[]>([]);
  const [userProgress, setUserProgress] = useState<Record<string, any>>({});
  const { triggerXPGain } = useXPNotifications();

  // Define micro achievements
  const microAchievements: MicroAchievement[] = [
    {
      id: 'first-line',
      title: 'First Line of Code',
      description: 'Write your very first line of Solidity',
      icon: Code,
      xpReward: 10,
      trigger: 'code_typed',
      category: 'coding',
      unlocked: false
    },
    {
      id: 'syntax-error-fixed',
      title: 'Syntax Detective',
      description: 'Fix your first syntax error',
      icon: CheckCircle,
      xpReward: 15,
      trigger: 'syntax_error_fixed',
      category: 'learning',
      unlocked: false
    },
    {
      id: 'function-created',
      title: 'Function Master',
      description: 'Create your first function',
      icon: Lightbulb,
      xpReward: 25,
      trigger: 'function_created',
      category: 'coding',
      unlocked: false
    },
    {
      id: 'quick-learner',
      title: 'Quick Learner',
      description: 'Complete a lesson in under 2 minutes',
      icon: Clock,
      xpReward: 30,
      trigger: 'lesson_completed_fast',
      category: 'speed',
      unlocked: false
    },
    {
      id: 'coffee-break',
      title: 'Coffee Break Coder',
      description: 'Code for 5 minutes straight',
      icon: Coffee,
      xpReward: 20,
      trigger: 'continuous_coding',
      category: 'consistency',
      unlocked: false
    },
    {
      id: 'explorer',
      title: 'Code Explorer',
      description: 'Try different code examples',
      icon: Rocket,
      xpReward: 15,
      trigger: 'code_exploration',
      category: 'exploration',
      unlocked: false
    },
    {
      id: 'perfectionist',
      title: 'Perfectionist',
      description: 'Complete a lesson without any errors',
      icon: Star,
      xpReward: 40,
      trigger: 'perfect_completion',
      category: 'learning',
      unlocked: false
    },
    {
      id: 'speed-demon',
      title: 'Speed Demon',
      description: 'Type 50 characters in 30 seconds',
      icon: TrendingUp,
      xpReward: 25,
      trigger: 'fast_typing',
      category: 'speed',
      unlocked: false
    }
  ];

  // Function to check and unlock achievements
  const checkAchievement = (trigger: string, data?: any) => {
    const achievement = microAchievements.find(a => a.trigger === trigger && !a.unlocked);
    if (!achievement) return;

    // Mark as unlocked
    achievement.unlocked = true;
    achievement.unlockedAt = new Date();

    // Show notification
    setNotifications(prev => [...prev, achievement]);

    // Trigger XP notification
    triggerXPGain(
      achievement.xpReward,
      'micro_achievement',
      achievement.title,
      undefined,
      {
        icon: achievement.icon,
        isBonus: true
      }
    );

    // Call callback
    onAchievementUnlocked?.(achievement);
  };

  // Remove notification
  const removeNotification = (achievementId: string) => {
    setNotifications(prev => prev.filter(n => n.id !== achievementId));
  };

  // Expose achievement checker globally
  useEffect(() => {
    (window as any).checkMicroAchievement = checkAchievement;
    
    return () => {
      delete (window as any).checkMicroAchievement;
    };
  }, []);

  return (
    <div className={className}>
      {/* Render notifications */}
      {notifications.map(achievement => (
        <MicroAchievementNotification
          key={achievement.id}
          achievement={achievement}
          onClose={() => removeNotification(achievement.id)}
        />
      ))}
    </div>
  );
}

// Achievement Preview Component
interface AchievementPreviewProps {
  achievements: MicroAchievement[];
  className?: string;
}

export function AchievementPreview({ achievements, className }: AchievementPreviewProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = [
    { id: 'all', name: 'All', icon: Award },
    { id: 'coding', name: 'Coding', icon: Code },
    { id: 'learning', name: 'Learning', icon: BookOpen },
    { id: 'speed', name: 'Speed', icon: TrendingUp },
    { id: 'consistency', name: 'Consistency', icon: Target },
    { id: 'exploration', name: 'Exploration', icon: Rocket }
  ];

  const filteredAchievements = selectedCategory === 'all' 
    ? achievements 
    : achievements.filter(a => a.category === selectedCategory);

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'coding':
        return 'text-blue-400 bg-blue-500/20 border-blue-500/30';
      case 'learning':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'speed':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'consistency':
        return 'text-purple-400 bg-purple-500/20 border-purple-500/30';
      case 'exploration':
        return 'text-indigo-400 bg-indigo-500/20 border-indigo-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => {
          const Icon = category.icon;
          return (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={cn(
                'flex items-center space-x-2 px-3 py-2 rounded-lg border transition-all',
                selectedCategory === category.id
                  ? 'bg-white/10 border-white/30 text-white'
                  : 'bg-white/5 border-white/10 text-gray-400 hover:bg-white/10'
              )}
            >
              <Icon className="w-4 h-4" />
              <span className="text-sm font-medium">{category.name}</span>
            </button>
          );
        })}
      </div>

      {/* Achievement Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredAchievements.map(achievement => {
          const Icon = achievement.icon;
          const isLocked = !achievement.unlocked;
          
          return (
            <motion.div
              key={achievement.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className={cn(
                'p-4 rounded-xl border transition-all',
                isLocked 
                  ? 'bg-gray-800/50 border-gray-700/50' 
                  : 'bg-white/5 border-white/20 hover:bg-white/10'
              )}
            >
              <div className="flex items-start space-x-3">
                <div className={cn(
                  'w-10 h-10 rounded-full flex items-center justify-center border',
                  isLocked 
                    ? 'bg-gray-700 border-gray-600' 
                    : getCategoryColor(achievement.category)
                )}>
                  <Icon className={cn(
                    'w-5 h-5',
                    isLocked ? 'text-gray-500' : 'text-current'
                  )} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className={cn(
                    'font-semibold text-sm',
                    isLocked ? 'text-gray-500' : 'text-white'
                  )}>
                    {achievement.title}
                  </h4>
                  <p className={cn(
                    'text-xs mt-1',
                    isLocked ? 'text-gray-600' : 'text-gray-400'
                  )}>
                    {achievement.description}
                  </p>
                  
                  <div className="flex items-center justify-between mt-2">
                    <div className="flex items-center space-x-1">
                      <Zap className={cn(
                        'w-3 h-3',
                        isLocked ? 'text-gray-600' : 'text-yellow-400'
                      )} />
                      <span className={cn(
                        'text-xs font-medium',
                        isLocked ? 'text-gray-600' : 'text-yellow-400'
                      )}>
                        +{achievement.xpReward} XP
                      </span>
                    </div>
                    
                    {achievement.unlocked && achievement.unlockedAt && (
                      <span className="text-xs text-green-400">
                        ✓ Unlocked
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}

// Hook for triggering micro achievements
export function useMicroAchievements() {
  const triggerMicroAchievement = (trigger: string, data?: any) => {
    if (typeof window !== 'undefined' && (window as any).checkMicroAchievement) {
      (window as any).checkMicroAchievement(trigger, data);
    }
  };

  return { triggerMicroAchievement };
}
