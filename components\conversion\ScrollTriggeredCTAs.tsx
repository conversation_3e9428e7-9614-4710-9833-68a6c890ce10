'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ArrowRight, 
  Zap, 
  Star, 
  Gift, 
  Clock,
  TrendingUp,
  Users,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface ScrollTriggeredCTAsProps {
  // For ComprehensiveConversionSystem usage
  milestones?: number[];
  ctaVariations?: string[];

  // Unified callback signatures
  onScrollMilestone?: ((percentage: number) => void) | ((milestone: number) => void);
  onCTAClick?: ((cta: string, context: any) => void) | ((action: string, context: any) => void);
  userContext?: any;

  // Common props
  className?: string;
}

interface ScrollCTA {
  id: string;
  trigger: number; // scroll percentage
  title: string;
  description: string;
  ctaText: string;
  ctaAction: string;
  variant: 'primary' | 'secondary' | 'urgency';
  icon: React.ComponentType<any>;
  position: 'top' | 'bottom' | 'center';
  animation: 'slide' | 'fade' | 'bounce';
  urgencyLevel?: 'low' | 'medium' | 'high';
}

const defaultCTAs: ScrollCTA[] = [
  {
    id: 'early-engagement',
    trigger: 25,
    title: 'Ready to Start Learning?',
    description: 'Join thousands of developers mastering Solidity',
    ctaText: 'Start Free Trial',
    ctaAction: 'start_trial',
    variant: 'primary',
    icon: Zap,
    position: 'bottom',
    animation: 'slide'
  },
  {
    id: 'mid-engagement',
    trigger: 50,
    title: 'Unlock Advanced Features',
    description: 'Get access to AI-powered code analysis and real-time feedback',
    ctaText: 'Upgrade Now',
    ctaAction: 'upgrade',
    variant: 'secondary',
    icon: Star,
    position: 'center',
    animation: 'fade'
  },
  {
    id: 'high-engagement',
    trigger: 75,
    title: 'Limited Time Offer!',
    description: '50% off premium features - expires in 24 hours',
    ctaText: 'Claim Discount',
    ctaAction: 'claim_discount',
    variant: 'urgency',
    icon: Gift,
    position: 'top',
    animation: 'bounce',
    urgencyLevel: 'high'
  },
  {
    id: 'exit-intent',
    trigger: 90,
    title: "Don't Miss Out!",
    description: 'Start your coding journey with our comprehensive Solidity course',
    ctaText: 'Get Started Now',
    ctaAction: 'final_cta',
    variant: 'primary',
    icon: TrendingUp,
    position: 'center',
    animation: 'slide'
  }
];

export function ScrollTriggeredCTAs({
  milestones = [25, 50, 75, 90],
  ctaVariations = ['Start Free Trial', 'Begin Learning', 'Get Started Now', 'Try It Free'],
  onScrollMilestone,
  onCTAClick,
  userContext,
  className
}: ScrollTriggeredCTAsProps) {
  const [scrollPercentage, setScrollPercentage] = useState(0);
  const [activeCTA, setActiveCTA] = useState<ScrollCTA | null>(null);
  const [triggeredCTAs, setTriggeredCTAs] = useState<Set<string>>(new Set());
  const [isVisible, setIsVisible] = useState(false);
  const ctaRef = useRef<HTMLDivElement>(null);

  // Calculate scroll percentage
  const calculateScrollPercentage = useCallback(() => {
    const scrollTop = window.pageYOffset;
    const docHeight = document.documentElement.scrollHeight - window.innerHeight;
    const percentage = Math.round((scrollTop / docHeight) * 100);
    return Math.min(100, Math.max(0, percentage));
  }, []);

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      const percentage = calculateScrollPercentage();
      setScrollPercentage(percentage);

      // Check for milestone triggers
      milestones.forEach(milestone => {
        if (percentage >= milestone && onScrollMilestone) {
          onScrollMilestone(milestone);
        }
      });

      // Find the appropriate CTA to show
      const applicableCTA = defaultCTAs
        .filter(cta => percentage >= cta.trigger)
        .sort((a, b) => b.trigger - a.trigger)[0];

      if (applicableCTA && !triggeredCTAs.has(applicableCTA.id)) {
        setActiveCTA(applicableCTA);
        setIsVisible(true);
        setTriggeredCTAs(prev => new Set([...prev, applicableCTA.id]));
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [milestones, onScrollMilestone, triggeredCTAs, calculateScrollPercentage]);

  // Auto-hide CTA after delay
  useEffect(() => {
    if (isVisible && activeCTA) {
      const timer = setTimeout(() => {
        setIsVisible(false);
      }, 8000); // Hide after 8 seconds

      return () => clearTimeout(timer);
    }
  }, [isVisible, activeCTA]);

  const handleCTAClick = (cta: ScrollCTA) => {
    if (onCTAClick) {
      // Handle both function signatures
      if (ctaVariations) {
        // ComprehensiveConversionSystem signature
        onCTAClick(cta.ctaText, {
          scrollPercentage,
          ctaId: cta.id,
          variant: cta.variant,
          userContext
        });
      } else {
        // ComprehensiveEngagementSystem signature
        onCTAClick(cta.ctaAction, {
          scrollPercentage,
          ctaId: cta.id,
          variant: cta.variant,
          userContext
        });
      }
    }
    setIsVisible(false);
  };

  const getVariantStyles = (variant: string) => {
    switch (variant) {
      case 'primary':
        return 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700';
      case 'secondary':
        return 'bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800';
      case 'urgency':
        return 'bg-gradient-to-r from-red-600 to-orange-600 hover:from-red-700 hover:to-orange-700 animate-pulse';
      default:
        return 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700';
    }
  };

  const getPositionStyles = (position: string) => {
    switch (position) {
      case 'top':
        return 'top-4';
      case 'bottom':
        return 'bottom-4';
      case 'center':
        return 'top-1/2 transform -translate-y-1/2';
      default:
        return 'bottom-4';
    }
  };

  const getAnimationVariants = (animation: string) => {
    switch (animation) {
      case 'slide':
        return {
          initial: { x: 100, opacity: 0 },
          animate: { x: 0, opacity: 1 },
          exit: { x: 100, opacity: 0 }
        };
      case 'fade':
        return {
          initial: { opacity: 0, scale: 0.9 },
          animate: { opacity: 1, scale: 1 },
          exit: { opacity: 0, scale: 0.9 }
        };
      case 'bounce':
        return {
          initial: { y: 50, opacity: 0 },
          animate: { y: 0, opacity: 1, transition: { type: 'spring', bounce: 0.4 } },
          exit: { y: 50, opacity: 0 }
        };
      default:
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 }
        };
    }
  };

  if (!activeCTA) return null;

  const Icon = activeCTA.icon;
  const variants = getAnimationVariants(activeCTA.animation);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          ref={ctaRef}
          className={cn(
            'fixed right-4 z-50 max-w-sm',
            getPositionStyles(activeCTA.position),
            className
          )}
          initial={variants.initial}
          animate={variants.animate}
          exit={variants.exit}
          transition={{ duration: 0.3 }}
        >
          <div className="bg-white/10 backdrop-blur-md rounded-lg border border-white/20 p-4 shadow-2xl">
            {/* Close button */}
            <button
              onClick={() => setIsVisible(false)}
              className="absolute top-2 right-2 text-gray-400 hover:text-white transition-colors"
              aria-label="Close"
            >
              ×
            </button>

            {/* Content */}
            <div className="flex items-start space-x-3">
              <div className={cn(
                'p-2 rounded-lg',
                activeCTA.variant === 'urgency' ? 'bg-red-500/20' : 'bg-blue-500/20'
              )}>
                <Icon className={cn(
                  'w-5 h-5',
                  activeCTA.variant === 'urgency' ? 'text-red-400' : 'text-blue-400'
                )} />
              </div>
              
              <div className="flex-1 min-w-0">
                <h3 className="text-white font-semibold text-sm mb-1">
                  {activeCTA.title}
                </h3>
                <p className="text-gray-300 text-xs mb-3 leading-relaxed">
                  {activeCTA.description}
                </p>
                
                <EnhancedButton
                  onClick={() => handleCTAClick(activeCTA)}
                  className={cn(
                    'w-full text-white text-sm font-medium px-4 py-2 rounded-lg transition-all duration-200',
                    getVariantStyles(activeCTA.variant)
                  )}
                  size="sm"
                >
                  {activeCTA.ctaText}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </EnhancedButton>
              </div>
            </div>

            {/* Progress indicator */}
            <div className="mt-3 w-full bg-gray-700 rounded-full h-1">
              <div
                className="bg-blue-400 h-1 rounded-full transition-all duration-300"
                style={{ width: `${scrollPercentage}%` }}
              />
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

export default ScrollTriggeredCTAs;
