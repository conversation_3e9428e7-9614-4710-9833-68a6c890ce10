/**
 * Route-based preloading utilities for optimizing navigation performance
 */
// Route preloading configuration
export const ROUTE_PRELOAD_CONFIG = {
  // Critical routes that should be preloaded immediately
  critical: [
    '/dashboard',
    '/learn',
    '/code'
  ],
  // Routes to preload on hover/focus
  onHover: [
    '/achievements',
    '/collaborate',
    '/profile'
  ],
  // Routes to preload on idle
  onIdle: [
    '/admin',
    '/settings',
    '/offline'
  ]
};
// Component preloading map
const COMPONENT_PRELOADERS = {
  '/dashboard': () => import('@/app/dashboard/page'),
  '/learn': () => import('@/app/learn/page'),
  '/code': () => import('@/app/code/page'),
  '/achievements': () => import('@/app/achievements/page'),
  '/collaborate': () => import('@/app/collaborate/page'),
  '/profile': () => import('@/app/profile/page'),
  '/admin': () => import('@/app/admin/page'),
  '/settings': () => import('@/app/settings/page'),
  '/offline': () => import('@/app/offline/page'),
  // Heavy components
  'monaco-editor': () => import('@monaco-editor/react'),
  'achievement-grid': () => import('@/components/achievements/AchievementGrid'),
  'admin-dashboard': () => import('@/components/admin/AdminDashboard'),
  'collaboration-panel': () => import('@/components/collaboration/RealTimeCollaboration'),
  'gamification-dashboard': () => import('@/components/gamification/GamificationDashboard'),
};
// Preload state tracking
const preloadedRoutes = new Set<string>();
const preloadedComponents = new Set<string>();
/**
 * Preload a specific route
 */
export async function preloadRoute(route: string): Promise<void> {
  if (preloadedRoutes.has(route) || typeof window === 'undefined') {
    return;
  }
  try {
    const preloader = COMPONENT_PRELOADERS[route];
    if (preloader) {
      await preloader();
      preloadedRoutes.add(route);
    }
  } catch (error) {
  }
}
/**
 * Preload a specific component
 */
export async function preloadComponent(componentKey: string): Promise<void> {
  if (preloadedComponents.has(componentKey) || typeof window === 'undefined') {
    return;
  }
  try {
    const preloader = COMPONENT_PRELOADERS[componentKey];
    if (preloader) {
      await preloader();
      preloadedComponents.add(componentKey);
    }
  } catch (error) {
  }
}
/**
 * Preload critical routes immediately
 */
export function preloadCriticalRoutes(): void {
  if (typeof window === 'undefined') return;
  ROUTE_PRELOAD_CONFIG.critical.forEach(route => {
    // Use requestIdleCallback for non-blocking preloading
    if ('requestIdleCallback' in window) {
      requestIdleCallback(() => preloadRoute(route));
    } else {
      // Fallback for browsers without requestIdleCallback
      setTimeout(() => preloadRoute(route), 100);
    }
  });
}
/**
 * Preload routes on idle
 */
export function preloadIdleRoutes(): void {
  if (typeof window === 'undefined') return;
  const preloadOnIdle = () => {
    ROUTE_PRELOAD_CONFIG.onIdle.forEach(route => {
      preloadRoute(route);
    });
  };
  if ('requestIdleCallback' in window) {
    requestIdleCallback(preloadOnIdle, { timeout: 5000 });
  } else {
    setTimeout(preloadOnIdle, 2000);
  }
}
/**
 * Setup hover preloading for navigation links
 */
export function setupHoverPreloading(): void {
  if (typeof window === 'undefined') return;
  // Add event listeners for hover preloading
  document.addEventListener('mouseover', (event) => {
    const target = event.target as HTMLElement;
    const link = target.closest('a[href]') as HTMLAnchorElement;
    if (link && link.href) {
      const url = new URL(link.href);
      const pathname = url.pathname;
      if (ROUTE_PRELOAD_CONFIG.onHover.includes(pathname)) {
        preloadRoute(pathname);
      }
    }
  });
  // Also setup focus preloading for keyboard navigation
  document.addEventListener('focusin', (event) => {
    const target = event.target as HTMLElement;
    if (target.tagName === 'A') {
      const link = target as HTMLAnchorElement;
      const url = new URL(link.href);
      const pathname = url.pathname;
      if (ROUTE_PRELOAD_CONFIG.onHover.includes(pathname)) {
        preloadRoute(pathname);
      }
    }
  });
}
/**
 * Preload components based on user interaction patterns
 */
export function setupIntelligentPreloading(): void {
  if (typeof window === 'undefined') return;
  // Track user navigation patterns
  const navigationHistory: string[] = [];
  // Listen for route changes
  const trackNavigation = () => {
    const currentPath = window.location.pathname;
    navigationHistory.push(currentPath);
    // Keep only last 10 routes
    if (navigationHistory.length > 10) {
      navigationHistory.shift();
    }
    // Predict next likely routes based on patterns
    predictAndPreload(currentPath, navigationHistory);
  };
  // Track initial page load
  trackNavigation();
  // Track navigation changes
  window.addEventListener('popstate', trackNavigation);
  // Track programmatic navigation (for SPA routing)
  const originalPushState = history.pushState;
  history.pushState = function(...args) {
    originalPushState.apply(history, args);
    trackNavigation();
  };
}
/**
 * Predict and preload likely next routes
 */
function predictAndPreload(currentPath: string, history: string[]): void {
  // Simple prediction logic based on common patterns
  const predictions: Record<string, string[]> = {
    '/dashboard': ['/learn', '/code', '/achievements'],
    '/learn': ['/code', '/dashboard'],
    '/code': ['/learn', '/dashboard'],
    '/achievements': ['/dashboard', '/profile'],
    '/profile': ['/settings', '/dashboard'],
  };
  const likelyNext = predictions[currentPath];
  if (likelyNext) {
    likelyNext.forEach(route => {
      // Preload with a small delay to avoid blocking current page
      setTimeout(() => preloadRoute(route), 1000);
    });
  }
}
/**
 * Initialize all preloading strategies
 */
export function initializeRoutePreloading(): void {
  if (typeof window === 'undefined') return;
  // Wait for initial page load
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      preloadCriticalRoutes();
      setupHoverPreloading();
      setupIntelligentPreloading();
      // Preload idle routes after a delay
      setTimeout(preloadIdleRoutes, 3000);
    });
  } else {
    preloadCriticalRoutes();
    setupHoverPreloading();
    setupIntelligentPreloading();
    setTimeout(preloadIdleRoutes, 3000);
  }
}
/**
 * Get preloading statistics
 */
export function getPreloadingStats() {
  return {
    preloadedRoutes: Array.from(preloadedRoutes),
    preloadedComponents: Array.from(preloadedComponents),
    totalPreloaded: preloadedRoutes.size + preloadedComponents.size,
  };
}
export default {
  preloadRoute,
  preloadComponent,
  preloadCriticalRoutes,
  preloadIdleRoutes,
  setupHoverPreloading,
  setupIntelligentPreloading,
  initializeRoutePreloading,
  getPreloadingStats,
};
