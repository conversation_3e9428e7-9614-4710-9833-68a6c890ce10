'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Users, 
  Star, 
  Trophy, 
  TrendingUp, 
  MapPin, 
  Clock,
  CheckCircle,
  Quote,
  Award,
  Zap
} from 'lucide-react';

interface SocialProofItem {
  id: string;
  type: 'numbers' | 'testimonials' | 'activity' | 'achievements';
  content: {
    title: string;
    description: string;
    value?: number;
    unit?: string;
    author?: {
      name: string;
      role?: string;
      avatar?: string;
      location?: string;
    };
    timestamp?: Date;
    rating?: number;
    verified?: boolean;
  };
  priority: number;
  displayDuration: number;
}

interface SocialProofDisplayProps {
  types: ('numbers' | 'testimonials' | 'activity' | 'achievements')[];
  frequency?: number;
  personalizedType?: string;
  position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  maxVisible?: number;
  onInteraction?: (type: string, data: any) => void;
  className?: string;
}

const socialProofData: SocialProofItem[] = [
  // Numbers
  {
    id: 'users_count',
    type: 'numbers',
    content: {
      title: '15,420+ Students',
      description: 'Learning Solidity worldwide',
      value: 15420,
      unit: 'students'
    },
    priority: 10,
    displayDuration: 4000
  },
  {
    id: 'completion_rate',
    type: 'numbers',
    content: {
      title: '94% Success Rate',
      description: 'Students complete their first course',
      value: 94,
      unit: '%'
    },
    priority: 9,
    displayDuration: 4000
  },
  {
    id: 'countries',
    type: 'numbers',
    content: {
      title: '127 Countries',
      description: 'Global learning community',
      value: 127,
      unit: 'countries'
    },
    priority: 8,
    displayDuration: 4000
  },

  // Testimonials
  {
    id: 'testimonial_1',
    type: 'testimonials',
    content: {
      title: 'Amazing course structure!',
      description: 'The hands-on approach helped me land my first blockchain developer job.',
      author: {
        name: 'Sarah Chen',
        role: 'Blockchain Developer',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah',
        location: 'San Francisco'
      },
      rating: 5,
      verified: true
    },
    priority: 9,
    displayDuration: 6000
  },
  {
    id: 'testimonial_2',
    type: 'testimonials',
    content: {
      title: 'Perfect for beginners',
      description: 'Started with zero blockchain knowledge, now building my own DApp!',
      author: {
        name: 'Marcus Johnson',
        role: 'Full-Stack Developer',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Marcus',
        location: 'London'
      },
      rating: 5,
      verified: true
    },
    priority: 8,
    displayDuration: 6000
  },
  {
    id: 'testimonial_3',
    type: 'testimonials',
    content: {
      title: 'Excellent practical examples',
      description: 'The real-world projects gave me confidence to start freelancing.',
      author: {
        name: 'Elena Rodriguez',
        role: 'Freelance Developer',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Elena',
        location: 'Madrid'
      },
      rating: 5,
      verified: true
    },
    priority: 7,
    displayDuration: 6000
  },

  // Activity
  {
    id: 'recent_signup_1',
    type: 'activity',
    content: {
      title: 'Alex from Tokyo just enrolled',
      description: 'Started "Solidity Fundamentals" course',
      timestamp: new Date(Date.now() - 2 * 60 * 1000),
      author: {
        name: 'Alex K.',
        location: 'Tokyo'
      }
    },
    priority: 6,
    displayDuration: 5000
  },
  {
    id: 'recent_completion_1',
    type: 'activity',
    content: {
      title: 'Maria completed a lesson',
      description: 'Just finished "Smart Contract Security"',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      author: {
        name: 'Maria S.',
        location: 'Barcelona'
      }
    },
    priority: 5,
    displayDuration: 5000
  },
  {
    id: 'recent_achievement_1',
    type: 'activity',
    content: {
      title: 'David earned a certificate',
      description: 'Completed "DeFi Development" course',
      timestamp: new Date(Date.now() - 10 * 60 * 1000),
      author: {
        name: 'David L.',
        location: 'Berlin'
      }
    },
    priority: 7,
    displayDuration: 5000
  },

  // Achievements
  {
    id: 'achievement_1',
    type: 'achievements',
    content: {
      title: '500+ Developers Hired',
      description: 'Our graduates work at top tech companies',
      value: 500,
      unit: 'developers hired'
    },
    priority: 8,
    displayDuration: 5000
  },
  {
    id: 'achievement_2',
    type: 'achievements',
    content: {
      title: '4.9/5 Average Rating',
      description: 'Based on 2,500+ student reviews',
      value: 4.9,
      unit: '/5 rating',
      rating: 4.9
    },
    priority: 9,
    displayDuration: 5000
  },
  {
    id: 'achievement_3',
    type: 'achievements',
    content: {
      title: 'Industry Recognition',
      description: 'Featured in TechCrunch and Forbes',
      verified: true
    },
    priority: 7,
    displayDuration: 5000
  }
];

export function SocialProofDisplay({
  types,
  frequency = 8000,
  personalizedType,
  position = 'bottom-left',
  maxVisible = 1,
  onInteraction,
  className
}: SocialProofDisplayProps) {
  const [visibleItems, setVisibleItems] = useState<SocialProofItem[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);

  // Filter items based on enabled types
  const availableItems = socialProofData.filter(item => 
    types.includes(item.type)
  ).sort((a, b) => b.priority - a.priority);

  // Prioritize personalized type
  const prioritizedItems = personalizedType 
    ? [
        ...availableItems.filter(item => item.type === personalizedType),
        ...availableItems.filter(item => item.type !== personalizedType)
      ]
    : availableItems;

  // Show social proof items
  useEffect(() => {
    if (prioritizedItems.length === 0) return;

    const showNextItem = () => {
      const item = prioritizedItems[currentIndex % prioritizedItems.length];
      
      setVisibleItems([item]);
      
      // Track display
      onInteraction?.('display', {
        itemId: item.id,
        type: item.type,
        position
      });

      // Auto-hide after display duration
      setTimeout(() => {
        setVisibleItems([]);
      }, item.displayDuration);

      setCurrentIndex(prev => prev + 1);
    };

    // Initial delay
    const initialTimeout = setTimeout(showNextItem, 2000);

    // Regular interval
    const interval = setInterval(showNextItem, frequency);

    return () => {
      clearTimeout(initialTimeout);
      clearInterval(interval);
    };
  }, [prioritizedItems, currentIndex, frequency, onInteraction, position]);

  const handleItemClick = (item: SocialProofItem) => {
    onInteraction?.('click', {
      itemId: item.id,
      type: item.type,
      content: item.content
    });

    // Handle different click actions based on type
    switch (item.type) {
      case 'testimonials':
        // Could open testimonials page or modal
        break;
      case 'numbers':
        // Could show detailed statistics
        break;
      case 'activity':
        // Could show live activity feed
        break;
      case 'achievements':
        // Could show achievements page
        break;
    }
  };

  const handleDismiss = (itemId: string) => {
    setVisibleItems(prev => prev.filter(item => item.id !== itemId));
    
    onInteraction?.('dismiss', {
      itemId,
      position
    });
  };

  const positionClasses = {
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4'
  };

  return (
    <div
      className={cn(
        'fixed z-40 pointer-events-none',
        positionClasses[position],
        className
      )}
      role="region"
      aria-label="Social proof notifications"
      aria-live="polite"
    >
      <AnimatePresence mode="popLayout">
        {visibleItems.slice(0, maxVisible).map((item, index) => (
          <motion.div
            key={item.id}
            initial={{ 
              opacity: 0, 
              scale: 0.8,
              x: position.includes('right') ? 100 : -100,
              y: position.includes('bottom') ? 20 : -20
            }}
            animate={{ 
              opacity: 1, 
              scale: 1,
              x: 0,
              y: 0
            }}
            exit={{ 
              opacity: 0, 
              scale: 0.8,
              x: position.includes('right') ? 100 : -100,
              transition: { duration: 0.2 }
            }}
            transition={{ 
              type: "spring", 
              stiffness: 300, 
              damping: 30,
              delay: index * 0.1
            }}
            layout
            className="mb-3 pointer-events-auto"
          >
            <SocialProofCard
              item={item}
              onClick={() => handleItemClick(item)}
              onDismiss={() => handleDismiss(item.id)}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

interface SocialProofCardProps {
  item: SocialProofItem;
  onClick: () => void;
  onDismiss: () => void;
}

function SocialProofCard({ item, onClick, onDismiss }: SocialProofCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const getTypeIcon = (type: SocialProofItem['type']) => {
    switch (type) {
      case 'numbers':
        return <TrendingUp className="w-4 h-4" />;
      case 'testimonials':
        return <Quote className="w-4 h-4" />;
      case 'activity':
        return <Zap className="w-4 h-4" />;
      case 'achievements':
        return <Award className="w-4 h-4" />;
      default:
        return <CheckCircle className="w-4 h-4" />;
    }
  };

  const getTypeColor = (type: SocialProofItem['type']) => {
    switch (type) {
      case 'numbers':
        return 'text-blue-500';
      case 'testimonials':
        return 'text-green-500';
      case 'activity':
        return 'text-purple-500';
      case 'achievements':
        return 'text-yellow-500';
      default:
        return 'text-gray-500';
    }
  };

  return (
    <motion.div
      className={cn(
        'relative w-80 p-4 rounded-lg border backdrop-blur-sm shadow-lg',
        'bg-white/95 dark:bg-gray-900/95 border-gray-200/50 dark:border-gray-700/50',
        'cursor-pointer transition-all duration-200',
        isHovered && 'scale-105 shadow-xl'
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onClick();
        }
      }}
      aria-label={`Social proof: ${item.content.title}. Click for more details.`}
    >
      {/* Close button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          onDismiss();
        }}
        className="absolute top-2 right-2 p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
        aria-label="Dismiss notification"
      >
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>

      <div className="flex items-start space-x-3">
        {/* Icon or Avatar */}
        <div className="flex-shrink-0">
          {item.content.author?.avatar ? (
            <img
              src={item.content.author.avatar}
              alt={`${item.content.author.name}'s avatar`}
              className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700"
            />
          ) : (
            <div className={cn(
              'w-10 h-10 rounded-full flex items-center justify-center',
              'bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800'
            )}>
              <div className={getTypeColor(item.type)}>
                {getTypeIcon(item.type)}
              </div>
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
              {item.content.title}
            </h4>
            {item.content.verified && (
              <CheckCircle className="w-4 h-4 text-blue-500 flex-shrink-0" />
            )}
          </div>
          
          <p className="text-xs text-gray-600 dark:text-gray-300 mb-2">
            {item.content.description}
          </p>

          {/* Rating */}
          {item.content.rating && (
            <div className="flex items-center space-x-1 mb-2">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={cn(
                    'w-3 h-3',
                    i < Math.floor(item.content.rating!) 
                      ? 'text-yellow-400 fill-current' 
                      : 'text-gray-300'
                  )}
                />
              ))}
              <span className="text-xs text-gray-500 ml-1">
                {item.content.rating}/5
              </span>
            </div>
          )}

          {/* Author info */}
          {item.content.author && (
            <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
              <span className="font-medium">{item.content.author.name}</span>
              {item.content.author.role && (
                <>
                  <span>•</span>
                  <span>{item.content.author.role}</span>
                </>
              )}
              {item.content.author.location && (
                <>
                  <MapPin className="w-3 h-3" />
                  <span>{item.content.author.location}</span>
                </>
              )}
            </div>
          )}

          {/* Timestamp */}
          {item.content.timestamp && (
            <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400 mt-1">
              <Clock className="w-3 h-3" />
              <span>{getRelativeTime(item.content.timestamp)}</span>
            </div>
          )}
        </div>
      </div>

      {/* Progress bar for auto-hide */}
      <motion.div
        className="absolute bottom-0 left-0 h-0.5 bg-current opacity-30"
        initial={{ width: '100%' }}
        animate={{ width: '0%' }}
        transition={{ duration: item.displayDuration / 1000, ease: 'linear' }}
      />
    </motion.div>
  );
}

function getRelativeTime(timestamp: Date): string {
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - timestamp.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) return 'just now';
  if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
  
  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) return `${diffInHours}h ago`;
  
  const diffInDays = Math.floor(diffInHours / 24);
  return `${diffInDays}d ago`;
}

export default SocialProofDisplay;
