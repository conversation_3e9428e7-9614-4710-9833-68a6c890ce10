import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/config';
import { prisma } from '@/lib/prisma';
// Configure for dynamic API routes
export const dynamic = 'force-dynamic';
export async function POST(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    const { messageId, sessionId } = await request.json();
    if (!messageId || !sessionId) {
      return NextResponse.json({ error: 'Message ID and session ID are required' }, { status: 400 });
    }
    // Check if user is participant in the session
    const collaboration = await prisma.collaboration.findFirst({
      where: {
        id: sessionId,
        participants: {
          some: {
            id: session.user.id,
          },
        },
      },
    });
    if (!collaboration) {
      return NextResponse.json({ error: 'Not authorized for this session' }, { status: 403 });
    }
    // In a real implementation, you'd have a Message model with isPinned field
    // const message = await prisma.message.findUnique({
    //   where: { id: messageId },
    // });
    // if (!message) {
    //   return NextResponse.json({ error: 'Message not found' }, { status: 404 });
    // }
    // await prisma.message.update({
    //   where: { id: messageId },
    //   data: {
    //     isPinned: !message.isPinned,
    //     pinnedBy: message.isPinned ? null : session.user.id,
    //     pinnedAt: message.isPinned ? null : new Date(),
    //   },
    // });
    return NextResponse.json({ 
      success: true, 
      message: 'Message pin status updated successfully' 
    });
  } catch (error) {
    console.error('Error updating message pin status:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
