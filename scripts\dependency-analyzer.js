#!/usr/bin/env node

/**
 * Comprehensive Dependency Analyzer
 * Analyzes dependencies for unused packages, security issues, and optimization opportunities
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DependencyAnalyzer {
  constructor() {
    this.packageJson = this.loadPackageJson();
    this.analysisResults = {
      unused: [],
      outdated: [],
      security: [],
      heavy: [],
      duplicates: [],
      recommendations: []
    };
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
  }

  loadPackageJson() {
    try {
      return JSON.parse(fs.readFileSync('package.json', 'utf8'));
    } catch (error) {
      this.log('Failed to load package.json', 'error');
      throw error;
    }
  }

  findUnusedDependencies() {
    this.log('Analyzing unused dependencies...');
    
    const dependencies = Object.keys(this.packageJson.dependencies || {});
    const devDependencies = Object.keys(this.packageJson.devDependencies || {});
    const allDependencies = [...dependencies, ...devDependencies];
    
    const usedDependencies = new Set();
    
    // Find all import/require statements
    const findImports = (dir) => {
      if (!fs.existsSync(dir)) return;
      
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const itemPath = path.join(dir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          findImports(itemPath);
        } else if (stat.isFile() && /\.(ts|tsx|js|jsx|json)$/.test(item)) {
          try {
            const content = fs.readFileSync(itemPath, 'utf8');
            
            // Find import statements
            const importMatches = content.match(/(?:import|require)\s*\(?['"`]([^'"`]+)['"`]\)?/g) || [];
            
            importMatches.forEach(match => {
              const moduleMatch = match.match(/['"`]([^'"`]+)['"`]/);
              if (moduleMatch) {
                const moduleName = moduleMatch[1];
                
                // Extract package name (handle scoped packages)
                let packageName = moduleName;
                if (moduleName.startsWith('@')) {
                  const parts = moduleName.split('/');
                  packageName = parts.slice(0, 2).join('/');
                } else {
                  packageName = moduleName.split('/')[0];
                }
                
                if (allDependencies.includes(packageName)) {
                  usedDependencies.add(packageName);
                }
              }
            });
          } catch (error) {
            // Skip files that can't be read
          }
        }
      }
    };

    // Scan source directories
    ['components', 'app', 'lib', 'hooks', 'utils', 'pages', 'src'].forEach(dir => {
      findImports(dir);
    });

    // Check configuration files for dependencies
    const configFiles = [
      'next.config.js',
      'tailwind.config.js',
      'postcss.config.js',
      '.eslintrc.js',
      '.eslintrc.json',
      'jest.config.js'
    ];

    configFiles.forEach(file => {
      if (fs.existsSync(file)) {
        try {
          const content = fs.readFileSync(file, 'utf8');
          const requireMatches = content.match(/require\(['"`]([^'"`]+)['"`]\)/g) || [];
          
          requireMatches.forEach(match => {
            const moduleMatch = match.match(/['"`]([^'"`]+)['"`]/);
            if (moduleMatch) {
              const packageName = moduleMatch[1].split('/')[0];
              if (allDependencies.includes(packageName)) {
                usedDependencies.add(packageName);
              }
            }
          });
        } catch (error) {
          // Skip files that can't be read
        }
      }
    });

    // Always consider these as used (they might be used in ways we can't detect)
    const alwaysUsed = [
      'next',
      'react',
      'react-dom',
      '@types/react',
      '@types/react-dom',
      '@types/node',
      'typescript',
      'eslint',
      'tailwindcss',
      'postcss',
      'autoprefixer'
    ];

    alwaysUsed.forEach(pkg => {
      if (allDependencies.includes(pkg)) {
        usedDependencies.add(pkg);
      }
    });

    // Find unused dependencies
    const unused = allDependencies.filter(dep => !usedDependencies.has(dep));
    
    this.analysisResults.unused = unused.map(dep => ({
      name: dep,
      type: dependencies.includes(dep) ? 'dependency' : 'devDependency',
      version: this.packageJson.dependencies?.[dep] || this.packageJson.devDependencies?.[dep]
    }));

    this.log(`Found ${unused.length} potentially unused dependencies`);
  }

  analyzeHeavyDependencies() {
    this.log('Analyzing heavy dependencies...');
    
    const heavyPackages = {
      'lodash': { size: '70KB', alternative: 'lodash-es or individual functions' },
      'moment': { size: '67KB', alternative: 'date-fns or dayjs' },
      'three': { size: '600KB', alternative: 'Use dynamic imports' },
      'chart.js': { size: '200KB', alternative: 'recharts or lightweight alternatives' },
      'd3': { size: '250KB', alternative: 'Import only needed modules' },
      '@monaco-editor/react': { size: '1.5MB', alternative: 'Already optimized with code splitting' },
      'rxjs': { size: '200KB', alternative: 'Import operators individually' },
      'antd': { size: '1MB', alternative: 'Import components individually' },
      '@mui/material': { size: '300KB', alternative: 'Import components individually' },
      'framer-motion': { size: '100KB', alternative: 'Consider lighter animation libraries' }
    };

    const dependencies = Object.keys(this.packageJson.dependencies || {});
    
    this.analysisResults.heavy = dependencies
      .filter(dep => heavyPackages[dep])
      .map(dep => ({
        name: dep,
        version: this.packageJson.dependencies[dep],
        estimatedSize: heavyPackages[dep].size,
        alternative: heavyPackages[dep].alternative
      }));

    this.log(`Found ${this.analysisResults.heavy.length} heavy dependencies`);
  }

  checkForDuplicates() {
    this.log('Checking for duplicate functionality...');
    
    const duplicateGroups = [
      {
        category: 'Date Libraries',
        packages: ['moment', 'date-fns', 'dayjs'],
        recommendation: 'Choose one date library and stick with it'
      },
      {
        category: 'Utility Libraries',
        packages: ['lodash', 'ramda', 'underscore'],
        recommendation: 'Use one utility library or native JavaScript'
      },
      {
        category: 'HTTP Clients',
        packages: ['axios', 'fetch', 'node-fetch', 'isomorphic-fetch'],
        recommendation: 'Use native fetch or one HTTP client'
      },
      {
        category: 'CSS-in-JS',
        packages: ['styled-components', 'emotion', '@emotion/react', '@emotion/styled'],
        recommendation: 'Use one CSS-in-JS solution'
      },
      {
        category: 'State Management',
        packages: ['redux', 'zustand', 'jotai', 'recoil'],
        recommendation: 'Use one state management solution'
      }
    ];

    const dependencies = Object.keys(this.packageJson.dependencies || {});
    
    duplicateGroups.forEach(group => {
      const foundPackages = group.packages.filter(pkg => dependencies.includes(pkg));
      if (foundPackages.length > 1) {
        this.analysisResults.duplicates.push({
          category: group.category,
          packages: foundPackages,
          recommendation: group.recommendation
        });
      }
    });

    this.log(`Found ${this.analysisResults.duplicates.length} potential duplicate categories`);
  }

  checkSecurity() {
    this.log('Checking for security vulnerabilities...');
    
    try {
      // Run npm audit
      const auditResult = execSync('npm audit --json', { encoding: 'utf8' });
      const audit = JSON.parse(auditResult);
      
      if (audit.vulnerabilities) {
        Object.entries(audit.vulnerabilities).forEach(([pkg, vuln]) => {
          this.analysisResults.security.push({
            package: pkg,
            severity: vuln.severity,
            title: vuln.title,
            url: vuln.url,
            fixAvailable: vuln.fixAvailable
          });
        });
      }
    } catch (error) {
      this.log('Security audit failed or no vulnerabilities found', 'warn');
    }

    this.log(`Found ${this.analysisResults.security.length} security issues`);
  }

  checkOutdated() {
    this.log('Checking for outdated dependencies...');
    
    try {
      const outdatedResult = execSync('npm outdated --json', { encoding: 'utf8' });
      const outdated = JSON.parse(outdatedResult);
      
      Object.entries(outdated).forEach(([pkg, info]) => {
        this.analysisResults.outdated.push({
          package: pkg,
          current: info.current,
          wanted: info.wanted,
          latest: info.latest,
          type: info.type
        });
      });
    } catch (error) {
      // npm outdated returns non-zero exit code when packages are outdated
      if (error.stdout) {
        try {
          const outdated = JSON.parse(error.stdout);
          Object.entries(outdated).forEach(([pkg, info]) => {
            this.analysisResults.outdated.push({
              package: pkg,
              current: info.current,
              wanted: info.wanted,
              latest: info.latest,
              type: info.type
            });
          });
        } catch (parseError) {
          this.log('Failed to parse outdated packages', 'warn');
        }
      }
    }

    this.log(`Found ${this.analysisResults.outdated.length} outdated dependencies`);
  }

  generateRecommendations() {
    this.log('Generating recommendations...');
    
    const recommendations = [];

    // Unused dependencies
    if (this.analysisResults.unused.length > 0) {
      recommendations.push({
        category: 'Cleanup',
        priority: 'high',
        title: 'Remove Unused Dependencies',
        description: `Remove ${this.analysisResults.unused.length} unused dependencies to reduce bundle size`,
        action: `npm uninstall ${this.analysisResults.unused.map(dep => dep.name).join(' ')}`,
        impact: 'Reduced bundle size and faster installs'
      });
    }

    // Heavy dependencies
    if (this.analysisResults.heavy.length > 0) {
      recommendations.push({
        category: 'Optimization',
        priority: 'medium',
        title: 'Optimize Heavy Dependencies',
        description: 'Replace or optimize heavy dependencies for better performance',
        actions: this.analysisResults.heavy.map(dep => 
          `Consider ${dep.alternative} for ${dep.name} (${dep.estimatedSize})`
        ),
        impact: 'Significantly reduced bundle size'
      });
    }

    // Security issues
    if (this.analysisResults.security.length > 0) {
      const criticalCount = this.analysisResults.security.filter(s => s.severity === 'critical').length;
      recommendations.push({
        category: 'Security',
        priority: criticalCount > 0 ? 'critical' : 'high',
        title: 'Fix Security Vulnerabilities',
        description: `Fix ${this.analysisResults.security.length} security issues`,
        action: 'npm audit fix',
        impact: 'Improved security posture'
      });
    }

    // Outdated packages
    if (this.analysisResults.outdated.length > 0) {
      recommendations.push({
        category: 'Maintenance',
        priority: 'low',
        title: 'Update Outdated Dependencies',
        description: `Update ${this.analysisResults.outdated.length} outdated packages`,
        action: 'npm update',
        impact: 'Latest features and bug fixes'
      });
    }

    // Duplicates
    if (this.analysisResults.duplicates.length > 0) {
      recommendations.push({
        category: 'Architecture',
        priority: 'medium',
        title: 'Resolve Duplicate Dependencies',
        description: 'Consolidate duplicate functionality',
        actions: this.analysisResults.duplicates.map(dup => dup.recommendation),
        impact: 'Reduced bundle size and complexity'
      });
    }

    this.analysisResults.recommendations = recommendations;
  }

  generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalDependencies: Object.keys(this.packageJson.dependencies || {}).length,
        totalDevDependencies: Object.keys(this.packageJson.devDependencies || {}).length,
        unusedCount: this.analysisResults.unused.length,
        heavyCount: this.analysisResults.heavy.length,
        securityIssues: this.analysisResults.security.length,
        outdatedCount: this.analysisResults.outdated.length,
        duplicateCategories: this.analysisResults.duplicates.length
      },
      analysis: this.analysisResults,
      estimatedSavings: this.calculateEstimatedSavings()
    };

    const reportPath = `dependency-analysis-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    return { report, reportPath };
  }

  calculateEstimatedSavings() {
    let estimatedKB = 0;
    
    // Estimate savings from removing unused dependencies
    estimatedKB += this.analysisResults.unused.length * 50; // Average 50KB per unused package
    
    // Estimate savings from optimizing heavy dependencies
    this.analysisResults.heavy.forEach(dep => {
      const sizeMatch = dep.estimatedSize.match(/(\d+)/);
      if (sizeMatch) {
        estimatedKB += parseInt(sizeMatch[1]) * 0.3; // Assume 30% savings from optimization
      }
    });

    return {
      estimatedKB: Math.round(estimatedKB),
      estimatedMB: Math.round(estimatedKB / 1024 * 100) / 100
    };
  }

  displaySummary(report) {
    console.log('\n=== DEPENDENCY ANALYSIS REPORT ===');
    console.log(`Total dependencies: ${report.summary.totalDependencies}`);
    console.log(`Total dev dependencies: ${report.summary.totalDevDependencies}`);
    console.log(`Unused dependencies: ${report.summary.unusedCount}`);
    console.log(`Heavy dependencies: ${report.summary.heavyCount}`);
    console.log(`Security issues: ${report.summary.securityIssues}`);
    console.log(`Outdated packages: ${report.summary.outdatedCount}`);
    console.log(`Duplicate categories: ${report.summary.duplicateCategories}`);
    
    console.log(`\nEstimated potential savings: ${report.estimatedSavings.estimatedMB} MB`);
    
    console.log('\nTop Recommendations:');
    report.analysis.recommendations.slice(0, 3).forEach((rec, index) => {
      console.log(`  ${index + 1}. [${rec.priority.toUpperCase()}] ${rec.title}`);
      console.log(`     ${rec.description}`);
    });
    
    if (report.summary.unusedCount > 0) {
      console.log('\nUnused Dependencies:');
      report.analysis.unused.slice(0, 5).forEach(dep => {
        console.log(`  - ${dep.name} (${dep.type})`);
      });
      if (report.analysis.unused.length > 5) {
        console.log(`  ... and ${report.analysis.unused.length - 5} more`);
      }
    }
  }

  run() {
    this.log('Starting comprehensive dependency analysis...');
    
    // Run all analyses
    this.findUnusedDependencies();
    this.analyzeHeavyDependencies();
    this.checkForDuplicates();
    this.checkSecurity();
    this.checkOutdated();
    this.generateRecommendations();
    
    // Generate and display report
    const { report, reportPath } = this.generateReport();
    this.displaySummary(report);
    
    console.log(`\nDetailed report saved to: ${reportPath}`);
    
    this.log('Dependency analysis completed successfully');
  }
}

// Run the analyzer
if (require.main === module) {
  const analyzer = new DependencyAnalyzer();
  analyzer.run();
}

module.exports = DependencyAnalyzer;
