'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Play,
  CheckCircle,
  ArrowRight,
  Code,
  Zap,
  Gift,
  Users,
  Star,
  Clock,
  Shield,
  Github,
  Mail,
  Chrome,
  Linkedin,
  Eye,
  EyeOff,
  Loader2,
  Trophy,
  Target,
  BookOpen
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import { useProgressTracking } from '@/components/progress/ProgressTrackingSystem';

interface TrialStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  estimatedTime: string;
  isCompleted: boolean;
  isActive: boolean;
  isLocked: boolean;
  value: string;
  benefits: string[];
}

interface OnboardingFlow {
  currentStep: number;
  totalSteps: number;
  completionPercentage: number;
  timeSpent: number;
  userEngagement: number;
  conversionTouchpoints: Array<{
    step: number;
    type: 'value_demonstration' | 'social_proof' | 'urgency' | 'trial_extension';
    message: string;
    cta: string;
  }>;
}

interface SocialLoginProvider {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  isPopular: boolean;
  conversionRate: number;
}

interface TrialAnalytics {
  signupSources: Record<string, number>;
  stepCompletionRates: Record<string, number>;
  dropOffPoints: Record<string, number>;
  timeToValue: number[];
  conversionTouchpointEffectiveness: Record<string, { views: number; conversions: number; rate: number }>;
  trialExtensionRequests: number;
  trialToPaidConversions: number;
}

export function TrialOnboardingSystem({
  className,
  onConversion,
  onAnalyticsUpdate
}: {
  className?: string;
  onConversion?: (step: string, action: string) => void;
  onAnalyticsUpdate?: (analytics: TrialAnalytics) => void;
}) {
  const [currentFlow, setCurrentFlow] = useState<OnboardingFlow>({
    currentStep: 0,
    totalSteps: 5,
    completionPercentage: 0,
    timeSpent: 0,
    userEngagement: 0,
    conversionTouchpoints: []
  });

  const [isSignupModalOpen, setIsSignupModalOpen] = useState(false);
  const [signupMethod, setSignupMethod] = useState<'social' | 'email' | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    experience: '',
    goals: [] as string[]
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [trialProgress, setTrialProgress] = useState(0);
  const [immediateValue, setImmediateValue] = useState<string[]>([]);

  const { userProgress } = useProgressTracking();
  const startTimeRef = useRef(Date.now());

  // Social login providers
  const socialProviders: SocialLoginProvider[] = [
    {
      id: 'github',
      name: 'GitHub',
      icon: Github,
      color: 'bg-gray-800 hover:bg-gray-700 text-white',
      isPopular: true,
      conversionRate: 78
    },
    {
      id: 'google',
      name: 'Google',
      icon: Chrome,
      color: 'bg-white hover:bg-gray-50 text-gray-900 border border-gray-300',
      isPopular: true,
      conversionRate: 82
    },
    {
      id: 'linkedin',
      name: 'LinkedIn',
      icon: Linkedin,
      color: 'bg-blue-600 hover:bg-blue-700 text-white',
      isPopular: false,
      conversionRate: 71
    }
  ];

  // Trial steps with immediate value
  const trialSteps: TrialStep[] = [
    {
      id: 'instant-access',
      title: 'Instant Platform Access',
      description: 'Get immediate access to our interactive code editor and first 3 lessons',
      icon: Zap,
      estimatedTime: '0 minutes',
      isCompleted: false,
      isActive: true,
      isLocked: false,
      value: 'Immediate',
      benefits: ['No waiting period', 'Full editor access', '3 complete lessons']
    },
    {
      id: 'skill-assessment',
      title: 'Personalized Learning Path',
      description: 'Take our 5-minute skill assessment to get a customized curriculum',
      icon: Target,
      estimatedTime: '5 minutes',
      isCompleted: false,
      isActive: false,
      isLocked: false,
      value: 'Personalized',
      benefits: ['Custom learning path', 'Skip basics if experienced', 'Optimized timeline']
    },
    {
      id: 'first-contract',
      title: 'Deploy Your First Smart Contract',
      description: 'Write and deploy a real smart contract to the blockchain testnet',
      icon: Code,
      estimatedTime: '15 minutes',
      isCompleted: false,
      isActive: false,
      isLocked: false,
      value: 'Hands-on',
      benefits: ['Real blockchain deployment', 'Testnet interaction', 'Portfolio project']
    },
    {
      id: 'community-access',
      title: 'Join Developer Community',
      description: 'Connect with 12,000+ blockchain developers in our Discord community',
      icon: Users,
      estimatedTime: '2 minutes',
      isCompleted: false,
      isActive: false,
      isLocked: false,
      value: 'Network',
      benefits: ['Expert mentorship', 'Peer support', 'Job opportunities']
    },
    {
      id: 'trial-extension',
      title: 'Extend Your Trial',
      description: 'Based on your progress, unlock 7 additional days of premium access',
      icon: Gift,
      estimatedTime: '1 minute',
      isCompleted: false,
      isActive: false,
      isLocked: true,
      value: 'Extended',
      benefits: ['7 extra days', 'Full premium features', 'No credit card required']
    }
  ];

  // Initialize onboarding flow
  useEffect(() => {
    const conversionTouchpoints = [
      {
        step: 1,
        type: 'value_demonstration' as const,
        message: 'See how our interactive editor makes learning Solidity 3x faster',
        cta: 'Try Interactive Editor'
      },
      {
        step: 2,
        type: 'social_proof' as const,
        message: '2,156 developers completed their first contract this week',
        cta: 'Join Successful Developers'
      },
      {
        step: 3,
        type: 'urgency' as const,
        message: 'Limited spots in this month\'s mentorship program',
        cta: 'Secure Your Spot'
      },
      {
        step: 4,
        type: 'trial_extension' as const,
        message: 'You\'re making great progress! Extend your trial by 7 days',
        cta: 'Extend Free Trial'
      }
    ];

    setCurrentFlow(prev => ({
      ...prev,
      conversionTouchpoints
    }));
  }, []);

  // Track time spent and engagement
  useEffect(() => {
    const interval = setInterval(() => {
      const timeSpent = Date.now() - startTimeRef.current;
      const engagementScore = Math.min(100,
        (timeSpent / 1000) * 0.1 + // Time factor
        trialProgress * 0.5 + // Progress factor
        immediateValue.length * 10 // Value realization factor
      );

      setCurrentFlow(prev => ({
        ...prev,
        timeSpent,
        userEngagement: engagementScore,
        completionPercentage: (trialProgress / trialSteps.length) * 100
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, [trialProgress, immediateValue.length]);

  // Handle social login
  const handleSocialLogin = useCallback(async (providerId: string) => {
    setIsLoading(true);

    try {
      // Simulate social login process
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Track conversion
      onConversion?.('social_signup', providerId);

      // Immediately provide value
      setImmediateValue(['editor_access', 'lesson_access', 'community_invite']);
      setTrialProgress(1);
      setIsSignupModalOpen(false);

      // Track with analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'trial_signup', {
          method: providerId,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Social login failed:', error);
    } finally {
      setIsLoading(false);
    }
  }, [onConversion]);

  // Handle email signup
  const handleEmailSignup = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Validate form
      if (!formData.email || !formData.password) {
        throw new Error('Email and password are required');
      }

      // Simulate signup process
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Track conversion
      onConversion?.('email_signup', 'manual');

      // Immediately provide value
      setImmediateValue(['editor_access', 'lesson_access', 'personalized_path']);
      setTrialProgress(1);
      setIsSignupModalOpen(false);

      // Track with analytics
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'trial_signup', {
          method: 'email',
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Email signup failed:', error);
    } finally {
      setIsLoading(false);
    }
  }, [formData, onConversion]);

  // Handle step completion
  const handleStepCompletion = useCallback((stepId: string) => {
    setTrialProgress(prev => prev + 1);
    setImmediateValue(prev => [...prev, stepId]);

    // Track step completion
    onConversion?.(stepId, 'completed');

    // Check for trial extension eligibility
    if (trialProgress >= 3 && currentFlow.userEngagement > 70) {
      // Unlock trial extension step
      const updatedSteps = trialSteps.map(step =>
        step.id === 'trial-extension' ? { ...step, isLocked: false } : step
      );
    }
  }, [trialProgress, currentFlow.userEngagement, onConversion]);

  // Handle trial extension
  const handleTrialExtension = useCallback(() => {
    onConversion?.('trial_extension', 'requested');

    // Show success message and extend trial
    setImmediateValue(prev => [...prev, 'trial_extended']);

    // Track with analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'trial_extension', {
        engagement_score: currentFlow.userEngagement,
        steps_completed: trialProgress,
        timestamp: new Date().toISOString()
      });
    }
  }, [currentFlow.userEngagement, trialProgress, onConversion]);

  return (
    <div className={cn('space-y-8', className)}>
      {/* Hero CTA Section */}
      <motion.div
        className="text-center bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg p-8 border border-blue-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <h2 className="text-3xl font-bold text-white mb-4">
          Start Learning Solidity Today
        </h2>
        <p className="text-gray-300 text-lg mb-6 max-w-2xl mx-auto">
          Get instant access to our interactive platform. No credit card required.
          Deploy your first smart contract in under 15 minutes.
        </p>

        {/* Value Propositions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          <div className="bg-white/5 rounded-lg p-4">
            <Zap className="w-8 h-8 text-blue-400 mx-auto mb-2" />
            <h3 className="font-semibold text-white mb-1">Instant Access</h3>
            <p className="text-gray-300 text-sm">Start coding immediately with our browser-based editor</p>
          </div>
          <div className="bg-white/5 rounded-lg p-4">
            <Code className="w-8 h-8 text-green-400 mx-auto mb-2" />
            <h3 className="font-semibold text-white mb-1">Real Deployment</h3>
            <p className="text-gray-300 text-sm">Deploy actual smart contracts to blockchain testnets</p>
          </div>
          <div className="bg-white/5 rounded-lg p-4">
            <Users className="w-8 h-8 text-purple-400 mx-auto mb-2" />
            <h3 className="font-semibold text-white mb-1">Expert Support</h3>
            <p className="text-gray-300 text-sm">Join 12,000+ developers in our community</p>
          </div>
        </div>

        {/* Primary CTA */}
        <EnhancedButton
          onClick={() => setIsSignupModalOpen(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white text-lg px-8 py-4 mb-4"
          touchTarget
        >
          <Play className="w-5 h-5 mr-2" />
          Start Free Trial
        </EnhancedButton>

        <div className="text-sm text-gray-400">
          ✓ No credit card required ✓ Instant access ✓ 7-day free trial
        </div>
      </motion.div>

      {/* Trial Progress Indicator */}
      {trialProgress > 0 && (
        <motion.div
          className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Your Trial Progress</h3>
            <div className="text-blue-400 font-medium">
              {Math.round(currentFlow.completionPercentage)}% Complete
            </div>
          </div>

          <div className="w-full bg-gray-700 rounded-full h-2 mb-4">
            <motion.div
              className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${currentFlow.completionPercentage}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3">
            {trialSteps.map((step, index) => (
              <div
                key={step.id}
                className={cn(
                  'p-3 rounded-lg border transition-all duration-200',
                  immediateValue.includes(step.id)
                    ? 'bg-green-500/20 border-green-500/50'
                    : index <= trialProgress
                      ? 'bg-blue-500/20 border-blue-500/50'
                      : 'bg-gray-500/20 border-gray-500/50'
                )}
              >
                <div className="flex items-center space-x-2 mb-2">
                  <step.icon className={cn(
                    'w-4 h-4',
                    immediateValue.includes(step.id) ? 'text-green-400' :
                    index <= trialProgress ? 'text-blue-400' : 'text-gray-400'
                  )} />
                  <span className={cn(
                    'text-xs font-medium',
                    immediateValue.includes(step.id) ? 'text-green-400' :
                    index <= trialProgress ? 'text-blue-400' : 'text-gray-400'
                  )}>
                    {step.value}
                  </span>
                </div>
                <h4 className="font-medium text-white text-sm mb-1">{step.title}</h4>
                <p className="text-gray-300 text-xs">{step.description}</p>

                {immediateValue.includes(step.id) && (
                  <div className="mt-2 flex items-center space-x-1">
                    <CheckCircle className="w-3 h-3 text-green-400" />
                    <span className="text-green-400 text-xs">Unlocked</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Immediate Value Showcase */}
      {immediateValue.length > 0 && (
        <motion.div
          className="bg-gradient-to-r from-green-600/20 to-blue-600/20 rounded-lg border border-green-500/30 p-6"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center space-x-2">
            <Trophy className="w-5 h-5 text-yellow-400" />
            <span>You've Unlocked Premium Features!</span>
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {immediateValue.includes('editor_access') && (
              <div className="bg-white/10 rounded-lg p-4">
                <Code className="w-6 h-6 text-blue-400 mb-2" />
                <h4 className="font-medium text-white mb-1">Interactive Code Editor</h4>
                <p className="text-gray-300 text-sm">Full-featured Solidity IDE with syntax highlighting</p>
              </div>
            )}

            {immediateValue.includes('lesson_access') && (
              <div className="bg-white/10 rounded-lg p-4">
                <BookOpen className="w-6 h-6 text-green-400 mb-2" />
                <h4 className="font-medium text-white mb-1">Premium Lessons</h4>
                <p className="text-gray-300 text-sm">Access to first 3 comprehensive Solidity lessons</p>
              </div>
            )}

            {immediateValue.includes('community_invite') && (
              <div className="bg-white/10 rounded-lg p-4">
                <Users className="w-6 h-6 text-purple-400 mb-2" />
                <h4 className="font-medium text-white mb-1">Developer Community</h4>
                <p className="text-gray-300 text-sm">Join 12,000+ blockchain developers</p>
              </div>
            )}
          </div>

          {trialProgress >= 3 && !immediateValue.includes('trial_extended') && (
            <div className="mt-6 text-center">
              <EnhancedButton
                onClick={handleTrialExtension}
                className="bg-yellow-600 hover:bg-yellow-700 text-white"
                touchTarget
              >
                <Gift className="w-4 h-4 mr-2" />
                Extend Trial by 7 Days
              </EnhancedButton>
              <p className="text-yellow-300 text-sm mt-2">
                Great progress! Unlock 7 additional days of premium access
              </p>
            </div>
          )}
        </motion.div>
      )}

      {/* Signup Modal */}
      <AnimatePresence>
        {isSignupModalOpen && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsSignupModalOpen(false)}
          >
            <motion.div
              className="bg-gray-900 rounded-lg shadow-2xl max-w-md w-full border border-gray-700"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-6">
                <h3 className="text-xl font-semibold text-white mb-6 text-center">
                  Start Your Free Trial
                </h3>

                {/* Social Login Options */}
                <div className="space-y-3 mb-6">
                  {socialProviders.map(provider => {
                    const Icon = provider.icon;
                    return (
                      <button
                        key={provider.id}
                        onClick={() => handleSocialLogin(provider.id)}
                        disabled={isLoading}
                        className={cn(
                          'w-full flex items-center justify-center space-x-3 py-3 px-4 rounded-lg font-medium transition-all duration-200 disabled:opacity-50',
                          provider.color
                        )}
                      >
                        {isLoading ? (
                          <Loader2 className="w-5 h-5 animate-spin" />
                        ) : (
                          <>
                            <Icon className="w-5 h-5" />
                            <span>Continue with {provider.name}</span>
                            {provider.isPopular && (
                              <span className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
                                Popular
                              </span>
                            )}
                          </>
                        )}
                      </button>
                    );
                  })}
                </div>

                {/* Divider */}
                <div className="relative mb-6">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-600"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-2 bg-gray-900 text-gray-400">or continue with email</span>
                  </div>
                </div>

                {/* Email Form */}
                <form onSubmit={handleEmailSignup} className="space-y-4">
                  <div>
                    <input
                      type="email"
                      placeholder="Email address"
                      value={formData.email}
                      onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                  </div>

                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Password"
                      value={formData.password}
                      onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                    >
                      {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                    </button>
                  </div>

                  <EnhancedButton
                    type="submit"
                    disabled={isLoading}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3"
                    touchTarget
                  >
                    {isLoading ? (
                      <Loader2 className="w-5 h-5 animate-spin mr-2" />
                    ) : (
                      <Play className="w-5 h-5 mr-2" />
                    )}
                    Start Free Trial
                  </EnhancedButton>
                </form>

                {/* Trust Indicators */}
                <div className="mt-6 text-center">
                  <div className="flex items-center justify-center space-x-4 text-gray-400 text-xs">
                    <div className="flex items-center space-x-1">
                      <Shield className="w-3 h-3" />
                      <span>Secure</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>Instant Access</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-3 h-3" />
                      <span>No Credit Card</span>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Hook for trial onboarding analytics
export function useTrialOnboardingAnalytics() {
  const [analytics, setAnalytics] = useState<TrialAnalytics>({
    signupSources: {},
    stepCompletionRates: {},
    dropOffPoints: {},
    timeToValue: [],
    conversionTouchpointEffectiveness: {},
    trialExtensionRequests: 0,
    trialToPaidConversions: 0
  });

  const [optimizationSettings, setOptimizationSettings] = useState({
    enableSmartOnboarding: true,
    personalizeFlow: true,
    adaptiveStepOrdering: true,
    intelligentTrialExtension: true,
    realTimeValueDelivery: true
  });

  useEffect(() => {
    // Load analytics from localStorage
    const savedAnalytics = localStorage.getItem('trial_onboarding_analytics');
    if (savedAnalytics) {
      try {
        setAnalytics(JSON.parse(savedAnalytics));
      } catch (error) {
        console.error('Failed to load trial onboarding analytics:', error);
      }
    }

    // Load optimization settings
    const savedSettings = localStorage.getItem('trial_optimization_settings');
    if (savedSettings) {
      try {
        setOptimizationSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Failed to load optimization settings:', error);
      }
    }
  }, []);

  const trackSignupSource = useCallback((source: string) => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        signupSources: {
          ...prev.signupSources,
          [source]: (prev.signupSources[source] || 0) + 1
        }
      };
      localStorage.setItem('trial_onboarding_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });
  }, []);

  const trackStepCompletion = useCallback((stepId: string, timeToComplete: number) => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        stepCompletionRates: {
          ...prev.stepCompletionRates,
          [stepId]: (prev.stepCompletionRates[stepId] || 0) + 1
        },
        timeToValue: [...prev.timeToValue, timeToComplete]
      };
      localStorage.setItem('trial_onboarding_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });
  }, []);

  const trackDropOff = useCallback((stepId: string) => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        dropOffPoints: {
          ...prev.dropOffPoints,
          [stepId]: (prev.dropOffPoints[stepId] || 0) + 1
        }
      };
      localStorage.setItem('trial_onboarding_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });
  }, []);

  const trackTrialExtension = useCallback(() => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        trialExtensionRequests: prev.trialExtensionRequests + 1
      };
      localStorage.setItem('trial_onboarding_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });
  }, []);

  const trackTrialToPaidConversion = useCallback(() => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        trialToPaidConversions: prev.trialToPaidConversions + 1
      };
      localStorage.setItem('trial_onboarding_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });
  }, []);

  const getOptimizationRecommendations = useCallback(() => {
    const recommendations = [];

    // Analyze signup sources
    const totalSignups = Object.values(analytics.signupSources).reduce((sum, count) => sum + count, 0);
    const bestSource = Object.entries(analytics.signupSources)
      .sort(([,a], [,b]) => b - a)[0];

    if (bestSource && totalSignups > 50) {
      const [source, count] = bestSource;
      const conversionRate = (count / totalSignups) * 100;

      if (conversionRate > 60) {
        recommendations.push({
          type: 'signup_optimization',
          priority: 'high',
          message: `${source} signup has ${conversionRate.toFixed(1)}% conversion rate. Consider promoting this option.`,
          action: 'promote_signup_method'
        });
      }
    }

    // Analyze step completion rates
    Object.entries(analytics.stepCompletionRates).forEach(([stepId, completions]) => {
      if (completions < totalSignups * 0.7 && totalSignups > 20) {
        recommendations.push({
          type: 'step_optimization',
          priority: 'medium',
          message: `Step "${stepId}" has low completion rate. Consider simplifying or providing more guidance.`,
          action: 'optimize_step'
        });
      }
    });

    // Analyze drop-off points
    const highestDropOff = Object.entries(analytics.dropOffPoints)
      .sort(([,a], [,b]) => b - a)[0];

    if (highestDropOff && highestDropOff[1] > totalSignups * 0.2) {
      recommendations.push({
        type: 'dropoff_reduction',
        priority: 'high',
        message: `High drop-off at "${highestDropOff[0]}". Investigate user experience issues.`,
        action: 'reduce_dropoff'
      });
    }

    return recommendations;
  }, [analytics]);

  const getTrialMetrics = useCallback(() => {
    const totalSignups = Object.values(analytics.signupSources).reduce((sum, count) => sum + count, 0);
    const totalStepCompletions = Object.values(analytics.stepCompletionRates).reduce((sum, count) => sum + count, 0);
    const averageTimeToValue = analytics.timeToValue.length > 0
      ? analytics.timeToValue.reduce((sum, time) => sum + time, 0) / analytics.timeToValue.length
      : 0;

    const trialToPaidRate = totalSignups > 0 ? (analytics.trialToPaidConversions / totalSignups) * 100 : 0;
    const extensionRate = totalSignups > 0 ? (analytics.trialExtensionRequests / totalSignups) * 100 : 0;

    return {
      totalSignups,
      totalStepCompletions,
      averageTimeToValue: Math.round(averageTimeToValue / 1000), // Convert to seconds
      trialToPaidRate: Math.round(trialToPaidRate * 100) / 100,
      extensionRate: Math.round(extensionRate * 100) / 100,
      recommendationsCount: getOptimizationRecommendations().length
    };
  }, [analytics, getOptimizationRecommendations]);

  return {
    analytics,
    optimizationSettings,
    trackSignupSource,
    trackStepCompletion,
    trackDropOff,
    trackTrialExtension,
    trackTrialToPaidConversion,
    getOptimizationRecommendations,
    getTrialMetrics
  };
}

// Progressive onboarding component
export function ProgressiveOnboarding({
  userSegment,
  onStepComplete
}: {
  userSegment: string;
  onStepComplete?: (stepId: string) => void;
}) {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());
  const { trackStepCompletion } = useTrialOnboardingAnalytics();

  const onboardingSteps = [
    {
      id: 'welcome',
      title: 'Welcome to Solidity Learning',
      content: 'Let\'s get you started with blockchain development',
      action: 'Continue',
      skippable: false
    },
    {
      id: 'skill_assessment',
      title: 'Quick Skill Assessment',
      content: 'Help us personalize your learning experience',
      action: 'Take Assessment',
      skippable: true
    },
    {
      id: 'first_lesson',
      title: 'Your First Lesson',
      content: 'Start with Solidity basics and syntax',
      action: 'Start Learning',
      skippable: false
    },
    {
      id: 'community_join',
      title: 'Join the Community',
      content: 'Connect with other developers',
      action: 'Join Discord',
      skippable: true
    }
  ];

  const handleStepComplete = useCallback((stepId: string) => {
    const startTime = Date.now();
    setCompletedSteps(prev => new Set([...prev, stepId]));
    setCurrentStep(prev => prev + 1);

    trackStepCompletion(stepId, Date.now() - startTime);
    onStepComplete?.(stepId);
  }, [trackStepCompletion, onStepComplete]);

  if (currentStep >= onboardingSteps.length) {
    return (
      <motion.div
        className="text-center p-8 bg-green-500/20 rounded-lg border border-green-500/50"
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <CheckCircle className="w-12 h-12 text-green-400 mx-auto mb-4" />
        <h3 className="text-xl font-bold text-white mb-2">Onboarding Complete!</h3>
        <p className="text-gray-300">You're all set to start your Solidity journey</p>
      </motion.div>
    );
  }

  const step = onboardingSteps[currentStep];

  return (
    <motion.div
      key={step.id}
      className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6"
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">{step.title}</h3>
        <div className="text-sm text-gray-400">
          Step {currentStep + 1} of {onboardingSteps.length}
        </div>
      </div>

      <p className="text-gray-300 mb-6">{step.content}</p>

      <div className="flex space-x-3">
        <EnhancedButton
          onClick={() => handleStepComplete(step.id)}
          className="bg-blue-600 hover:bg-blue-700 text-white"
          touchTarget
        >
          {step.action}
        </EnhancedButton>

        {step.skippable && (
          <EnhancedButton
            onClick={() => setCurrentStep(prev => prev + 1)}
            variant="ghost"
            className="text-gray-400 hover:text-white"
          >
            Skip
          </EnhancedButton>
        )}
      </div>

      {/* Progress indicator */}
      <div className="mt-6">
        <div className="w-full bg-gray-700 rounded-full h-1">
          <motion.div
            className="h-full bg-blue-500 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${((currentStep + 1) / onboardingSteps.length) * 100}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </div>
    </motion.div>
  );
}