import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { axe, toHaveNoViolations } from 'jest-axe';
import '@testing-library/jest-dom';
import { XPNotification } from '@/components/xp/XPNotification';
import { AchievementCelebration } from '@/components/gamification/AchievementCelebration';
import { StreakTracker } from '@/components/gamification/StreakTracker';
import { LevelProgressionRoadmap } from '@/components/gamification/LevelProgressionRoadmap';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock framer-motion for accessibility testing
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock data
const mockXPGain = {
  id: 'test-xp',
  amount: 100,
  source: 'lesson',
  description: 'Completed lesson',
  timestamp: new Date(),
};

const mockAchievement = {
  id: 'test-achievement',
  title: 'Test Achievement',
  description: 'A test achievement',
  rarity: 'rare' as const,
  category: 'learning' as const,
  requirements: {
    type: 'lesson_completion' as const,
    target: 5,
    current: 5
  },
  rewards: {
    xp: 100,
    badge: 'Test Badge'
  },
  unlockedAt: new Date(),
  progress: 100
};

const mockStreakData = {
  currentStreak: 5,
  longestStreak: 12,
  lastActivity: new Date(),
  streakGoal: 30,
  todayCompleted: false,
  streakHistory: [new Date()],
  milestones: []
};

const mockLevels = [
  {
    level: 1,
    title: 'Beginner',
    description: 'Starting your journey',
    xpRequired: 0,
    xpTotal: 0,
    color: 'bg-gray-500',
    rewards: [],
    unlocks: [],
    isUnlocked: true,
    isCurrentLevel: true
  },
  {
    level: 2,
    title: 'Novice',
    description: 'Learning the basics',
    xpRequired: 1000,
    xpTotal: 1000,
    color: 'bg-blue-500',
    rewards: [],
    unlocks: [],
    isUnlocked: false
  }
];

describe('Accessibility Tests', () => {
  describe('XP Notification Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <XPNotification
          xpGain={mockXPGain}
          onComplete={() => {}}
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('provides screen reader accessible content', () => {
      render(
        <XPNotification
          xpGain={mockXPGain}
          onComplete={() => {}}
        />
      );

      // Should have descriptive text for screen readers
      expect(screen.getByText('+100')).toBeInTheDocument();
      expect(screen.getByText('XP')).toBeInTheDocument();
      expect(screen.getByText('Completed lesson')).toBeInTheDocument();
    });

    it('has sufficient color contrast', () => {
      const { container } = render(
        <XPNotification
          xpGain={mockXPGain}
          onComplete={() => {}}
        />
      );

      const xpText = screen.getByText('+100');
      const computedStyle = window.getComputedStyle(xpText);
      
      // Should have color defined (basic check)
      expect(computedStyle.color).toBeTruthy();
    });

    it('respects reduced motion preferences', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      const { container } = render(
        <XPNotification
          xpGain={mockXPGain}
          onComplete={() => {}}
        />
      );

      // Should still render but with reduced animations
      expect(container.firstChild).toBeInTheDocument();
    });
  });

  describe('Achievement Celebration Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <AchievementCelebration
          achievement={mockAchievement}
          onClose={() => {}}
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper focus management', () => {
      render(
        <AchievementCelebration
          achievement={mockAchievement}
          onClose={() => {}}
        />
      );

      const closeButton = screen.getByLabelText('Close celebration');
      expect(closeButton).toBeInTheDocument();
      expect(closeButton.getAttribute('tabIndex')).not.toBe('-1');
    });

    it('supports keyboard navigation', () => {
      render(
        <AchievementCelebration
          achievement={mockAchievement}
          onClose={() => {}}
        />
      );

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button.getAttribute('tabIndex')).not.toBe('-1');
      });
    });

    it('provides proper ARIA labels', () => {
      render(
        <AchievementCelebration
          achievement={mockAchievement}
          onClose={() => {}}
        />
      );

      const closeButton = screen.getByLabelText('Close celebration');
      expect(closeButton).toHaveAttribute('aria-label', 'Close celebration');
    });

    it('has proper heading hierarchy', () => {
      render(
        <AchievementCelebration
          achievement={mockAchievement}
          onClose={() => {}}
        />
      );

      // Should have proper heading structure
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
      expect(screen.getByText('Achievement Unlocked!')).toBeInTheDocument();
    });

    it('handles escape key for closing', () => {
      const mockOnClose = jest.fn();
      
      render(
        <AchievementCelebration
          achievement={mockAchievement}
          onClose={mockOnClose}
        />
      );

      // Simulate escape key press
      fireEvent.keyDown(document, { key: 'Escape', code: 'Escape' });
      
      // Should close on escape (implementation dependent)
      // This test would need the actual escape key handler implementation
    });
  });

  describe('Streak Tracker Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <StreakTracker
          streakData={mockStreakData}
          onStreakUpdate={() => {}}
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('provides descriptive labels for streak information', () => {
      render(
        <StreakTracker
          streakData={mockStreakData}
          onStreakUpdate={() => {}}
        />
      );

      expect(screen.getByText('Learning Streak')).toBeInTheDocument();
      expect(screen.getByText('Keep the momentum going!')).toBeInTheDocument();
      expect(screen.getByText('days in a row')).toBeInTheDocument();
    });

    it('has accessible progress indicators', () => {
      render(
        <StreakTracker
          streakData={mockStreakData}
          onStreakUpdate={() => {}}
        />
      );

      // Should have accessible progress information
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.getByText('Longest streak')).toBeInTheDocument();
    });

    it('provides screen reader friendly time information', () => {
      render(
        <StreakTracker
          streakData={mockStreakData}
          onStreakUpdate={() => {}}
        />
      );

      // Should have time-related information that's accessible
      const timeElements = screen.getAllByText(/\d+h \d+m/);
      expect(timeElements.length).toBeGreaterThan(0);
    });
  });

  describe('Level Progression Accessibility', () => {
    it('should not have accessibility violations', async () => {
      const { container } = render(
        <LevelProgressionRoadmap
          levels={mockLevels}
          currentLevel={1}
          currentXP={500}
        />
      );

      const results = await axe(container);
      expect(results).toHaveNoViolations();
    });

    it('has proper button accessibility', () => {
      render(
        <LevelProgressionRoadmap
          levels={mockLevels}
          currentLevel={1}
          currentXP={500}
        />
      );

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeInTheDocument();
        expect(button.getAttribute('tabIndex')).not.toBe('-1');
      });
    });

    it('provides accessible level information', () => {
      render(
        <LevelProgressionRoadmap
          levels={mockLevels}
          currentLevel={1}
          currentXP={500}
        />
      );

      expect(screen.getByText('Level Progression Roadmap')).toBeInTheDocument();
      expect(screen.getByText('Unlock new features and rewards as you level up')).toBeInTheDocument();
    });

    it('has accessible progress bars', () => {
      render(
        <LevelProgressionRoadmap
          levels={mockLevels}
          currentLevel={1}
          currentXP={500}
        />
      );

      // Should have progress information
      expect(screen.getByText(/XP/)).toBeInTheDocument();
    });
  });

  describe('Touch Target Accessibility', () => {
    it('has minimum 44px touch targets for buttons', () => {
      render(
        <AchievementCelebration
          achievement={mockAchievement}
          onClose={() => {}}
        />
      );

      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        const computedStyle = window.getComputedStyle(button);
        const minSize = 44; // 44px minimum touch target
        
        // Note: In a real test environment, you'd check actual computed dimensions
        // This is a simplified check
        expect(button).toBeInTheDocument();
      });
    });

    it('provides adequate spacing between interactive elements', () => {
      render(
        <AchievementCelebration
          achievement={mockAchievement}
          onClose={() => {}}
        />
      );

      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(1);
      
      // Should have multiple buttons with adequate spacing
      // In a real implementation, you'd measure actual spacing
    });
  });

  describe('Screen Reader Compatibility', () => {
    it('provides meaningful text alternatives', () => {
      render(
        <XPNotification
          xpGain={mockXPGain}
          onComplete={() => {}}
        />
      );

      // Should have text that makes sense to screen readers
      expect(screen.getByText('Completed lesson')).toBeInTheDocument();
      expect(screen.getByText('+100')).toBeInTheDocument();
      expect(screen.getByText('XP')).toBeInTheDocument();
    });

    it('announces important state changes', () => {
      const { rerender } = render(
        <StreakTracker
          streakData={mockStreakData}
          onStreakUpdate={() => {}}
        />
      );

      const updatedStreakData = {
        ...mockStreakData,
        currentStreak: 6
      };

      rerender(
        <StreakTracker
          streakData={updatedStreakData}
          onStreakUpdate={() => {}}
        />
      );

      // Should show updated streak
      expect(screen.getByText('6')).toBeInTheDocument();
    });

    it('provides context for numerical values', () => {
      render(
        <StreakTracker
          streakData={mockStreakData}
          onStreakUpdate={() => {}}
        />
      );

      // Numbers should have context
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.getByText('days in a row')).toBeInTheDocument();
    });
  });

  describe('High Contrast Mode Support', () => {
    it('maintains visibility in high contrast mode', () => {
      // Mock high contrast media query
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-contrast: high)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      const { container } = render(
        <XPNotification
          xpGain={mockXPGain}
          onComplete={() => {}}
        />
      );

      // Should render without issues in high contrast mode
      expect(container.firstChild).toBeInTheDocument();
    });
  });

  describe('Language and Internationalization', () => {
    it('supports RTL languages', () => {
      // Mock RTL direction
      document.dir = 'rtl';

      const { container } = render(
        <AchievementCelebration
          achievement={mockAchievement}
          onClose={() => {}}
        />
      );

      // Should render correctly in RTL
      expect(container.firstChild).toBeInTheDocument();

      // Reset
      document.dir = 'ltr';
    });

    it('has proper lang attributes', () => {
      render(
        <AchievementCelebration
          achievement={mockAchievement}
          onClose={() => {}}
        />
      );

      // Should have proper language context
      // In a real implementation, you'd check for lang attributes
      expect(screen.getByText('Achievement Unlocked!')).toBeInTheDocument();
    });
  });
});
