'use client';

import React, { useState, useEffect, useC<PERSON>back, createContext, useContext } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import {
  Search,
  Filter,
  BookOpen,
  Video,
  FileText,
  Code,
  Download,
  Share2,
  Bookmark,
  Star,
  Clock,
  User,
  Tag,
  TrendingUp,
  Eye,
  ThumbsUp,
  MessageCircle,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Play,
  Pause,
  Volume2,
  VolumeX,
  Settings,
  Maximize,
  RotateCcw
} from 'lucide-react';

// Content types and interfaces
interface ContentItem {
  id: string;
  type: 'article' | 'video' | 'tutorial' | 'documentation' | 'example' | 'quiz';
  title: string;
  description: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
    role: string;
  };
  metadata: {
    difficulty: 'beginner' | 'intermediate' | 'advanced';
    duration: number; // in minutes
    tags: string[];
    category: string;
    prerequisites: string[];
    learningObjectives: string[];
    lastUpdated: Date;
    version: string;
  };
  engagement: {
    views: number;
    likes: number;
    bookmarks: number;
    comments: number;
    rating: number;
    completions: number;
  };
  accessibility: {
    hasTranscript: boolean;
    hasClosedCaptions: boolean;
    hasAudioDescription: boolean;
    readingLevel: number; // Flesch reading ease score
  };
  seo: {
    slug: string;
    metaTitle: string;
    metaDescription: string;
    keywords: string[];
    canonicalUrl?: string;
  };
}

interface ContentFilter {
  type?: ContentItem['type'][];
  difficulty?: ContentItem['metadata']['difficulty'][];
  category?: string[];
  tags?: string[];
  duration?: { min: number; max: number };
  rating?: { min: number; max: number };
  author?: string[];
  sortBy?: 'relevance' | 'date' | 'popularity' | 'rating' | 'duration';
  sortOrder?: 'asc' | 'desc';
}

interface ContentContextType {
  items: ContentItem[];
  filteredItems: ContentItem[];
  searchQuery: string;
  filters: ContentFilter;
  isLoading: boolean;
  error: string | null;

  // Methods
  setSearchQuery: (query: string) => void;
  setFilters: (filters: Partial<ContentFilter>) => void;
  getContentById: (id: string) => ContentItem | null;
  trackContentInteraction: (contentId: string, interaction: string, data?: any) => void;
  bookmarkContent: (contentId: string) => void;
  rateContent: (contentId: string, rating: number) => void;
  shareContent: (contentId: string, platform: string) => void;
}

const ContentContext = createContext<ContentContextType | null>(null);

// Mock content data
const mockContentItems: ContentItem[] = [
  {
    id: 'solidity-basics-intro',
    type: 'article',
    title: 'Introduction to Solidity Programming',
    description: 'Learn the fundamentals of Solidity, the programming language for Ethereum smart contracts.',
    content: `# Introduction to Solidity Programming

Solidity is a statically-typed programming language designed for developing smart contracts that run on the Ethereum Virtual Machine (EVM). It was influenced by C++, Python, and JavaScript and is designed to target the EVM.

## Key Features

- **Static Typing**: Variables must be declared with their types
- **Inheritance**: Contracts can inherit from other contracts
- **Libraries**: Reusable code that can be deployed once and used by many contracts
- **Complex User-Defined Types**: Structs and enums for organizing data

## Basic Syntax

\`\`\`solidity
pragma solidity ^0.8.0;

contract HelloWorld {
    string public message;

    constructor(string memory _message) {
        message = _message;
    }

    function setMessage(string memory _message) public {
        message = _message;
    }
}
\`\`\`

This simple contract demonstrates variable declaration, constructor usage, and function definition.`,
    author: {
      id: 'author-1',
      name: 'Dr. Sarah Johnson',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah',
      role: 'Blockchain Educator'
    },
    metadata: {
      difficulty: 'beginner',
      duration: 15,
      tags: ['solidity', 'basics', 'smart-contracts', 'ethereum'],
      category: 'Programming Fundamentals',
      prerequisites: ['basic-programming-concepts'],
      learningObjectives: [
        'Understand what Solidity is and its purpose',
        'Learn basic Solidity syntax',
        'Write your first smart contract',
        'Understand contract structure'
      ],
      lastUpdated: new Date('2024-01-15'),
      version: '1.2.0'
    },
    engagement: {
      views: 15420,
      likes: 1247,
      bookmarks: 892,
      comments: 156,
      rating: 4.8,
      completions: 12340
    },
    accessibility: {
      hasTranscript: true,
      hasClosedCaptions: false,
      hasAudioDescription: false,
      readingLevel: 65 // Flesch reading ease score
    },
    seo: {
      slug: 'introduction-to-solidity-programming',
      metaTitle: 'Introduction to Solidity Programming | Learn Smart Contract Development',
      metaDescription: 'Master Solidity fundamentals with our comprehensive guide. Learn smart contract development, syntax, and best practices for Ethereum blockchain.',
      keywords: ['solidity', 'smart contracts', 'ethereum', 'blockchain', 'programming', 'tutorial'],
      canonicalUrl: 'https://solidity-learning.com/content/introduction-to-solidity-programming'
    }
  },
  {
    id: 'smart-contract-security',
    type: 'video',
    title: 'Smart Contract Security Best Practices',
    description: 'Essential security patterns and common vulnerabilities in smart contract development.',
    content: 'Video content with interactive examples and security audit checklist.',
    author: {
      id: 'author-2',
      name: 'Alex Chen',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Alex',
      role: 'Security Auditor'
    },
    metadata: {
      difficulty: 'advanced',
      duration: 45,
      tags: ['security', 'auditing', 'best-practices', 'vulnerabilities'],
      category: 'Security',
      prerequisites: ['solidity-basics', 'contract-deployment'],
      learningObjectives: [
        'Identify common security vulnerabilities',
        'Implement security best practices',
        'Perform basic security audits',
        'Use security tools and frameworks'
      ],
      lastUpdated: new Date('2024-01-20'),
      version: '2.1.0'
    },
    engagement: {
      views: 8934,
      likes: 756,
      bookmarks: 634,
      comments: 89,
      rating: 4.9,
      completions: 6789
    },
    accessibility: {
      hasTranscript: true,
      hasClosedCaptions: true,
      hasAudioDescription: true,
      readingLevel: 58
    },
    seo: {
      slug: 'smart-contract-security-best-practices',
      metaTitle: 'Smart Contract Security Best Practices | Solidity Security Guide',
      metaDescription: 'Learn essential smart contract security patterns, common vulnerabilities, and auditing techniques to build secure DApps.',
      keywords: ['smart contract security', 'solidity security', 'blockchain security', 'audit', 'vulnerabilities'],
      canonicalUrl: 'https://solidity-learning.com/content/smart-contract-security-best-practices'
    }
  },
  {
    id: 'defi-protocols-tutorial',
    type: 'tutorial',
    title: 'Building DeFi Protocols with Solidity',
    description: 'Step-by-step guide to creating decentralized finance protocols including AMMs and lending platforms.',
    content: 'Interactive tutorial with code examples and deployment instructions.',
    author: {
      id: 'author-3',
      name: 'Maria Rodriguez',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=Maria',
      role: 'DeFi Developer'
    },
    metadata: {
      difficulty: 'advanced',
      duration: 90,
      tags: ['defi', 'amm', 'lending', 'protocols', 'advanced'],
      category: 'DeFi Development',
      prerequisites: ['smart-contract-security', 'solidity-advanced'],
      learningObjectives: [
        'Understand DeFi protocol architecture',
        'Build automated market makers',
        'Implement lending protocols',
        'Deploy and test DeFi contracts'
      ],
      lastUpdated: new Date('2024-01-25'),
      version: '1.0.0'
    },
    engagement: {
      views: 5678,
      likes: 445,
      bookmarks: 389,
      comments: 67,
      rating: 4.7,
      completions: 3456
    },
    accessibility: {
      hasTranscript: true,
      hasClosedCaptions: false,
      hasAudioDescription: false,
      readingLevel: 52
    },
    seo: {
      slug: 'building-defi-protocols-solidity',
      metaTitle: 'Building DeFi Protocols with Solidity | Advanced Tutorial',
      metaDescription: 'Learn to build decentralized finance protocols including AMMs and lending platforms with our comprehensive Solidity tutorial.',
      keywords: ['defi', 'solidity', 'amm', 'lending', 'protocols', 'tutorial'],
      canonicalUrl: 'https://solidity-learning.com/content/building-defi-protocols-solidity'
    }
  }
];

interface ComprehensiveContentSystemProps {
  children: React.ReactNode;
  initialContent?: ContentItem[];
  enableAnalytics?: boolean;
  enablePersonalization?: boolean;
}

export function ComprehensiveContentSystem({
  children,
  initialContent = mockContentItems,
  enableAnalytics = true,
  enablePersonalization = true
}: ComprehensiveContentSystemProps) {
  const [items, setItems] = useState<ContentItem[]>(initialContent);
  const [filteredItems, setFilteredItems] = useState<ContentItem[]>(initialContent);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<ContentFilter>({
    sortBy: 'relevance',
    sortOrder: 'desc'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Filter and search content
  useEffect(() => {
    let filtered = [...items];

    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.description.toLowerCase().includes(query) ||
        item.content.toLowerCase().includes(query) ||
        item.metadata.tags.some(tag => tag.toLowerCase().includes(query)) ||
        item.author.name.toLowerCase().includes(query)
      );
    }

    // Apply filters
    if (filters.type?.length) {
      filtered = filtered.filter(item => filters.type!.includes(item.type));
    }

    if (filters.difficulty?.length) {
      filtered = filtered.filter(item => filters.difficulty!.includes(item.metadata.difficulty));
    }

    if (filters.category?.length) {
      filtered = filtered.filter(item => filters.category!.includes(item.metadata.category));
    }

    if (filters.tags?.length) {
      filtered = filtered.filter(item =>
        filters.tags!.some(tag => item.metadata.tags.includes(tag))
      );
    }

    if (filters.duration) {
      filtered = filtered.filter(item =>
        item.metadata.duration >= filters.duration!.min &&
        item.metadata.duration <= filters.duration!.max
      );
    }

    if (filters.rating) {
      filtered = filtered.filter(item =>
        item.engagement.rating >= filters.rating!.min &&
        item.engagement.rating <= filters.rating!.max
      );
    }

    if (filters.author?.length) {
      filtered = filtered.filter(item => filters.author!.includes(item.author.id));
    }

    // Apply sorting
    if (filters.sortBy) {
      filtered.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (filters.sortBy) {
          case 'date':
            aValue = a.metadata.lastUpdated.getTime();
            bValue = b.metadata.lastUpdated.getTime();
            break;
          case 'popularity':
            aValue = a.engagement.views;
            bValue = b.engagement.views;
            break;
          case 'rating':
            aValue = a.engagement.rating;
            bValue = b.engagement.rating;
            break;
          case 'duration':
            aValue = a.metadata.duration;
            bValue = b.metadata.duration;
            break;
          default: // relevance
            aValue = calculateRelevanceScore(a, searchQuery);
            bValue = calculateRelevanceScore(b, searchQuery);
        }

        return filters.sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
      });
    }

    setFilteredItems(filtered);
  }, [items, searchQuery, filters]);

  const getContentById = useCallback((id: string) => {
    return items.find(item => item.id === id) || null;
  }, [items]);

  const trackContentInteraction = useCallback((contentId: string, interaction: string, data: any = {}) => {
    if (!enableAnalytics) return;

    // Track interaction with analytics
    console.log('Content interaction:', { contentId, interaction, data });

    // Update engagement metrics
    setItems(prev => prev.map(item => {
      if (item.id === contentId) {
        const updatedEngagement = { ...item.engagement };

        switch (interaction) {
          case 'view':
            updatedEngagement.views += 1;
            break;
          case 'like':
            updatedEngagement.likes += data.liked ? 1 : -1;
            break;
          case 'bookmark':
            updatedEngagement.bookmarks += data.bookmarked ? 1 : -1;
            break;
          case 'comment':
            updatedEngagement.comments += 1;
            break;
          case 'complete':
            updatedEngagement.completions += 1;
            break;
        }

        return { ...item, engagement: updatedEngagement };
      }
      return item;
    }));
  }, [enableAnalytics]);

  const bookmarkContent = useCallback((contentId: string) => {
    // Toggle bookmark status
    const bookmarked = !isContentBookmarked(contentId);

    // Update local storage
    const bookmarks = getBookmarkedContent();
    if (bookmarked) {
      bookmarks.push(contentId);
    } else {
      const index = bookmarks.indexOf(contentId);
      if (index > -1) bookmarks.splice(index, 1);
    }
    localStorage.setItem('bookmarked_content', JSON.stringify(bookmarks));

    trackContentInteraction(contentId, 'bookmark', { bookmarked });
  }, [trackContentInteraction]);

  const rateContent = useCallback((contentId: string, rating: number) => {
    // Update rating (in real app, this would be sent to backend)
    setItems(prev => prev.map(item => {
      if (item.id === contentId) {
        // Simple rating update (in real app, would calculate average)
        return {
          ...item,
          engagement: {
            ...item.engagement,
            rating: (item.engagement.rating + rating) / 2
          }
        };
      }
      return item;
    }));

    trackContentInteraction(contentId, 'rate', { rating });
  }, [trackContentInteraction]);

  const shareContent = useCallback((contentId: string, platform: string) => {
    const content = getContentById(contentId);
    if (!content) return;

    const shareUrl = `${window.location.origin}/content/${content.seo.slug}`;
    const shareText = `Check out this great content: ${content.title}`;

    switch (platform) {
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`);
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`);
        break;
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`);
        break;
      case 'copy':
        navigator.clipboard.writeText(shareUrl);
        break;
    }

    trackContentInteraction(contentId, 'share', { platform });
  }, [getContentById, trackContentInteraction]);

  const contextValue: ContentContextType = {
    items,
    filteredItems,
    searchQuery,
    filters,
    isLoading,
    error,
    setSearchQuery,
    setFilters: (newFilters) => setFilters(prev => ({ ...prev, ...newFilters })),
    getContentById,
    trackContentInteraction,
    bookmarkContent,
    rateContent,
    shareContent
  };

  return (
    <ContentContext.Provider value={contextValue}>
      {children}
    </ContentContext.Provider>
  );
}

// Hook to use content context
export function useContentSystem() {
  const context = useContext(ContentContext);
  if (!context) {
    throw new Error('useContentSystem must be used within ComprehensiveContentSystem');
  }
  return context;
}

// Utility functions
function calculateRelevanceScore(item: ContentItem, query: string): number {
  if (!query.trim()) return item.engagement.views; // Default to popularity

  const queryLower = query.toLowerCase();
  let score = 0;

  // Title match (highest weight)
  if (item.title.toLowerCase().includes(queryLower)) score += 100;

  // Description match
  if (item.description.toLowerCase().includes(queryLower)) score += 50;

  // Tag match
  if (item.metadata.tags.some(tag => tag.toLowerCase().includes(queryLower))) score += 30;

  // Content match (lower weight due to length)
  if (item.content.toLowerCase().includes(queryLower)) score += 10;

  // Author match
  if (item.author.name.toLowerCase().includes(queryLower)) score += 20;

  // Boost by engagement
  score += item.engagement.rating * 10;
  score += Math.log(item.engagement.views + 1) * 5;

  return score;
}

function isContentBookmarked(contentId: string): boolean {
  const bookmarks = getBookmarkedContent();
  return bookmarks.includes(contentId);
}

function getBookmarkedContent(): string[] {
  try {
    const bookmarks = localStorage.getItem('bookmarked_content');
    return bookmarks ? JSON.parse(bookmarks) : [];
  } catch {
    return [];
  }
}

export default ComprehensiveContentSystem;