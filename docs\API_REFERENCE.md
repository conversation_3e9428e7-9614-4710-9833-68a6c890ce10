# API Reference - Solidity Learning Platform

## Table of Contents

1. [Component APIs](#component-apis)
2. [Hook APIs](#hook-apis)
3. [Context APIs](#context-apis)
4. [Utility APIs](#utility-apis)
5. [Type Definitions](#type-definitions)
6. [Event System](#event-system)

## Component APIs

### Engagement System

#### `<ComprehensiveEngagementSystem>`

Main provider for all engagement features.

**Props:**
```typescript
interface ComprehensiveEngagementSystemProps {
  config: EngagementConfig;
  userContext: UserContext;
  className?: string;
  onEngagementEvent?: (event: string, data: any) => void;
}

interface EngagementConfig {
  enableNotifications: boolean;
  enableStatsCounter: boolean;
  enableAIAssistant: boolean;
  enableFAQ: boolean;
  enableExitIntent: boolean;
  enableScrollCTAs: boolean;
  enableUrgencyTimers: boolean;
  enableSocialSharing: boolean;
  enableAchievements: boolean;
  enableAnalytics: boolean;
  trackEngagement: boolean;
  theme: 'light' | 'dark' | 'auto';
  position: 'bottom-right' | 'bottom-left';
  animationLevel: 'minimal' | 'standard' | 'enhanced';
}

interface UserContext {
  id?: string;
  name?: string;
  email?: string;
  currentPage: string;
  progress?: UserProgress;
  preferences?: UserPreferences;
}
```

**Example:**
```tsx
<ComprehensiveEngagementSystem
  config={{
    enableNotifications: true,
    enableAIAssistant: true,
    theme: 'auto',
    position: 'bottom-right',
    animationLevel: 'standard'
  }}
  userContext={{
    id: 'user-123',
    currentPage: '/learn/solidity-basics',
    progress: {
      currentLesson: 'variables-and-types',
      completedLessons: 5,
      totalXP: 250
    }
  }}
  onEngagementEvent={(event, data) => {
    analytics.track(event, data);
  }}
>
  <YourAppContent />
</ComprehensiveEngagementSystem>
```

#### `<RealTimeNotificationSystem>`

Displays live user activity notifications.

**Props:**
```typescript
interface RealTimeNotificationSystemProps {
  className?: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  maxVisible?: number;
  autoHideDuration?: number;
  enableSound?: boolean;
}
```

**Default Values:**
- `position`: `'bottom-right'`
- `maxVisible`: `3`
- `autoHideDuration`: `5000`
- `enableSound`: `false`

#### `<AnimatedStatsCounter>`

Animated statistics display with intersection observer.

**Props:**
```typescript
interface AnimatedStatsCounterProps {
  className?: string;
  layout?: 'grid' | 'horizontal' | 'vertical';
  animationDuration?: number;
  enableParticles?: boolean;
  showDescriptions?: boolean;
}
```

**Custom Stats:**
```tsx
const { updateStat, addStat } = useAnimatedStats();

// Update existing stat
updateStat('active_users', 15420);

// Add new stat
addStat({
  id: 'custom_metric',
  label: 'Custom Metric',
  value: 100,
  icon: <CustomIcon />,
  color: 'text-blue-500'
});
```

#### `<AIAssistantWidget>`

Contextual AI chat assistant.

**Props:**
```typescript
interface AIAssistantWidgetProps {
  className?: string;
  position?: 'bottom-right' | 'bottom-left';
  enableContextualHelp?: boolean;
  currentPage?: string;
  userProgress?: UserProgress;
}
```

**Contextual Suggestions:**
```typescript
const contextualSuggestions = {
  '/learn': [
    'Explain this concept in simpler terms',
    'Show me a practical example',
    'What are the prerequisites?'
  ],
  '/code': [
    'Help me debug this code',
    'Explain this error message',
    'What are best practices?'
  ]
};
```

### Analytics System

#### `<ComprehensiveAnalyticsSystem>`

Analytics provider with user segmentation.

**Props:**
```typescript
interface ComprehensiveAnalyticsSystemProps {
  children: React.ReactNode;
  config: AnalyticsConfig;
  userId?: string;
  userTraits?: any;
}

interface AnalyticsConfig {
  googleAnalyticsId?: string;
  hotjarId?: string;
  sentryDsn?: string;
  enableHeatmaps: boolean;
  enableSessionRecordings: boolean;
  enableUserConsent: boolean;
  enableABTesting: boolean;
  enablePerformanceMonitoring: boolean;
  enableFeedbackWidgets: boolean;
  privacyCompliant: boolean;
  trackingLevel: 'minimal' | 'standard' | 'enhanced';
}
```

#### `<RealTimeAnalyticsDashboard>`

Live analytics dashboard component.

**Props:**
```typescript
interface RealTimeAnalyticsDashboardProps {
  className?: string;
  refreshInterval?: number;
  showFilters?: boolean;
  showExport?: boolean;
  compactMode?: boolean;
}
```

### Content System

#### `<ComprehensiveContentSystem>`

Content management provider.

**Props:**
```typescript
interface ComprehensiveContentSystemProps {
  children: React.ReactNode;
  initialContent?: ContentItem[];
  enableAnalytics?: boolean;
  enablePersonalization?: boolean;
}
```

#### `<InteractiveContentViewer>`

Rich content viewer with media support.

**Props:**
```typescript
interface InteractiveContentViewerProps {
  contentId: string;
  className?: string;
  autoPlay?: boolean;
  showControls?: boolean;
  showMetadata?: boolean;
  showEngagement?: boolean;
  enableComments?: boolean;
  enableNotes?: boolean;
}
```

## Hook APIs

### `useEngagementSystem()`

Access engagement system context.

**Returns:**
```typescript
interface EngagementContextType {
  config: EngagementConfig;
  events: EngagementEvent[];
  conversionProbability: number;
  triggerConversionAction: (action: string, context?: any) => void;
  trackEngagementEvent: (event: EngagementEvent) => void;
}
```

**Usage:**
```tsx
const { 
  conversionProbability, 
  triggerConversionAction,
  trackEngagementEvent 
} = useEngagementSystem();

// Trigger conversion action
const handleCTAClick = () => {
  triggerConversionAction('start_trial', {
    source: 'hero_section',
    variant: 'primary'
  });
};

// Track custom engagement
trackEngagementEvent({
  type: 'interaction',
  element: 'video_player',
  action: 'play',
  timestamp: new Date()
});
```

### `useComprehensiveAnalytics()`

Access analytics functionality.

**Returns:**
```typescript
interface AnalyticsContextType {
  trackEvent: (event: string, properties?: any) => void;
  trackConversion: (step: string, value?: number) => void;
  trackUserJourney: (step: string, metadata?: any) => void;
  identifyUser: (userId: string, traits?: any) => void;
  userSegment: UserSegment | null;
  conversionFunnel: ConversionFunnel[];
}
```

**Usage:**
```tsx
const { 
  trackEvent, 
  trackConversion, 
  userSegment 
} = useComprehensiveAnalytics();

// Track user events
trackEvent('lesson_completed', {
  lessonId: 'intro-to-solidity',
  timeSpent: 1200,
  score: 85
});

// Track conversions
trackConversion('trial_signup', 29.99);

// Access user segment
if (userSegment?.id === 'returning_visitor') {
  // Show personalized content
}
```

### `useContentSystem()`

Access content management.

**Returns:**
```typescript
interface ContentContextType {
  items: ContentItem[];
  filteredItems: ContentItem[];
  searchQuery: string;
  filters: ContentFilter;
  setSearchQuery: (query: string) => void;
  setFilters: (filters: Partial<ContentFilter>) => void;
  getContentById: (id: string) => ContentItem | null;
  trackContentInteraction: (contentId: string, interaction: string) => void;
  bookmarkContent: (contentId: string) => void;
  rateContent: (contentId: string, rating: number) => void;
}
```

**Usage:**
```tsx
const { 
  filteredItems, 
  setSearchQuery, 
  trackContentInteraction 
} = useContentSystem();

// Search content
const handleSearch = (query: string) => {
  setSearchQuery(query);
};

// Track interactions
const handleContentView = (contentId: string) => {
  trackContentInteraction(contentId, 'view');
};
```

### `useConversionOptimization()`

Get optimized content for conversions.

**Returns:**
```typescript
interface ConversionOptimizationType {
  getOptimalCTA: () => string;
  getOptimalUrgencyLevel: () => 'low' | 'medium' | 'high';
  shouldShowOffer: () => boolean;
  conversionProbability: number;
  triggerConversionAction: (action: string, context?: any) => void;
}
```

**Usage:**
```tsx
const { 
  getOptimalCTA, 
  shouldShowOffer, 
  conversionProbability 
} = useConversionOptimization();

// Get personalized CTA text
const ctaText = getOptimalCTA(); // "Start Free Trial" or "Begin Learning"

// Show offer based on user behavior
if (shouldShowOffer()) {
  return <SpecialOfferModal />;
}
```

### `usePerformanceMonitoring()`

Monitor application performance.

**Returns:**
```typescript
interface PerformanceMonitoringType {
  trackMetric: (metric: string, value: number) => void;
  reportError: (error: Error, context?: any) => void;
  getMetrics: () => PerformanceMetrics;
}
```

**Usage:**
```tsx
const { trackMetric, reportError } = usePerformanceMonitoring();

// Track custom metrics
trackMetric('api_response_time', 150);

// Report errors
try {
  // Some operation
} catch (error) {
  reportError(error, {
    component: 'ContentViewer',
    action: 'loadContent'
  });
}
```

## Context APIs

### EngagementContext

Provides engagement system state and methods.

```typescript
const EngagementContext = createContext<EngagementContextType | null>(null);

// Provider usage
<EngagementContext.Provider value={contextValue}>
  {children}
</EngagementContext.Provider>

// Consumer usage
const context = useContext(EngagementContext);
```

### AnalyticsContext

Provides analytics functionality.

```typescript
const AnalyticsContext = createContext<AnalyticsContextType | null>(null);

// Access analytics anywhere in the component tree
const analytics = useContext(AnalyticsContext);
analytics.trackEvent('custom_event', { data: 'value' });
```

### ContentContext

Provides content management state.

```typescript
const ContentContext = createContext<ContentContextType | null>(null);

// Access content system
const content = useContext(ContentContext);
const lesson = content.getContentById('lesson-123');
```

## Utility APIs

### Performance Utilities

```typescript
// Core Web Vitals tracking
trackLCP(value: number): void;
trackFID(value: number): void;
trackCLS(value: number): void;

// Custom performance metrics
trackCustomMetric(name: string, value: number): void;

// Error reporting
reportError(error: Error, context?: ErrorContext): void;

interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  metadata?: any;
}
```

### Analytics Utilities

```typescript
// Event tracking
trackEvent(event: string, properties?: EventProperties): void;

interface EventProperties {
  category?: string;
  action?: string;
  label?: string;
  value?: number;
  customParameters?: Record<string, any>;
}

// User identification
identifyUser(userId: string, traits?: UserTraits): void;

interface UserTraits {
  name?: string;
  email?: string;
  plan?: string;
  signupDate?: Date;
  [key: string]: any;
}

// Conversion tracking
trackConversion(event: string, value?: number): void;
```

### Content Utilities

```typescript
// Content filtering
filterContent(items: ContentItem[], filters: ContentFilter): ContentItem[];

// Search functionality
searchContent(items: ContentItem[], query: string): ContentItem[];

// Relevance scoring
calculateRelevanceScore(item: ContentItem, query: string): number;
```

## Type Definitions

### Core Types

```typescript
interface UserProgress {
  currentLesson?: string;
  completedLessons?: number;
  currentCourse?: string;
  totalXP?: number;
  level?: number;
  streak?: number;
  achievements?: Achievement[];
}

interface Achievement {
  id: string;
  name: string;
  description: string;
  icon?: string;
  earnedAt: Date;
  rarity?: 'common' | 'rare' | 'epic' | 'legendary';
}

interface ContentItem {
  id: string;
  type: 'article' | 'video' | 'tutorial' | 'documentation' | 'example' | 'quiz';
  title: string;
  description: string;
  content: string;
  author: Author;
  metadata: ContentMetadata;
  engagement: EngagementMetrics;
  accessibility: AccessibilityInfo;
  seo: SEOInfo;
}

interface EngagementEvent {
  id: string;
  type: 'view' | 'click' | 'conversion' | 'exit_intent' | 'scroll_milestone';
  element?: string;
  value?: number;
  timestamp: Date;
  userSegment?: string;
  metadata?: any;
}
```

### Analytics Types

```typescript
interface AnalyticsConfig {
  googleAnalyticsId?: string;
  hotjarId?: string;
  sentryDsn?: string;
  enableHeatmaps: boolean;
  enableSessionRecordings: boolean;
  enableUserConsent: boolean;
  privacyCompliant: boolean;
  trackingLevel: 'minimal' | 'standard' | 'enhanced';
}

interface UserSegment {
  id: string;
  name: string;
  criteria: SegmentCriteria;
  personalizations: PersonalizationConfig;
}

interface ConversionFunnel {
  step: string;
  name: string;
  users: number;
  conversions: number;
  conversionRate: number;
  dropOffRate: number;
  averageTime: number;
}
```

## Event System

### Event Types

```typescript
// Engagement Events
'engagement_interaction' | 'scroll_milestone' | 'cta_click' | 'ai_assistant_interaction'

// Analytics Events
'user_identified' | 'conversion' | 'ab_test_assignment' | 'performance_metric'

// Content Events
'content_view' | 'content_interaction' | 'bookmark_added' | 'rating_submitted'

// System Events
'error_reported' | 'performance_alert' | 'update_available'
```

### Event Handlers

```typescript
// Global event handler
const handleEngagementEvent = (event: string, data: any) => {
  switch (event) {
    case 'cta_click':
      analytics.trackConversion('cta_click', data.value);
      break;
    case 'content_view':
      analytics.trackEvent('content_viewed', {
        contentId: data.contentId,
        contentType: data.contentType
      });
      break;
    default:
      analytics.trackEvent(event, data);
  }
};

// Component-specific handlers
const handleAIInteraction = (type: string, data: any) => {
  trackEvent('ai_interaction', {
    interactionType: type,
    ...data
  });
};
```

### Custom Events

```typescript
// Define custom events
interface CustomEvents {
  'lesson_completed': {
    lessonId: string;
    timeSpent: number;
    score: number;
  };
  'achievement_earned': {
    achievementId: string;
    userId: string;
    timestamp: Date;
  };
  'social_share': {
    platform: string;
    contentId: string;
    contentType: string;
  };
}

// Type-safe event tracking
const trackCustomEvent = <K extends keyof CustomEvents>(
  event: K,
  data: CustomEvents[K]
) => {
  trackEvent(event, data);
};
```
