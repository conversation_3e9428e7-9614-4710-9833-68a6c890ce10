'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, RotateCcw, Settings, HelpCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

import { GuidedTourSystem } from './GuidedTourSystem';
import { 
  getTourConfiguration, 
  getTourProgress, 
  resetTourProgress, 
  shouldShowTour 
} from './TourConfigurations';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface TourManagerProps {
  className?: string;
  enableAutoStart?: boolean;
  userVisitCount?: number;
  onTourComplete?: (tourId: string) => void;
  onTourSkip?: (tourId: string) => void;
}

export function TourManager({
  className,
  enableAutoStart = true,
  userVisitCount = 1,
  onTourComplete,
  onTourSkip
}: TourManagerProps) {
  const [activeTour, setActiveTour] = useState<string | null>(null);
  const [showTourMenu, setShowTourMenu] = useState(false);
  const [tourStats, setTourStats] = useState({
    platform: getTourProgress('platform-tour'),
    quickstart: getTourProgress('quickstart-tour'),
    advanced: getTourProgress('advanced-tour')
  });

  // Check for auto-start tours on mount
  useEffect(() => {
    if (!enableAutoStart) return;

    // Check if platform tour should auto-start
    if (shouldShowTour('platform-tour', userVisitCount)) {
      setActiveTour('platform-tour');
    }
  }, [enableAutoStart, userVisitCount]);

  // Update tour stats when tours complete
  useEffect(() => {
    const updateStats = () => {
      setTourStats({
        platform: getTourProgress('platform-tour'),
        quickstart: getTourProgress('quickstart-tour'),
        advanced: getTourProgress('advanced-tour')
      });
    };

    // Listen for storage changes (tour completions)
    window.addEventListener('storage', updateStats);
    return () => window.removeEventListener('storage', updateStats);
  }, []);

  const startTour = (tourType: 'platform' | 'quickstart' | 'advanced') => {
    setActiveTour(tourType);
    setShowTourMenu(false);
  };

  const handleTourComplete = (tourId: string) => {
    setActiveTour(null);
    setTourStats(prev => ({
      ...prev,
      [tourId.replace('-tour', '')]: getTourProgress(tourId)
    }));
    
    if (onTourComplete) {
      onTourComplete(tourId);
    }
  };

  const handleTourSkip = (tourId: string) => {
    setActiveTour(null);
    setTourStats(prev => ({
      ...prev,
      [tourId.replace('-tour', '')]: getTourProgress(tourId)
    }));
    
    if (onTourSkip) {
      onTourSkip(tourId);
    }
  };

  const resetTour = (tourType: 'platform' | 'quickstart' | 'advanced') => {
    const tourId = `${tourType}-tour`;
    resetTourProgress(tourId);
    setTourStats(prev => ({
      ...prev,
      [tourType]: getTourProgress(tourId)
    }));
  };

  const getTourStatusIcon = (progress: any) => {
    if (progress?.completed) return '✅';
    if (progress?.skipped) return '⏭️';
    if (progress?.hasStarted) return '⏸️';
    return '▶️';
  };

  const getTourStatusText = (progress: any) => {
    if (progress?.completed) return 'Completed';
    if (progress?.skipped) return 'Skipped';
    if (progress?.hasStarted) return 'In Progress';
    return 'Not Started';
  };

  return (
    <div className={cn('relative', className)}>
      {/* Active Tour */}
      {activeTour && (
        <GuidedTourSystem
          {...getTourConfiguration(activeTour as any)}
          enableGamification={true}
          enableAccessibility={true}
          enableSound={false}
          onTourComplete={() => handleTourComplete(`${activeTour}-tour`)}
          onTourSkip={() => handleTourSkip(`${activeTour}-tour`)}
        />
      )}

      {/* Tour Menu Button */}
      <div className="fixed bottom-4 right-20 z-40">
        <EnhancedButton
          onClick={() => setShowTourMenu(!showTourMenu)}
          className={cn(
            'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg',
            'hover:shadow-xl transition-all duration-300',
            showTourMenu && 'ring-2 ring-purple-400 ring-offset-2'
          )}
          touchTarget
          aria-label="Tour options"
        >
          <HelpCircle className="w-5 h-5" />
        </EnhancedButton>

        {/* Tour Menu */}
        <AnimatePresence>
          {showTourMenu && (
            <motion.div
              className="absolute bottom-full right-0 mb-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden"
              initial={{ opacity: 0, scale: 0.9, y: 10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 10 }}
              transition={{ duration: 0.2 }}
            >
              {/* Header */}
              <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-4">
                <h3 className="font-semibold flex items-center space-x-2">
                  <HelpCircle className="w-5 h-5" />
                  <span>Guided Tours</span>
                </h3>
                <p className="text-purple-100 text-sm mt-1">
                  Learn the platform with interactive tours
                </p>
              </div>

              {/* Tour Options */}
              <div className="p-4 space-y-3">
                {/* Platform Tour */}
                <div className="border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getTourStatusIcon(tourStats.platform)}</span>
                      <div>
                        <h4 className="font-medium text-gray-900">Platform Tour</h4>
                        <p className="text-xs text-gray-500">
                          {getTourStatusText(tourStats.platform)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      {tourStats.platform?.completed && (
                        <button
                          onClick={() => resetTour('platform')}
                          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Reset tour"
                        >
                          <RotateCcw className="w-3 h-3" />
                        </button>
                      )}
                      <EnhancedButton
                        onClick={() => startTour('platform')}
                        size="sm"
                        variant={tourStats.platform?.completed ? "ghost" : "default"}
                        className="text-xs"
                      >
                        {tourStats.platform?.completed ? 'Replay' : 'Start'}
                      </EnhancedButton>
                    </div>
                  </div>
                  <p className="text-xs text-gray-600">
                    Complete overview of all platform features and tools
                  </p>
                </div>

                {/* Quick Start Tour */}
                <div className="border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getTourStatusIcon(tourStats.quickstart)}</span>
                      <div>
                        <h4 className="font-medium text-gray-900">Quick Start</h4>
                        <p className="text-xs text-gray-500">
                          {getTourStatusText(tourStats.quickstart)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      {tourStats.quickstart?.completed && (
                        <button
                          onClick={() => resetTour('quickstart')}
                          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Reset tour"
                        >
                          <RotateCcw className="w-3 h-3" />
                        </button>
                      )}
                      <EnhancedButton
                        onClick={() => startTour('quickstart')}
                        size="sm"
                        variant={tourStats.quickstart?.completed ? "ghost" : "default"}
                        className="text-xs"
                      >
                        {tourStats.quickstart?.completed ? 'Replay' : 'Start'}
                      </EnhancedButton>
                    </div>
                  </div>
                  <p className="text-xs text-gray-600">
                    Get coding in 3 simple steps - perfect for beginners
                  </p>
                </div>

                {/* Advanced Features Tour */}
                <div className="border border-gray-200 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getTourStatusIcon(tourStats.advanced)}</span>
                      <div>
                        <h4 className="font-medium text-gray-900">Advanced Features</h4>
                        <p className="text-xs text-gray-500">
                          {getTourStatusText(tourStats.advanced)}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      {tourStats.advanced?.completed && (
                        <button
                          onClick={() => resetTour('advanced')}
                          className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                          title="Reset tour"
                        >
                          <RotateCcw className="w-3 h-3" />
                        </button>
                      )}
                      <EnhancedButton
                        onClick={() => startTour('advanced')}
                        size="sm"
                        variant={tourStats.advanced?.completed ? "ghost" : "default"}
                        className="text-xs"
                      >
                        {tourStats.advanced?.completed ? 'Replay' : 'Start'}
                      </EnhancedButton>
                    </div>
                  </div>
                  <p className="text-xs text-gray-600">
                    Discover powerful tools for experienced developers
                  </p>
                </div>
              </div>

              {/* Footer */}
              <div className="bg-gray-50 px-4 py-3 border-t border-gray-200">
                <div className="flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    Tours completed: {Object.values(tourStats).filter(s => s?.completed).length}/3
                  </div>
                  <button
                    onClick={() => setShowTourMenu(false)}
                    className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Tour Completion Celebration */}
      <AnimatePresence>
        {Object.values(tourStats).every(s => s?.completed) && (
          <motion.div
            className="fixed top-4 right-4 z-50 bg-gradient-to-r from-green-500 to-emerald-500 text-white p-4 rounded-lg shadow-lg max-w-sm"
            initial={{ opacity: 0, x: 100 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 100 }}
          >
            <div className="flex items-center space-x-2 mb-2">
              <span className="text-2xl">🎉</span>
              <h4 className="font-semibold">Tour Master!</h4>
            </div>
            <p className="text-green-100 text-sm">
              You've completed all guided tours. You're now a platform expert!
            </p>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

// Hook for tour management
export function useTourManager() {
  const [tourStats, setTourStats] = useState({
    platform: getTourProgress('platform-tour'),
    quickstart: getTourProgress('quickstart-tour'),
    advanced: getTourProgress('advanced-tour')
  });

  const refreshStats = () => {
    setTourStats({
      platform: getTourProgress('platform-tour'),
      quickstart: getTourProgress('quickstart-tour'),
      advanced: getTourProgress('advanced-tour')
    });
  };

  const hasCompletedAnyTour = () => {
    return Object.values(tourStats).some(s => s?.completed);
  };

  const hasCompletedAllTours = () => {
    return Object.values(tourStats).every(s => s?.completed);
  };

  const getCompletionPercentage = () => {
    const completed = Object.values(tourStats).filter(s => s?.completed).length;
    return Math.round((completed / 3) * 100);
  };

  return {
    tourStats,
    refreshStats,
    hasCompletedAnyTour,
    hasCompletedAllTours,
    getCompletionPercentage
  };
}
