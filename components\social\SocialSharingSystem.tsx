'use client';

import React, { useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Share2, 
  Twitter, 
  Linkedin, 
  MessageSquare, 
  Copy, 
  Download,
  CheckCircle,
  Trophy,
  Star,
  Zap,
  Target,
  Award,
  TrendingUp,
  Users,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useProgressTracking } from '@/components/progress/ProgressTrackingSystem';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface SocialSharingSystemProps {
  className?: string;
  achievement?: {
    id: string;
    title: string;
    description: string;
    icon: React.ComponentType<{ className?: string }>;
    xpReward: number;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
    unlockedAt: Date;
  };
  progressData?: {
    level: number;
    totalXP: number;
    completionPercentage: number;
    streak: number;
    pathName?: string;
  };
  shareType: 'achievement' | 'level_up' | 'streak' | 'progress' | 'completion';
  onShare?: (platform: string, shareData: any) => void;
  showAnalytics?: boolean;
}

interface ShareTemplate {
  platform: 'twitter' | 'linkedin' | 'discord' | 'copy';
  text: string;
  hashtags?: string[];
  url: string;
  image?: string;
}

export function SocialSharingSystem({
  className,
  achievement,
  progressData,
  shareType,
  onShare,
  showAnalytics = true
}: SocialSharingSystemProps) {
  const [isSharing, setIsSharing] = useState(false);
  const [shareSuccess, setShareSuccess] = useState<string | null>(null);
  const [showShareModal, setShowShareModal] = useState(false);
  const [generatedImage, setGeneratedImage] = useState<string | null>(null);
  
  const { userProgress, addXP } = useProgressTracking();
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Generate share templates based on share type
  const generateShareTemplates = useCallback((): ShareTemplate[] => {
    const baseUrl = 'https://solidity-learning.dev';
    const platformName = 'Solidity Learning Platform';
    
    switch (shareType) {
      case 'achievement':
        if (!achievement) return [];
        return [
          {
            platform: 'twitter',
            text: `🏆 Just unlocked "${achievement.title}" on ${platformName}! ${achievement.description} (+${achievement.xpReward} XP) 🚀`,
            hashtags: ['Solidity', 'Blockchain', 'Web3', 'SmartContracts', 'Achievement'],
            url: `${baseUrl}/achievements/${achievement.id}`,
            image: generatedImage || undefined
          },
          {
            platform: 'linkedin',
            text: `Excited to share that I just unlocked the "${achievement.title}" achievement on ${platformName}! ${achievement.description}\n\nContinuing my journey in blockchain development and smart contract programming. The hands-on learning approach is really paying off!\n\n#Solidity #Blockchain #Web3 #ProfessionalDevelopment`,
            url: `${baseUrl}/achievements/${achievement.id}`,
            image: generatedImage || undefined
          },
          {
            platform: 'discord',
            text: `🎉 Achievement Unlocked! Just got "${achievement.title}" on ${platformName}! ${achievement.description} (+${achievement.xpReward} XP)\n\nAnyone else learning Solidity? This platform is amazing! 🔥`,
            url: `${baseUrl}/achievements/${achievement.id}`
          },
          {
            platform: 'copy',
            text: `🏆 Achievement Unlocked: "${achievement.title}"\n${achievement.description}\n+${achievement.xpReward} XP earned!\n\nLearning Solidity on ${platformName}\n${baseUrl}`,
            url: `${baseUrl}/achievements/${achievement.id}`
          }
        ];

      case 'level_up':
        if (!progressData) return [];
        return [
          {
            platform: 'twitter',
            text: `🎯 Level ${progressData.level} achieved on ${platformName}! ${progressData.totalXP} XP and counting! 💪`,
            hashtags: ['Solidity', 'LevelUp', 'Blockchain', 'Web3', 'Progress'],
            url: baseUrl
          },
          {
            platform: 'linkedin',
            text: `Milestone achieved! Just reached Level ${progressData.level} on ${platformName} with ${progressData.totalXP} XP!\n\nThe structured learning path and hands-on coding experience is accelerating my blockchain development skills.\n\n#Solidity #Blockchain #ContinuousLearning #Web3`,
            url: baseUrl
          },
          {
            platform: 'discord',
            text: `🚀 Level ${progressData.level} unlocked! ${progressData.totalXP} XP on ${platformName}! The grind continues! 💯`,
            url: baseUrl
          },
          {
            platform: 'copy',
            text: `🎯 Level ${progressData.level} Achieved!\n${progressData.totalXP} XP earned on ${platformName}\n\nJoin me in learning Solidity: ${baseUrl}`,
            url: baseUrl
          }
        ];

      case 'streak':
        if (!progressData) return [];
        return [
          {
            platform: 'twitter',
            text: `🔥 ${progressData.streak} day learning streak on ${platformName}! Consistency is key in mastering Solidity! 📚`,
            hashtags: ['Solidity', 'Streak', 'Consistency', 'Blockchain', 'DailyLearning'],
            url: baseUrl
          },
          {
            platform: 'linkedin',
            text: `Celebrating ${progressData.streak} consecutive days of learning on ${platformName}! \n\nConsistent daily practice is proving to be the most effective way to master smart contract development.\n\n#Solidity #ConsistentLearning #Blockchain #ProfessionalGrowth`,
            url: baseUrl
          },
          {
            platform: 'discord',
            text: `🔥 ${progressData.streak} day streak! Who else is keeping up their daily Solidity practice? Let's keep the momentum going! 💪`,
            url: baseUrl
          },
          {
            platform: 'copy',
            text: `🔥 ${progressData.streak} Day Learning Streak!\nConsistent progress on ${platformName}\n\nStart your Solidity journey: ${baseUrl}`,
            url: baseUrl
          }
        ];

      case 'progress':
        if (!progressData) return [];
        return [
          {
            platform: 'twitter',
            text: `📈 ${progressData.completionPercentage}% complete on my Solidity learning journey! Level ${progressData.level} with ${progressData.totalXP} XP on ${platformName}! 🚀`,
            hashtags: ['Solidity', 'Progress', 'Blockchain', 'Learning', 'Web3'],
            url: baseUrl
          },
          {
            platform: 'linkedin',
            text: `Progress update: ${progressData.completionPercentage}% through my Solidity learning path on ${platformName}!\n\nCurrently at Level ${progressData.level} with ${progressData.totalXP} XP. The interactive coding environment and real-time feedback are game-changers for learning blockchain development.\n\n#Solidity #Blockchain #LearningJourney #Web3`,
            url: baseUrl
          },
          {
            platform: 'discord',
            text: `📊 Progress check: ${progressData.completionPercentage}% done with Solidity fundamentals! Level ${progressData.level}, ${progressData.totalXP} XP! Anyone else grinding through the courses? 💻`,
            url: baseUrl
          },
          {
            platform: 'copy',
            text: `📈 Learning Progress: ${progressData.completionPercentage}%\nLevel ${progressData.level} | ${progressData.totalXP} XP\n\nLearning Solidity on ${platformName}\n${baseUrl}`,
            url: baseUrl
          }
        ];

      case 'completion':
        if (!progressData) return [];
        return [
          {
            platform: 'twitter',
            text: `🎓 Course completed! Just finished "${progressData.pathName || 'Solidity Fundamentals'}" on ${platformName}! Ready for the next challenge! 💪`,
            hashtags: ['Solidity', 'CourseComplete', 'Blockchain', 'Achievement', 'Web3'],
            url: baseUrl
          },
          {
            platform: 'linkedin',
            text: `Excited to announce that I've completed "${progressData.pathName || 'Solidity Fundamentals'}" on ${platformName}!\n\nThe comprehensive curriculum and hands-on projects have significantly enhanced my smart contract development skills. Looking forward to applying these skills in real-world blockchain projects.\n\n#Solidity #CourseCompletion #Blockchain #ProfessionalDevelopment`,
            url: baseUrl
          },
          {
            platform: 'discord',
            text: `🎉 Just completed "${progressData.pathName || 'Solidity Fundamentals'}" on ${platformName}! Time to build some DApps! Who wants to collaborate? 🤝`,
            url: baseUrl
          },
          {
            platform: 'copy',
            text: `🎓 Course Completed!\n"${progressData.pathName || 'Solidity Fundamentals'}"\n\nCompleted on ${platformName}\nStart your journey: ${baseUrl}`,
            url: baseUrl
          }
        ];

      default:
        return [];
    }
  }, [shareType, achievement, progressData, generatedImage]);

  // Generate achievement card image
  const generateAchievementCard = useCallback(async () => {
    if (!canvasRef.current || !achievement) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    canvas.width = 800;
    canvas.height = 400;

    // Background gradient
    const gradient = ctx.createLinearGradient(0, 0, 800, 400);
    gradient.addColorStop(0, '#1e3a8a'); // blue-800
    gradient.addColorStop(1, '#7c3aed'); // violet-600
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, 800, 400);

    // Platform branding
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 24px Arial';
    ctx.fillText('Solidity Learning Platform', 40, 50);

    // Achievement title
    ctx.fillStyle = '#fbbf24'; // amber-400
    ctx.font = 'bold 36px Arial';
    ctx.fillText(achievement.title, 40, 120);

    // Achievement description
    ctx.fillStyle = '#e5e7eb'; // gray-200
    ctx.font = '20px Arial';
    const words = achievement.description.split(' ');
    let line = '';
    let y = 160;
    for (let n = 0; n < words.length; n++) {
      const testLine = line + words[n] + ' ';
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      if (testWidth > 720 && n > 0) {
        ctx.fillText(line, 40, y);
        line = words[n] + ' ';
        y += 30;
      } else {
        line = testLine;
      }
    }
    ctx.fillText(line, 40, y);

    // XP reward
    ctx.fillStyle = '#10b981'; // emerald-500
    ctx.font = 'bold 28px Arial';
    ctx.fillText(`+${achievement.xpReward} XP`, 40, y + 60);

    // User stats
    if (progressData) {
      ctx.fillStyle = '#ffffff';
      ctx.font = '18px Arial';
      ctx.fillText(`Level ${progressData.level} • ${progressData.totalXP} Total XP`, 40, y + 100);
    }

    // Rarity badge
    const rarityColors = {
      common: '#6b7280',
      rare: '#3b82f6',
      epic: '#8b5cf6',
      legendary: '#f59e0b'
    };
    ctx.fillStyle = rarityColors[achievement.rarity];
    ctx.fillRect(600, 40, 160, 40);
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Arial';
    ctx.fillText(achievement.rarity.toUpperCase(), 620, 65);

    // Convert to blob and create URL
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        setGeneratedImage(url);
      }
    }, 'image/png');
  }, [achievement, progressData]);

  // Generate image when component mounts or data changes
  React.useEffect(() => {
    if (achievement && shareType === 'achievement') {
      generateAchievementCard();
    }
  }, [achievement, shareType, generateAchievementCard]);

  const handleShare = async (platform: string, template: ShareTemplate) => {
    setIsSharing(true);
    
    try {
      switch (platform) {
        case 'twitter':
          const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(template.text)}&url=${encodeURIComponent(template.url)}${template.hashtags ? `&hashtags=${template.hashtags.join(',')}` : ''}`;
          window.open(twitterUrl, '_blank', 'width=600,height=400');
          break;

        case 'linkedin':
          const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(template.url)}&summary=${encodeURIComponent(template.text)}`;
          window.open(linkedinUrl, '_blank', 'width=600,height=400');
          break;

        case 'discord':
          // For Discord, we'll copy the text and provide instructions
          await navigator.clipboard.writeText(`${template.text}\n${template.url}`);
          setShareSuccess('discord');
          break;

        case 'copy':
          await navigator.clipboard.writeText(template.text);
          setShareSuccess('copy');
          break;
      }

      // Award XP for sharing
      addXP(10, `Shared ${shareType} on ${platform}`);
      
      // Track social influencer achievement
      const shareCount = parseInt(localStorage.getItem('social_shares') || '0') + 1;
      localStorage.setItem('social_shares', shareCount.toString());
      
      if (shareCount >= 5) {
        // Trigger social influencer achievement
        window.dispatchEvent(new CustomEvent('achievement_unlocked', {
          detail: { achievementId: 'social-influencer' }
        }));
      }

      if (onShare) {
        onShare(platform, { template, shareType, achievement, progressData });
      }

      setTimeout(() => setShareSuccess(null), 3000);
    } catch (error) {
      console.error('Share failed:', error);
    } finally {
      setIsSharing(false);
    }
  };

  const shareTemplates = generateShareTemplates();

  if (shareTemplates.length === 0) {
    return null;
  }

  return (
    <div className={cn('space-y-4', className)}>
      {/* Hidden canvas for image generation */}
      <canvas ref={canvasRef} className="hidden" />

      {/* Quick Share Buttons */}
      <div className="flex items-center space-x-3">
        <span className="text-sm text-gray-400">Share:</span>
        {shareTemplates.slice(0, 3).map((template) => {
          const Icon = template.platform === 'twitter' ? Twitter :
                      template.platform === 'linkedin' ? Linkedin :
                      template.platform === 'discord' ? MessageSquare : Copy;
          
          return (
            <motion.button
              key={template.platform}
              onClick={() => handleShare(template.platform, template)}
              disabled={isSharing}
              className={cn(
                'flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200',
                template.platform === 'twitter' && 'bg-blue-600 hover:bg-blue-700 text-white',
                template.platform === 'linkedin' && 'bg-blue-700 hover:bg-blue-800 text-white',
                template.platform === 'discord' && 'bg-indigo-600 hover:bg-indigo-700 text-white',
                template.platform === 'copy' && 'bg-gray-600 hover:bg-gray-700 text-white',
                isSharing && 'opacity-50 cursor-not-allowed'
              )}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Icon className="w-4 h-4" />
              {shareSuccess === template.platform && (
                <CheckCircle className="w-4 h-4 text-green-400" />
              )}
            </motion.button>
          );
        })}
        
        <EnhancedButton
          onClick={() => setShowShareModal(true)}
          variant="ghost"
          size="sm"
          className="text-gray-400 hover:text-white"
        >
          <Share2 className="w-4 h-4 mr-1" />
          More
        </EnhancedButton>
      </div>

      {/* Success Messages */}
      <AnimatePresence>
        {shareSuccess && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="flex items-center space-x-2 text-green-400 text-sm"
          >
            <CheckCircle className="w-4 h-4" />
            <span>
              {shareSuccess === 'copy' ? 'Copied to clipboard!' :
               shareSuccess === 'discord' ? 'Copied for Discord! Paste in your server.' :
               'Shared successfully! +10 XP earned'}
            </span>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Detailed Share Modal */}
      <AnimatePresence>
        {showShareModal && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowShareModal(false)}
          >
            <motion.div
              className="bg-gray-900 rounded-lg shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-y-auto border border-gray-700"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="p-6 border-b border-gray-700">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-semibold text-white">Share Your Achievement</h3>
                  <button
                    onClick={() => setShowShareModal(false)}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    ×
                  </button>
                </div>
                {achievement && (
                  <p className="text-gray-400 mt-2">
                    Share "{achievement.title}" with your network
                  </p>
                )}
              </div>

              {/* Share Options */}
              <div className="p-6 space-y-6">
                {shareTemplates.map((template) => {
                  const Icon = template.platform === 'twitter' ? Twitter :
                              template.platform === 'linkedin' ? Linkedin :
                              template.platform === 'discord' ? MessageSquare : Copy;
                  
                  return (
                    <div key={template.platform} className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <Icon className="w-5 h-5 text-gray-400" />
                        <span className="font-medium text-white capitalize">
                          {template.platform === 'copy' ? 'Copy Text' : template.platform}
                        </span>
                      </div>
                      
                      <div className="bg-gray-800 rounded-lg p-4 border border-gray-700">
                        <p className="text-gray-300 text-sm leading-relaxed">
                          {template.text}
                        </p>
                        {template.hashtags && (
                          <div className="mt-2 flex flex-wrap gap-1">
                            {template.hashtags.map((tag) => (
                              <span key={tag} className="text-blue-400 text-xs">
                                #{tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                      
                      <EnhancedButton
                        onClick={() => handleShare(template.platform, template)}
                        disabled={isSharing}
                        className="w-full"
                        touchTarget
                      >
                        {isSharing ? 'Sharing...' : `Share on ${template.platform === 'copy' ? 'Clipboard' : template.platform}`}
                        <ExternalLink className="w-4 h-4 ml-2" />
                      </EnhancedButton>
                    </div>
                  );
                })}
              </div>

              {/* Generated Image Preview */}
              {generatedImage && (
                <div className="p-6 border-t border-gray-700">
                  <h4 className="font-medium text-white mb-3">Achievement Card</h4>
                  <img 
                    src={generatedImage} 
                    alt="Achievement card"
                    className="w-full rounded-lg border border-gray-700"
                  />
                  <EnhancedButton
                    onClick={() => {
                      const link = document.createElement('a');
                      link.download = `achievement-${achievement?.id || 'card'}.png`;
                      link.href = generatedImage;
                      link.click();
                    }}
                    variant="ghost"
                    size="sm"
                    className="mt-3"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download Image
                  </EnhancedButton>
                </div>
              )}

              {/* Analytics */}
              {showAnalytics && (
                <div className="p-6 border-t border-gray-700 bg-gray-800/50">
                  <h4 className="font-medium text-white mb-3">Sharing Impact</h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Total Shares:</span>
                      <span className="text-white ml-2">{localStorage.getItem('social_shares') || '0'}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">XP from Sharing:</span>
                      <span className="text-green-400 ml-2">+{(parseInt(localStorage.getItem('social_shares') || '0') * 10)} XP</span>
                    </div>
                  </div>
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
