'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

// Import engagement components
import { RealTimeNotificationSystem } from './RealTimeNotificationSystem';
import { AnimatedStatsCounter } from './AnimatedStatsCounter';
import { AIAssistantWidget } from './AIAssistantWidget';
import { InteractiveFAQ } from './InteractiveFAQ';

// Import conversion components
import { ExitIntentSystem } from '@/components/conversion/ExitIntentSystem';
import { ScrollTriggeredCTAs } from '@/components/conversion/ScrollTriggeredCTAs';
import { UrgencyScarcitySystem } from '@/components/conversion/UrgencyScarcitySystem';

// Import social components
import { SocialSharing } from '@/components/social/SocialSharing';
import { AchievementCelebration } from '@/components/gamification/AchievementCelebration';

// Import analytics
import { useEnhancedTracking } from '@/components/analytics/AnalyticsIntegration';

interface EngagementConfig {
  // Real-time features
  enableNotifications: boolean;
  enableStatsCounter: boolean;
  enableAIAssistant: boolean;
  enableFAQ: boolean;
  
  // Conversion optimization
  enableExitIntent: boolean;
  enableScrollCTAs: boolean;
  enableUrgencyTimers: boolean;
  
  // Social features
  enableSocialSharing: boolean;
  enableAchievements: boolean;
  
  // Analytics
  enableAnalytics: boolean;
  trackEngagement: boolean;
  
  // Customization
  theme: 'light' | 'dark' | 'auto';
  position: 'bottom-right' | 'bottom-left';
  animationLevel: 'minimal' | 'standard' | 'enhanced';
}

interface UserContext {
  id?: string;
  name?: string;
  email?: string;
  currentPage: string;
  progress?: {
    currentLesson?: string;
    completedLessons?: number;
    currentCourse?: string;
    totalXP?: number;
    level?: number;
  };
  preferences?: {
    reducedMotion?: boolean;
    notifications?: boolean;
    sounds?: boolean;
  };
}

interface ComprehensiveEngagementSystemProps {
  config: EngagementConfig;
  userContext: UserContext;
  className?: string;
  onEngagementEvent?: (event: string, data: any) => void;
}

const defaultConfig: EngagementConfig = {
  enableNotifications: true,
  enableStatsCounter: true,
  enableAIAssistant: true,
  enableFAQ: true,
  enableExitIntent: true,
  enableScrollCTAs: true,
  enableUrgencyTimers: true,
  enableSocialSharing: true,
  enableAchievements: true,
  enableAnalytics: true,
  trackEngagement: true,
  theme: 'auto',
  position: 'bottom-right',
  animationLevel: 'standard'
};

export function ComprehensiveEngagementSystem({
  config = defaultConfig,
  userContext,
  className,
  onEngagementEvent
}: ComprehensiveEngagementSystemProps) {
  const router = useRouter();
  const [isVisible, setIsVisible] = useState(true);
  const [engagementScore, setEngagementScore] = useState(0);
  const [sessionData, setSessionData] = useState({
    startTime: Date.now(),
    pageViews: 1,
    interactions: 0,
    timeOnPage: 0
  });

  // Analytics tracking
  const { trackEvent, trackUserEngagement } = useEnhancedTracking({
    enableAnalytics: config.enableAnalytics,
    trackingLevel: 'enhanced'
  });

  // Track engagement events
  const handleEngagementEvent = useCallback((event: string, data: any = {}) => {
    if (config.trackEngagement) {
      trackEvent({
        event: 'engagement_interaction',
        category: 'engagement',
        action: event,
        customParameters: {
          ...data,
          page: userContext.currentPage,
          userId: userContext.id,
          sessionId: sessionData.startTime.toString(),
          timestamp: Date.now()
        }
      });
    }

    // Update engagement score
    setEngagementScore(prev => Math.min(prev + 10, 100));
    
    // Update session interactions
    setSessionData(prev => ({
      ...prev,
      interactions: prev.interactions + 1
    }));

    // Call external handler
    onEngagementEvent?.(event, data);
  }, [config.trackEngagement, trackEvent, userContext, sessionData.startTime, onEngagementEvent]);

  // Track time on page
  useEffect(() => {
    const interval = setInterval(() => {
      setSessionData(prev => ({
        ...prev,
        timeOnPage: Date.now() - prev.startTime
      }));
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Track user engagement metrics
  useEffect(() => {
    if (config.enableAnalytics) {
      trackUserEngagement({
        engagementScore,
        timeOnPage: sessionData.timeOnPage,
        interactions: sessionData.interactions,
        page: userContext.currentPage
      });
    }
  }, [engagementScore, sessionData, userContext.currentPage, config.enableAnalytics, trackUserEngagement]);

  // Handle exit intent
  const handleExitIntent = useCallback(() => {
    handleEngagementEvent('exit_intent_triggered', {
      timeOnPage: sessionData.timeOnPage,
      interactions: sessionData.interactions
    });
  }, [handleEngagementEvent, sessionData]);

  // Handle scroll milestones
  const handleScrollMilestone = useCallback((percentage: number) => {
    handleEngagementEvent('scroll_milestone', {
      scrollPercentage: percentage,
      timeOnPage: sessionData.timeOnPage
    });
  }, [handleEngagementEvent, sessionData.timeOnPage]);

  // Handle CTA interactions
  const handleCTAClick = useCallback((ctaType: string, ctaText: string) => {
    handleEngagementEvent('cta_click', {
      ctaType,
      ctaText,
      engagementScore
    });
  }, [handleEngagementEvent, engagementScore]);

  // Handle AI assistant interactions
  const handleAIInteraction = useCallback((interactionType: string, data: any) => {
    handleEngagementEvent('ai_assistant_interaction', {
      interactionType,
      ...data
    });
  }, [handleEngagementEvent]);

  // Handle FAQ interactions
  const handleFAQInteraction = useCallback((action: string, questionId?: string) => {
    handleEngagementEvent('faq_interaction', {
      action,
      questionId
    });
  }, [handleEngagementEvent]);

  // Respect user preferences
  const shouldShowAnimations = userContext.preferences?.reducedMotion !== true && 
                              config.animationLevel !== 'minimal';
  const shouldPlaySounds = userContext.preferences?.sounds !== false;
  const shouldShowNotifications = userContext.preferences?.notifications !== false && 
                                 config.enableNotifications;

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 left-4 z-50 p-2 bg-blue-500 text-white rounded-full shadow-lg hover:bg-blue-600 transition-colors"
        aria-label="Show engagement features"
      >
        Show Features
      </button>
    );
  }

  return (
    <div className={cn('relative', className)}>
      {/* Exit Intent System */}
      {config.enableExitIntent && (
        <ExitIntentSystem
          config={{
            enabled: true,
            sensitivity: 20,
            delay: 1000,
            maxTriggers: 1,
            onExitIntent: handleExitIntent
          }}
        />
      )}

      {/* Scroll-triggered CTAs */}
      {config.enableScrollCTAs && (
        <ScrollTriggeredCTAs
          onScrollMilestone={handleScrollMilestone}
          onCTAClick={handleCTAClick}
          userContext={userContext}
        />
      )}

      {/* Real-time Notifications */}
      {shouldShowNotifications && (
        <RealTimeNotificationSystem
          position={config.position}
          enableSound={shouldPlaySounds}
          className="z-40"
        />
      )}

      {/* AI Assistant Widget */}
      {config.enableAIAssistant && (
        <AIAssistantWidget
          position={config.position === 'bottom-right' ? 'bottom-left' : 'bottom-right'}
          enableContextualHelp={true}
          currentPage={userContext.currentPage}
          userProgress={userContext.progress}
        />
      )}

      {/* Urgency/Scarcity Elements */}
      {config.enableUrgencyTimers && (
        <UrgencyScarcitySystem
          onInteraction={(type, data) => handleEngagementEvent('urgency_interaction', { type, ...data })}
        />
      )}

      {/* Achievement Celebrations */}
      {config.enableAchievements && (
        <AchievementCelebration
          onCelebration={(achievement) => handleEngagementEvent('achievement_celebration', { achievement })}
          enableConfetti={shouldShowAnimations}
          enableSound={shouldPlaySounds}
        />
      )}

      {/* Social Sharing */}
      {config.enableSocialSharing && userContext.progress && (
        <SocialSharing
          onShare={(platform, content) => handleEngagementEvent('social_share', { platform, content })}
          userProgress={userContext.progress}
        />
      )}

      {/* Engagement Score Indicator (for development/testing) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-4 right-4 z-50 p-2 bg-black/80 text-white rounded text-xs">
          Engagement: {engagementScore}%
          <br />
          Time: {Math.floor(sessionData.timeOnPage / 1000)}s
          <br />
          Interactions: {sessionData.interactions}
        </div>
      )}

      {/* Hide/Show Toggle */}
      <button
        onClick={() => setIsVisible(false)}
        className="fixed top-4 left-4 z-50 p-2 bg-gray-500/20 text-gray-600 rounded-full hover:bg-gray-500/30 transition-colors"
        aria-label="Hide engagement features"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>
    </div>
  );
}

// Hook for managing engagement configuration
export function useEngagementConfig(initialConfig?: Partial<EngagementConfig>) {
  const [config, setConfig] = useState<EngagementConfig>({
    ...defaultConfig,
    ...initialConfig
  });

  const updateConfig = useCallback((updates: Partial<EngagementConfig>) => {
    setConfig(prev => ({ ...prev, ...updates }));
  }, []);

  const resetConfig = useCallback(() => {
    setConfig(defaultConfig);
  }, []);

  return {
    config,
    updateConfig,
    resetConfig
  };
}

// Hook for tracking engagement metrics
export function useEngagementMetrics() {
  const [metrics, setMetrics] = useState({
    totalEngagementEvents: 0,
    averageTimeOnPage: 0,
    conversionRate: 0,
    bounceRate: 0,
    interactionRate: 0
  });

  const updateMetrics = useCallback((newMetrics: Partial<typeof metrics>) => {
    setMetrics(prev => ({ ...prev, ...newMetrics }));
  }, []);

  return {
    metrics,
    updateMetrics
  };
}

export default ComprehensiveEngagementSystem;
