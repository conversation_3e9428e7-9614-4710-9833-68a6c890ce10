'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Trophy, 
  Star, 
  Flame, 
  BarChart3, 
  ChevronDown,
  ChevronUp,
  Target,
  Clock,
  Award
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useProgressTracking } from './ProgressTrackingSystem';
import { ProgressDashboard } from './ProgressDashboard';

interface HeroProgressIndicatorProps {
  className?: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  variant?: 'minimal' | 'compact' | 'detailed';
  showExpandedView?: boolean;
  autoHide?: boolean;
}

export function HeroProgressIndicator({
  className,
  position = 'top-right',
  variant = 'compact',
  showExpandedView = true,
  autoHide = false
}: HeroProgressIndicatorProps) {
  const {
    userProgress,
    getProgressPercentage,
    getRecommendations
  } = useProgressTracking();

  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(!autoHide);

  const overallProgress = getProgressPercentage();
  const recommendations = getRecommendations();
  const nextRecommendation = recommendations[0];

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'top-right':
        return 'top-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      default:
        return 'top-4 right-4';
    }
  };

  const getLevelProgress = () => {
    const currentLevelXP = (userProgress.level - 1) * 100 + 50;
    const nextLevelXP = userProgress.level * 100 + 50;
    const progressInLevel = userProgress.totalXP - currentLevelXP;
    const levelRange = nextLevelXP - currentLevelXP;
    return Math.max(0, Math.min(100, (progressInLevel / levelRange) * 100));
  };

  const getXPForNextLevel = () => {
    const currentLevelXP = (userProgress.level - 1) * 100 + 50;
    const nextLevelXP = userProgress.level * 100 + 50;
    return nextLevelXP - userProgress.totalXP;
  };

  if (!isVisible) {
    return (
      <motion.button
        className={cn(
          'fixed z-40 p-2 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-colors',
          getPositionClasses(),
          className
        )}
        onClick={() => setIsVisible(true)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        <BarChart3 className="w-5 h-5" />
      </motion.button>
    );
  }

  if (variant === 'minimal') {
    return (
      <motion.div
        className={cn(
          'fixed z-40 bg-white/10 backdrop-blur-md rounded-lg p-3 border border-white/20 shadow-lg',
          getPositionClasses(),
          className
        )}
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        whileHover={{ scale: 1.05 }}
      >
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <span className="text-white text-sm font-bold">{userProgress.level}</span>
          </div>
          <div>
            <div className="text-sm font-medium text-white">{userProgress.totalXP} XP</div>
            <div className="text-xs text-gray-400">{overallProgress}% complete</div>
          </div>
          {userProgress.currentStreak > 0 && (
            <div className="flex items-center space-x-1">
              <Flame className="w-4 h-4 text-orange-400" />
              <span className="text-sm text-orange-400">{userProgress.currentStreak}</span>
            </div>
          )}
          {autoHide && (
            <button
              onClick={() => setIsVisible(false)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              ×
            </button>
          )}
        </div>
      </motion.div>
    );
  }

  return (
    <div className={cn('fixed z-40', getPositionClasses(), className)}>
      {/* Compact Progress Card */}
      <motion.div
        className="bg-white/10 backdrop-blur-md rounded-lg border border-white/20 shadow-lg overflow-hidden"
        initial={{ opacity: 0, scale: 0.8, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* Header */}
        <div className="p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-semibold text-white">Your Progress</h3>
            <div className="flex items-center space-x-2">
              {autoHide && (
                <button
                  onClick={() => setIsVisible(false)}
                  className="text-gray-400 hover:text-white transition-colors text-xs"
                >
                  ×
                </button>
              )}
              {showExpandedView && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="text-gray-400 hover:text-white transition-colors"
                >
                  {isExpanded ? (
                    <ChevronUp className="w-4 h-4" />
                  ) : (
                    <ChevronDown className="w-4 h-4" />
                  )}
                </button>
              )}
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-3 mb-3">
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <Trophy className="w-4 h-4 text-blue-400" />
                <span className="text-lg font-bold text-white">{userProgress.level}</span>
              </div>
              <div className="text-xs text-gray-400">Level</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 mb-1">
                <Star className="w-4 h-4 text-yellow-400" />
                <span className="text-lg font-bold text-white">{userProgress.totalXP}</span>
              </div>
              <div className="text-xs text-gray-400">XP</div>
            </div>
          </div>

          {/* Level Progress */}
          <div className="mb-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs text-gray-400">Level Progress</span>
              <span className="text-xs text-gray-400">{Math.round(getLevelProgress())}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <motion.div
                className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${getLevelProgress()}%` }}
                transition={{ duration: 1, ease: "easeOut" }}
              />
            </div>
            <div className="text-xs text-gray-400 mt-1">
              {getXPForNextLevel()} XP to level {userProgress.level + 1}
            </div>
          </div>

          {/* Overall Progress */}
          <div className="mb-3">
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs text-gray-400">Overall Progress</span>
              <span className="text-xs text-gray-400">{overallProgress}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <motion.div
                className="h-full bg-gradient-to-r from-green-500 to-blue-500 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${overallProgress}%` }}
                transition={{ duration: 1, delay: 0.2, ease: "easeOut" }}
              />
            </div>
          </div>

          {/* Streak */}
          {userProgress.currentStreak > 0 && (
            <div className="flex items-center justify-center space-x-2 p-2 bg-orange-500/20 rounded-lg border border-orange-500/30">
              <Flame className="w-4 h-4 text-orange-400" />
              <span className="text-sm text-orange-400 font-medium">
                {userProgress.currentStreak} day streak
              </span>
            </div>
          )}

          {/* Next Recommendation */}
          {nextRecommendation && !isExpanded && (
            <div className="mt-3 p-3 bg-blue-500/20 rounded-lg border border-blue-500/30">
              <div className="flex items-center space-x-2 mb-1">
                <Target className="w-3 h-3 text-blue-400" />
                <span className="text-xs text-blue-400 font-medium">Next Goal</span>
              </div>
              <div className="text-sm text-white font-medium">{nextRecommendation.title}</div>
              <div className="text-xs text-gray-400">{nextRecommendation.description}</div>
              <div className="flex items-center justify-between mt-2">
                <span className="text-xs text-yellow-400">+{nextRecommendation.xpReward} XP</span>
                <div className={cn(
                  'px-2 py-1 rounded text-xs',
                  nextRecommendation.difficulty === 'beginner' ? 'bg-green-500/20 text-green-400' :
                  nextRecommendation.difficulty === 'intermediate' ? 'bg-yellow-500/20 text-yellow-400' :
                  'bg-red-500/20 text-red-400'
                )}>
                  {nextRecommendation.difficulty}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Expanded Dashboard */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="border-t border-white/20 max-h-96 overflow-y-auto"
            >
              <div className="p-4">
                <ProgressDashboard
                  variant="compact"
                  showRecommendations={true}
                  showStats={false}
                  showPaths={true}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
}

// Navigation Progress Indicator
export function NavigationProgressIndicator({ className }: { className?: string }) {
  const { userProgress, getProgressPercentage } = useProgressTracking();
  const overallProgress = getProgressPercentage();

  return (
    <div className={cn('flex items-center space-x-3', className)}>
      <div className="flex items-center space-x-2">
        <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
          <span className="text-white text-xs font-bold">{userProgress.level}</span>
        </div>
        <div className="hidden sm:block">
          <div className="text-sm font-medium text-white">{userProgress.totalXP} XP</div>
        </div>
      </div>
      
      <div className="w-20 bg-gray-700 rounded-full h-2 hidden md:block">
        <motion.div
          className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
          initial={{ width: 0 }}
          animate={{ width: `${overallProgress}%` }}
          transition={{ duration: 1 }}
        />
      </div>
      
      {userProgress.currentStreak > 0 && (
        <div className="flex items-center space-x-1">
          <Flame className="w-4 h-4 text-orange-400" />
          <span className="text-sm text-orange-400 hidden sm:inline">{userProgress.currentStreak}</span>
        </div>
      )}
    </div>
  );
}
