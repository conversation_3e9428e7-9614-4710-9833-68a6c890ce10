# Examples and Use Cases - Solidity Learning Platform

## Table of Contents

1. [Basic Analytics Integration](#basic-analytics-integration)
2. [Advanced A/B Testing Implementation](#advanced-ab-testing-implementation)
3. [Custom Performance Monitoring](#custom-performance-monitoring)
4. [Feedback Widget Customization](#feedback-widget-customization)
5. [Progressive Loading Implementation](#progressive-loading-implementation)
6. [PWA Offline Functionality](#pwa-offline-functionality)
7. [Real-world Integration Examples](#real-world-integration-examples)

## Basic Analytics Integration

### Simple Setup

```tsx
// app/layout.tsx - Basic analytics integration
import { ComprehensiveAnalyticsSystem } from '@/components/analytics/ComprehensiveAnalyticsSystem';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <ComprehensiveAnalyticsSystem
          config={{
            googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
            enableHeatmaps: false,
            enableSessionRecordings: false,
            enableUserConsent: true,
            privacyCompliant: true,
            trackingLevel: 'standard'
          }}
          userId="user-123"
          userTraits={{
            plan: 'free',
            signupDate: '2024-01-01',
            experience: 'beginner'
          }}
        >
          {children}
        </ComprehensiveAnalyticsSystem>
      </body>
    </html>
  );
}
```

### Custom Event Tracking

```tsx
// components/LessonCard.tsx - Track lesson interactions
import { useComprehensiveAnalytics } from '@/hooks/useAnalytics';

export function LessonCard({ lesson }: { lesson: Lesson }) {
  const { trackEvent, trackUserJourney } = useComprehensiveAnalytics();

  const handleLessonStart = () => {
    // Track lesson start event
    trackEvent('lesson_started', {
      lessonId: lesson.id,
      lessonTitle: lesson.title,
      difficulty: lesson.difficulty,
      category: lesson.category
    });

    // Track user journey step
    trackUserJourney('lesson_engagement', {
      step: 'lesson_start',
      lessonId: lesson.id,
      timestamp: new Date()
    });
  };

  const handleLessonComplete = () => {
    trackEvent('lesson_completed', {
      lessonId: lesson.id,
      timeSpent: calculateTimeSpent(),
      score: getUserScore(),
      completionRate: getCompletionRate()
    });
  };

  return (
    <div className="lesson-card">
      <h3>{lesson.title}</h3>
      <button onClick={handleLessonStart}>
        Start Lesson
      </button>
    </div>
  );
}
```

### Conversion Tracking

```tsx
// components/TrialSignup.tsx - Track conversion funnel
import { useComprehensiveAnalytics } from '@/hooks/useAnalytics';

export function TrialSignup() {
  const { trackConversion, trackEvent } = useComprehensiveAnalytics();

  const handleTrialSignup = async (formData: FormData) => {
    try {
      // Track conversion step
      trackConversion('trial_signup_started', 0);

      const response = await fetch('/api/signup', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        // Track successful conversion
        trackConversion('trial_signup_completed', 29.99);
        
        // Track detailed event
        trackEvent('trial_signup_success', {
          plan: formData.get('plan'),
          source: 'hero_cta',
          variant: 'primary'
        });
      }
    } catch (error) {
      trackEvent('trial_signup_error', {
        error: error.message,
        step: 'form_submission'
      });
    }
  };

  return (
    <form onSubmit={handleTrialSignup}>
      {/* Form fields */}
      <button type="submit">Start Free Trial</button>
    </form>
  );
}
```

## Advanced A/B Testing Implementation

### A/B Test Setup

```tsx
// components/HeroSection.tsx - A/B test hero messaging
import { useABTesting } from '@/hooks/useABTesting';

export function HeroSection() {
  const { getVariant, trackConversion } = useABTesting();
  
  // Get variant for hero messaging test
  const heroVariant = getVariant('hero_messaging_test', {
    control: 'Learn Solidity Programming',
    variant_a: 'Master Blockchain Development',
    variant_b: 'Build DeFi Applications'
  });

  const handleCTAClick = () => {
    trackConversion('hero_cta_click', {
      variant: heroVariant.name,
      testId: 'hero_messaging_test'
    });
  };

  return (
    <section className="hero">
      <h1>{heroVariant.value}</h1>
      <button onClick={handleCTAClick}>
        Get Started
      </button>
    </section>
  );
}
```

### Multivariate Testing

```tsx
// components/PricingSection.tsx - Test multiple elements
import { useABTesting } from '@/hooks/useABTesting';

export function PricingSection() {
  const { getVariant } = useABTesting();
  
  // Test pricing display format
  const pricingFormat = getVariant('pricing_format_test', {
    control: 'monthly',
    variant_a: 'annual_discount',
    variant_b: 'lifetime_deal'
  });
  
  // Test CTA button text
  const ctaText = getVariant('pricing_cta_test', {
    control: 'Start Free Trial',
    variant_a: 'Begin Learning Today',
    variant_b: 'Join 10,000+ Students'
  });

  return (
    <section className="pricing">
      <PricingCard format={pricingFormat.value} />
      <button className="cta-button">
        {ctaText.value}
      </button>
    </section>
  );
}
```

### Statistical Significance Tracking

```tsx
// hooks/useABTestResults.ts - Monitor test results
import { useComprehensiveAnalytics } from './useAnalytics';

export function useABTestResults(testId: string) {
  const { trackEvent } = useComprehensiveAnalytics();
  
  const checkSignificance = async () => {
    const response = await fetch(`/api/ab-tests/${testId}/results`);
    const results = await response.json();
    
    if (results.isSignificant) {
      trackEvent('ab_test_significant', {
        testId,
        winningVariant: results.winner,
        confidenceLevel: results.confidence,
        sampleSize: results.sampleSize
      });
    }
    
    return results;
  };

  return { checkSignificance };
}
```

## Custom Performance Monitoring

### Core Web Vitals Tracking

```tsx
// hooks/usePerformanceMonitoring.ts - Custom performance tracking
import { useCallback, useEffect } from 'react';

export function usePerformanceMonitoring() {
  const trackMetric = useCallback((name: string, value: number) => {
    // Send to analytics
    if (window.gtag) {
      window.gtag('event', 'performance_metric', {
        metric_name: name,
        metric_value: value,
        custom_parameter_1: window.location.pathname
      });
    }
  }, []);

  useEffect(() => {
    // Track Core Web Vitals
    import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
      getCLS(metric => trackMetric('cls', metric.value));
      getFID(metric => trackMetric('fid', metric.value));
      getFCP(metric => trackMetric('fcp', metric.value));
      getLCP(metric => trackMetric('lcp', metric.value));
      getTTFB(metric => trackMetric('ttfb', metric.value));
    });
  }, [trackMetric]);

  return { trackMetric };
}
```

### Custom Performance Metrics

```tsx
// components/VideoPlayer.tsx - Track video performance
import { usePerformanceMonitoring } from '@/hooks/usePerformanceMonitoring';

export function VideoPlayer({ src }: { src: string }) {
  const { trackMetric } = usePerformanceMonitoring();
  
  const handleVideoLoad = (event: Event) => {
    const video = event.target as HTMLVideoElement;
    const loadTime = performance.now() - startTime;
    
    trackMetric('video_load_time', loadTime);
    trackMetric('video_duration', video.duration);
  };

  const handleVideoPlay = () => {
    trackMetric('video_play_start', performance.now());
  };

  const handleVideoEnd = () => {
    trackMetric('video_completion', 1);
  };

  return (
    <video
      src={src}
      onLoadedData={handleVideoLoad}
      onPlay={handleVideoPlay}
      onEnded={handleVideoEnd}
      controls
    />
  );
}
```

### API Performance Monitoring

```tsx
// utils/apiMonitoring.ts - Monitor API performance
export async function monitoredFetch(url: string, options?: RequestInit) {
  const startTime = performance.now();
  
  try {
    const response = await fetch(url, options);
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Track successful API call
    trackPerformanceMetric('api_response_time', duration);
    trackPerformanceMetric('api_success_rate', 1);
    
    return response;
  } catch (error) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    // Track failed API call
    trackPerformanceMetric('api_response_time', duration);
    trackPerformanceMetric('api_error_rate', 1);
    
    throw error;
  }
}
```

## Feedback Widget Customization

### Custom Feedback Widget

```tsx
// components/CustomFeedbackWidget.tsx - Customized feedback collection
import { useState } from 'react';
import { useComprehensiveAnalytics } from '@/hooks/useAnalytics';

interface FeedbackData {
  rating: number;
  category: string;
  message: string;
  page: string;
}

export function CustomFeedbackWidget() {
  const [isOpen, setIsOpen] = useState(false);
  const [feedback, setFeedback] = useState<Partial<FeedbackData>>({});
  const { trackEvent } = useComprehensiveAnalytics();

  const handleSubmit = async () => {
    try {
      await fetch('/api/feedback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...feedback,
          page: window.location.pathname,
          timestamp: new Date(),
          userAgent: navigator.userAgent
        })
      });

      trackEvent('feedback_submitted', {
        rating: feedback.rating,
        category: feedback.category,
        page: window.location.pathname
      });

      setIsOpen(false);
    } catch (error) {
      trackEvent('feedback_error', {
        error: error.message,
        page: window.location.pathname
      });
    }
  };

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 bg-blue-500 text-white p-3 rounded-full"
        aria-label="Give feedback"
      >
        💬
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg max-w-md w-full">
            <h3>How was your experience?</h3>
            
            {/* Rating */}
            <div className="my-4">
              {[1, 2, 3, 4, 5].map(rating => (
                <button
                  key={rating}
                  onClick={() => setFeedback(prev => ({ ...prev, rating }))}
                  className={`text-2xl ${feedback.rating === rating ? 'text-yellow-500' : 'text-gray-300'}`}
                >
                  ⭐
                </button>
              ))}
            </div>

            {/* Category */}
            <select
              value={feedback.category || ''}
              onChange={(e) => setFeedback(prev => ({ ...prev, category: e.target.value }))}
              className="w-full p-2 border rounded mb-4"
            >
              <option value="">Select category</option>
              <option value="content">Content Quality</option>
              <option value="performance">Site Performance</option>
              <option value="usability">Ease of Use</option>
              <option value="bug">Bug Report</option>
            </select>

            {/* Message */}
            <textarea
              value={feedback.message || ''}
              onChange={(e) => setFeedback(prev => ({ ...prev, message: e.target.value }))}
              placeholder="Tell us more..."
              className="w-full p-2 border rounded mb-4"
              rows={3}
            />

            <div className="flex gap-2">
              <button onClick={handleSubmit} className="bg-blue-500 text-white px-4 py-2 rounded">
                Submit
              </button>
              <button onClick={() => setIsOpen(false)} className="bg-gray-300 px-4 py-2 rounded">
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
```

### Context-Aware Feedback

```tsx
// components/ContextualFeedback.tsx - Show feedback based on user behavior
import { useEffect, useState } from 'react';
import { useComprehensiveAnalytics } from '@/hooks/useAnalytics';

export function ContextualFeedback() {
  const [shouldShow, setShouldShow] = useState(false);
  const { trackEvent } = useComprehensiveAnalytics();

  useEffect(() => {
    // Show feedback after user completes a lesson
    const handleLessonComplete = () => {
      setTimeout(() => {
        setShouldShow(true);
        trackEvent('feedback_prompt_shown', {
          trigger: 'lesson_completion',
          page: window.location.pathname
        });
      }, 2000);
    };

    // Show feedback if user seems frustrated (multiple back/forward actions)
    let navigationCount = 0;
    const handleNavigation = () => {
      navigationCount++;
      if (navigationCount > 3) {
        setShouldShow(true);
        trackEvent('feedback_prompt_shown', {
          trigger: 'navigation_frustration',
          navigationCount
        });
      }
    };

    window.addEventListener('lesson_completed', handleLessonComplete);
    window.addEventListener('popstate', handleNavigation);

    return () => {
      window.removeEventListener('lesson_completed', handleLessonComplete);
      window.removeEventListener('popstate', handleNavigation);
    };
  }, [trackEvent]);

  if (!shouldShow) return null;

  return (
    <div className="fixed bottom-4 left-4 bg-white border shadow-lg rounded-lg p-4 max-w-sm">
      <h4 className="font-semibold mb-2">Quick feedback?</h4>
      <p className="text-sm text-gray-600 mb-3">
        How was your experience with this lesson?
      </p>
      <div className="flex gap-2">
        <button
          onClick={() => {
            trackEvent('quick_feedback', { rating: 'positive' });
            setShouldShow(false);
          }}
          className="bg-green-500 text-white px-3 py-1 rounded text-sm"
        >
          👍 Good
        </button>
        <button
          onClick={() => {
            trackEvent('quick_feedback', { rating: 'negative' });
            setShouldShow(false);
          }}
          className="bg-red-500 text-white px-3 py-1 rounded text-sm"
        >
          👎 Needs work
        </button>
        <button
          onClick={() => setShouldShow(false)}
          className="text-gray-500 px-3 py-1 text-sm"
        >
          ✕
        </button>
      </div>
    </div>
  );
}
```

## Progressive Loading Implementation

### Skeleton Loading States

```tsx
// components/SkeletonLoader.tsx - Reusable skeleton components
import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  animate?: boolean;
}

export function Skeleton({ className, animate = true }: SkeletonProps) {
  return (
    <div
      className={cn(
        'bg-gray-200 dark:bg-gray-700 rounded',
        animate && 'animate-pulse',
        className
      )}
      role="status"
      aria-label="Loading content"
    />
  );
}

export function LessonCardSkeleton() {
  return (
    <div className="border rounded-lg p-4 space-y-3">
      <Skeleton className="h-4 w-3/4" />
      <Skeleton className="h-3 w-1/2" />
      <Skeleton className="h-32 w-full" />
      <div className="flex gap-2">
        <Skeleton className="h-8 w-20" />
        <Skeleton className="h-8 w-16" />
      </div>
    </div>
  );
}
```

### Progressive Enhancement

```tsx
// components/ProgressiveVideoPlayer.tsx - Progressive video loading
import { useState, useEffect } from 'react';
import { Skeleton } from './SkeletonLoader';

interface ProgressiveVideoPlayerProps {
  src: string;
  poster?: string;
  title: string;
}

export function ProgressiveVideoPlayer({ src, poster, title }: ProgressiveVideoPlayerProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  return (
    <div className="relative">
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <Skeleton className="w-full h-full" />
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2" />
              <p className="text-sm text-gray-600">Loading video...</p>
            </div>
          </div>
        </div>
      )}
      
      <video
        src={src}
        poster={poster}
        controls
        className={cn('w-full', isLoading && 'opacity-0')}
        onLoadStart={() => setIsLoading(true)}
        onCanPlay={() => setIsLoading(false)}
        onError={() => {
          setIsLoading(false);
          setHasError(true);
        }}
        aria-label={title}
      />
      
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="text-center">
            <p className="text-red-600 mb-2">Failed to load video</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-500 text-white px-4 py-2 rounded"
            >
              Retry
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
```

## PWA Offline Functionality

### Offline Content Caching

```tsx
// hooks/useOfflineLearning.ts - Offline lesson management
import { useState, useEffect, useCallback } from 'react';

interface CachedLesson {
  id: string;
  title: string;
  content: string;
  cachedAt: Date;
}

export function useOfflineLearning() {
  const [cachedLessons, setCachedLessons] = useState<CachedLesson[]>([]);
  const [isOffline, setIsOffline] = useState(!navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOffline(false);
    const handleOffline = () => setIsOffline(true);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const cacheLesson = useCallback(async (lesson: Lesson) => {
    try {
      // Cache in IndexedDB
      const db = await openDB();
      await db.put('lessons', {
        id: lesson.id,
        title: lesson.title,
        content: lesson.content,
        cachedAt: new Date()
      });

      setCachedLessons(prev => [...prev, lesson]);
    } catch (error) {
      console.error('Failed to cache lesson:', error);
    }
  }, []);

  const getCachedLesson = useCallback(async (lessonId: string) => {
    try {
      const db = await openDB();
      return await db.get('lessons', lessonId);
    } catch (error) {
      console.error('Failed to get cached lesson:', error);
      return null;
    }
  }, []);

  return {
    cachedLessons,
    isOffline,
    cacheLesson,
    getCachedLesson
  };
}

// Utility function to open IndexedDB
async function openDB() {
  return new Promise<IDBDatabase>((resolve, reject) => {
    const request = indexedDB.open('SolidityLearning', 1);
    
    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);
    
    request.onupgradeneeded = () => {
      const db = request.result;
      if (!db.objectStoreNames.contains('lessons')) {
        db.createObjectStore('lessons', { keyPath: 'id' });
      }
    };
  });
}
```

### Background Sync

```tsx
// utils/backgroundSync.ts - Sync data when online
export class BackgroundSyncManager {
  private syncQueue: any[] = [];

  constructor() {
    this.setupEventListeners();
  }

  private setupEventListeners() {
    window.addEventListener('online', () => {
      this.processSyncQueue();
    });

    // Register service worker sync
    if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      navigator.serviceWorker.ready.then(registration => {
        registration.sync.register('background-sync');
      });
    }
  }

  addToSyncQueue(data: any) {
    this.syncQueue.push({
      ...data,
      timestamp: new Date(),
      id: Math.random().toString(36).substr(2, 9)
    });

    // Store in localStorage for persistence
    localStorage.setItem('syncQueue', JSON.stringify(this.syncQueue));
  }

  private async processSyncQueue() {
    if (!navigator.onLine || this.syncQueue.length === 0) return;

    const queue = [...this.syncQueue];
    this.syncQueue = [];

    for (const item of queue) {
      try {
        await fetch('/api/sync', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(item)
        });
      } catch (error) {
        // Re-add to queue if failed
        this.syncQueue.push(item);
      }
    }

    localStorage.setItem('syncQueue', JSON.stringify(this.syncQueue));
  }
}

export const backgroundSync = new BackgroundSyncManager();
```

## Real-world Integration Examples

### Complete Learning Platform Integration

```tsx
// app/learn/[lessonId]/page.tsx - Complete lesson page with all features
import { ComprehensiveIntegrationSystem } from '@/components/integration/ComprehensiveIntegrationSystem';
import { LessonViewer } from '@/components/LessonViewer';
import { ProgressTracker } from '@/components/ProgressTracker';
import { CustomFeedbackWidget } from '@/components/CustomFeedbackWidget';

export default function LessonPage({ params }: { params: { lessonId: string } }) {
  return (
    <ComprehensiveIntegrationSystem
      config={{
        enableEngagement: true,
        enableAnalytics: true,
        enableConversion: true,
        enableContent: true,
        enablePerformanceMonitoring: true,
        enablePWA: true,
        analyticsConfig: {
          googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID,
          hotjarId: process.env.NEXT_PUBLIC_HOTJAR_ID,
          enableHeatmaps: true,
          enableSessionRecordings: true,
          privacyCompliant: true,
          trackingLevel: 'enhanced'
        }
      }}
      userContext={{
        currentPage: `/learn/${params.lessonId}`,
        sessionId: generateSessionId()
      }}
    >
      <main className="container mx-auto px-4 py-8">
        <ProgressTracker lessonId={params.lessonId} />
        <LessonViewer lessonId={params.lessonId} />
        <CustomFeedbackWidget />
      </main>
    </ComprehensiveIntegrationSystem>
  );
}
```

This comprehensive examples guide provides practical, tested implementations that developers can use immediately to integrate the analytics and performance monitoring system into their applications.
