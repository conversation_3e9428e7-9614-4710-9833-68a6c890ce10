name: Build and Test Next.js Application

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

# Sets permissions for the workflow
permissions:
  contents: read
  pull-requests: write

# Allow only one concurrent build, skipping runs queued between the run in-progress and latest queued.
concurrency:
  group: "build-${{ github.ref }}"
  cancel-in-progress: true

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Verify environment variables
      env:
        GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
      run: |
        echo "Checking environment variables..."
        echo "GEMINI_API_KEY is set: $([[ -n "$GEMINI_API_KEY" ]] && echo "Yes" || echo "No")"
        echo "GEMINI_API_KEY length: ${#GEMINI_API_KEY}"

    - name: Build
      env:
        GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY }}
        API_KEY: ${{ secrets.GEMINI_API_KEY }}
        NODE_ENV: production
        NEXT_PUBLIC_APP_URL: https://ezekaj.github.io/learning_sol
        NEXT_PUBLIC_APP_NAME: "Solidity Learning Platform"
        DATABASE_URL: "postgresql://localhost:5432/dummy"
        NEXTAUTH_URL: https://ezekaj.github.io/learning_sol
        NEXTAUTH_SECRET: "build-only-nextauth-secret-32-chars-minimum-required-for-validation"
      run: npm run build

    - name: Clean build cache
      run: |
        echo "🧹 Cleaning webpack cache to reduce build size..."
        rm -rf ./.next/cache
        echo "✅ Cache cleaned successfully"

    - name: Check for large files in build
      run: |
        echo "🔍 Checking for large files in build output (excluding cache)..."
        find ./.next -type f -size +50M -not -path "*/.next/cache/*" -exec ls -lh {} \; | while read line; do
          echo "❌ Large file found: $line"
          exit 1
        done
        echo "✅ No large files found in deployable build output"

    - name: Verify build output
      run: |
        echo "📊 Build output summary:"
        du -sh ./.next
        echo "📁 Build output structure:"
        find ./.next -type f -name "*.js" -o -name "*.css" -o -name "*.html" | head -20

    - name: Run type check
      run: npm run type-check

    - name: Run linting
      run: npm run lint

    - name: Deployment Notice
      run: |
        echo "🚀 Build completed successfully!"
        echo ""
        echo "⚠️  Note: This application now uses server-side functionality and cannot be deployed to GitHub Pages."
        echo ""
        echo "🌐 Recommended deployment platforms:"
        echo "   • Vercel: https://vercel.com (recommended for Next.js)"
        echo "   • Netlify: https://netlify.com"
        echo "   • Railway: https://railway.app"
        echo "   • Render: https://render.com"
        echo ""
        echo "📖 To deploy to Vercel:"
        echo "   1. Connect your GitHub repository to Vercel"
        echo "   2. Set environment variables in Vercel dashboard"
        echo "   3. Deploy automatically on push to main branch"
