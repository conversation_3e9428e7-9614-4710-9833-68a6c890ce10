// Hooks barrel export
// Centralized access to all custom React hooks

// Core hooks
export * from './useAuth';
export * from './useLocalStorage';
export * from './useSessionStorage';
export * from './useDebounce';
export * from './useThrottle';

// Learning-specific hooks
export * from './useLearningProgress';
export * from './useAchievements';
export * from './useGamification';

// Analytics hooks
export * from './useAnalytics';
export * from './usePerformanceMonitoring';
export * from './useABTesting';

// Performance hooks
export * from './useLoadingState';
export * from './useLazyLoading';
export * from './useServiceWorker';

// Social hooks
export * from './useSocialSharing';
export * from './useCommunity';

// Collaboration hooks
export * from './useCollaboration';
export * from './useRealTimeEditor';

// Navigation hooks
export * from './useNavigation';
export * from './useBreadcrumbs';

// Form hooks
export * from './useForm';
export * from './useValidation';

// API hooks
export * from './useApi';
export * from './useMutation';
export * from './useQuery';

// UI hooks
export * from './useToast';
export * from './useModal';
export * from './useTooltip';

// Accessibility hooks
export * from './useAccessibility';
export * from './useKeyboardNavigation';

// Version info
export const HOOKS_VERSION = '2.0.0';
export const LAST_UPDATED = '2024-12-26';

// Hook categories for organization
export const HOOK_CATEGORIES = {
  CORE: ['useAuth', 'useLocalStorage', 'useSessionStorage', 'useDebounce', 'useThrottle'],
  LEARNING: ['useLearningProgress', 'useAchievements', 'useGamification'],
  ANALYTICS: ['useAnalytics', 'usePerformanceMonitoring', 'useABTesting'],
  PERFORMANCE: ['useLoadingState', 'useLazyLoading', 'useServiceWorker'],
  SOCIAL: ['useSocialSharing', 'useCommunity'],
  COLLABORATION: ['useCollaboration', 'useRealTimeEditor'],
  NAVIGATION: ['useNavigation', 'useBreadcrumbs'],
  FORMS: ['useForm', 'useValidation'],
  API: ['useApi', 'useMutation', 'useQuery'],
  UI: ['useToast', 'useModal', 'useTooltip'],
  ACCESSIBILITY: ['useAccessibility', 'useKeyboardNavigation']
} as const;

// Default export
export default {
  version: HOOKS_VERSION,
  lastUpdated: LAST_UPDATED,
  categories: HOOK_CATEGORIES
};
