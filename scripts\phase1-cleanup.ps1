# Phase 1 Cleanup Script for Windows PowerShell
# Removes confirmed unused files with high confidence
# Estimated bundle reduction: 180-220KB

param(
    [switch]$DryRun = $false,
    [switch]$Rollback = $false
)

# Colors for output
$Colors = @{
    Red = "Red"
    Green = "Green"
    Yellow = "Yellow"
    Blue = "Blue"
    Cyan = "Cyan"
}

function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Header {
    param([string]$Message)
    Write-Host ""
    Write-ColorOutput "============================================================" -Color $Colors.Cyan
    Write-ColorOutput "  $Message" -Color $Colors.Cyan
    Write-ColorOutput "============================================================" -Color $Colors.Cyan
}

function Write-Success {
    param([string]$Message)
    Write-ColorOutput "✅ $Message" -Color $Colors.Green
}

function Write-Warning {
    param([string]$Message)
    Write-ColorOutput "⚠️  $Message" -Color $Colors.Yellow
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "❌ $Message" -Color $Colors.Red
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "ℹ️  $Message" -Color $Colors.Blue
}

# Check if we're in the right directory
if (-not (Test-Path "package.json") -or -not (Test-Path "components")) {
    Write-Error "This script must be run from the project root directory"
    exit 1
}

# Create backup directory
$BackupDir = "./backups/phase1-cleanup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
Write-Info "Creating backup directory: $BackupDir"
New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null

# Function to safely remove file with backup
function Remove-FileWithBackup {
    param(
        [string]$FilePath,
        [string]$Reason
    )
    
    if (Test-Path $FilePath) {
        # Create backup
        $BackupPath = Join-Path $BackupDir $FilePath
        $BackupParent = Split-Path $BackupPath -Parent
        if (-not (Test-Path $BackupParent)) {
            New-Item -ItemType Directory -Path $BackupParent -Force | Out-Null
        }
        
        Copy-Item $FilePath $BackupPath
        
        # Get file size for reporting
        $FileSize = (Get-Item $FilePath).Length
        
        if (-not $DryRun) {
            # Remove the file
            Remove-Item $FilePath -Force
            Write-Success "Removed $FilePath ($FileSize bytes) - $Reason"
        } else {
            Write-Info "Would remove $FilePath ($FileSize bytes) - $Reason"
        }
        
        return $FileSize
    } else {
        Write-Warning "File not found: $FilePath"
        return 0
    }
}

# Main cleanup function
function Start-Cleanup {
    Write-Header "Starting Phase 1 Safe File Cleanup $(if ($DryRun) { '(DRY RUN)' })"
    
    $TotalRemoved = 0
    $TotalSize = 0
    
    # Define files to remove with reasons
    $FilesToRemove = @(
        @{ Path = "components/AchievementsPage.tsx"; Reason = "Duplicate of components/achievements/AchievementsPage.tsx" },
        @{ Path = "components/achievements/AchievementNotificationSystem.tsx"; Reason = "Notification system not integrated" },
        @{ Path = "components/admin/CommunityControls.tsx"; Reason = "Admin feature not implemented" },
        @{ Path = "components/admin/ContentVersionControl.tsx"; Reason = "Version control feature not used" },
        @{ Path = "components/admin/PerformanceDashboard.tsx"; Reason = "Performance dashboard not integrated" },
        @{ Path = "components/admin/SafetyConfirmation.tsx"; Reason = "Safety confirmation not used" },
        @{ Path = "components/admin/UserAnalytics.tsx"; Reason = "User analytics not implemented" },
        @{ Path = "components/ai/AICodeAnalyzer.tsx"; Reason = "AI analyzer not integrated" },
        @{ Path = "components/ai/AIContractGenerator.tsx"; Reason = "Contract generator not used" }
    )
    
    # Remove duplicate components
    Write-Info "Processing identified unused files..."
    
    foreach ($File in $FilesToRemove) {
        $Size = Remove-FileWithBackup -FilePath $File.Path -Reason $File.Reason
        if ($Size -gt 0) {
            $TotalRemoved++
            $TotalSize += $Size
        }
    }
    
    # Clean up empty directories if not dry run
    if (-not $DryRun) {
        Write-Info "Cleaning up empty directories..."
        
        $EmptyDirs = @("components/admin", "components/ai") | Where-Object { 
            Test-Path $_ -and (Get-ChildItem $_ -Force | Measure-Object).Count -eq 0 
        }
        
        foreach ($Dir in $EmptyDirs) {
            Remove-Item $Dir -Force
            Write-Success "Removed empty directory: $Dir"
        }
    }
    
    # Generate cleanup report
    Write-Header "Cleanup Summary"
    Write-Success "Files $(if ($DryRun) { 'would be ' })removed: $TotalRemoved"
    Write-Success "Total size $(if ($DryRun) { 'would be ' })reduced: $([math]::Round($TotalSize / 1KB, 1)) KB"
    
    if (-not $DryRun) {
        Write-Success "Backup created at: $BackupDir"
    }
    
    if ($TotalRemoved -gt 0) {
        Write-Success "Estimated bundle size reduction: 180-220KB"
        Write-Info "Run 'npm run build' to see actual bundle size changes"
    } else {
        Write-Warning "No files were $(if ($DryRun) { 'would be ' })removed - they may have already been cleaned up"
    }
    
    # Next steps
    Write-Header "Next Steps"
    Write-Info "1. Run tests to ensure nothing is broken: npm test"
    Write-Info "2. Build the project to check for errors: npm run build"
    if (-not $DryRun) {
        Write-Info "3. If everything works, you can delete the backup: Remove-Item '$BackupDir' -Recurse -Force"
    }
    Write-Info "4. Consider running the debug cleanup script: node scripts/remove-debug-statements.js"
    
    return @{
        FilesRemoved = $TotalRemoved
        SizeReduced = $TotalSize
        BackupDir = $BackupDir
    }
}

# Rollback function
function Start-Rollback {
    Write-Header "Rolling Back Changes"
    
    $LatestBackup = Get-ChildItem "./backups" -Directory | 
        Where-Object { $_.Name -like "phase1-cleanup-*" } | 
        Sort-Object CreationTime -Descending | 
        Select-Object -First 1
    
    if ($LatestBackup) {
        Write-Info "Restoring files from backup: $($LatestBackup.FullName)"
        Copy-Item "$($LatestBackup.FullName)/*" "./" -Recurse -Force
        Write-Success "Files restored from backup"
        Remove-Item $LatestBackup.FullName -Recurse -Force
        Write-Success "Backup directory cleaned up"
    } else {
        Write-Error "No backup directory found"
        return $false
    }
    
    return $true
}

# Main execution
try {
    if ($Rollback) {
        $Success = Start-Rollback
        exit $(if ($Success) { 0 } else { 1 })
    } else {
        $Result = Start-Cleanup
        
        # Save results for reporting
        $ReportPath = "./reports/phase1-cleanup-results-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
        $Report = @{
            timestamp = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ss.fffZ")
            dryRun = $DryRun
            filesRemoved = $Result.FilesRemoved
            sizeReduced = $Result.SizeReduced
            backupDir = $Result.BackupDir
        } | ConvertTo-Json -Depth 3
        
        if (-not (Test-Path "./reports")) {
            New-Item -ItemType Directory -Path "./reports" -Force | Out-Null
        }
        
        $Report | Out-File -FilePath $ReportPath -Encoding UTF8
        Write-Success "Results saved to: $ReportPath"
        
        exit 0
    }
} catch {
    Write-Error "Cleanup failed: $($_.Exception.Message)"
    exit 1
}
