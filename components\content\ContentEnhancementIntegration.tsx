'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

// Import all content enhancement components
import { InteractiveFAQ } from './InteractiveFAQ';
import { LearningPathPreview } from './LearningPathPreview';
import { CompetitiveComparison } from './CompetitiveComparison';
import { EnhancedBreadcrumbs } from '../navigation/EnhancedBreadcrumbs';
import { LearningOutcomes } from './LearningOutcomes';

interface ContentEnhancementIntegrationProps {
  className?: string;
  variant?: 'full' | 'landing' | 'course' | 'support';
  showFAQ?: boolean;
  showLearningPath?: boolean;
  showComparison?: boolean;
  showOutcomes?: boolean;
  showBreadcrumbs?: boolean;
}

// Main integration component
export function ContentEnhancementIntegration({
  className,
  variant = 'full',
  showFAQ = true,
  showLearningPath = true,
  showComparison = true,
  showOutcomes = true,
  showBreadcrumbs = false
}: ContentEnhancementIntegrationProps) {
  
  if (variant === 'landing') {
    return (
      <div className={cn('space-y-16', className)}>
        {/* Learning Path Preview for landing */}
        {showLearningPath && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <LearningPathPreview showPersonalization={true} />
          </motion.section>
        )}

        {/* Learning Outcomes */}
        {showOutcomes && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <LearningOutcomes />
          </motion.section>
        )}

        {/* Competitive Comparison */}
        {showComparison && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <CompetitiveComparison />
          </motion.section>
        )}

        {/* FAQ Section */}
        {showFAQ && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            <InteractiveFAQ />
          </motion.section>
        )}
      </div>
    );
  }

  if (variant === 'course') {
    return (
      <div className={cn('space-y-8', className)}>
        {/* Breadcrumb Navigation */}
        {showBreadcrumbs && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <EnhancedBreadcrumbs
              items={[
                { id: 'home', label: 'Home', href: '/', isActive: false },
                { id: 'courses', label: 'Courses', href: '/courses', isActive: false },
                { id: 'solidity', label: 'Solidity Fundamentals', href: '/courses/solidity', isActive: false },
                { id: 'lesson', label: 'Variables & Data Types', href: '/courses/solidity/lesson-3', isActive: true }
              ]}
              showProgress={true}
              showKeyboardShortcuts={true}
            />
          </motion.section>
        )}

        {/* Learning Outcomes for course context */}
        {showOutcomes && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <LearningOutcomes />
          </motion.section>
        )}
      </div>
    );
  }

  if (variant === 'support') {
    return (
      <div className={cn('space-y-12', className)}>
        {/* FAQ Section for support */}
        {showFAQ && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <InteractiveFAQ />
          </motion.section>
        )}

        {/* Learning Path for guidance */}
        {showLearningPath && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <LearningPathPreview showPersonalization={false} />
          </motion.section>
        )}
      </div>
    );
  }

  // Full variant - all components
  return (
    <div className={cn('space-y-16', className)}>
      {/* Breadcrumb Navigation */}
      {showBreadcrumbs && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <EnhancedBreadcrumbs
            items={[
              { id: 'home', label: 'Home', href: '/', isActive: false },
              { id: 'platform', label: 'Platform Overview', href: '/platform', isActive: true }
            ]}
            showProgress={true}
            showKeyboardShortcuts={true}
          />
        </motion.section>
      )}

      {/* Learning Path Preview */}
      {showLearningPath && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <LearningPathPreview showPersonalization={true} />
        </motion.section>
      )}

      {/* Learning Outcomes */}
      {showOutcomes && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <LearningOutcomes />
        </motion.section>
      )}

      {/* Competitive Comparison */}
      {showComparison && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <CompetitiveComparison />
        </motion.section>
      )}

      {/* Interactive FAQ */}
      {showFAQ && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          <InteractiveFAQ />
        </motion.section>
      )}
    </div>
  );
}

// Hook for content analytics
export function useContentAnalytics() {
  const [analytics, setAnalytics] = React.useState({
    faqInteractions: 0,
    learningPathViews: 0,
    comparisonInteractions: 0,
    outcomeViews: 0,
    breadcrumbClicks: 0
  });

  const trackInteraction = (component: string, action: string) => {
    setAnalytics(prev => ({
      ...prev,
      [`${component}${action}`]: prev[`${component}${action}` as keyof typeof prev] + 1
    }));

    // Track with analytics service
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'content_interaction', {
        component,
        action,
        timestamp: new Date().toISOString()
      });
    }
  };

  return {
    analytics,
    trackInteraction
  };
}

// Success metrics tracking
export function useContentSuccessMetrics() {
  const [metrics, setMetrics] = React.useState({
    supportTicketReduction: 0,
    courseEnrollmentIncrease: 0,
    conversionRateImprovement: 0,
    navigationIssueReduction: 0,
    userConfidenceBoost: 0
  });

  React.useEffect(() => {
    // Load metrics from localStorage
    const savedMetrics = localStorage.getItem('content_success_metrics');
    if (savedMetrics) {
      setMetrics(JSON.parse(savedMetrics));
    }
  }, []);

  const updateMetric = (metric: keyof typeof metrics, value: number) => {
    setMetrics(prev => {
      const newMetrics = { ...prev, [metric]: value };
      localStorage.setItem('content_success_metrics', JSON.stringify(newMetrics));
      return newMetrics;
    });
  };

  return {
    metrics,
    updateMetric
  };
}

// SEO optimization hook
export function useContentSEO() {
  const generateStructuredData = (component: string) => {
    const baseData = {
      '@context': 'https://schema.org',
      '@type': 'EducationalOrganization',
      'name': 'Solidity Learning Platform',
      'description': 'Comprehensive Solidity and blockchain development education',
      'url': 'https://solidity-learning.dev'
    };

    switch (component) {
      case 'faq':
        return {
          ...baseData,
          '@type': 'FAQPage',
          'mainEntity': [
            {
              '@type': 'Question',
              'name': 'How long does it take to learn Solidity?',
              'acceptedAnswer': {
                '@type': 'Answer',
                'text': 'The time to learn Solidity depends on your programming background. Complete beginners typically need 3-6 months to become proficient, while experienced developers can learn the basics in 4-8 weeks.'
              }
            }
          ]
        };

      case 'course':
        return {
          ...baseData,
          '@type': 'Course',
          'name': '30-Day Solidity Learning Path',
          'description': 'Comprehensive 30-day structured learning path for Solidity development',
          'provider': baseData,
          'educationalLevel': 'Beginner to Advanced',
          'timeRequired': 'P30D'
        };

      default:
        return baseData;
    }
  };

  const injectStructuredData = (component: string) => {
    if (typeof window === 'undefined') return;

    const structuredData = generateStructuredData(component);
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.text = JSON.stringify(structuredData);
    
    // Remove existing structured data for this component
    const existing = document.querySelector(`script[data-component="${component}"]`);
    if (existing) {
      existing.remove();
    }
    
    script.setAttribute('data-component', component);
    document.head.appendChild(script);
  };

  return {
    generateStructuredData,
    injectStructuredData
  };
}

// Export all components for easy access
export {
  InteractiveFAQ,
  LearningPathPreview,
  CompetitiveComparison,
  EnhancedBreadcrumbs,
  LearningOutcomes
};

// Default export
export default ContentEnhancementIntegration;
