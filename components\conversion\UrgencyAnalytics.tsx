'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { AlertCircle, CheckCircle, TrendingUp, Shield } from 'lucide-react';
import { cn } from '@/lib/utils';

interface UrgencyAnalytics {
  timerPerformance: Record<string, { views: number; conversions: number; dismissals: number; rate: number }>;
  scarcityEffectiveness: Record<string, { impressions: number; interactions: number; conversions: number }>;
  ethicalCompliance: Record<string, { isGenuine: boolean; userFeedback: number; reportCount: number }>;
  timeToAction: Record<string, number[]>;
  userSegmentResponse: Record<string, { segment: string; conversionRate: number; avgTimeToAction: number }>;
  abTestResults: Record<string, { variant: string; performance: number }>;
}

interface EthicalGuidelines {
  maxUrgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  requireGenuineScarcity: boolean;
  respectUserPreferences: boolean;
  provideGracePeriods: boolean;
  allowOptOut: boolean;
  transparentCommunication: boolean;
  minimumValueThreshold: number;
}

// Hook for urgency and scarcity analytics
export function useUrgencyScarcityAnalytics() {
  const [analytics, setAnalytics] = useState<UrgencyAnalytics>({
    timerPerformance: {},
    scarcityEffectiveness: {},
    ethicalCompliance: {},
    timeToAction: {},
    userSegmentResponse: {},
    abTestResults: {}
  });

  const [ethicalGuidelines, setEthicalGuidelines] = useState<EthicalGuidelines>({
    maxUrgencyLevel: 'high',
    requireGenuineScarcity: true,
    respectUserPreferences: true,
    provideGracePeriods: true,
    allowOptOut: true,
    transparentCommunication: true,
    minimumValueThreshold: 50
  });

  useEffect(() => {
    // Load analytics from localStorage
    const savedAnalytics = localStorage.getItem('urgency_scarcity_analytics');
    if (savedAnalytics) {
      try {
        setAnalytics(JSON.parse(savedAnalytics));
      } catch (error) {
        console.error('Failed to load urgency scarcity analytics:', error);
      }
    }

    // Load ethical guidelines
    const savedGuidelines = localStorage.getItem('ethical_guidelines');
    if (savedGuidelines) {
      try {
        setEthicalGuidelines(JSON.parse(savedGuidelines));
      } catch (error) {
        console.error('Failed to load ethical guidelines:', error);
      }
    }
  }, []);

  const trackTimerPerformance = useCallback((timerId: string, action: 'view' | 'conversion' | 'dismissal') => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        timerPerformance: {
          ...prev.timerPerformance,
          [timerId]: {
            views: (prev.timerPerformance[timerId]?.views || 0) + (action === 'view' ? 1 : 0),
            conversions: (prev.timerPerformance[timerId]?.conversions || 0) + (action === 'conversion' ? 1 : 0),
            dismissals: (prev.timerPerformance[timerId]?.dismissals || 0) + (action === 'dismissal' ? 1 : 0),
            rate: 0 // Will be calculated
          }
        }
      };

      // Calculate conversion rate
      const timer = newAnalytics.timerPerformance[timerId];
      timer.rate = timer.views > 0 ? (timer.conversions / timer.views) * 100 : 0;

      localStorage.setItem('urgency_scarcity_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });
  }, []);

  const trackScarcityEffectiveness = useCallback((indicatorId: string, action: 'impression' | 'interaction' | 'conversion') => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        scarcityEffectiveness: {
          ...prev.scarcityEffectiveness,
          [indicatorId]: {
            impressions: (prev.scarcityEffectiveness[indicatorId]?.impressions || 0) + (action === 'impression' ? 1 : 0),
            interactions: (prev.scarcityEffectiveness[indicatorId]?.interactions || 0) + (action === 'interaction' ? 1 : 0),
            conversions: (prev.scarcityEffectiveness[indicatorId]?.conversions || 0) + (action === 'conversion' ? 1 : 0)
          }
        }
      };

      localStorage.setItem('urgency_scarcity_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });
  }, []);

  const validateEthicalCompliance = useCallback((elementId: string, elementData: any) => {
    const compliance = {
      isGenuine: true,
      issues: [] as string[],
      score: 100
    };

    // Check if scarcity is genuine
    if (elementData.type === 'scarcity' && !elementData.ethicalGuidelines?.isGenuine) {
      compliance.isGenuine = false;
      compliance.issues.push('Scarcity indicator may not reflect genuine limitations');
      compliance.score -= 30;
    }

    // Check urgency level compliance
    if (elementData.urgencyLevel === 'critical' && ethicalGuidelines.maxUrgencyLevel !== 'critical') {
      compliance.issues.push('Urgency level exceeds ethical guidelines');
      compliance.score -= 25;
    }

    // Check value threshold
    if (elementData.value && elementData.value < ethicalGuidelines.minimumValueThreshold) {
      compliance.issues.push('Offer value below minimum threshold for urgency tactics');
      compliance.score -= 20;
    }

    // Check for grace periods
    if (elementData.type === 'deadline' && !elementData.gracePeriod && ethicalGuidelines.provideGracePeriods) {
      compliance.issues.push('No grace period provided for deadline');
      compliance.score -= 15;
    }

    // Update compliance tracking
    setAnalytics(prev => ({
      ...prev,
      ethicalCompliance: {
        ...prev.ethicalCompliance,
        [elementId]: {
          isGenuine: compliance.isGenuine,
          userFeedback: prev.ethicalCompliance[elementId]?.userFeedback || 0,
          reportCount: prev.ethicalCompliance[elementId]?.reportCount || 0
        }
      }
    }));

    return compliance;
  }, [ethicalGuidelines]);

  const getOptimizationRecommendations = useCallback(() => {
    const recommendations = [];

    // Analyze timer performance
    Object.entries(analytics.timerPerformance).forEach(([timerId, performance]) => {
      if (performance.views > 50) { // Minimum sample size
        if (performance.rate < 5) {
          recommendations.push({
            type: 'timer',
            id: timerId,
            issue: 'Low conversion rate',
            suggestion: 'Consider adjusting urgency level, messaging, or timing',
            priority: 'high'
          });
        } else if (performance.dismissals / performance.views > 0.3) {
          recommendations.push({
            type: 'timer',
            id: timerId,
            issue: 'High dismissal rate',
            suggestion: 'Review messaging for user-friendliness and value proposition',
            priority: 'medium'
          });
        }
      }
    });

    // Analyze scarcity effectiveness
    Object.entries(analytics.scarcityEffectiveness).forEach(([indicatorId, effectiveness]) => {
      if (effectiveness.impressions > 100) {
        const interactionRate = effectiveness.interactions / effectiveness.impressions;
        if (interactionRate < 0.1) {
          recommendations.push({
            type: 'scarcity',
            id: indicatorId,
            issue: 'Low interaction rate',
            suggestion: 'Make scarcity indicator more prominent or compelling',
            priority: 'medium'
          });
        }
      }
    });

    return recommendations;
  }, [analytics]);

  const updateEthicalGuidelines = useCallback((newGuidelines: Partial<EthicalGuidelines>) => {
    const updated = { ...ethicalGuidelines, ...newGuidelines };
    setEthicalGuidelines(updated);
    localStorage.setItem('ethical_guidelines', JSON.stringify(updated));
  }, [ethicalGuidelines]);

  const getConversionInsights = useCallback(() => {
    const totalTimerConversions = Object.values(analytics.timerPerformance)
      .reduce((sum, timer) => sum + timer.conversions, 0);
    
    const totalScarcityConversions = Object.values(analytics.scarcityEffectiveness)
      .reduce((sum, indicator) => sum + indicator.conversions, 0);

    const averageTimerConversionRate = Object.values(analytics.timerPerformance)
      .reduce((sum, timer) => sum + timer.rate, 0) / Object.keys(analytics.timerPerformance).length || 0;

    const ethicalComplianceScore = Object.values(analytics.ethicalCompliance)
      .reduce((sum, compliance) => sum + (compliance.isGenuine ? 100 : 0), 0) / 
      Object.keys(analytics.ethicalCompliance).length || 100;

    return {
      totalTimerConversions,
      totalScarcityConversions,
      averageTimerConversionRate: Math.round(averageTimerConversionRate * 100) / 100,
      ethicalComplianceScore: Math.round(ethicalComplianceScore),
      totalRevenue: (totalTimerConversions + totalScarcityConversions) * 49, // Assuming $49 average
      recommendationsCount: getOptimizationRecommendations().length
    };
  }, [analytics, getOptimizationRecommendations]);

  return {
    analytics,
    ethicalGuidelines,
    trackTimerPerformance,
    trackScarcityEffectiveness,
    validateEthicalCompliance,
    getOptimizationRecommendations,
    updateEthicalGuidelines,
    getConversionInsights
  };
}

// Ethical compliance checker component
export function EthicalComplianceChecker({ 
  elementData, 
  onComplianceUpdate 
}: { 
  elementData: any; 
  onComplianceUpdate?: (compliance: any) => void; 
}) {
  const { validateEthicalCompliance } = useUrgencyScarcityAnalytics();
  const [compliance, setCompliance] = useState<any>(null);

  useEffect(() => {
    const result = validateEthicalCompliance(elementData.id, elementData);
    setCompliance(result);
    onComplianceUpdate?.(result);
  }, [elementData, validateEthicalCompliance, onComplianceUpdate]);

  if (!compliance || compliance.score >= 90) return null;

  return (
    <motion.div
      className="bg-yellow-500/20 border border-yellow-500/50 rounded-lg p-3 mt-2"
      initial={{ opacity: 0, height: 0 }}
      animate={{ opacity: 1, height: 'auto' }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-start space-x-2">
        <AlertCircle className="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0" />
        <div>
          <h4 className="text-yellow-400 font-medium text-sm">Ethical Compliance Alert</h4>
          <div className="text-yellow-300 text-xs mt-1">
            Compliance Score: {compliance.score}/100
          </div>
          {compliance.issues.length > 0 && (
            <ul className="text-yellow-300 text-xs mt-2 space-y-1">
              {compliance.issues.map((issue: string, index: number) => (
                <li key={index}>• {issue}</li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </motion.div>
  );
}

// Smart urgency optimizer
export function SmartUrgencyOptimizer({ 
  userSegment, 
  onOptimizationUpdate 
}: { 
  userSegment: string; 
  onOptimizationUpdate?: (optimization: any) => void; 
}) {
  const { analytics, getOptimizationRecommendations } = useUrgencyScarcityAnalytics();
  const [optimization, setOptimization] = useState<any>(null);

  useEffect(() => {
    const recommendations = getOptimizationRecommendations();
    const userSpecificOptimization = {
      recommendedUrgencyLevel: 'medium',
      optimalTiming: 30000, // 30 seconds
      personalizedMessaging: true,
      scarcityThreshold: 50,
      recommendations
    };

    // Adjust based on user segment
    if (userSegment === 'price_sensitive') {
      userSpecificOptimization.recommendedUrgencyLevel = 'high';
      userSpecificOptimization.scarcityThreshold = 25;
    } else if (userSegment === 'quality_focused') {
      userSpecificOptimization.recommendedUrgencyLevel = 'low';
      userSpecificOptimization.personalizedMessaging = true;
    } else if (userSegment === 'time_constrained') {
      userSpecificOptimization.optimalTiming = 15000; // 15 seconds
      userSpecificOptimization.recommendedUrgencyLevel = 'high';
    }

    setOptimization(userSpecificOptimization);
    onOptimizationUpdate?.(userSpecificOptimization);
  }, [userSegment, analytics, getOptimizationRecommendations, onOptimizationUpdate]);

  return null; // This is a logic-only component
}

// Urgency analytics dashboard component
export function UrgencyAnalyticsDashboard({ className }: { className?: string }) {
  const { analytics, getConversionInsights, getOptimizationRecommendations } = useUrgencyScarcityAnalytics();
  const [insights, setInsights] = useState<any>(null);
  const [recommendations, setRecommendations] = useState<any[]>([]);

  useEffect(() => {
    setInsights(getConversionInsights());
    setRecommendations(getOptimizationRecommendations());
  }, [getConversionInsights, getOptimizationRecommendations]);

  if (!insights) return null;

  return (
    <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6', className)}>
      <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
        <TrendingUp className="w-5 h-5" />
        <span>Urgency & Scarcity Analytics</span>
      </h3>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-white/5 rounded-lg p-4">
          <div className="text-2xl font-bold text-blue-400">{insights.totalTimerConversions}</div>
          <div className="text-sm text-gray-400">Timer Conversions</div>
        </div>
        <div className="bg-white/5 rounded-lg p-4">
          <div className="text-2xl font-bold text-green-400">{insights.averageTimerConversionRate}%</div>
          <div className="text-sm text-gray-400">Avg Conversion Rate</div>
        </div>
        <div className="bg-white/5 rounded-lg p-4">
          <div className="text-2xl font-bold text-purple-400">{insights.ethicalComplianceScore}%</div>
          <div className="text-sm text-gray-400">Ethical Compliance</div>
        </div>
      </div>

      {/* Recommendations */}
      {recommendations.length > 0 && (
        <div>
          <h4 className="font-medium text-white mb-3 flex items-center space-x-2">
            <Shield className="w-4 h-4" />
            <span>Optimization Recommendations</span>
          </h4>
          <div className="space-y-2">
            {recommendations.slice(0, 3).map((rec, index) => (
              <div key={index} className="bg-white/5 rounded-lg p-3">
                <div className="flex items-start justify-between">
                  <div>
                    <div className="font-medium text-white text-sm">{rec.issue}</div>
                    <div className="text-gray-300 text-xs mt-1">{rec.suggestion}</div>
                  </div>
                  <div className={cn(
                    'px-2 py-1 rounded text-xs font-medium',
                    rec.priority === 'high' ? 'bg-red-500/20 text-red-400' :
                    rec.priority === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                    'bg-blue-500/20 text-blue-400'
                  )}>
                    {rec.priority}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
