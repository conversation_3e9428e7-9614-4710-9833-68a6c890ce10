'use client';

import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { ArrowR<PERSON>, Play, Sparkles, Zap, Clock, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { useNotifications } from '@/components/ui/NotificationSystem';

interface CTAButtonProps {
  href: string;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ComponentType<{ className?: string }>;
  loading?: boolean;
  disabled?: boolean;
  onClick?: () => void | Promise<void>;
  className?: string;
  'aria-label'?: string;
}

interface EnhancedCTAButtonsProps {
  className?: string;
  showUrgency?: boolean;
  variant?: 'stacked' | 'horizontal' | 'minimal';
}

/**
 * Enhanced CTA Buttons with glassmorphism styling and async handling
 */
export function EnhancedCTAButtons({ 
  className = '', 
  showUrgency = true,
  variant = 'horizontal' 
}: EnhancedCTAButtonsProps) {
  const [primaryLoading, setPrimaryLoading] = useState(false);
  const [secondaryLoading, setSecondaryLoading] = useState(false);
  const { showSuccess, showInfo } = useNotifications();

  const handlePrimaryAction = async () => {
    setPrimaryLoading(true);
    
    try {
      // Simulate async action (e.g., account creation, analytics tracking)
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      showSuccess({
        title: 'Welcome to SolanaLearn!',
        description: 'Your learning journey begins now. Redirecting to your dashboard...',
        duration: 3000
      });
      
      // Redirect would happen here
      // In a real app, this would use Next.js router
      // router.push('/learn');
      
    } catch (error) {
      console.error('Primary action failed:', error);
    } finally {
      setPrimaryLoading(false);
    }
  };

  const handleSecondaryAction = async () => {
    setSecondaryLoading(true);
    
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      
      showInfo({
        title: 'Demo Loading',
        description: 'Preparing interactive demo experience...',
        duration: 2000
      });
      
      // In a real app, this would use Next.js router
      // router.push('/demo');
      
    } catch (error) {
      console.error('Secondary action failed:', error);
    } finally {
      setSecondaryLoading(false);
    }
  };

  if (variant === 'minimal') {
    return (
      <motion.div
        className={cn('flex items-center space-x-4', className)}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8, duration: 0.6 }}
      >
        <CTAButton
          href="/learn"
          variant="primary"
          size="md"
          icon={ArrowRight}
          onClick={handlePrimaryAction}
          loading={primaryLoading}
          aria-label="Start learning Solidity for free - Begin your blockchain development journey"
        >
          Start Learning
        </CTAButton>
        
        <CTAButton
          href="/demo"
          variant="ghost"
          size="md"
          icon={Play}
          onClick={handleSecondaryAction}
          loading={secondaryLoading}
          aria-label="Watch platform demo - See SolanaLearn features in action"
        >
          Demo
        </CTAButton>
      </motion.div>
    );
  }

  if (variant === 'stacked') {
    return (
      <motion.div
        className={cn('space-y-4 w-full max-w-md mx-auto', className)}
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.6, duration: 0.8 }}
      >
        {showUrgency && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.8, duration: 0.5 }}
            className="text-center"
          >
            <div className="inline-flex items-center space-x-2 px-3 py-1 rounded-full bg-gradient-to-r from-purple-600/20 to-blue-600/20 border border-purple-400/30 backdrop-blur-sm">
              <Clock className="w-3 h-3 text-purple-400" />
              <span className="text-xs font-medium text-purple-300">
                Get started in 30 seconds
              </span>
              <Sparkles className="w-3 h-3 text-purple-400" />
            </div>
          </motion.div>
        )}
        
        <CTAButton
          href="/learn"
          variant="primary"
          size="lg"
          icon={ArrowRight}
          onClick={handlePrimaryAction}
          loading={primaryLoading}
          className="w-full justify-center"
          aria-label="Start learning Solidity for free - Begin your blockchain development journey"
        >
          Get Started in 30 Seconds
        </CTAButton>
        
        <CTAButton
          href="/demo"
          variant="secondary"
          size="lg"
          icon={Play}
          onClick={handleSecondaryAction}
          loading={secondaryLoading}
          className="w-full justify-center"
          aria-label="Watch platform demo - See SolanaLearn features in action"
        >
          Watch Interactive Demo
        </CTAButton>
      </motion.div>
    );
  }

  // Horizontal variant (default)
  return (
    <motion.div
      className={cn(
        'flex flex-col sm:flex-row gap-4 justify-center items-center',
        className
      )}
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.6, duration: 0.8 }}
      role="group"
      aria-label="Primary actions"
    >
      {showUrgency && (
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          className="order-first w-full sm:w-auto text-center sm:absolute sm:-top-8"
        >
          <div className="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-gradient-to-r from-green-600/20 to-blue-600/20 border border-green-400/30 backdrop-blur-sm">
            <Zap className="w-4 h-4 text-green-400" />
            <span className="text-sm font-medium text-green-300">
              Free access • No credit card required
            </span>
            <CheckCircle className="w-4 h-4 text-green-400" />
          </div>
        </motion.div>
      )}
      
      <CTAButton
        href="/learn"
        variant="primary"
        size="lg"
        icon={ArrowRight}
        onClick={handlePrimaryAction}
        loading={primaryLoading}
        aria-label="Start learning Solidity for free - Begin your blockchain development journey"
      >
        Get Started in 30 Seconds
      </CTAButton>
      
      <CTAButton
        href="/demo"
        variant="secondary"
        size="lg"
        icon={Play}
        onClick={handleSecondaryAction}
        loading={secondaryLoading}
        aria-label="Watch platform demo - See SolanaLearn features in action"
      >
        Try Interactive Editor
      </CTAButton>
    </motion.div>
  );
}

/**
 * Individual CTA Button Component
 */
function CTAButton({
  href,
  children,
  variant = 'primary',
  size = 'md',
  icon: Icon,
  loading = false,
  disabled = false,
  onClick,
  className = '',
  'aria-label': ariaLabel,
  ...props
}: CTAButtonProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const buttonRef = useRef<HTMLAnchorElement>(null);

  const sizeClasses = {
    sm: 'px-4 py-2 text-sm min-h-[36px]',
    md: 'px-6 py-3 text-base min-h-[44px]',
    lg: 'px-8 py-4 text-lg min-h-[48px]'
  };

  const variantClasses = {
    primary: cn(
      'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg',
      'hover:from-blue-700 hover:to-purple-700 hover:shadow-xl',
      'focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-transparent',
      'disabled:from-gray-600 disabled:to-gray-700'
    ),
    secondary: cn(
      'glass border border-white/20 text-white backdrop-blur-md bg-white/10',
      'hover:bg-white/20 hover:border-white/30 hover:shadow-lg',
      'focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent',
      'disabled:bg-gray-600/20 disabled:border-gray-600/20'
    ),
    ghost: cn(
      'text-white hover:bg-white/10 border border-transparent',
      'hover:border-white/20 focus:ring-2 focus:ring-white/30',
      'disabled:text-gray-400'
    )
  };

  const handleClick = async (e: React.MouseEvent) => {
    if (disabled || loading) {
      e.preventDefault();
      return;
    }

    if (onClick) {
      e.preventDefault();
      setIsPressed(true);
      
      try {
        await onClick();
      } finally {
        setIsPressed(false);
      }
    }
  };

  return (
    <motion.div
      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
      transition={{ type: "spring", stiffness: 400, damping: 17 }}
    >
      <Link
        ref={buttonRef}
        href={disabled || loading ? '#' : href}
        className={cn(
          'inline-flex items-center justify-center font-medium rounded-lg',
          'transition-all duration-300 ease-out relative overflow-hidden',
          'focus:outline-none disabled:cursor-not-allowed',
          sizeClasses[size],
          variantClasses[variant],
          (disabled || loading) && 'opacity-60 cursor-not-allowed',
          className
        )}
        onClick={handleClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        aria-label={ariaLabel}
        aria-disabled={disabled || loading}
        {...props}
      >
        {/* Ripple effect background */}
        <motion.div
          className="absolute inset-0 bg-white/20 rounded-lg"
          initial={{ scale: 0, opacity: 0 }}
          animate={isPressed ? { scale: 1, opacity: 1 } : { scale: 0, opacity: 0 }}
          transition={{ duration: 0.3 }}
        />
        
        {/* Loading spinner */}
        {loading && (
          <motion.div
            className="absolute inset-0 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.2 }}
          >
            <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
          </motion.div>
        )}
        
        {/* Button content */}
        <motion.div
          className={cn(
            'flex items-center space-x-2 relative z-10',
            loading && 'opacity-0'
          )}
          animate={{ opacity: loading ? 0 : 1 }}
          transition={{ duration: 0.2 }}
        >
          <span>{children}</span>
          {Icon && (
            <motion.div
              animate={{ x: isHovered ? 2 : 0 }}
              transition={{ type: "spring", stiffness: 400, damping: 17 }}
            >
              <Icon className="w-5 h-5" aria-hidden="true" />
            </motion.div>
          )}
        </motion.div>
      </Link>
    </motion.div>
  );
}
