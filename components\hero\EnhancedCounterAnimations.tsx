'use client';

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { motion, useInView, useAnimation } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Users, BookOpen, Code, Trophy, Star, Zap, Target, Award, TrendingUp, Globe } from 'lucide-react';
import { cn } from '@/lib/utils';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface EnhancedMetric {
  id: string;
  label: string;
  value: number;
  targetValue: number;
  suffix: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  gradient: string;
  description: string;
  milestones: number[];
  particleColor: string;
}

interface EnhancedCounterAnimationsProps {
  className?: string;
  triggerThreshold?: number;
  animationDuration?: number;
  staggerDelay?: number;
  enableParticles?: boolean;
  respectReducedMotion?: boolean;
  enableScrollTrigger?: boolean;
  showProgressBars?: boolean;
  enableAccessibility?: boolean;
  variant?: 'default' | 'compact' | 'detailed';
}

/**
 * Enhanced Scroll-Triggered Counter Animations
 * 
 * Extends DynamicUserMetrics with sophisticated animations, particle effects,
 * and staggered counting patterns. Triggers when 30% in viewport using Intersection Observer.
 * 
 * @component
 * @example
 * ```tsx
 * <EnhancedCounterAnimations 
 *   triggerThreshold={0.3}
 *   animationDuration={3000}
 *   enableParticles={true}
 *   respectReducedMotion={true}
 * />
 * ```
 */
export function EnhancedCounterAnimations({
  className = '',
  triggerThreshold = 0.3,
  animationDuration = 3000,
  staggerDelay = 200,
  enableParticles = true,
  respectReducedMotion = true,
  enableScrollTrigger = true,
  showProgressBars = true,
  enableAccessibility = true,
  variant = 'default'
}: EnhancedCounterAnimationsProps) {
  const [metrics, setMetrics] = useState<EnhancedMetric[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [hasAnimated, setHasAnimated] = useState(false);
  const [currentValues, setCurrentValues] = useState<number[]>([]);
  const [progressValues, setProgressValues] = useState<number[]>([]);

  const containerRef = useRef<HTMLDivElement>(null);
  const counterRefs = useRef<(HTMLSpanElement | null)[]>([]);
  const particleContainerRefs = useRef<(HTMLDivElement | null)[]>([]);
  const animationRefs = useRef<gsap.core.Tween[]>([]);
  const progressBarRefs = useRef<(HTMLDivElement | null)[]>([]);

  // Framer Motion hooks for enhanced animations
  const controls = useAnimation();
  const isInView = useInView(containerRef, {
    threshold: triggerThreshold,
    once: true
  });

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false;

  const shouldAnimate = respectReducedMotion ? !prefersReducedMotion : true;

  // Enhanced metrics data with real-time updates
  const getEnhancedMetrics = (): EnhancedMetric[] => {
    const baseTime = Date.now();
    const dailyVariation = Math.sin(baseTime / (1000 * 60 * 60 * 24)) * 50; // Daily variation

    return [
      {
        id: 'learners',
        label: 'Active Learners',
        value: 0,
        targetValue: Math.floor(10247 + dailyVariation),
        suffix: '+',
        icon: Users,
        color: 'text-blue-400',
        gradient: 'from-blue-500 to-cyan-500',
        description: 'Developers currently learning Solidity',
        milestones: [1000, 5000, 10000],
        particleColor: '#3b82f6'
      },
      {
        id: 'courses',
        label: 'Learning Paths',
        value: 0,
        targetValue: 52,
        suffix: '+',
        icon: BookOpen,
        color: 'text-green-400',
        gradient: 'from-green-500 to-emerald-500',
        description: 'Comprehensive learning modules available',
        milestones: [10, 25, 50],
        particleColor: '#10b981'
      },
      {
        id: 'projects',
        label: 'Smart Contracts',
        value: 0,
        targetValue: Math.floor(25834 + dailyVariation * 10),
        suffix: '+',
        icon: Code,
        color: 'text-purple-400',
        gradient: 'from-purple-500 to-pink-500',
        description: 'Smart contracts successfully deployed',
        milestones: [1000, 10000, 25000],
        particleColor: '#8b5cf6'
      },
      {
        id: 'success',
        label: 'Success Rate',
        value: 0,
        targetValue: 94,
        suffix: '%',
        icon: Trophy,
        color: 'text-yellow-400',
        gradient: 'from-yellow-500 to-orange-500',
        description: 'Course completion success rate',
        milestones: [70, 85, 90],
        particleColor: '#f59e0b'
      },
      {
        id: 'community',
        label: 'Community Members',
        value: 0,
        targetValue: Math.floor(47892 + dailyVariation * 20),
        suffix: '+',
        icon: Globe,
        color: 'text-cyan-400',
        gradient: 'from-cyan-500 to-blue-500',
        description: 'Active community members worldwide',
        milestones: [10000, 30000, 45000],
        particleColor: '#06b6d4'
      },
      {
        id: 'growth',
        label: 'Monthly Growth',
        value: 0,
        targetValue: 127,
        suffix: '%',
        icon: TrendingUp,
        color: 'text-emerald-400',
        gradient: 'from-emerald-500 to-green-500',
        description: 'Platform growth rate this month',
        milestones: [50, 100, 120],
        particleColor: '#10b981'
      }
    ];
  };

  // Initialize metrics with dynamic data
  useEffect(() => {
    const dynamicMetrics = getEnhancedMetrics();
    setMetrics(dynamicMetrics);
    setCurrentValues(dynamicMetrics.map(() => 0));
    setProgressValues(dynamicMetrics.map(() => 0));
  }, []);

  // Enhanced scroll trigger with GSAP and Framer Motion
  useEffect(() => {
    if (enableScrollTrigger && isInView && !hasAnimated) {
      setIsVisible(true);
      setHasAnimated(true);
      startEnhancedAnimations();
    }
  }, [isInView, hasAnimated, enableScrollTrigger]);

  // GSAP ScrollTrigger for advanced animations
  useEffect(() => {
    if (!enableScrollTrigger || !containerRef.current) return;

    const ctx = gsap.context(() => {
      gsap.fromTo(
        '.metric-card',
        {
          y: 50,
          opacity: 0,
          scale: 0.9
        },
        {
          y: 0,
          opacity: 1,
          scale: 1,
          duration: 0.8,
          stagger: staggerDelay / 1000,
          ease: 'back.out(1.7)',
          scrollTrigger: {
            trigger: containerRef.current,
            start: 'top 80%',
            end: 'bottom 20%',
            toggleActions: 'play none none reverse'
          }
        }
      );
    }, containerRef);

    return () => ctx.revert();
  }, [enableScrollTrigger, staggerDelay]);

  // Enhanced animation sequence with accessibility
  const startEnhancedAnimations = useCallback(() => {
    if (!shouldAnimate) {
      // Instantly set values for reduced motion
      setMetrics(prev => prev.map(metric => ({
        ...metric,
        value: metric.targetValue
      })));
      setCurrentValues(metrics.map(m => m.targetValue));
      setProgressValues(metrics.map(() => 100));
      return;
    }

    // Animate counters with staggered timing
    metrics.forEach((metric, index) => {
      const delay = index * staggerDelay;

      // Counter animation
      const counterAnimation = gsap.to({}, {
        duration: animationDuration / 1000,
        delay: delay / 1000,
        ease: 'power2.out',
        onUpdate: function() {
          const progress = this.progress();
          const currentValue = Math.floor(metric.targetValue * progress);

          setCurrentValues(prev => {
            const newValues = [...prev];
            newValues[index] = currentValue;
            return newValues;
          });

          // Update progress for progress bars
          if (showProgressBars) {
            setProgressValues(prev => {
              const newProgress = [...prev];
              newProgress[index] = progress * 100;
              return newProgress;
            });
          }

          // Accessibility: Announce milestone achievements
          if (enableAccessibility && metric.milestones) {
            metric.milestones.forEach(milestone => {
              if (currentValue >= milestone && currentValue < milestone + 10) {
                announceToScreenReader(`${metric.label} reached ${milestone}${metric.suffix}`);
              }
            });
          }
        },
        onComplete: () => {
          // Trigger particle effects on completion
          if (enableParticles && particleContainerRefs.current[index]) {
            createParticleEffect(index, metric.particleColor);
          }
        }
      });

      animationRefs.current.push(counterAnimation);
    });
  }, [metrics, shouldAnimate, animationDuration, staggerDelay, showProgressBars, enableAccessibility, enableParticles]);

  // Accessibility function for screen reader announcements
  const announceToScreenReader = (message: string) => {
    if (!enableAccessibility) return;

    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;

    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  };

  // Enhanced particle effect system
  const createParticleEffect = (index: number, color: string) => {
    const container = particleContainerRefs.current[index];
    if (!container) return;

    const particleCount = 12;
    const particles: HTMLDivElement[] = [];

    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-2 h-2 rounded-full pointer-events-none';
      particle.style.backgroundColor = color;
      particle.style.left = '50%';
      particle.style.top = '50%';

      container.appendChild(particle);
      particles.push(particle);

      // Animate particle
      gsap.to(particle, {
        x: (Math.random() - 0.5) * 100,
        y: (Math.random() - 0.5) * 100,
        opacity: 0,
        scale: 0,
        duration: 1.5,
        ease: 'power2.out',
        onComplete: () => {
          if (container.contains(particle)) {
            container.removeChild(particle);
          }
        }
      });
    }
  };

    metrics.forEach((metric, index) => {
      const counter = counterRefs.current[index];
      const particleContainer = particleContainerRefs.current[index];
      
      if (!counter) return;

      // Create realistic counting animation with easing
      const animation = gsap.to(metric, {
        value: metric.targetValue,
        duration: animationDuration / 1000,
        delay: (index * staggerDelay) / 1000,
        ease: "power2.out",
        onUpdate: function() {
          const currentValue = Math.round(this.targets()[0].value);
          counter.textContent = currentValue.toLocaleString();
          
          // Update state for React
          setMetrics(prev => prev.map(m => 
            m.id === metric.id 
              ? { ...m, value: currentValue }
              : m
          ));

          // Trigger particle effects at milestones
          if (enableParticles && particleContainer) {
            metric.milestones.forEach(milestone => {
              if (currentValue === milestone) {
                createParticleEffect(particleContainer, metric.particleColor);
              }
            });
          }
        },
        onComplete: () => {
          // Final particle burst
          if (enableParticles && particleContainer) {
            createParticleEffect(particleContainer, metric.particleColor, true);
          }
        }
      });

      animationRefs.current[index] = animation;

      // Color transition animation
      gsap.fromTo(counter, 
        { color: '#6b7280' },
        {
          color: metric.color.replace('text-', '#'),
          duration: 0.5,
          delay: (index * staggerDelay) / 1000 + 0.5,
          ease: "power2.out"
        }
      );
    });
  };

  // Create particle effect
  const createParticleEffect = (container: HTMLDivElement, color: string, isFinal = false) => {
    const particleCount = isFinal ? 12 : 6;
    
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div');
      particle.className = 'absolute w-2 h-2 rounded-full pointer-events-none';
      particle.style.backgroundColor = color;
      particle.style.left = '50%';
      particle.style.top = '50%';
      particle.style.transform = 'translate(-50%, -50%)';
      
      container.appendChild(particle);

      // Animate particle
      gsap.to(particle, {
        x: (Math.random() - 0.5) * 100,
        y: (Math.random() - 0.5) * 100,
        opacity: 0,
        scale: isFinal ? 1.5 : 1,
        duration: isFinal ? 1.5 : 1,
        ease: "power2.out",
        onComplete: () => {
          if (particle.parentNode) {
            particle.parentNode.removeChild(particle);
          }
        }
      });
    }
  };

  // Cleanup animations on unmount
  useEffect(() => {
    return () => {
      animationRefs.current.forEach(animation => {
        if (animation) animation.kill();
      });
    };
  }, []);

  // Get grid layout based on variant
  const getGridLayout = () => {
    switch (variant) {
      case 'compact':
        return 'grid grid-cols-3 md:grid-cols-6 gap-4';
      case 'detailed':
        return 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8';
      default:
        return 'grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6';
    }
  };

  return (
    <motion.div
      ref={containerRef}
      className={cn(
        getGridLayout(),
        'max-w-7xl mx-auto',
        className
      )}
      initial={{ opacity: 0, y: 50 }}
      animate={controls}
      transition={{ delay: 0.5, duration: 0.8 }}
      role="region"
      aria-label="Platform statistics and metrics"
      aria-live={enableAccessibility ? "polite" : undefined}
    >
      {/* Screen reader summary */}
      {enableAccessibility && (
        <div className="sr-only">
          Platform statistics: {metrics.map(m =>
            `${m.label}: ${currentValues[metrics.indexOf(m)] || 0}${m.suffix}`
          ).join(', ')}
        </div>
      )}

      {metrics.map((metric, index) => (
        <EnhancedMetricCard
          key={metric.id}
          metric={{
            ...metric,
            value: currentValues[index] || 0
          }}
          index={index}
          shouldAnimate={shouldAnimate}
          isVisible={isVisible}
          enableParticles={enableParticles}
          showProgressBar={showProgressBars}
          progressValue={progressValues[index] || 0}
          variant={variant}
          enableAccessibility={enableAccessibility}
          ref={(counterEl, particleEl, progressEl) => {
            counterRefs.current[index] = counterEl;
            particleContainerRefs.current[index] = particleEl;
            progressBarRefs.current[index] = progressEl;
          }}
        />
      ))}
    </motion.div>
  );
}

/**
 * Enhanced Metric Card Component
 */
interface EnhancedMetricCardProps {
  metric: EnhancedMetric;
  index: number;
  shouldAnimate: boolean;
  isVisible: boolean;
  enableParticles: boolean;
  showProgressBar?: boolean;
  progressValue?: number;
  variant?: 'default' | 'compact' | 'detailed';
  enableAccessibility?: boolean;
  ref: (counterEl: HTMLSpanElement | null, particleEl: HTMLDivElement | null, progressEl?: HTMLDivElement | null) => void;
}

const EnhancedMetricCard = React.forwardRef<HTMLDivElement, EnhancedMetricCardProps>(
  ({
    metric,
    index,
    shouldAnimate,
    isVisible,
    enableParticles,
    showProgressBar = true,
    progressValue = 0,
    variant = 'default',
    enableAccessibility = true
  }, forwardedRef) => {
    const counterRef = useRef<HTMLSpanElement>(null);
    const particleRef = useRef<HTMLDivElement>(null);
    const progressRef = useRef<HTMLDivElement>(null);
    const cardRef = useRef<HTMLDivElement>(null);

    const IconComponent = metric.icon;

    useEffect(() => {
      if (typeof forwardedRef === 'function') {
        forwardedRef(counterRef.current, particleRef.current, progressRef.current);
      }
    }, [forwardedRef]);

    // Card hover animation
    const handleMouseEnter = () => {
      if (!shouldAnimate || !cardRef.current) return;
      
      gsap.to(cardRef.current, {
        scale: 1.05,
        y: -5,
        duration: 0.3,
        ease: "power2.out"
      });
    };

    const handleMouseLeave = () => {
      if (!shouldAnimate || !cardRef.current) return;
      
      gsap.to(cardRef.current, {
        scale: 1,
        y: 0,
        duration: 0.3,
        ease: "power2.out"
      });
    };

    return (
      <motion.div
        ref={cardRef}
        className="text-center group relative"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: index * 0.1, duration: 0.6 }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        role="article"
        aria-labelledby={`metric-${metric.id}-label`}
        aria-describedby={`metric-${metric.id}-description`}
      >
        <div className="glass p-6 rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 backdrop-blur-md bg-white/5 relative overflow-hidden">
          {/* Background gradient animation */}
          <motion.div
            className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-300"
            animate={{
              background: shouldAnimate && isVisible
                ? `linear-gradient(45deg, ${metric.particleColor}20, transparent, ${metric.particleColor}20)`
                : 'transparent'
            }}
          />

          {/* Particle container */}
          {enableParticles && (
            <div
              ref={particleRef}
              className="absolute inset-0 pointer-events-none"
              aria-hidden="true"
            />
          )}

          <div className="relative z-10 space-y-4">
            {/* Icon with pulse animation */}
            <motion.div
              className={cn(
                'w-12 h-12 rounded-full flex items-center justify-center mx-auto',
                'bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm',
                'group-hover:scale-110 transition-transform duration-300'
              )}
              animate={shouldAnimate && isVisible ? {
                scale: [1, 1.1, 1],
                rotate: [0, 5, 0]
              } : {}}
              transition={{ 
                duration: 2, 
                repeat: Infinity, 
                repeatDelay: 3,
                delay: index * 0.5 
              }}
            >
              <IconComponent className={cn('w-6 h-6', metric.color)} />
            </motion.div>

            {/* Counter with enhanced styling */}
            <div className="space-y-2">
              <div className="flex items-center justify-center space-x-1">
                <span
                  ref={counterRef}
                  className={cn(
                    'text-3xl md:text-4xl font-bold font-mono tabular-nums',
                    'drop-shadow-lg transition-colors duration-500',
                    shouldAnimate ? 'text-gray-400' : metric.color,
                    variant === 'compact' && 'text-2xl md:text-3xl',
                    variant === 'detailed' && 'text-4xl md:text-5xl'
                  )}
                  aria-live={enableAccessibility ? "polite" : undefined}
                  aria-atomic="true"
                  aria-label={enableAccessibility ? `${metric.label}: ${metric.value}${metric.suffix}` : undefined}
                >
                  {metric.value.toLocaleString()}
                </span>
                <span className={cn('text-xl font-bold', metric.color)}>
                  {metric.suffix}
                </span>
              </div>

              {/* Enhanced Progress indicator */}
              {showProgressBar && (
                <div className={cn(
                  'w-full bg-white/10 rounded-full overflow-hidden',
                  variant === 'compact' ? 'h-1' : 'h-2'
                )}>
                  <motion.div
                    ref={progressRef}
                    className={cn('h-full bg-gradient-to-r', metric.gradient)}
                    initial={{ width: '0%' }}
                    animate={{
                      width: shouldAnimate && isVisible
                        ? `${progressValue}%`
                        : '100%'
                    }}
                    transition={{
                      duration: 2,
                      delay: index * 0.2,
                      ease: "easeOut"
                    }}
                    aria-label={enableAccessibility ? `Progress: ${Math.round(progressValue)}%` : undefined}
                  />
                </div>
              )}

              {/* Progress percentage for detailed variant */}
              {variant === 'detailed' && showProgressBar && (
                <div className="text-xs text-gray-400 text-center mt-1">
                  {Math.round(progressValue)}% complete
                </div>
              )}
            </div>

            {/* Label */}
            <div>
              <h3 
                id={`metric-${metric.id}-label`}
                className="text-sm font-medium text-gray-300"
              >
                {metric.label}
              </h3>
              <p 
                id={`metric-${metric.id}-description`}
                className="text-xs text-gray-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              >
                {metric.description}
              </p>
            </div>

            {/* Milestone indicators */}
            <div className="flex justify-center space-x-1">
              {metric.milestones.map((milestone, milestoneIndex) => (
                <motion.div
                  key={milestone}
                  className={cn(
                    'w-2 h-2 rounded-full',
                    metric.value >= milestone 
                      ? `bg-gradient-to-r ${metric.gradient}`
                      : 'bg-white/20'
                  )}
                  animate={shouldAnimate && metric.value >= milestone ? {
                    scale: [1, 1.3, 1],
                    opacity: [0.7, 1, 0.7]
                  } : {}}
                  transition={{ 
                    duration: 0.5,
                    delay: milestoneIndex * 0.1
                  }}
                />
              ))}
            </div>
          </div>
        </div>
      </motion.div>
    );
  }
);

EnhancedMetricCard.displayName = 'EnhancedMetricCard';
