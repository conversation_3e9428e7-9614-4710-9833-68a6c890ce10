/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { InteractiveMiniDemo } from '../InteractiveMiniDemo';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
  },
  AnimatePresence: ({ children }: any) => <div>{children}</div>,
}));

// Mock window.gtag for analytics
Object.defineProperty(window, 'gtag', {
  writable: true,
  value: jest.fn(),
});

// Mock requestIdleCallback
Object.defineProperty(window, 'requestIdleCallback', {
  writable: true,
  value: (callback: Function) => setTimeout(callback, 0),
});

describe('InteractiveMiniDemo', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('renders trigger button with default text', () => {
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    expect(triggerButton).toBeInTheDocument();
    expect(triggerButton).toHaveTextContent('Try Interactive Demo');
  });

  it('renders trigger button with custom text', () => {
    render(<InteractiveMiniDemo triggerText="Custom Demo Text" />);
    
    const triggerButton = screen.getByRole('button');
    expect(triggerButton).toHaveTextContent('Custom Demo Text');
  });

  it('opens modal when trigger button is clicked', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
    expect(screen.getByText('Interactive Platform Demo')).toBeInTheDocument();
  });

  it('closes modal when close button is clicked', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    // Open modal
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Close modal
    const closeButton = screen.getByLabelText('Close demo');
    await user.click(closeButton);
    
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  it('closes modal when clicking outside', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    // Open modal
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Click outside modal (on backdrop)
    const backdrop = screen.getByRole('dialog').parentElement;
    if (backdrop) {
      await user.click(backdrop);
    }
    
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  it('displays demo steps navigation', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    expect(screen.getByText('Write Smart Contract')).toBeInTheDocument();
    expect(screen.getByText('Get AI Help')).toBeInTheDocument();
    expect(screen.getByText('Deploy & Share')).toBeInTheDocument();
  });

  it('shows progress bar with correct percentage', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    expect(screen.getByText('Step 1 of 3')).toBeInTheDocument();
    expect(screen.getByText('33% Complete')).toBeInTheDocument();
  });

  it('navigates to next step when Next button is clicked', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    const nextButton = screen.getByRole('button', { name: /next step/i });
    await user.click(nextButton);
    
    expect(screen.getByText('Step 2 of 3')).toBeInTheDocument();
    expect(screen.getByText('67% Complete')).toBeInTheDocument();
  });

  it('navigates to previous step when Previous button is clicked', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Go to step 2
    const nextButton = screen.getByRole('button', { name: /next step/i });
    await user.click(nextButton);
    
    // Go back to step 1
    const prevButton = screen.getByRole('button', { name: /previous step/i });
    await user.click(prevButton);
    
    expect(screen.getByText('Step 1 of 3')).toBeInTheDocument();
  });

  it('disables Previous button on first step', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    const prevButton = screen.getByRole('button', { name: /previous step/i });
    expect(prevButton).toBeDisabled();
  });

  it('shows Finish button on last step', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Navigate to last step
    const nextButton = screen.getByRole('button', { name: /next step/i });
    await user.click(nextButton); // Step 2
    await user.click(nextButton); // Step 3
    
    expect(screen.getByText('Finish')).toBeInTheDocument();
  });

  it('allows direct navigation to steps', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Click on step 2 directly
    const step2Button = screen.getByLabelText(/go to step 2/i);
    await user.click(step2Button);
    
    expect(screen.getByText('Step 2 of 3')).toBeInTheDocument();
    expect(screen.getByText('Get AI Help')).toBeInTheDocument();
  });

  it('auto-advances through steps when playing', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Fast-forward time to trigger auto-advance
    jest.advanceTimersByTime(8000);
    
    await waitFor(() => {
      expect(screen.getByText('Step 2 of 3')).toBeInTheDocument();
    });
  });

  it('pauses and resumes playback', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    const playPauseButton = screen.getByLabelText(/pause demo/i);
    await user.click(playPauseButton);
    
    expect(screen.getByLabelText(/play demo/i)).toBeInTheDocument();
  });

  it('displays code editor demo content', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    expect(screen.getByText('Write Smart Contract')).toBeInTheDocument();
    expect(screen.getByText(/Start with a simple Solidity contract/)).toBeInTheDocument();
  });

  it('displays AI assistance demo content on step 2', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Navigate to step 2
    const nextButton = screen.getByRole('button', { name: /next step/i });
    await user.click(nextButton);
    
    expect(screen.getByText('Get AI Help')).toBeInTheDocument();
    expect(screen.getByText(/AI assistant provides real-time suggestions/)).toBeInTheDocument();
  });

  it('displays collaboration demo content on step 3', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Navigate to step 3
    const nextButton = screen.getByRole('button', { name: /next step/i });
    await user.click(nextButton); // Step 2
    await user.click(nextButton); // Step 3
    
    expect(screen.getByText('Deploy & Share')).toBeInTheDocument();
    expect(screen.getByText(/Compile, deploy to testnet/)).toBeInTheDocument();
  });

  it('tracks analytics when enabled', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo trackAnalytics={true} />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    expect(window.gtag).toHaveBeenCalledWith('event', 'demo_started', {
      event_category: 'engagement',
      event_label: 'hero_mini_demo'
    });
  });

  it('handles keyboard navigation', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Test Escape key
    await user.keyboard('{Escape}');
    
    await waitFor(() => {
      expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
    });
  });

  it('handles arrow key navigation', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Test right arrow key
    await user.keyboard('{ArrowRight}');
    
    expect(screen.getByText('Step 2 of 3')).toBeInTheDocument();
    
    // Test left arrow key
    await user.keyboard('{ArrowLeft}');
    
    expect(screen.getByText('Step 1 of 3')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<InteractiveMiniDemo className="custom-demo" />);
    
    const triggerButton = screen.getByRole('button');
    expect(triggerButton).toHaveClass('custom-demo');
  });

  it('auto-starts when autoStart is true', () => {
    render(<InteractiveMiniDemo autoStart={true} />);
    
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  it('marks steps as completed when navigating forward', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);
    
    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);
    
    // Navigate to step 2
    const nextButton = screen.getByRole('button', { name: /next step/i });
    await user.click(nextButton);
    
    // Step 1 should be marked as completed
    const step1Button = screen.getByLabelText(/go to step 1/i);
    expect(step1Button).toHaveClass('bg-green-500/20');
  });

  it('prevents modal content click from closing modal', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    // Click on modal content
    const modalContent = screen.getByText('Interactive Platform Demo');
    await user.click(modalContent);

    // Modal should still be open
    expect(screen.getByRole('dialog')).toBeInTheDocument();
  });

  // Enhanced features tests
  it('displays timer countdown when enabled', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo enableTimer={true} totalDuration={180000} />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    expect(screen.getByText(/3:00/)).toBeInTheDocument();
  });

  it('shows voice-over controls when enabled', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo enableVoiceOver={true} />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    expect(screen.getByLabelText(/voice-over/i)).toBeInTheDocument();
  });

  it('displays XP and achievement information', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    expect(screen.getByText(/XP/)).toBeInTheDocument();
    expect(screen.getByText(/achievements/i)).toBeInTheDocument();
  });

  it('shows learning objectives for each step', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    expect(screen.getByText('Learning Objectives:')).toBeInTheDocument();
  });

  it('displays gamification sidebar', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    expect(screen.getByText('Your Progress')).toBeInTheDocument();
    expect(screen.getByText('Leaderboard')).toBeInTheDocument();
  });

  it('handles account creation flow', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    // Complete all steps to trigger account creation
    for (let i = 0; i < 5; i++) {
      const nextButton = screen.getByRole('button', { name: /next step|finish/i });
      await user.click(nextButton);
    }

    // Account creation should be triggered
    await waitFor(() => {
      expect(screen.getByText(/save your progress/i)).toBeInTheDocument();
    });
  });

  it('tracks analytics events when enabled', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo trackAnalytics={true} />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    expect(window.gtag).toHaveBeenCalledWith('event', 'demo_started', expect.any(Object));
  });

  it('shows 5 demo steps instead of 3', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    expect(screen.getByText('Step 1 of 5')).toBeInTheDocument();
  });

  it('displays advanced code editor with syntax highlighting', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    // Navigate to code editor step
    const nextButton = screen.getByRole('button', { name: /next step/i });
    await user.click(nextButton);

    await waitFor(() => {
      expect(screen.getByText('HelloWorld.sol')).toBeInTheDocument();
    });
  });

  it('shows compilation results in compilation step', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    // Navigate to compilation step
    for (let i = 0; i < 3; i++) {
      const nextButton = screen.getByRole('button', { name: /next step/i });
      await user.click(nextButton);
    }

    await waitFor(() => {
      expect(screen.getByText('Compile & Test Your Contract')).toBeInTheDocument();
    });
  });

  it('handles voice-over toggle', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo enableVoiceOver={true} />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    const voiceOverButton = screen.getByLabelText(/voice-over/i);
    await user.click(voiceOverButton);

    expect(voiceOverButton).toBeInTheDocument();
  });

  it('updates timer countdown', async () => {
    jest.useFakeTimers();
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

    render(<InteractiveMiniDemo enableTimer={true} totalDuration={180000} />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    expect(screen.getByText(/3:00/)).toBeInTheDocument();

    jest.advanceTimersByTime(60000); // 1 minute

    await waitFor(() => {
      expect(screen.getByText(/2:00/)).toBeInTheDocument();
    });

    jest.useRealTimers();
  });

  it('awards XP for step completion', async () => {
    const user = userEvent.setup();
    render(<InteractiveMiniDemo />);

    const triggerButton = screen.getByRole('button', { name: /open interactive demo/i });
    await user.click(triggerButton);

    const initialXP = screen.getByText(/0 XP/);
    expect(initialXP).toBeInTheDocument();

    const nextButton = screen.getByRole('button', { name: /next step/i });
    await user.click(nextButton);

    await waitFor(() => {
      expect(screen.getByText(/25 XP/)).toBeInTheDocument();
    });
  });
});
