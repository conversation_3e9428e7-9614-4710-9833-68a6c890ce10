'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { Users, BookOpen, Code, TrendingUp, Zap, Target } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProgressMetric {
  id: string;
  label: string;
  currentValue: number;
  targetValue: number;
  unit: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  gradient: string;
  description: string;
  updateFrequency: number; // milliseconds
}

interface AnimatedProgressVisualizationProps {
  className?: string;
  variant?: 'horizontal' | 'vertical' | 'circular';
  showLabels?: boolean;
  showValues?: boolean;
  animationDuration?: number;
  updateInterval?: number;
  respectReducedMotion?: boolean;
}

/**
 * Real-Time Animated Progress Visualization
 * 
 * Displays live platform activity metrics with smooth GSAP animations and glassmorphism design.
 * Optimized for 60fps performance using CSS transforms and hardware acceleration.
 * 
 * @component
 * @example
 * ```tsx
 * <AnimatedProgressVisualization 
 *   variant="horizontal"
 *   showLabels={true}
 *   animationDuration={2000}
 *   updateInterval={5000}
 * />
 * ```
 */
export function AnimatedProgressVisualization({
  className = '',
  variant = 'horizontal',
  showLabels = true,
  showValues = true,
  animationDuration = 2000,
  updateInterval = 5000,
  respectReducedMotion = true
}: AnimatedProgressVisualizationProps) {
  const [metrics, setMetrics] = useState<ProgressMetric[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const progressRefs = useRef<(HTMLDivElement | null)[]>([]);
  const animationRefs = useRef<gsap.core.Tween[]>([]);

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false;

  const shouldAnimate = respectReducedMotion ? !prefersReducedMotion : true;

  // Initialize metrics data
  const initialMetrics: ProgressMetric[] = [
    {
      id: 'online-learners',
      label: 'Learners Online',
      currentValue: 0,
      targetValue: 127,
      unit: '',
      icon: Users,
      color: 'text-blue-400',
      gradient: 'from-blue-500 to-cyan-500',
      description: 'Developers currently learning',
      updateFrequency: 3000
    },
    {
      id: 'courses-completed',
      label: 'Courses Completed Today',
      currentValue: 0,
      targetValue: 34,
      unit: '',
      icon: BookOpen,
      color: 'text-green-400',
      gradient: 'from-green-500 to-emerald-500',
      description: 'Courses finished today',
      updateFrequency: 8000
    },
    {
      id: 'contracts-deployed',
      label: 'Smart Contracts This Week',
      currentValue: 0,
      targetValue: 156,
      unit: '',
      icon: Code,
      color: 'text-purple-400',
      gradient: 'from-purple-500 to-pink-500',
      description: 'Contracts deployed this week',
      updateFrequency: 12000
    },
    {
      id: 'success-rate',
      label: 'Success Rate',
      currentValue: 0,
      targetValue: 94,
      unit: '%',
      icon: Target,
      color: 'text-yellow-400',
      gradient: 'from-yellow-500 to-orange-500',
      description: 'Course completion rate',
      updateFrequency: 15000
    }
  ];

  useEffect(() => {
    setMetrics(initialMetrics);
  }, []);

  // Intersection Observer for triggering animations
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          startAnimations();
        }
      },
      { threshold: 0.3 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  // Start progress animations
  const startAnimations = () => {
    if (!shouldAnimate) {
      // Instantly set values for reduced motion
      setMetrics(prev => prev.map(metric => ({
        ...metric,
        currentValue: metric.targetValue
      })));
      return;
    }

    metrics.forEach((metric, index) => {
      const progressBar = progressRefs.current[index];
      if (!progressBar) return;

      // Animate progress bar fill
      const fillAnimation = gsap.fromTo(progressBar, 
        { 
          scaleX: 0,
          transformOrigin: 'left center'
        },
        {
          scaleX: 1,
          duration: animationDuration / 1000,
          delay: index * 0.2,
          ease: "power3.out",
          onUpdate: function() {
            const progress = this.progress();
            const currentValue = Math.round(metric.targetValue * progress);
            
            setMetrics(prev => prev.map(m => 
              m.id === metric.id 
                ? { ...m, currentValue }
                : m
            ));
          }
        }
      );

      animationRefs.current[index] = fillAnimation;
    });
  };

  // Real-time updates simulation
  useEffect(() => {
    if (!isVisible) return;

    const intervals = metrics.map((metric) => {
      return setInterval(() => {
        setMetrics(prev => prev.map(m => {
          if (m.id === metric.id) {
            // Simulate small fluctuations
            const variation = Math.floor(Math.random() * 6) - 3; // -3 to +3
            const newValue = Math.max(0, Math.min(m.targetValue + 10, m.currentValue + variation));
            return { ...m, currentValue: newValue };
          }
          return m;
        }));
      }, metric.updateFrequency);
    });

    return () => {
      intervals.forEach(interval => clearInterval(interval));
    };
  }, [isVisible, metrics]);

  // Cleanup animations on unmount
  useEffect(() => {
    return () => {
      animationRefs.current.forEach(animation => {
        if (animation) animation.kill();
      });
    };
  }, []);

  const getVariantClasses = () => {
    switch (variant) {
      case 'vertical':
        return 'flex-col space-y-6';
      case 'circular':
        return 'grid grid-cols-2 gap-6';
      default:
        return 'space-y-4';
    }
  };

  return (
    <motion.div
      ref={containerRef}
      className={cn(
        'glass p-6 rounded-xl border border-white/10 backdrop-blur-md bg-white/5',
        'hover:border-white/20 transition-all duration-300',
        className
      )}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      role="region"
      aria-label="Live platform metrics"
    >
      <div className="flex items-center space-x-2 mb-6">
        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
          <TrendingUp className="w-4 h-4 text-blue-400" />
        </div>
        <h3 className="text-lg font-semibold text-white">Live Activity</h3>
        <motion.div
          className="w-2 h-2 bg-green-400 rounded-full"
          animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity }}
        />
      </div>

      <div className={cn('flex', getVariantClasses())}>
        {metrics.map((metric, index) => (
          <ProgressMetricCard
            key={metric.id}
            metric={metric}
            index={index}
            variant={variant}
            showLabels={showLabels}
            showValues={showValues}
            shouldAnimate={shouldAnimate}
            ref={(el) => progressRefs.current[index] = el}
          />
        ))}
      </div>

      {/* Real-time indicator */}
      <motion.div
        className="mt-4 pt-4 border-t border-white/10 text-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1, duration: 0.6 }}
      >
        <p className="text-xs text-gray-400">
          <Zap className="w-3 h-3 inline mr-1 text-yellow-400" />
          Updates every few seconds
        </p>
      </motion.div>
    </motion.div>
  );
}

/**
 * Individual Progress Metric Card
 */
interface ProgressMetricCardProps {
  metric: ProgressMetric;
  index: number;
  variant: 'horizontal' | 'vertical' | 'circular';
  showLabels: boolean;
  showValues: boolean;
  shouldAnimate: boolean;
}

const ProgressMetricCard = React.forwardRef<HTMLDivElement, ProgressMetricCardProps>(
  ({ metric, index, variant, showLabels, showValues, shouldAnimate }, ref) => {
    const IconComponent = metric.icon;
    const percentage = (metric.currentValue / metric.targetValue) * 100;

    if (variant === 'circular') {
      return (
        <div className="text-center">
          <div className="relative w-20 h-20 mx-auto mb-3">
            <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 80 80">
              {/* Background circle */}
              <circle
                cx="40"
                cy="40"
                r="32"
                stroke="currentColor"
                strokeWidth="6"
                fill="none"
                className="text-white/10"
              />
              {/* Progress circle */}
              <motion.circle
                cx="40"
                cy="40"
                r="32"
                stroke="url(#gradient)"
                strokeWidth="6"
                fill="none"
                strokeLinecap="round"
                strokeDasharray={`${2 * Math.PI * 32}`}
                initial={{ strokeDashoffset: 2 * Math.PI * 32 }}
                animate={{ 
                  strokeDashoffset: shouldAnimate 
                    ? 2 * Math.PI * 32 * (1 - percentage / 100)
                    : 0
                }}
                transition={{ duration: 2, delay: index * 0.2, ease: "easeOut" }}
              />
              <defs>
                <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                  <stop offset="0%" stopColor="#3b82f6" />
                  <stop offset="100%" stopColor="#8b5cf6" />
                </linearGradient>
              </defs>
            </svg>
            <div className="absolute inset-0 flex items-center justify-center">
              <IconComponent className={cn('w-6 h-6', metric.color)} />
            </div>
          </div>
          {showValues && (
            <div className="text-lg font-bold text-white mb-1">
              {metric.currentValue}{metric.unit}
            </div>
          )}
          {showLabels && (
            <div className="text-xs text-gray-400">{metric.label}</div>
          )}
        </div>
      );
    }

    return (
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <IconComponent className={cn('w-4 h-4', metric.color)} />
            {showLabels && (
              <span className="text-sm font-medium text-white">{metric.label}</span>
            )}
          </div>
          {showValues && (
            <span className={cn('text-sm font-bold tabular-nums', metric.color)}>
              {metric.currentValue}{metric.unit}
            </span>
          )}
        </div>

        <div className="relative">
          {/* Background bar */}
          <div className="w-full h-2 bg-white/10 rounded-full overflow-hidden">
            {/* Progress bar */}
            <div
              ref={ref}
              className={cn(
                'h-full rounded-full bg-gradient-to-r',
                metric.gradient,
                'transform origin-left'
              )}
              style={{ 
                width: shouldAnimate ? '100%' : `${percentage}%`,
                transform: shouldAnimate ? 'scaleX(0)' : 'scaleX(1)'
              }}
            />
          </div>

          {/* Glow effect */}
          <motion.div
            className={cn(
              'absolute inset-0 h-2 rounded-full bg-gradient-to-r opacity-50 blur-sm',
              metric.gradient
            )}
            initial={{ scaleX: 0, transformOrigin: 'left center' }}
            animate={{ scaleX: shouldAnimate ? percentage / 100 : 1 }}
            transition={{ duration: 2, delay: index * 0.2, ease: "easeOut" }}
          />
        </div>

        {showLabels && (
          <p className="text-xs text-gray-400">{metric.description}</p>
        )}
      </div>
    );
  }
);

ProgressMetricCard.displayName = 'ProgressMetricCard';
