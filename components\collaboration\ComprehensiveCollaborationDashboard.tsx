import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Code, 
  MessageCircle, 
  Users, 
  Settings, 
  Maximize2, 
  Minimize2,
  Grid,
  Sidebar,
  Video,
  Mic,
  MicOff,
  VideoOff,
  Share,
  Download,
  Upload
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import MonacoCollaborativeEditor from './MonacoCollaborativeEditor';
import LiveChatSystem from './LiveChatSystem';
import AdvancedUserPresence from './AdvancedUserPresence';
import { useSocket } from '@/lib/socket/client';
import { useAuth } from '@/components/auth/EnhancedAuthProvider';
import { useToast } from '@/components/ui/use-toast';

interface ComprehensiveCollaborationDashboardProps {
  sessionId: string;
  initialCode?: string;
  language?: string;
  onCodeChange?: (code: string) => void;
  onSave?: (code: string) => void;
}

type LayoutMode = 'split' | 'editor-focus' | 'chat-focus' | 'grid';

export const ComprehensiveCollaborationDashboard: React.FC<ComprehensiveCollaborationDashboardProps> = ({
  sessionId,
  initialCode = '',
  language = 'solidity',
  onCodeChange,
  onSave
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const { 
    isConnected, 
    session, 
    participants,
    leaveSession 
  } = useSocket();

  const [layoutMode, setLayoutMode] = useState<LayoutMode>('split');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [activeTab, setActiveTab] = useState('editor');
  const [isAudioEnabled, setIsAudioEnabled] = useState(false);
  const [isVideoEnabled, setIsVideoEnabled] = useState(false);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 's':
            e.preventDefault();
            // Trigger save
            break;
          case 'Enter':
            if (e.shiftKey) {
              e.preventDefault();
              // Toggle fullscreen
              setIsFullscreen(!isFullscreen);
            }
            break;
          case '1':
            e.preventDefault();
            setLayoutMode('editor-focus');
            break;
          case '2':
            e.preventDefault();
            setLayoutMode('chat-focus');
            break;
          case '3':
            e.preventDefault();
            setLayoutMode('split');
            break;
          case '4':
            e.preventDefault();
            setLayoutMode('grid');
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isFullscreen]);

  const handleUserClick = (userId: string) => {
    setSelectedUser(userId);
    // Could open user profile or start direct message
    toast({
      title: 'User Selected',
      description: `Selected user: ${participants.find(p => p.id === userId)?.name}`,
    });
  };

  const toggleAudio = async () => {
    try {
      if (isAudioEnabled) {
        // Disable audio
        setIsAudioEnabled(false);
        toast({
          title: 'Audio Disabled',
          description: 'Your microphone has been muted.',
        });
      } else {
        // Enable audio with stream processing
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

        // Process audio stream for collaboration features
        const audioContext = new AudioContext();
        const source = audioContext.createMediaStreamSource(stream);
        const analyser = audioContext.createAnalyser();
        source.connect(analyser);

        // Store stream for real-time audio analysis
        localStorage.setItem(`audio_stream_${sessionId}`, JSON.stringify({
          enabled: true,
          timestamp: Date.now(),
          sampleRate: audioContext.sampleRate
        }));

        setIsAudioEnabled(true);
        toast({
          title: 'Audio Enabled',
          description: 'Your microphone is now active with real-time processing.',
        });
      }
    } catch (error) {
      toast({
        title: 'Audio Error',
        description: 'Failed to access microphone.',
        variant: 'destructive'
      });
    }
  };

  const toggleVideo = async () => {
    try {
      if (isVideoEnabled) {
        // Disable video
        setIsVideoEnabled(false);
        toast({
          title: 'Video Disabled',
          description: 'Your camera has been turned off.',
        });
      } else {
        // Enable video with stream processing
        const stream = await navigator.mediaDevices.getUserMedia({ video: true });

        // Process video stream for collaboration features
        const videoTrack = stream.getVideoTracks()[0];
        const videoSettings = videoTrack.getSettings();

        // Store video stream metadata for collaboration
        localStorage.setItem(`video_stream_${sessionId}`, JSON.stringify({
          enabled: true,
          timestamp: Date.now(),
          resolution: `${videoSettings.width}x${videoSettings.height}`,
          frameRate: videoSettings.frameRate
        }));

        setIsVideoEnabled(true);
        toast({
          title: 'Video Enabled',
          description: 'Your camera is now active with stream processing.',
        });
      }
    } catch (error) {
      toast({
        title: 'Video Error',
        description: 'Failed to access camera.',
        variant: 'destructive'
      });
    }
  };

  const toggleScreenShare = async () => {
    try {
      if (isScreenSharing) {
        // Stop screen sharing
        setIsScreenSharing(false);
        toast({
          title: 'Screen Share Stopped',
          description: 'You stopped sharing your screen.',
        });
      } else {
        // Start screen sharing with stream processing
        const stream = await navigator.mediaDevices.getDisplayMedia({ video: true });

        // Process screen share stream for collaboration features
        const videoTrack = stream.getVideoTracks()[0];
        const screenSettings = videoTrack.getSettings();

        // Store screen share metadata for collaboration
        localStorage.setItem(`screen_stream_${sessionId}`, JSON.stringify({
          enabled: true,
          timestamp: Date.now(),
          displaySurface: screenSettings.displaySurface,
          resolution: `${screenSettings.width}x${screenSettings.height}`
        }));

        // Handle stream end event
        videoTrack.onended = () => {
          setIsScreenSharing(false);
          localStorage.removeItem(`screen_stream_${sessionId}`);
        };

        setIsScreenSharing(true);
        toast({
          title: 'Screen Share Started',
          description: 'You are now sharing your screen with stream processing.',
        });
      }
    } catch (error) {
      toast({
        title: 'Screen Share Error',
        description: 'Failed to start screen sharing.',
        variant: 'destructive'
      });
    }
  };

  const exportSession = () => {
    const sessionData = {
      sessionId,
      participants: participants.map(p => ({ id: p.id, name: p.name })),
      code: initialCode,
      timestamp: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(sessionData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `collaboration-session-${sessionId}-${Date.now()}.json`;
    a.click();
    URL.revokeObjectURL(url);

    toast({
      title: 'Session Exported',
      description: 'Session data has been downloaded.',
    });
  };

  const renderLayout = () => {
    const editorComponent = (
      <MonacoCollaborativeEditor
        sessionId={sessionId}
        initialCode={initialCode}
        language={language}
        onCodeChange={onCodeChange}
        onSave={onSave}
      />
    );

    const chatComponent = (
      <LiveChatSystem
        sessionId={sessionId}
        showHeader={layoutMode !== 'grid'}
        maxHeight={layoutMode === 'chat-focus' ? '80vh' : '600px'}
      />
    );

    const presenceComponent = (
      <AdvancedUserPresence
        sessionId={sessionId}
        showDetails={true}
        onUserClick={handleUserClick}
      />
    );

    switch (layoutMode) {
      case 'editor-focus':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 h-full">
            <div className="lg:col-span-3 h-full">
              {editorComponent}
            </div>
            <div className="lg:col-span-1 space-y-4">
              {presenceComponent}
              <div className="h-64">
                {chatComponent}
              </div>
            </div>
          </div>
        );

      case 'chat-focus':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4 h-full">
            <div className="lg:col-span-1 space-y-4">
              {presenceComponent}
              <div className="h-64">
                {editorComponent}
              </div>
            </div>
            <div className="lg:col-span-3 h-full">
              {chatComponent}
            </div>
          </div>
        );

      case 'grid':
        return (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 h-full">
            <div className="space-y-4">
              <div className="h-96">
                {editorComponent}
              </div>
              {presenceComponent}
            </div>
            <div className="h-full">
              {chatComponent}
            </div>
          </div>
        );

      case 'split':
      default:
        return (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 h-full">
            <div className="lg:col-span-2 h-full">
              {editorComponent}
            </div>
            <div className="lg:col-span-1 space-y-4">
              {presenceComponent}
              <div className="flex-1">
                {chatComponent}
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className={`flex flex-col bg-slate-900 ${isFullscreen ? 'fixed inset-0 z-50' : 'h-screen'}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 bg-slate-800 border-b border-slate-700">
        <div className="flex items-center space-x-4">
          <h1 className="text-xl font-bold text-white">
            Collaboration Session
          </h1>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-400' : 'bg-red-400'}`} />
            <span className="text-sm text-gray-400">
              {session?.title || 'Untitled Session'}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {/* Layout controls */}
          <div className="flex items-center space-x-1 bg-slate-700 rounded-lg p-1">
            <Button
              size="sm"
              variant={layoutMode === 'editor-focus' ? 'default' : 'ghost'}
              onClick={() => setLayoutMode('editor-focus')}
              title="Editor Focus (Ctrl+1)"
            >
              <Code className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant={layoutMode === 'chat-focus' ? 'default' : 'ghost'}
              onClick={() => setLayoutMode('chat-focus')}
              title="Chat Focus (Ctrl+2)"
            >
              <MessageCircle className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant={layoutMode === 'split' ? 'default' : 'ghost'}
              onClick={() => setLayoutMode('split')}
              title="Split View (Ctrl+3)"
            >
              <Sidebar className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant={layoutMode === 'grid' ? 'default' : 'ghost'}
              onClick={() => setLayoutMode('grid')}
              title="Grid View (Ctrl+4)"
            >
              <Grid className="w-4 h-4" />
            </Button>
          </div>

          {/* Media controls */}
          <div className="flex items-center space-x-1">
            <Button
              size="sm"
              variant={isAudioEnabled ? 'default' : 'outline'}
              onClick={toggleAudio}
            >
              {isAudioEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
            </Button>
            <Button
              size="sm"
              variant={isVideoEnabled ? 'default' : 'outline'}
              onClick={toggleVideo}
            >
              {isVideoEnabled ? <Video className="w-4 h-4" /> : <VideoOff className="w-4 h-4" />}
            </Button>
            <Button
              size="sm"
              variant={isScreenSharing ? 'default' : 'outline'}
              onClick={toggleScreenShare}
            >
              <Share className="w-4 h-4" />
            </Button>
          </div>

          {/* Session controls */}
          <Button
            size="sm"
            variant="outline"
            onClick={() => setSelectedUser(selectedUser ? null : participants[0]?.id || null)}
            title="Manage participants"
          >
            <Users className="w-4 h-4" />
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              // File upload functionality
              const input = document.createElement('input');
              input.type = 'file';
              input.accept = '.sol,.js,.ts,.json';
              input.onchange = (e) => {
                const file = (e.target as HTMLInputElement).files?.[0];
                if (file) {
                  toast({
                    title: 'File Upload',
                    description: `Uploading ${file.name}...`,
                  });
                  // In real app, would upload file and share with participants
                }
              };
              input.click();
            }}
            title="Upload file"
          >
            <Upload className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="outline" onClick={exportSession}>
            <Download className="w-4 h-4" />
          </Button>
          <Button size="sm" variant="outline" onClick={() => setShowSettings(!showSettings)}>
            <Settings className="w-4 h-4" />
          </Button>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={() => setIsFullscreen(!isFullscreen)}
            title="Toggle Fullscreen (Ctrl+Shift+Enter)"
          >
            {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 p-4 overflow-hidden">
        {renderLayout()}
      </div>

      {/* Settings panel */}
      <AnimatePresence>
        {showSettings && (
          <motion.div
            initial={{ opacity: 0, x: 300 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 300 }}
            className="fixed right-0 top-0 h-full w-80 bg-slate-800 border-l border-slate-700 p-4 z-40"
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-white">Session Settings</h3>
              <Button size="sm" variant="ghost" onClick={() => setShowSettings(false)}>
                ×
              </Button>
            </div>
            
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="session">Session</TabsTrigger>
                <TabsTrigger value="participants">Participants</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
              </TabsList>

              <TabsContent value="session" className="space-y-4">
                <Card>
                  <CardContent className="p-4">
                    <div>
                      <label className="text-sm text-gray-400">Session ID</label>
                      <div className="text-white font-mono text-sm bg-slate-700 p-2 rounded">
                        {sessionId}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="participants" className="space-y-4">
                <Card>
                  <CardContent className="p-4">
                    <div>
                      <label className="text-sm text-gray-400">Participants ({participants.length})</label>
                      <div className="space-y-2 mt-2">
                        {participants.map(participant => (
                          <div key={participant.id} className="flex items-center space-x-2 text-sm text-white">
                            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center text-xs">
                              {participant.name?.charAt(0) || 'A'}
                            </div>
                            <span>{participant.name}</span>
                            {participant.id === user?.id && <span className="text-gray-400">(You)</span>}
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="settings" className="space-y-4">
                <Card>
                  <CardContent className="p-4">
                    <div className="pt-4 border-t border-slate-700">
                      <Button
                        variant="destructive"
                        className="w-full"
                        onClick={() => {
                          leaveSession();
                          toast({
                            title: 'Left Session',
                            description: 'You have left the collaboration session.',
                          });
                        }}
                      >
                        Leave Session
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default ComprehensiveCollaborationDashboard;
