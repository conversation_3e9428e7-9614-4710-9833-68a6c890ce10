'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Activity, 
  Zap, 
  Clock, 
  TrendingUp, 
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Monitor,
  Wifi,
  WifiOff,
  RefreshCw,
  Download,
  Upload
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { GlassCard } from '@/components/ui/Glassmorphism';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import GamificationAnalytics from '@/lib/analytics/GamificationAnalytics';
import { useOfflineProgress } from '@/lib/storage/OfflineProgressStorage';

interface PerformanceDashboardProps {
  className?: string;
  compact?: boolean;
  showDetails?: boolean;
}

export function PerformanceDashboard({
  className,
  compact = false,
  showDetails = false
}: PerformanceDashboardProps) {
  const [analytics] = useState(() => GamificationAnalytics.getInstance());
  const [performanceReport, setPerformanceReport] = useState<any[]>([]);
  const [sessionMetrics, setSessionMetrics] = useState<any>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const { syncStatus, forceSync } = useOfflineProgress();

  useEffect(() => {
    const updateMetrics = () => {
      setPerformanceReport(analytics.getPerformanceReport());
      setSessionMetrics(analytics.getSessionMetrics());
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [analytics]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await forceSync();
      setPerformanceReport(analytics.getPerformanceReport());
      setSessionMetrics(analytics.getSessionMetrics());
    } catch (error) {
      console.error('Failed to refresh metrics:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'warning':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'critical':
        return 'text-red-400 bg-red-500/20 border-red-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return CheckCircle;
      case 'warning':
      case 'critical':
        return AlertTriangle;
      default:
        return Activity;
    }
  };

  if (compact) {
    return (
      <GlassCard className={cn('p-4', className)}>
        <div className="space-y-3">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-blue-400" />
              <span className="font-semibold text-white">Performance</span>
            </div>
            
            <div className="flex items-center space-x-2">
              {syncStatus?.isOnline ? (
                <Wifi className="w-4 h-4 text-green-400" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-400" />
              )}
              
              <EnhancedButton
                onClick={handleRefresh}
                variant="ghost"
                size="sm"
                className="w-6 h-6 p-0"
                disabled={isRefreshing}
              >
                <RefreshCw className={cn('w-3 h-3', isRefreshing && 'animate-spin')} />
              </EnhancedButton>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-2 gap-2">
            {performanceReport.slice(0, 4).map((metric, index) => {
              const StatusIcon = getStatusIcon(metric.status);
              return (
                <div
                  key={metric.category}
                  className={cn(
                    'p-2 rounded-lg border text-center',
                    getStatusColor(metric.status)
                  )}
                >
                  <div className="flex items-center justify-center space-x-1 mb-1">
                    <StatusIcon className="w-3 h-3" />
                    <span className="text-xs font-medium capitalize">
                      {metric.category.replace('_', ' ')}
                    </span>
                  </div>
                  <div className="text-sm font-bold">
                    {metric.average}
                    {metric.category.includes('time') ? 'ms' : 
                     metric.category === 'animation_fps' ? 'fps' : 
                     metric.category === 'memory_usage' ? 'MB' : ''}
                  </div>
                </div>
              );
            })}
          </div>

          {/* Sync Status */}
          {syncStatus && (
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-400">
                {syncStatus.pendingChanges > 0 ? 
                  `${syncStatus.pendingChanges} pending` : 
                  'Synced'
                }
              </span>
              <span className="text-gray-400">
                {syncStatus.lastSyncTime ? 
                  syncStatus.lastSyncTime.toLocaleTimeString() : 
                  'Never'
                }
              </span>
            </div>
          )}
        </div>
      </GlassCard>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
            <Activity className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-white">Performance Dashboard</h2>
            <p className="text-sm text-gray-400">Real-time system metrics and analytics</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Online Status */}
          <div className={cn(
            'flex items-center space-x-2 px-3 py-1 rounded-full border',
            syncStatus?.isOnline 
              ? 'bg-green-500/20 border-green-500/30 text-green-400'
              : 'bg-red-500/20 border-red-500/30 text-red-400'
          )}>
            {syncStatus?.isOnline ? (
              <Wifi className="w-4 h-4" />
            ) : (
              <WifiOff className="w-4 h-4" />
            )}
            <span className="text-sm font-medium">
              {syncStatus?.isOnline ? 'Online' : 'Offline'}
            </span>
          </div>
          
          <EnhancedButton
            onClick={handleRefresh}
            variant="outline"
            size="sm"
            disabled={isRefreshing}
          >
            <RefreshCw className={cn('w-4 h-4 mr-2', isRefreshing && 'animate-spin')} />
            Refresh
          </EnhancedButton>
        </div>
      </div>

      {/* Performance Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {performanceReport.map((metric, index) => {
          const StatusIcon = getStatusIcon(metric.status);
          
          return (
            <motion.div
              key={metric.category}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <GlassCard className="p-4">
                <div className="space-y-3">
                  {/* Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <StatusIcon className={cn('w-5 h-5', 
                        metric.status === 'good' ? 'text-green-400' :
                        metric.status === 'warning' ? 'text-yellow-400' :
                        'text-red-400'
                      )} />
                      <span className="font-medium text-white capitalize">
                        {metric.category.replace('_', ' ')}
                      </span>
                    </div>
                    
                    <div className={cn(
                      'px-2 py-1 rounded-full text-xs font-medium border',
                      getStatusColor(metric.status)
                    )}>
                      {metric.status}
                    </div>
                  </div>

                  {/* Main Metric */}
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">
                      {metric.average}
                      <span className="text-sm text-gray-400 ml-1">
                        {metric.category.includes('time') ? 'ms' : 
                         metric.category === 'animation_fps' ? 'fps' : 
                         metric.category === 'memory_usage' ? 'MB' : ''}
                      </span>
                    </div>
                    <div className="text-xs text-gray-400">Average</div>
                  </div>

                  {/* Details */}
                  {showDetails && (
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="text-center">
                        <div className="font-medium text-white">{metric.minimum}</div>
                        <div className="text-gray-400">Min</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium text-white">{metric.maximum}</div>
                        <div className="text-gray-400">Max</div>
                      </div>
                    </div>
                  )}

                  {/* Issues */}
                  {(metric.criticalIssues > 0 || metric.warnings > 0) && (
                    <div className="flex items-center justify-between text-xs">
                      {metric.criticalIssues > 0 && (
                        <span className="text-red-400">
                          {metric.criticalIssues} critical
                        </span>
                      )}
                      {metric.warnings > 0 && (
                        <span className="text-yellow-400">
                          {metric.warnings} warnings
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </GlassCard>
            </motion.div>
          );
        })}
      </div>

      {/* Session Metrics */}
      {sessionMetrics && (
        <GlassCard className="p-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <BarChart3 className="w-5 h-5 mr-2 text-purple-400" />
              Session Analytics
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {Math.round(sessionMetrics.sessionDuration / 60000)}m
                </div>
                <div className="text-sm text-gray-400">Session Time</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {sessionMetrics.xpPerSession}
                </div>
                <div className="text-sm text-gray-400">XP Earned</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {sessionMetrics.achievementsUnlocked}
                </div>
                <div className="text-sm text-gray-400">Achievements</div>
              </div>
              
              <div className="text-center">
                <div className={cn(
                  'text-2xl font-bold',
                  sessionMetrics.engagementLevel === 'very_high' ? 'text-green-400' :
                  sessionMetrics.engagementLevel === 'high' ? 'text-blue-400' :
                  sessionMetrics.engagementLevel === 'medium' ? 'text-yellow-400' :
                  'text-gray-400'
                )}>
                  {sessionMetrics.engagementLevel.replace('_', ' ')}
                </div>
                <div className="text-sm text-gray-400">Engagement</div>
              </div>
            </div>
          </div>
        </GlassCard>
      )}

      {/* Sync Status */}
      {syncStatus && (
        <GlassCard className="p-6">
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <Monitor className="w-5 h-5 mr-2 text-green-400" />
              Sync Status
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-3">
                <div className={cn(
                  'w-3 h-3 rounded-full',
                  syncStatus.isOnline ? 'bg-green-400' : 'bg-red-400'
                )}>
                </div>
                <div>
                  <div className="font-medium text-white">
                    {syncStatus.isOnline ? 'Online' : 'Offline'}
                  </div>
                  <div className="text-sm text-gray-400">Connection Status</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Upload className="w-4 h-4 text-blue-400" />
                <div>
                  <div className="font-medium text-white">
                    {syncStatus.pendingChanges}
                  </div>
                  <div className="text-sm text-gray-400">Pending Changes</div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <Clock className="w-4 h-4 text-purple-400" />
                <div>
                  <div className="font-medium text-white">
                    {syncStatus.lastSyncTime ? 
                      syncStatus.lastSyncTime.toLocaleTimeString() : 
                      'Never'
                    }
                  </div>
                  <div className="text-sm text-gray-400">Last Sync</div>
                </div>
              </div>
            </div>
            
            {syncStatus.lastError && (
              <div className="p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4 text-red-400" />
                  <span className="text-sm text-red-300">
                    Sync Error: {syncStatus.lastError}
                  </span>
                </div>
              </div>
            )}
            
            {syncStatus.syncInProgress && (
              <div className="flex items-center space-x-2 text-blue-400">
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span className="text-sm">Syncing...</span>
              </div>
            )}
          </div>
        </GlassCard>
      )}
    </div>
  );
}
