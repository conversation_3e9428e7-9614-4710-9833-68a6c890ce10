{"timestamp": "2025-06-25T23:58:42.179Z", "summary": {"totalSourceFiles": 432, "totalAssetFiles": 8, "unusedSourceFiles": 143, "unusedAssetFiles": 1, "totalExports": 1520, "unusedExports": 1512, "structureIssues": 3, "structureRecommendations": 31}, "deadCode": {"unusedFiles": {"source": [{"path": "app\\api\\achievements\\route.ts", "size": 3852, "lastModified": "2025-06-25T12:32:46.866Z", "confidence": "medium"}, {"path": "app\\api\\ai\\assistant\\route.ts", "size": 5273, "lastModified": "2025-06-25T12:32:46.868Z", "confidence": "medium"}, {"path": "app\\api\\auth\\login\\route.ts", "size": 8474, "lastModified": "2025-06-25T23:24:34.820Z", "confidence": "medium"}, {"path": "app\\api\\auth\\register\\route.ts", "size": 3766, "lastModified": "2025-06-25T02:22:30.239Z", "confidence": "medium"}, {"path": "app\\api\\chat\\delete\\route.ts", "size": 2641, "lastModified": "2025-06-25T12:32:46.869Z", "confidence": "medium"}, {"path": "app\\api\\chat\\pin\\route.ts", "size": 2135, "lastModified": "2025-06-25T12:32:46.871Z", "confidence": "medium"}, {"path": "app\\api\\chat\\reactions\\route.ts", "size": 3854, "lastModified": "2025-06-25T12:32:46.872Z", "confidence": "medium"}, {"path": "app\\api\\collaboration\\route.ts", "size": 6540, "lastModified": "2025-06-25T12:32:46.872Z", "confidence": "medium"}, {"path": "app\\api\\community\\enhanced-stats\\route.ts", "size": 5783, "lastModified": "2025-06-25T12:32:46.873Z", "confidence": "medium"}, {"path": "app\\api\\community\\leaderboard\\categories\\route.ts", "size": 2354, "lastModified": "2025-06-24T14:10:45.287Z", "confidence": "medium"}, {"path": "app\\api\\community\\leaderboard\\route.ts", "size": 7781, "lastModified": "2025-06-25T12:32:46.875Z", "confidence": "medium"}, {"path": "app\\api\\community\\milestones\\route.ts", "size": 4144, "lastModified": "2025-06-25T12:32:46.875Z", "confidence": "medium"}, {"path": "app\\api\\community\\stats\\route.ts", "size": 7831, "lastModified": "2025-06-25T12:32:46.877Z", "confidence": "medium"}, {"path": "app\\api\\community\\trending\\route.ts", "size": 3481, "lastModified": "2025-06-25T12:32:46.878Z", "confidence": "medium"}, {"path": "app\\api\\compile\\route.ts", "size": 3492, "lastModified": "2025-06-25T12:32:46.879Z", "confidence": "medium"}, {"path": "app\\api\\contact\\submit\\route.ts", "size": 5365, "lastModified": "2025-06-25T12:32:46.880Z", "confidence": "medium"}, {"path": "app\\api\\courses\\route.ts", "size": 10804, "lastModified": "2025-06-25T02:23:18.557Z", "confidence": "medium"}, {"path": "app\\api\\courses\\[id]\\route.ts", "size": 8969, "lastModified": "2025-06-25T02:23:56.041Z", "confidence": "medium"}, {"path": "app\\api\\deployments\\route.ts", "size": 2266, "lastModified": "2025-06-25T12:32:46.882Z", "confidence": "medium"}, {"path": "app\\api\\errors\\route.ts", "size": 7600, "lastModified": "2025-06-25T02:27:23.601Z", "confidence": "medium"}, {"path": "app\\api\\feedback\\submit\\route.ts", "size": 12674, "lastModified": "2025-06-25T12:32:46.884Z", "confidence": "medium"}, {"path": "app\\api\\health\\route.ts", "size": 14240, "lastModified": "2025-06-24T21:06:53.164Z", "confidence": "medium"}, {"path": "app\\api\\leaderboard\\route.ts", "size": 6388, "lastModified": "2025-06-25T12:32:46.885Z", "confidence": "medium"}, {"path": "app\\api\\learning-paths\\route.ts", "size": 8282, "lastModified": "2025-06-25T12:32:46.886Z", "confidence": "medium"}, {"path": "app\\api\\metrics\\route.ts", "size": 8666, "lastModified": "2025-06-25T02:28:02.762Z", "confidence": "medium"}, {"path": "app\\api\\monitoring\\performance\\route.ts", "size": 4075, "lastModified": "2025-06-25T12:32:46.887Z", "confidence": "medium"}, {"path": "app\\api\\projects\\route.ts", "size": 10112, "lastModified": "2025-06-25T12:32:46.888Z", "confidence": "medium"}, {"path": "app\\api\\settings\\route.ts", "size": 11542, "lastModified": "2025-06-25T02:24:45.242Z", "confidence": "medium"}, {"path": "app\\api\\socket\\route.ts", "size": 3478, "lastModified": "2025-06-25T12:32:46.889Z", "confidence": "medium"}, {"path": "app\\api\\uat\\dashboard\\route.ts", "size": 13462, "lastModified": "2025-06-25T12:32:46.890Z", "confidence": "medium"}, {"path": "app\\api\\user\\activity-feed\\route.ts", "size": 5483, "lastModified": "2025-06-25T12:32:46.891Z", "confidence": "medium"}, {"path": "app\\api\\user\\code-stats\\route.ts", "size": 3714, "lastModified": "2025-06-25T12:32:46.892Z", "confidence": "medium"}, {"path": "app\\api\\user\\community-stats\\route.ts", "size": 4222, "lastModified": "2025-06-25T12:32:46.892Z", "confidence": "medium"}, {"path": "app\\api\\user\\profile\\route.ts", "size": 6527, "lastModified": "2025-06-25T12:32:46.893Z", "confidence": "medium"}, {"path": "app\\api\\user\\progress\\route.ts", "size": 5799, "lastModified": "2025-06-25T12:32:46.894Z", "confidence": "medium"}, {"path": "app\\api\\user\\progress-stats\\route.ts", "size": 6645, "lastModified": "2025-06-25T12:32:46.895Z", "confidence": "medium"}, {"path": "app\\api\\user\\study-schedule\\route.ts", "size": 8041, "lastModified": "2025-06-25T12:39:47.880Z", "confidence": "medium"}, {"path": "app\\api\\user\\xp\\route.ts", "size": 3047, "lastModified": "2025-06-25T12:32:46.897Z", "confidence": "medium"}, {"path": "app\\api\\v1\\admin\\maintenance\\route.ts", "size": 7574, "lastModified": "2025-06-24T14:54:00.546Z", "confidence": "medium"}, {"path": "app\\api\\v1\\admin\\maintenance\\schedules\\route.ts", "size": 2931, "lastModified": "2025-06-24T14:54:20.287Z", "confidence": "medium"}, {"path": "app\\api\\v1\\admin\\maintenance\\schedules\\[id]\\route.ts", "size": 6080, "lastModified": "2025-06-24T14:54:44.196Z", "confidence": "medium"}, {"path": "app\\api\\v1\\auth\\login\\route.ts", "size": 5190, "lastModified": "2025-06-24T14:28:48.749Z", "confidence": "medium"}, {"path": "app\\api\\v1\\auth\\refresh\\route.ts", "size": 3575, "lastModified": "2025-06-24T14:29:37.914Z", "confidence": "medium"}, {"path": "app\\api\\v1\\auth\\register\\route.ts", "size": 4391, "lastModified": "2025-06-24T14:29:16.610Z", "confidence": "medium"}, {"path": "app\\api\\v1\\health\\route.ts", "size": 6173, "lastModified": "2025-06-24T14:32:24.556Z", "confidence": "medium"}, {"path": "app\\api\\v1\\lessons\\route.ts", "size": 9625, "lastModified": "2025-06-24T14:31:40.815Z", "confidence": "medium"}, {"path": "app\\api\\v1\\users\\route.ts", "size": 7888, "lastModified": "2025-06-24T14:30:17.782Z", "confidence": "medium"}, {"path": "app\\api\\v1\\users\\[id]\\route.ts", "size": 8853, "lastModified": "2025-06-24T14:30:55.447Z", "confidence": "medium"}, {"path": "app\\ping\\route.ts", "size": 2562, "lastModified": "2025-06-24T17:38:15.334Z", "confidence": "medium"}, {"path": "components\\achievements\\AchievementNotificationSystem.tsx", "size": 15015, "lastModified": "2025-06-23T15:04:38.928Z", "confidence": "medium"}, {"path": "components\\achievements\\AchievementsPage.tsx", "size": 10586, "lastModified": "2025-06-23T15:07:11.423Z", "confidence": "medium"}, {"path": "components\\AchievementsPage.tsx", "size": 5544, "lastModified": "2025-06-19T17:40:42.360Z", "confidence": "medium"}, {"path": "components\\admin\\CommunityControls.tsx", "size": 18305, "lastModified": "2025-06-24T14:17:12.120Z", "confidence": "medium"}, {"path": "components\\admin\\ContentVersionControl.tsx", "size": 20877, "lastModified": "2025-06-24T13:20:01.305Z", "confidence": "medium"}, {"path": "components\\admin\\PerformanceDashboard.tsx", "size": 9650, "lastModified": "2025-06-24T22:07:35.952Z", "confidence": "medium"}, {"path": "components\\admin\\SafetyConfirmation.tsx", "size": 28648, "lastModified": "2025-06-24T13:22:26.008Z", "confidence": "medium"}, {"path": "components\\admin\\UserAnalytics.tsx", "size": 11710, "lastModified": "2025-06-24T13:17:20.506Z", "confidence": "medium"}, {"path": "components\\ai\\AICodeAnalyzer.tsx", "size": 19938, "lastModified": "2025-06-21T14:39:04.694Z", "confidence": "medium"}, {"path": "components\\ai\\AIContractGenerator.tsx", "size": 30417, "lastModified": "2025-06-21T16:31:41.899Z", "confidence": "medium"}, {"path": "components\\ai\\AILearningPath.tsx", "size": 20955, "lastModified": "2025-06-21T17:58:39.257Z", "confidence": "medium"}, {"path": "components\\ai\\EnhancedAIAssistant.tsx", "size": 18979, "lastModified": "2025-06-21T17:23:52.759Z", "confidence": "medium"}, {"path": "components\\auth\\EnhancedLoginModal.tsx", "size": 16576, "lastModified": "2025-06-23T14:38:17.244Z", "confidence": "medium"}, {"path": "components\\auth\\PasswordResetModal.tsx", "size": 18300, "lastModified": "2025-06-23T23:36:30.452Z", "confidence": "medium"}, {"path": "components\\auth\\PasswordStrengthIndicator.tsx", "size": 9026, "lastModified": "2025-06-23T23:38:01.427Z", "confidence": "medium"}, {"path": "components\\blockchain\\BlockchainIntegration.tsx", "size": 17051, "lastModified": "2025-06-21T15:14:26.564Z", "confidence": "medium"}, {"path": "components\\code\\CodeLab.tsx", "size": 29464, "lastModified": "2025-06-23T13:29:08.391Z", "confidence": "medium"}, {"path": "components\\collaboration\\ComprehensiveCollaborationDashboard.tsx", "size": 19019, "lastModified": "2025-06-23T10:33:41.264Z", "confidence": "medium"}, {"path": "components\\ConfirmationModal.tsx", "size": 2044, "lastModified": "2025-06-09T20:04:23.136Z", "confidence": "medium"}, {"path": "components\\curriculum\\PrerequisiteDisplay.tsx", "size": 10687, "lastModified": "2025-06-24T01:17:04.574Z", "confidence": "medium"}, {"path": "components\\discovery\\SmartTooltip.tsx", "size": 11336, "lastModified": "2025-06-24T23:01:23.287Z", "confidence": "medium"}, {"path": "components\\editor\\AdvancedIDEInterface.tsx", "size": 27366, "lastModified": "2025-06-25T00:29:34.779Z", "confidence": "medium"}, {"path": "components\\error\\ErrorBoundaryFallback.tsx", "size": 2452, "lastModified": "2025-06-22T14:56:25.538Z", "confidence": "medium"}, {"path": "components\\error-handling\\AsyncErrorBoundary.tsx", "size": 10683, "lastModified": "2025-06-25T02:59:28.926Z", "confidence": "medium"}, {"path": "components\\forms\\ContactForm.tsx", "size": 9446, "lastModified": "2025-06-23T23:02:44.520Z", "confidence": "medium"}, {"path": "components\\GeminiChat.tsx", "size": 5837, "lastModified": "2025-06-19T17:40:42.361Z", "confidence": "medium"}, {"path": "components\\help\\ContextualTooltip.tsx", "size": 10267, "lastModified": "2025-06-24T22:55:36.089Z", "confidence": "medium"}, {"path": "components\\LandingPage.tsx", "size": 10332, "lastModified": "2025-06-10T14:26:19.311Z", "confidence": "medium"}, {"path": "components\\lazy\\LazyComponents.tsx", "size": 9571, "lastModified": "2025-06-24T21:04:08.775Z", "confidence": "medium"}, {"path": "components\\lazy\\LazyMonacoEditor.tsx", "size": 4853, "lastModified": "2025-06-24T21:03:26.066Z", "confidence": "medium"}, {"path": "components\\learning\\ComprehensiveLearningPlatform.tsx", "size": 27503, "lastModified": "2025-06-23T14:32:57.744Z", "confidence": "medium"}, {"path": "components\\learning\\LearningDashboard.tsx", "size": 28796, "lastModified": "2025-06-23T13:52:46.007Z", "confidence": "medium"}, {"path": "components\\MobileNavigation.tsx", "size": 5964, "lastModified": "2025-06-19T17:47:22.252Z", "confidence": "medium"}, {"path": "components\\ModuleContent.tsx", "size": 11718, "lastModified": "2025-06-22T13:12:39.754Z", "confidence": "medium"}, {"path": "components\\navigation\\AuthenticatedNavbar.tsx", "size": 9109, "lastModified": "2025-06-23T21:13:49.778Z", "confidence": "medium"}, {"path": "components\\navigation\\GuidedOnboarding.tsx", "size": 16762, "lastModified": "2025-06-25T09:44:37.059Z", "confidence": "medium"}, {"path": "components\\navigation\\NavigationFlowOptimizer.tsx", "size": 15871, "lastModified": "2025-06-25T09:43:00.468Z", "confidence": "medium"}, {"path": "components\\navigation\\SmartNavigation.tsx", "size": 13459, "lastModified": "2025-06-25T09:41:45.981Z", "confidence": "medium"}, {"path": "components\\onboarding\\InteractiveTutorial.tsx", "size": 10416, "lastModified": "2025-06-24T22:59:30.247Z", "confidence": "medium"}, {"path": "components\\profile\\UserProfile.tsx", "size": 19213, "lastModified": "2025-06-23T16:27:08.824Z", "confidence": "medium"}, {"path": "components\\progress\\ProgressDashboard.tsx", "size": 33012, "lastModified": "2025-06-23T06:43:22.858Z", "confidence": "medium"}, {"path": "components\\providers\\FallbackProvider.tsx", "size": 9366, "lastModified": "2025-06-25T02:15:12.789Z", "confidence": "medium"}, {"path": "components\\providers\\SessionProvider.tsx", "size": 510, "lastModified": "2025-06-23T17:04:25.211Z", "confidence": "medium"}, {"path": "components\\settings\\__tests__\\integration.test.tsx", "size": 12977, "lastModified": "2025-06-25T01:25:10.031Z", "confidence": "medium"}, {"path": "components\\settings\\__tests__\\ProfileSection.test.tsx", "size": 6698, "lastModified": "2025-06-25T01:21:20.501Z", "confidence": "medium"}, {"path": "components\\settings\\__tests__\\SecuritySection.test.tsx", "size": 10604, "lastModified": "2025-06-25T01:22:02.378Z", "confidence": "medium"}, {"path": "components\\settings\\__tests__\\SettingsPage.test.tsx", "size": 9316, "lastModified": "2025-06-25T01:16:49.034Z", "confidence": "medium"}, {"path": "components\\testing\\FeedbackCollectionSystem.tsx", "size": 18824, "lastModified": "2025-06-23T07:43:27.691Z", "confidence": "medium"}, {"path": "components\\testing\\NotificationTestingPage.tsx", "size": 16552, "lastModified": "2025-06-24T23:48:10.113Z", "confidence": "medium"}, {"path": "components\\testing\\UATDashboard.tsx", "size": 20107, "lastModified": "2025-06-23T11:46:24.099Z", "confidence": "medium"}, {"path": "components\\ui\\AdvancedAnimations.tsx", "size": 11013, "lastModified": "2025-06-21T14:20:27.307Z", "confidence": "medium"}, {"path": "components\\ui\\AnimatedButton.tsx", "size": 3155, "lastModified": "2025-06-21T22:35:24.034Z", "confidence": "medium"}, {"path": "components\\ui\\AnimationShowcase.tsx", "size": 11689, "lastModified": "2025-06-21T18:32:34.710Z", "confidence": "medium"}, {"path": "components\\ui\\Branding.tsx", "size": 10267, "lastModified": "2025-06-19T18:08:02.911Z", "confidence": "medium"}, {"path": "components\\ui\\EnhancedButton.stories.tsx", "size": 10888, "lastModified": "2025-06-25T17:37:12.722Z", "confidence": "medium"}, {"path": "components\\ui\\EnhancedCard.tsx", "size": 1609, "lastModified": "2025-06-21T22:35:35.999Z", "confidence": "medium"}, {"path": "components\\ui\\EnhancedLoadingStates.tsx", "size": 13385, "lastModified": "2025-06-25T02:52:18.666Z", "confidence": "medium"}, {"path": "components\\ui\\EnhancedProgress.tsx", "size": 10325, "lastModified": "2025-06-23T16:27:27.233Z", "confidence": "medium"}, {"path": "components\\ui\\ErrorHandling.tsx", "size": 9437, "lastModified": "2025-06-21T18:32:48.146Z", "confidence": "medium"}, {"path": "components\\ui\\FeatureState.tsx", "size": 12427, "lastModified": "2025-06-25T02:09:23.337Z", "confidence": "medium"}, {"path": "components\\ui\\GlassCard.stories.tsx", "size": 7524, "lastModified": "2025-06-25T17:36:19.186Z", "confidence": "medium"}, {"path": "components\\ui\\GlassmorphismModal.tsx", "size": 3387, "lastModified": "2025-06-21T14:20:27.347Z", "confidence": "medium"}, {"path": "components\\ui\\GlassNeumorphDemo.tsx", "size": 11797, "lastModified": "2025-06-21T17:12:22.616Z", "confidence": "medium"}, {"path": "components\\ui\\index.ts", "size": 2684, "lastModified": "2025-06-23T23:26:30.782Z", "confidence": "medium"}, {"path": "components\\ui\\LazyLoadingComponents.tsx", "size": 10119, "lastModified": "2025-06-25T02:53:07.070Z", "confidence": "medium"}, {"path": "components\\ui\\NotificationCenter.tsx", "size": 16256, "lastModified": "2025-06-24T23:43:08.775Z", "confidence": "medium"}, {"path": "components\\ui\\Onboarding.tsx", "size": 17101, "lastModified": "2025-06-21T18:11:59.289Z", "confidence": "medium"}, {"path": "components\\ui\\OptimizedImage.tsx", "size": 7622, "lastModified": "2025-06-24T20:59:22.941Z", "confidence": "medium"}, {"path": "components\\ui\\PageTransition.tsx", "size": 5747, "lastModified": "2025-06-19T17:51:09.463Z", "confidence": "medium"}, {"path": "components\\ui\\separator.tsx", "size": 770, "lastModified": "2025-06-23T15:00:42.027Z", "confidence": "medium"}, {"path": "components\\ui\\SkeletonLoader.tsx", "size": 12240, "lastModified": "2025-06-25T02:48:30.379Z", "confidence": "medium"}, {"path": "components\\ui\\switch.tsx", "size": 1153, "lastModified": "2025-06-23T15:00:33.607Z", "confidence": "medium"}, {"path": "components\\ui\\Typography.tsx", "size": 11898, "lastModified": "2025-06-23T16:29:05.753Z", "confidence": "medium"}, {"path": "components\\ui\\VisualFeedbackSystem.tsx", "size": 12988, "lastModified": "2025-06-23T16:28:46.449Z", "confidence": "medium"}, {"path": "lib\\api\\documentation.ts", "size": 17143, "lastModified": "2025-06-25T02:36:56.620Z", "confidence": "medium"}, {"path": "lib\\api\\integration.ts", "size": 13642, "lastModified": "2025-06-25T02:35:50.437Z", "confidence": "medium"}, {"path": "lib\\api\\optimizedApiClient.ts", "size": 8873, "lastModified": "2025-06-24T22:06:56.674Z", "confidence": "medium"}, {"path": "lib\\api\\security.ts", "size": 12191, "lastModified": "2025-06-25T02:30:02.475Z", "confidence": "medium"}, {"path": "lib\\api\\testing.ts", "size": 13494, "lastModified": "2025-06-25T02:34:30.610Z", "confidence": "medium"}, {"path": "lib\\auth\\navigationGuard.ts", "size": 10832, "lastModified": "2025-06-24T00:17:19.718Z", "confidence": "medium"}, {"path": "lib\\config\\secrets.ts", "size": 10458, "lastModified": "2025-06-23T10:49:36.970Z", "confidence": "medium"}, {"path": "lib\\hooks\\useEnhancedKeyboardNavigation.ts", "size": 8448, "lastModified": "2025-06-24T19:22:04.361Z", "confidence": "medium"}, {"path": "lib\\hooks\\useKeyboardNavigation.ts", "size": 8068, "lastModified": "2025-06-23T23:04:43.338Z", "confidence": "medium"}, {"path": "lib\\hooks\\useSolidityAnalyzer.ts", "size": 11431, "lastModified": "2025-06-25T00:21:51.504Z", "confidence": "medium"}, {"path": "lib\\hooks\\__tests__\\useSettings.test.ts", "size": 9920, "lastModified": "2025-06-25T01:22:41.110Z", "confidence": "medium"}, {"path": "lib\\security\\headers.ts", "size": 12547, "lastModified": "2025-06-23T11:40:55.440Z", "confidence": "medium"}, {"path": "lib\\testing\\ux-testing.ts", "size": 20925, "lastModified": "2025-06-25T09:46:33.250Z", "confidence": "medium"}, {"path": "lib\\theme\\ThemeProvider.tsx", "size": 13823, "lastModified": "2025-06-23T16:30:29.354Z", "confidence": "medium"}, {"path": "lib\\utils\\assetOptimization.ts", "size": 7786, "lastModified": "2025-06-24T21:00:33.759Z", "confidence": "medium"}, {"path": "hooks\\useLoadingState.ts", "size": 5942, "lastModified": "2025-06-19T18:04:15.920Z", "confidence": "medium"}, {"path": "hooks\\usePerformance.ts", "size": 9204, "lastModified": "2025-06-21T14:42:52.182Z", "confidence": "medium"}, {"path": "hooks\\useProgress.ts", "size": 1856, "lastModified": "2025-06-19T17:40:42.369Z", "confidence": "medium"}, {"path": "types\\global.d.ts", "size": 1585, "lastModified": "2025-06-21T11:39:38.553Z", "confidence": "medium"}, {"path": "types\\next-auth.d.ts", "size": 484, "lastModified": "2025-06-21T11:30:44.935Z", "confidence": "medium"}], "assets": [{"path": "public\\favicon.svg", "size": 227, "confidence": "medium"}]}, "unusedExports": [{"file": "app\\achievements\\page.tsx", "name": "AchievementsPage", "type": "default", "line": 13, "confidence": "low"}, {"file": "app\\admin\\audit\\page.tsx", "name": "AdminAuditPage", "type": "default", "line": 9, "confidence": "low"}, {"file": "app\\admin\\community\\page.tsx", "name": "AdminCommunityPage", "type": "default", "line": 9, "confidence": "low"}, {"file": "app\\admin\\content\\page.tsx", "name": "AdminContentPage", "type": "default", "line": 9, "confidence": "low"}, {"file": "app\\admin\\page.tsx", "name": "AdminPage", "type": "default", "line": 9, "confidence": "low"}, {"file": "app\\admin\\security\\page.tsx", "name": "AdminSecurityPage", "type": "default", "line": 9, "confidence": "low"}, {"file": "app\\admin\\users\\page.tsx", "name": "AdminUsersPage", "type": "default", "line": 9, "confidence": "low"}, {"file": "app\\api\\achievements\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\auth\\login\\route.ts", "name": "POST", "type": "named", "line": 306, "confidence": "low"}, {"file": "app\\api\\auth\\register\\route.ts", "name": "POST", "type": "named", "line": 145, "confidence": "low"}, {"file": "app\\api\\chat\\delete\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\chat\\pin\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\collaboration\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\community\\stats\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\compile\\route.ts", "name": "dynamic", "type": "named", "line": 8, "confidence": "low"}, {"file": "app\\api\\courses\\route.ts", "name": "GET", "type": "named", "line": 309, "confidence": "low"}, {"file": "app\\api\\courses\\route.ts", "name": "POST", "type": "named", "line": 310, "confidence": "low"}, {"file": "app\\api\\courses\\[id]\\route.ts", "name": "GET", "type": "named", "line": 257, "confidence": "low"}, {"file": "app\\api\\courses\\[id]\\route.ts", "name": "PUT", "type": "named", "line": 258, "confidence": "low"}, {"file": "app\\api\\courses\\[id]\\route.ts", "name": "DELETE", "type": "named", "line": 259, "confidence": "low"}, {"file": "app\\api\\errors\\route.ts", "name": "GET", "type": "named", "line": 251, "confidence": "low"}, {"file": "app\\api\\errors\\route.ts", "name": "POST", "type": "named", "line": 252, "confidence": "low"}, {"file": "app\\api\\errors\\route.ts", "name": "DELETE", "type": "named", "line": 253, "confidence": "low"}, {"file": "app\\api\\leaderboard\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\learning-paths\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\metrics\\route.ts", "name": "GET", "type": "named", "line": 253, "confidence": "low"}, {"file": "app\\api\\projects\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\settings\\route.ts", "name": "GET", "type": "named", "line": 349, "confidence": "low"}, {"file": "app\\api\\settings\\route.ts", "name": "PUT", "type": "named", "line": 350, "confidence": "low"}, {"file": "app\\api\\socket\\route.ts", "name": "dynamic", "type": "named", "line": 6, "confidence": "low"}, {"file": "app\\api\\user\\activity-feed\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\user\\code-stats\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\user\\community-stats\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\user\\profile\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\user\\progress\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\user\\progress-stats\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\user\\study-schedule\\route.ts", "name": "dynamic", "type": "named", "line": 7, "confidence": "low"}, {"file": "app\\api\\v1\\admin\\maintenance\\route.ts", "name": "GET", "type": "named", "line": 52, "confidence": "low"}, {"file": "app\\api\\v1\\admin\\maintenance\\route.ts", "name": "POST", "type": "named", "line": 126, "confidence": "low"}, {"file": "app\\api\\v1\\admin\\maintenance\\schedules\\route.ts", "name": "GET", "type": "named", "line": 42, "confidence": "low"}, {"file": "app\\api\\v1\\admin\\maintenance\\schedules\\route.ts", "name": "POST", "type": "named", "line": 54, "confidence": "low"}, {"file": "app\\api\\v1\\admin\\maintenance\\schedules\\[id]\\route.ts", "name": "GET", "type": "named", "line": 39, "confidence": "low"}, {"file": "app\\api\\v1\\admin\\maintenance\\schedules\\[id]\\route.ts", "name": "PUT", "type": "named", "line": 68, "confidence": "low"}, {"file": "app\\api\\v1\\admin\\maintenance\\schedules\\[id]\\route.ts", "name": "DELETE", "type": "named", "line": 107, "confidence": "low"}, {"file": "app\\api\\v1\\admin\\maintenance\\schedules\\[id]\\route.ts", "name": "POST", "type": "named", "line": 138, "confidence": "low"}, {"file": "app\\api\\v1\\auth\\login\\route.ts", "name": "POST", "type": "named", "line": 112, "confidence": "low"}, {"file": "app\\api\\v1\\auth\\refresh\\route.ts", "name": "POST", "type": "named", "line": 56, "confidence": "low"}, {"file": "app\\api\\v1\\auth\\register\\route.ts", "name": "POST", "type": "named", "line": 69, "confidence": "low"}, {"file": "app\\api\\v1\\health\\route.ts", "name": "GET", "type": "named", "line": 152, "confidence": "low"}, {"file": "app\\api\\v1\\lessons\\route.ts", "name": "GET", "type": "named", "line": 178, "confidence": "low"}, {"file": "app\\api\\v1\\lessons\\route.ts", "name": "POST", "type": "named", "line": 223, "confidence": "low"}, {"file": "app\\api\\v1\\users\\route.ts", "name": "GET", "type": "named", "line": 162, "confidence": "low"}, {"file": "app\\api\\v1\\users\\route.ts", "name": "POST", "type": "named", "line": 193, "confidence": "low"}, {"file": "app\\api\\v1\\users\\[id]\\route.ts", "name": "GET", "type": "named", "line": 96, "confidence": "low"}, {"file": "app\\api\\v1\\users\\[id]\\route.ts", "name": "PUT", "type": "named", "line": 163, "confidence": "low"}, {"file": "app\\api\\v1\\users\\[id]\\route.ts", "name": "DELETE", "type": "named", "line": 238, "confidence": "low"}, {"file": "app\\auth\\demo\\page.tsx", "name": "AuthDemoPage", "type": "default", "line": 22, "confidence": "low"}, {"file": "app\\auth\\local-test\\page.tsx", "name": "LocalAuthTestPage", "type": "default", "line": 401, "confidence": "low"}, {"file": "app\\auth\\test\\page.tsx", "name": "AuthTestPage", "type": "default", "line": 20, "confidence": "low"}, {"file": "app\\auth-testing\\page.tsx", "name": "AuthTestingPage", "type": "default", "line": 7, "confidence": "low"}, {"file": "app\\button-testing\\page.tsx", "name": "ButtonTestingPage", "type": "default", "line": 7, "confidence": "low"}, {"file": "app\\code\\page.tsx", "name": "CodePage", "type": "default", "line": 13, "confidence": "low"}, {"file": "app\\collaborate\\page.tsx", "name": "CollaboratePage", "type": "default", "line": 7, "confidence": "low"}, {"file": "app\\community\\page.tsx", "name": "CommunityPage", "type": "default", "line": 7, "confidence": "low"}, {"file": "app\\contact\\page.tsx", "name": "Contact", "type": "default", "line": 5, "confidence": "low"}, {"file": "app\\cookies\\page.tsx", "name": "CookiePolicyPage", "type": "default", "line": 8, "confidence": "low"}, {"file": "app\\dashboard\\page.tsx", "name": "DashboardPage", "type": "default", "line": 297, "confidence": "low"}, {"file": "app\\documentation\\page.tsx", "name": "DocumentationPage", "type": "default", "line": 17, "confidence": "low"}, {"file": "app\\error-testing\\page.tsx", "name": "ErrorTestingPage", "type": "default", "line": 7, "confidence": "low"}, {"file": "app\\examples\\page.tsx", "name": "ExamplesPage", "type": "default", "line": 17, "confidence": "low"}, {"file": "app\\instructor\\page.tsx", "name": "InstructorPage", "type": "default", "line": 9, "confidence": "low"}, {"file": "app\\layout.tsx", "name": "metadata", "type": "named", "line": 25, "confidence": "low"}, {"file": "app\\layout.tsx", "name": "RootLayout", "type": "default", "line": 68, "confidence": "low"}, {"file": "app\\learn\\page.tsx", "name": "LearnPage", "type": "default", "line": 13, "confidence": "low"}, {"file": "app\\mentor\\page.tsx", "name": "MentorPage", "type": "default", "line": 9, "confidence": "low"}, {"file": "app\\not-found.tsx", "name": "metadata", "type": "named", "line": 4, "confidence": "low"}, {"file": "app\\not-found.tsx", "name": "NotFound", "type": "default", "line": 10, "confidence": "low"}, {"file": "app\\offline\\page.tsx", "name": "OfflinePage", "type": "default", "line": 6, "confidence": "low"}, {"file": "app\\page.tsx", "name": "HomePage", "type": "default", "line": 13, "confidence": "low"}, {"file": "app\\ping\\route.ts", "name": "dynamic", "type": "named", "line": 15, "confidence": "low"}, {"file": "app\\privacy\\page.tsx", "name": "PrivacyPolicyPage", "type": "default", "line": 8, "confidence": "low"}, {"file": "app\\profile\\page.tsx", "name": "Profile", "type": "default", "line": 14, "confidence": "low"}, {"file": "app\\providers.tsx", "name": "Providers", "type": "named", "line": 21, "confidence": "low"}, {"file": "app\\session-expired\\page.tsx", "name": "SessionExpiredPage", "type": "default", "line": 21, "confidence": "low"}, {"file": "app\\settings\\page.tsx", "name": "Settings", "type": "default", "line": 13, "confidence": "low"}, {"file": "app\\terms\\page.tsx", "name": "TermsOfServicePage", "type": "default", "line": 8, "confidence": "low"}, {"file": "app\\tutorials\\page.tsx", "name": "TutorialsPage", "type": "default", "line": 16, "confidence": "low"}, {"file": "app\\unauthorized\\page.tsx", "name": "UnauthorizedPage", "type": "default", "line": 31, "confidence": "low"}, {"file": "components\\achievements\\AchievementCard.tsx", "name": "AchievementCard", "type": "named", "line": 30, "confidence": "low"}, {"file": "components\\achievements\\AchievementGrid.tsx", "name": "<PERSON><PERSON><PERSON>", "type": "named", "line": 31, "confidence": "low"}, {"file": "components\\achievements\\AchievementNotification.tsx", "name": "AchievementNotification", "type": "named", "line": 32, "confidence": "low"}, {"file": "components\\achievements\\AchievementNotification.tsx", "name": "AchievementNotificationManager", "type": "named", "line": 314, "confidence": "low"}, {"file": "components\\achievements\\AchievementNotification.tsx", "name": "AchievementToast", "type": "named", "line": 370, "confidence": "low"}, {"file": "components\\achievements\\AchievementNotificationSystem.tsx", "name": "StudyReminderSystem", "type": "named", "line": 169, "confidence": "low"}, {"file": "components\\achievements\\AchievementNotificationSystem.tsx", "name": "AchievementNotificationSystem", "type": "named", "line": 213, "confidence": "low"}, {"file": "components\\achievements\\AchievementsPage.tsx", "name": "AchievementsPage", "type": "named", "line": 58, "confidence": "low"}, {"file": "components\\AchievementsPage.tsx", "name": "AchievementsPage", "type": "default", "line": 125, "confidence": "low"}, {"file": "components\\admin\\AdminDashboard.tsx", "name": "AdminDashboard", "type": "named", "line": 245, "confidence": "low"}, {"file": "components\\admin\\AdminGuard.tsx", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 237, "confidence": "low"}, {"file": "components\\admin\\AdminGuard.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 309, "confidence": "low"}, {"file": "components\\admin\\AdminGuard.tsx", "name": "useAdminAuth", "type": "named", "line": 324, "confidence": "low"}, {"file": "components\\admin\\AdminLayout.tsx", "name": "AdminLayout", "type": "named", "line": 47, "confidence": "low"}, {"file": "components\\admin\\AuditLogViewer.tsx", "name": "AuditLogViewer", "type": "named", "line": 197, "confidence": "low"}, {"file": "components\\admin\\CommunityControls.tsx", "name": "CommunityControls", "type": "named", "line": 128, "confidence": "low"}, {"file": "components\\admin\\CommunityManagement.tsx", "name": "CommunityManagement", "type": "named", "line": 263, "confidence": "low"}, {"file": "components\\admin\\ContentManagement.tsx", "name": "ContentManagement", "type": "named", "line": 277, "confidence": "low"}, {"file": "components\\admin\\ContentVersionControl.tsx", "name": "ContentVersionControl", "type": "named", "line": 206, "confidence": "low"}, {"file": "components\\admin\\PerformanceDashboard.tsx", "name": "PerformanceDashboard", "type": "default", "line": 252, "confidence": "low"}, {"file": "components\\admin\\SafetyConfirmation.tsx", "name": "SafetyConfirmation", "type": "named", "line": 154, "confidence": "low"}, {"file": "components\\admin\\SafetyConfirmation.tsx", "name": "SoftDeleteManager", "type": "named", "line": 407, "confidence": "low"}, {"file": "components\\admin\\SafetyConfirmation.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 599, "confidence": "low"}, {"file": "components\\admin\\SecurityManagement.tsx", "name": "SecurityManagement", "type": "named", "line": 224, "confidence": "low"}, {"file": "components\\admin\\UserAnalytics.tsx", "name": "UserAnalytics", "type": "named", "line": 199, "confidence": "low"}, {"file": "components\\admin\\UserManagement.tsx", "name": "UserManagement", "type": "named", "line": 264, "confidence": "low"}, {"file": "components\\ai\\AICodeAnalyzer.tsx", "name": "AICodeAnalyzer", "type": "default", "line": 505, "confidence": "low"}, {"file": "components\\ai\\AIContractGenerator.tsx", "name": "AIContractGenerator", "type": "default", "line": 862, "confidence": "low"}, {"file": "components\\ai\\AILearningPath.tsx", "name": "AILearningPath", "type": "default", "line": 525, "confidence": "low"}, {"file": "components\\ai\\EnhancedAIAssistant.tsx", "name": "EnhancedAIAssistant", "type": "default", "line": 541, "confidence": "low"}, {"file": "components\\auth\\AuthModal.tsx", "name": "AuthModal", "type": "named", "line": 42, "confidence": "low"}, {"file": "components\\auth\\AuthTesting.tsx", "name": "AuthTesting", "type": "named", "line": 41, "confidence": "low"}, {"file": "components\\auth\\EnhancedAuthProvider.tsx", "name": "Enhan<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "default", "line": 460, "confidence": "low"}, {"file": "components\\auth\\EnhancedAuthProvider.tsx", "name": "useAuth", "type": "named", "line": 268, "confidence": "low"}, {"file": "components\\auth\\EnhancedAuthProvider.tsx", "name": "UserProfileCard", "type": "named", "line": 282, "confidence": "low"}, {"file": "components\\auth\\EnhancedAuthProvider.tsx", "name": "PermissionGuard", "type": "named", "line": 415, "confidence": "low"}, {"file": "components\\auth\\EnhancedAuthProvider.tsx", "name": "RoleBadge", "type": "named", "line": 435, "confidence": "low"}, {"file": "components\\auth\\EnhancedLoginModal.tsx", "name": "EnhancedLoginModal", "type": "default", "line": 436, "confidence": "low"}, {"file": "components\\auth\\PasswordResetModal.tsx", "name": "PasswordResetModal", "type": "named", "line": 52, "confidence": "low"}, {"file": "components\\auth\\PasswordStrengthIndicator.tsx", "name": "PasswordStrength", "type": "named", "line": 8, "confidence": "low"}, {"file": "components\\auth\\PasswordStrengthIndicator.tsx", "name": "PasswordStrengthIndicator", "type": "named", "line": 30, "confidence": "low"}, {"file": "components\\auth\\PasswordStrengthIndicator.tsx", "name": "calculatePasswordStrength", "type": "named", "line": 187, "confidence": "low"}, {"file": "components\\auth\\PasswordStrengthIndicator.tsx", "name": "usePasswordValidation", "type": "named", "line": 319, "confidence": "low"}, {"file": "components\\auth\\ProtectedRoute.tsx", "name": "UserRole", "type": "named", "line": 19, "confidence": "low"}, {"file": "components\\auth\\ProtectedRoute.tsx", "name": "RoutePermission", "type": "named", "line": 21, "confidence": "low"}, {"file": "components\\auth\\ProtectedRoute.tsx", "name": "ProtectedRouteProps", "type": "named", "line": 28, "confidence": "low"}, {"file": "components\\auth\\ProtectedRoute.tsx", "name": "ROUTE_PERMISSIONS", "type": "named", "line": 42, "confidence": "low"}, {"file": "components\\auth\\ProtectedRoute.tsx", "name": "ProtectedRoute", "type": "named", "line": 79, "confidence": "low"}, {"file": "components\\auth\\ProtectedRoute.tsx", "name": "<PERSON><PERSON><PERSON>", "type": "named", "line": 316, "confidence": "low"}, {"file": "components\\auth\\ProtectedRoute.tsx", "name": "useRouteProtection", "type": "named", "line": 465, "confidence": "low"}, {"file": "components\\auth\\ProtectedRoute.tsx", "name": "useAuthGuard", "type": "named", "line": 514, "confidence": "low"}, {"file": "components\\blockchain\\BlockchainIntegration.tsx", "name": "BlockchainIntegration", "type": "default", "line": 462, "confidence": "low"}, {"file": "components\\blockchain\\ContractDeployer.tsx", "name": "ContractDeployer", "type": "named", "line": 37, "confidence": "low"}, {"file": "components\\blockchain\\WalletConnect.tsx", "name": "WalletConnect", "type": "named", "line": 20, "confidence": "low"}, {"file": "components\\code\\CodeLab.tsx", "name": "CodeLab", "type": "named", "line": 66, "confidence": "low"}, {"file": "components\\code\\CodeLab.tsx", "name": "StaticCodeLab", "type": "named", "line": 666, "confidence": "low"}, {"file": "components\\collaboration\\AdvancedUserPresence.tsx", "name": "AdvancedUserPresence", "type": "default", "line": 680, "confidence": "low"}, {"file": "components\\collaboration\\CollaborationChat.tsx", "name": "CollaborationChat", "type": "named", "line": 67, "confidence": "low"}, {"file": "components\\collaboration\\CollaborationChat.tsx", "name": "useChatMessages", "type": "named", "line": 571, "confidence": "low"}, {"file": "components\\collaboration\\CollaborationHub.tsx", "name": "CollaborationHub", "type": "named", "line": 30, "confidence": "low"}, {"file": "components\\collaboration\\CollaborativeEditor.tsx", "name": "CollaborativeEditor", "type": "named", "line": 58, "confidence": "low"}, {"file": "components\\collaboration\\ComprehensiveCollaborationDashboard.tsx", "name": "ComprehensiveCollaborationDashboard", "type": "default", "line": 573, "confidence": "low"}, {"file": "components\\collaboration\\ConnectionStatusIndicator.tsx", "name": "ConnectionStatusIndicator", "type": "named", "line": 41, "confidence": "low"}, {"file": "components\\collaboration\\FileSharing.tsx", "name": "FileSharing", "type": "named", "line": 62, "confidence": "low"}, {"file": "components\\collaboration\\LiveChatSystem.tsx", "name": "LiveChatSystem", "type": "default", "line": 711, "confidence": "low"}, {"file": "components\\collaboration\\MonacoCollaborativeEditor.tsx", "name": "MonacoCollaborativeEditor", "type": "default", "line": 783, "confidence": "low"}, {"file": "components\\collaboration\\RealTimeCodeEditor.tsx", "name": "RealTimeCodeEditor", "type": "default", "line": 538, "confidence": "low"}, {"file": "components\\collaboration\\SessionRecovery.tsx", "name": "SessionRecovery", "type": "named", "line": 45, "confidence": "low"}, {"file": "components\\collaboration\\SessionRecovery.tsx", "name": "useSessionRecovery", "type": "named", "line": 354, "confidence": "low"}, {"file": "components\\collaboration\\UserPresenceIndicator.tsx", "name": "UserPresenceIndicator", "type": "default", "line": 306, "confidence": "low"}, {"file": "components\\collaboration\\UserPresencePanel.tsx", "name": "UserPresencePanel", "type": "named", "line": 43, "confidence": "low"}, {"file": "components\\community\\CommunityHub.tsx", "name": "CommunityHub", "type": "named", "line": 141, "confidence": "low"}, {"file": "components\\community\\CommunityStats.tsx", "name": "CommunityStats", "type": "named", "line": 347, "confidence": "low"}, {"file": "components\\community\\Leaderboards.tsx", "name": "Leaderboards", "type": "named", "line": 313, "confidence": "low"}, {"file": "components\\ConfirmationModal.tsx", "name": "ConfirmationModal", "type": "default", "line": 65, "confidence": "low"}, {"file": "components\\CopyButton.tsx", "name": "Copy<PERSON><PERSON><PERSON>", "type": "default", "line": 51, "confidence": "low"}, {"file": "components\\curriculum\\CurriculumDashboard.tsx", "name": "CurriculumDashboard", "type": "named", "line": 38, "confidence": "low"}, {"file": "components\\curriculum\\LearningAnalytics.tsx", "name": "LearningAnalytics", "type": "named", "line": 33, "confidence": "low"}, {"file": "components\\curriculum\\LearningPathVisualization.tsx", "name": "LearningPathVisualization", "type": "named", "line": 33, "confidence": "low"}, {"file": "components\\curriculum\\LessonCard.tsx", "name": "LessonCard", "type": "named", "line": 36, "confidence": "low"}, {"file": "components\\curriculum\\ModuleCard.tsx", "name": "ModuleCard", "type": "named", "line": 33, "confidence": "low"}, {"file": "components\\curriculum\\PrerequisiteDisplay.tsx", "name": "PrerequisiteDisplay", "type": "named", "line": 29, "confidence": "low"}, {"file": "components\\debugging\\SolidityDebuggerInterface.tsx", "name": "SolidityDebuggerInterfaceProps", "type": "named", "line": 33, "confidence": "low"}, {"file": "components\\debugging\\SolidityDebuggerInterface.tsx", "name": "SolidityDebuggerInterface", "type": "named", "line": 44, "confidence": "low"}, {"file": "components\\dev\\AccessibilityTester.tsx", "name": "AccessibilityTester", "type": "default", "line": 286, "confidence": "low"}, {"file": "components\\discovery\\DiscoveryProvider.tsx", "name": "DiscoveryProvider", "type": "default", "line": 302, "confidence": "low"}, {"file": "components\\discovery\\DiscoveryProvider.tsx", "name": "useDiscovery", "type": "named", "line": 181, "confidence": "low"}, {"file": "components\\discovery\\DiscoveryProvider.tsx", "name": "FeatureTracker", "type": "named", "line": 198, "confidence": "low"}, {"file": "components\\discovery\\DiscoveryProvider.tsx", "name": "DiscoverySettings", "type": "named", "line": 237, "confidence": "low"}, {"file": "components\\discovery\\FeatureSpotlight.tsx", "name": "FeatureSpotlight", "type": "default", "line": 381, "confidence": "low"}, {"file": "components\\discovery\\FeatureSpotlight.tsx", "name": "PLATFORM_FEATURES", "type": "named", "line": 311, "confidence": "low"}, {"file": "components\\discovery\\SmartTooltip.tsx", "name": "SmartTooltip", "type": "default", "line": 379, "confidence": "low"}, {"file": "components\\editor\\AdvancedCollaborativeMonacoEditor.tsx", "name": "AdvancedCollaborativeMonacoEditorProps", "type": "named", "line": 30, "confidence": "low"}, {"file": "components\\editor\\AdvancedCollaborativeMonacoEditor.tsx", "name": "AdvancedCollaborativeMonacoEditor", "type": "named", "line": 47, "confidence": "low"}, {"file": "components\\editor\\AdvancedIDEInterface.tsx", "name": "IDELayout", "type": "named", "line": 36, "confidence": "low"}, {"file": "components\\editor\\AdvancedIDEInterface.tsx", "name": "AdvancedIDEInterfaceProps", "type": "named", "line": 67, "confidence": "low"}, {"file": "components\\editor\\AdvancedIDEInterface.tsx", "name": "AdvancedIDEInterface", "type": "named", "line": 107, "confidence": "low"}, {"file": "components\\error\\ErrorBoundaryFallback.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "default", "line": 75, "confidence": "low"}, {"file": "components\\error-handling\\AsyncErrorBoundary.tsx", "name": "AsyncErrorBoundary", "type": "named", "line": 33, "confidence": "low"}, {"file": "components\\error-handling\\AsyncErrorBoundary.tsx", "name": "ApiErrorBoundary", "type": "named", "line": 316, "confidence": "low"}, {"file": "components\\error-handling\\AsyncErrorBoundary.tsx", "name": "UploadErrorBoundary", "type": "named", "line": 324, "confidence": "low"}, {"file": "components\\error-handling\\AsyncErrorBoundary.tsx", "name": "AuthErrorBoundary", "type": "named", "line": 332, "confidence": "low"}, {"file": "components\\error-handling\\ErrorBoundary.tsx", "name": "Error<PERSON>ou<PERSON><PERSON>", "type": "named", "line": 40, "confidence": "low"}, {"file": "components\\error-handling\\ErrorBoundary.tsx", "name": "Page<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 492, "confidence": "low"}, {"file": "components\\error-handling\\ErrorBoundary.tsx", "name": "SectionErrorBoundary", "type": "named", "line": 500, "confidence": "low"}, {"file": "components\\error-handling\\ErrorBoundary.tsx", "name": "ComponentErrorBoundary", "type": "named", "line": 508, "confidence": "low"}, {"file": "components\\error-handling\\NotFoundPage.tsx", "name": "NotFoundPage", "type": "named", "line": 93, "confidence": "low"}, {"file": "components\\errors\\ErrorBoundary.tsx", "name": "Error<PERSON>ou<PERSON><PERSON>", "type": "named", "line": 30, "confidence": "low"}, {"file": "components\\errors\\ErrorBoundary.tsx", "name": "Page<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 362, "confidence": "low"}, {"file": "components\\errors\\ErrorBoundary.tsx", "name": "FeatureErrorBoundary", "type": "named", "line": 370, "confidence": "low"}, {"file": "components\\errors\\ErrorBoundary.tsx", "name": "ComponentErrorBoundary", "type": "named", "line": 378, "confidence": "low"}, {"file": "components\\errors\\SpecializedErrorBoundaries.tsx", "name": "CodeEditorErrorBoundary", "type": "named", "line": 11, "confidence": "low"}, {"file": "components\\errors\\SpecializedErrorBoundaries.tsx", "name": "LearningModuleErrorBoundary", "type": "named", "line": 79, "confidence": "low"}, {"file": "components\\errors\\SpecializedErrorBoundaries.tsx", "name": "AuthErrorBoundary", "type": "named", "line": 137, "confidence": "low"}, {"file": "components\\errors\\SpecializedErrorBoundaries.tsx", "name": "FileUploadErrorBoundary", "type": "named", "line": 192, "confidence": "low"}, {"file": "components\\errors\\SpecializedErrorBoundaries.tsx", "name": "NetworkErrorBoundary", "type": "named", "line": 229, "confidence": "low"}, {"file": "components\\errors\\SpecializedErrorBoundaries.tsx", "name": "SettingsErrorBoundary", "type": "named", "line": 287, "confidence": "low"}, {"file": "components\\errors\\SpecializedErrorBoundaries.tsx", "name": "AsyncComponentErrorBoundary", "type": "named", "line": 345, "confidence": "low"}, {"file": "components\\forms\\ContactForm.tsx", "name": "ContactForm", "type": "named", "line": 28, "confidence": "low"}, {"file": "components\\forms\\ContactForm.tsx", "name": "ContactFormPage", "type": "named", "line": 214, "confidence": "low"}, {"file": "components\\GeminiChat.tsx", "name": "GeminiChat", "type": "default", "line": 122, "confidence": "low"}, {"file": "components\\help\\ContextualTooltip.tsx", "name": "ContextualTooltip", "type": "default", "line": 334, "confidence": "low"}, {"file": "components\\help\\ContextualTooltip.tsx", "name": "TOOLTIP_CONTENTS", "type": "named", "line": 291, "confidence": "low"}, {"file": "components\\help\\HelpProvider.tsx", "name": "HelpProvider", "type": "default", "line": 219, "confidence": "low"}, {"file": "components\\help\\HelpProvider.tsx", "name": "useHelp", "type": "named", "line": 161, "confidence": "low"}, {"file": "components\\help\\HelpProvider.tsx", "name": "HelpTrigger", "type": "named", "line": 177, "confidence": "low"}, {"file": "components\\help\\HelpProvider.tsx", "name": "ShortcutsTrigger", "type": "named", "line": 202, "confidence": "low"}, {"file": "components\\help\\HelpSystem.tsx", "name": "HelpSystem", "type": "default", "line": 378, "confidence": "low"}, {"file": "components\\help\\KeyboardShortcuts.tsx", "name": "KeyboardShortcuts", "type": "default", "line": 273, "confidence": "low"}, {"file": "components\\icons\\BotIcon.tsx", "name": "BotIcon", "type": "default", "line": 22, "confidence": "low"}, {"file": "components\\icons\\CheckIcon.tsx", "name": "CheckIcon", "type": "default", "line": 18, "confidence": "low"}, {"file": "components\\icons\\MenuIcon.tsx", "name": "MenuIcon", "type": "default", "line": 36, "confidence": "low"}, {"file": "components\\icons\\SendIcon.tsx", "name": "SendIcon", "type": "default", "line": 17, "confidence": "low"}, {"file": "components\\icons\\SpinnerIcon.tsx", "name": "SpinnerIcon", "type": "default", "line": 29, "confidence": "low"}, {"file": "components\\icons\\UserIcon.tsx", "name": "UserIcon", "type": "default", "line": 19, "confidence": "low"}, {"file": "components\\LandingPage.tsx", "name": "LandingPage", "type": "default", "line": 219, "confidence": "low"}, {"file": "components\\layout\\Footer.tsx", "name": "Footer", "type": "named", "line": 6, "confidence": "low"}, {"file": "components\\layout\\Navigation.tsx", "name": "Navigation", "type": "named", "line": 44, "confidence": "low"}, {"file": "components\\lazy\\LazyComponents.tsx", "name": "LazyGamificationDashboard", "type": "named", "line": 78, "confidence": "low"}, {"file": "components\\lazy\\LazyComponents.tsx", "name": "LazyAITutoringInterface", "type": "named", "line": 126, "confidence": "low"}, {"file": "components\\lazy\\LazyComponents.tsx", "name": "LazyRealTimeCollaboration", "type": "named", "line": 174, "confidence": "low"}, {"file": "components\\lazy\\LazyComponents.tsx", "name": "LazyAdvancedAnalytics", "type": "named", "line": 228, "confidence": "low"}, {"file": "components\\lazy\\LazyComponents.tsx", "name": "LazyUserManagement", "type": "named", "line": 283, "confidence": "low"}, {"file": "components\\lazy\\LazyComponents.tsx", "name": "preloadGamificationDashboard", "type": "named", "line": 314, "confidence": "low"}, {"file": "components\\lazy\\LazyComponents.tsx", "name": "preloadAITutoringInterface", "type": "named", "line": 320, "confidence": "low"}, {"file": "components\\lazy\\LazyComponents.tsx", "name": "preloadRealTimeCollaboration", "type": "named", "line": 326, "confidence": "low"}, {"file": "components\\lazy\\LazyComponents.tsx", "name": "preloadAdvancedAnalytics", "type": "named", "line": 332, "confidence": "low"}, {"file": "components\\lazy\\LazyComponents.tsx", "name": "preloadAllHeavyComponents", "type": "named", "line": 339, "confidence": "low"}, {"file": "components\\lazy\\LazyMonacoEditor.tsx", "name": "LazyMonacoEditor", "type": "default", "line": 147, "confidence": "low"}, {"file": "components\\lazy\\LazyMonacoEditor.tsx", "name": "preloadMonacoEditor", "type": "named", "line": 116, "confidence": "low"}, {"file": "components\\lazy\\LazyMonacoEditor.tsx", "name": "useMonacoEditor", "type": "named", "line": 131, "confidence": "low"}, {"file": "components\\learning\\ComprehensiveLearningPlatform.tsx", "name": "ComprehensiveLearningPlatform", "type": "default", "line": 602, "confidence": "low"}, {"file": "components\\learning\\GamificationSystem.tsx", "name": "GamificationSystem", "type": "default", "line": 653, "confidence": "low"}, {"file": "components\\learning\\InteractiveCodeEditor.tsx", "name": "InteractiveCodeEditor", "type": "default", "line": 1939, "confidence": "low"}, {"file": "components\\learning\\LearningDashboard.tsx", "name": "LearningDashboard", "type": "named", "line": 49, "confidence": "low"}, {"file": "components\\learning\\LearningDashboard.tsx", "name": "StaticLearningDashboard", "type": "named", "line": 576, "confidence": "low"}, {"file": "components\\learning\\LessonProgressTracker.tsx", "name": "LessonProgressTracker", "type": "named", "line": 42, "confidence": "low"}, {"file": "components\\learning\\ProjectBasedLearning.tsx", "name": "ProjectBasedLearning", "type": "default", "line": 764, "confidence": "low"}, {"file": "components\\learning\\StructuredCurriculum.tsx", "name": "StructuredCurriculum", "type": "default", "line": 775, "confidence": "low"}, {"file": "components\\MobileNavigation.tsx", "name": "MobileNavigation", "type": "default", "line": 165, "confidence": "low"}, {"file": "components\\ModuleContent.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "default", "line": 260, "confidence": "low"}, {"file": "components\\monitoring\\PerformanceMonitor.tsx", "name": "PerformanceMonitor", "type": "default", "line": 300, "confidence": "low"}, {"file": "components\\monitoring\\PerformanceMonitor.tsx", "name": "usePerformanceMetrics", "type": "named", "line": 242, "confidence": "low"}, {"file": "components\\navigation\\AuthenticatedNavbar.tsx", "name": "AuthenticatedNavbar", "type": "named", "line": 23, "confidence": "low"}, {"file": "components\\navigation\\GuidedOnboarding.tsx", "name": "GuidedOnboarding", "type": "named", "line": 51, "confidence": "low"}, {"file": "components\\navigation\\NavigationFlowOptimizer.tsx", "name": "NavigationFlowOptimizer", "type": "named", "line": 55, "confidence": "low"}, {"file": "components\\navigation\\SmartNavigation.tsx", "name": "SmartBackButton", "type": "named", "line": 30, "confidence": "low"}, {"file": "components\\navigation\\SmartNavigation.tsx", "name": "SmartBreadcrumbs", "type": "named", "line": 108, "confidence": "low"}, {"file": "components\\navigation\\SmartNavigation.tsx", "name": "ContinueLearning", "type": "named", "line": 187, "confidence": "low"}, {"file": "components\\navigation\\SmartNavigation.tsx", "name": "NavigationStatus", "type": "named", "line": 363, "confidence": "low"}, {"file": "components\\notifications\\NotificationIntegrations.tsx", "name": "NotificationIntegrations", "type": "named", "line": 19, "confidence": "low"}, {"file": "components\\notifications\\NotificationIntegrations.tsx", "name": "useManualNotificationTriggers", "type": "named", "line": 282, "confidence": "low"}, {"file": "components\\onboarding\\InteractiveTutorial.tsx", "name": "InteractiveTutorial", "type": "default", "line": 321, "confidence": "low"}, {"file": "components\\onboarding\\OnboardingFlow.tsx", "name": "OnboardingFlow", "type": "default", "line": 578, "confidence": "low"}, {"file": "components\\onboarding\\OnboardingFlow.tsx", "name": "TutorialStep", "type": "named", "line": 482, "confidence": "low"}, {"file": "components\\onboarding\\OnboardingFlow.tsx", "name": "FEATURE_TUTORIALS", "type": "named", "line": 492, "confidence": "low"}, {"file": "components\\performance\\PerformanceOptimizer.tsx", "name": "PerformanceOptimizer", "type": "default", "line": 279, "confidence": "low"}, {"file": "components\\performance\\PerformanceOptimizer.tsx", "name": "CriticalCSS", "type": "named", "line": 142, "confidence": "low"}, {"file": "components\\performance\\PerformanceOptimizer.tsx", "name": "Font<PERSON><PERSON><PERSON>der", "type": "named", "line": 194, "confidence": "low"}, {"file": "components\\performance\\PerformanceOptimizer.tsx", "name": "ResourceHints", "type": "named", "line": 216, "confidence": "low"}, {"file": "components\\performance\\PerformanceOptimizer.tsx", "name": "PerformanceMonitor", "type": "named", "line": 252, "confidence": "low"}, {"file": "components\\performance\\ServiceWorkerManager.tsx", "name": "ServiceWorkerManager", "type": "default", "line": 335, "confidence": "low"}, {"file": "components\\performance\\ServiceWorkerManager.tsx", "name": "useServiceWorker", "type": "named", "line": 292, "confidence": "low"}, {"file": "components\\profile\\UserProfile.tsx", "name": "UserProfile", "type": "named", "line": 68, "confidence": "low"}, {"file": "components\\progress\\ProgressDashboard.tsx", "name": "ProgressDashboard", "type": "default", "line": 796, "confidence": "low"}, {"file": "components\\providers\\FallbackProvider.tsx", "name": "useFallback", "type": "named", "line": 20, "confidence": "low"}, {"file": "components\\providers\\FallbackProvider.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 37, "confidence": "low"}, {"file": "components\\providers\\FallbackProvider.tsx", "name": "withFallbackProtection", "type": "named", "line": 143, "confidence": "low"}, {"file": "components\\providers\\FallbackProvider.tsx", "name": "useAsyncFallback", "type": "named", "line": 178, "confidence": "low"}, {"file": "components\\providers\\FallbackProvider.tsx", "name": "ErrorMetrics", "type": "named", "line": 221, "confidence": "low"}, {"file": "components\\providers\\FallbackProvider.tsx", "name": "setupGlobalErrorHandling", "type": "named", "line": 282, "confidence": "low"}, {"file": "components\\providers\\SessionProvider.tsx", "name": "Session<PERSON>rov<PERSON>", "type": "named", "line": 11, "confidence": "low"}, {"file": "components\\QuizComponent.tsx", "name": "QuizComponent", "type": "default", "line": 152, "confidence": "low"}, {"file": "components\\sections\\CompetitiveAnalysisSection.tsx", "name": "CompetitiveAnalysisSection", "type": "named", "line": 6, "confidence": "low"}, {"file": "components\\sections\\CTASection.tsx", "name": "CTASection", "type": "named", "line": 6, "confidence": "low"}, {"file": "components\\sections\\EnhancedFeaturesShowcase.tsx", "name": "EnhancedFeaturesShowcase", "type": "named", "line": 29, "confidence": "low"}, {"file": "components\\sections\\FeaturesSection.tsx", "name": "FeaturesSection", "type": "named", "line": 18, "confidence": "low"}, {"file": "components\\sections\\GamificationPreview.tsx", "name": "GamificationPreview", "type": "named", "line": 40, "confidence": "low"}, {"file": "components\\sections\\HeroSection.tsx", "name": "HeroSection", "type": "named", "line": 13, "confidence": "low"}, {"file": "components\\sections\\InteractiveDemoSection.tsx", "name": "InteractiveDemoSection", "type": "named", "line": 32, "confidence": "low"}, {"file": "components\\sections\\TestimonialsSection.tsx", "name": "TestimonialsSection", "type": "named", "line": 7, "confidence": "low"}, {"file": "components\\settings\\AccessibilitySection.tsx", "name": "AccessibilitySectionProps", "type": "named", "line": 29, "confidence": "low"}, {"file": "components\\settings\\AccessibilitySection.tsx", "name": "AccessibilitySection", "type": "named", "line": 36, "confidence": "low"}, {"file": "components\\settings\\LearningPreferencesSection.tsx", "name": "LearningPreferencesSectionProps", "type": "named", "line": 38, "confidence": "low"}, {"file": "components\\settings\\LearningPreferencesSection.tsx", "name": "LearningPreferencesSection", "type": "named", "line": 48, "confidence": "low"}, {"file": "components\\settings\\NotificationSection.tsx", "name": "NotificationSectionProps", "type": "named", "line": 29, "confidence": "low"}, {"file": "components\\settings\\NotificationSection.tsx", "name": "NotificationSection", "type": "named", "line": 36, "confidence": "low"}, {"file": "components\\settings\\PrivacySection.tsx", "name": "PrivacySectionProps", "type": "named", "line": 29, "confidence": "low"}, {"file": "components\\settings\\PrivacySection.tsx", "name": "PrivacySection", "type": "named", "line": 38, "confidence": "low"}, {"file": "components\\settings\\PrivacySection.tsx", "name": "handleDataExport", "type": "named", "line": 56, "confidence": "low"}, {"file": "components\\settings\\ProfileSection.tsx", "name": "ProfileSectionProps", "type": "named", "line": 26, "confidence": "low"}, {"file": "components\\settings\\ProfileSection.tsx", "name": "ProfileSection", "type": "named", "line": 34, "confidence": "low"}, {"file": "components\\settings\\SecuritySection.tsx", "name": "SecuritySectionProps", "type": "named", "line": 29, "confidence": "low"}, {"file": "components\\settings\\SecuritySection.tsx", "name": "SecuritySection", "type": "named", "line": 52, "confidence": "low"}, {"file": "components\\settings\\SettingsPage.tsx", "name": "SettingsPageProps", "type": "named", "line": 31, "confidence": "low"}, {"file": "components\\settings\\SettingsPage.tsx", "name": "SettingsPage", "type": "named", "line": 76, "confidence": "low"}, {"file": "components\\Sidebar.tsx", "name": "Sidebar", "type": "default", "line": 111, "confidence": "low"}, {"file": "components\\testing\\FeedbackCollectionSystem.tsx", "name": "FeedbackCollectionSystem", "type": "default", "line": 595, "confidence": "low"}, {"file": "components\\testing\\NotificationTestingPage.tsx", "name": "NotificationTestingPage", "type": "named", "line": 33, "confidence": "low"}, {"file": "components\\testing\\UATDashboard.tsx", "name": "UATDashboard", "type": "default", "line": 564, "confidence": "low"}, {"file": "components\\ui\\Accessibility.tsx", "name": "SkipLink", "type": "default", "line": 586, "confidence": "low"}, {"file": "components\\ui\\AccessibleForm.tsx", "name": "AccessibleField", "type": "default", "line": 330, "confidence": "low"}, {"file": "components\\ui\\AccessibleForm.tsx", "name": "AccessibleForm", "type": "named", "line": 178, "confidence": "low"}, {"file": "components\\ui\\AccessibleForm.tsx", "name": "FormSuccess", "type": "named", "line": 222, "confidence": "low"}, {"file": "components\\ui\\AccessibleForm.tsx", "name": "FormErrorSummary", "type": "named", "line": 247, "confidence": "low"}, {"file": "components\\ui\\AccessibleForm.tsx", "name": "AccessibleSubmitButton", "type": "named", "line": 289, "confidence": "low"}, {"file": "components\\ui\\AnimatedButton.tsx", "name": "AnimatedButton", "type": "default", "line": 105, "confidence": "low"}, {"file": "components\\ui\\AnimationShowcase.tsx", "name": "AnimationShowcase", "type": "default", "line": 314, "confidence": "low"}, {"file": "components\\ui\\badge.tsx", "name": "BadgeProps", "type": "named", "line": 28, "confidence": "low"}, {"file": "components\\ui\\Branding.tsx", "name": "Logo", "type": "default", "line": 308, "confidence": "low"}, {"file": "components\\ui\\button.tsx", "name": "ButtonProps", "type": "named", "line": 38, "confidence": "low"}, {"file": "components\\ui\\ButtonTesting.tsx", "name": "ButtonTesting", "type": "named", "line": 34, "confidence": "low"}, {"file": "components\\ui\\CelebrationAnimations.tsx", "name": "ConfettiExplosion", "type": "named", "line": 34, "confidence": "low"}, {"file": "components\\ui\\CelebrationAnimations.tsx", "name": "CelebrationModal", "type": "named", "line": 118, "confidence": "low"}, {"file": "components\\ui\\CelebrationAnimations.tsx", "name": "QuickSuccessAnimation", "type": "named", "line": 368, "confidence": "low"}, {"file": "components\\ui\\CelebrationAnimations.tsx", "name": "ProgressCelebration", "type": "named", "line": 439, "confidence": "low"}, {"file": "components\\ui\\checkbox.tsx", "name": "Checkbox", "type": "default", "line": 63, "confidence": "low"}, {"file": "components\\ui\\ContextualHelp.tsx", "name": "ContextualHelp", "type": "named", "line": 164, "confidence": "low"}, {"file": "components\\ui\\ContextualHelp.tsx", "name": "QuickHelp", "type": "named", "line": 404, "confidence": "low"}, {"file": "components\\ui\\CustomToast.tsx", "name": "CustomToast", "type": "default", "line": 58, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "EmptyState", "type": "named", "line": 43, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "EmptyCoursesState", "type": "named", "line": 106, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "EmptySearchState", "type": "named", "line": 124, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "EmptyLessonsState", "type": "named", "line": 146, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "EmptyProgressState", "type": "named", "line": 164, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "EmptyNotificationsState", "type": "named", "line": 182, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "EmptyAchievementsState", "type": "named", "line": 197, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "EmptyPlaygroundState", "type": "named", "line": 215, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "ErrorState", "type": "named", "line": 235, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "OfflineState", "type": "named", "line": 264, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "LoadingErrorState", "type": "named", "line": 279, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "UnauthorizedState", "type": "named", "line": 297, "confidence": "low"}, {"file": "components\\ui\\EmptyState.tsx", "name": "MaintenanceState", "type": "named", "line": 315, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "<PERSON><PERSON><PERSON>", "type": "named", "line": 123, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "Variants", "type": "named", "line": 132, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "Sizes", "type": "named", "line": 154, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "WithIcons", "type": "named", "line": 177, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "LoadingStates", "type": "named", "line": 206, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "DisabledStates", "type": "named", "line": 230, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "MobileOptimized", "type": "named", "line": 254, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "WithRipple", "type": "named", "line": 288, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "AccessibilityDemo", "type": "named", "line": 305, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "RealWorldExamples", "type": "named", "line": 363, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.stories.tsx", "name": "meta", "type": "default", "line": 117, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.tsx", "name": "Enhanced<PERSON><PERSON><PERSON>", "type": "named", "line": 45, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.tsx", "name": "PrimaryButton", "type": "named", "line": 383, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.tsx", "name": "SecondaryButton", "type": "named", "line": 400, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.tsx", "name": "SuccessButton", "type": "named", "line": 416, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.tsx", "name": "DangerButton", "type": "named", "line": 430, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.tsx", "name": "FloatingActionButton", "type": "named", "line": 443, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.tsx", "name": "AsyncSubmitButton", "type": "named", "line": 462, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.tsx", "name": "AsyncSaveButton", "type": "named", "line": 487, "confidence": "low"}, {"file": "components\\ui\\EnhancedButton.tsx", "name": "AsyncDeleteButton", "type": "named", "line": 518, "confidence": "low"}, {"file": "components\\ui\\EnhancedCard.tsx", "name": "EnhancedCard", "type": "default", "line": 58, "confidence": "low"}, {"file": "components\\ui\\EnhancedLoadingStates.tsx", "name": "FormLoadingOverlay", "type": "named", "line": 33, "confidence": "low"}, {"file": "components\\ui\\EnhancedLoadingStates.tsx", "name": "FileUploadProgress", "type": "named", "line": 160, "confidence": "low"}, {"file": "components\\ui\\EnhancedLoadingStates.tsx", "name": "SearchLoading", "type": "named", "line": 336, "confidence": "low"}, {"file": "components\\ui\\EnhancedProgress.tsx", "name": "EnhancedProgress", "type": "named", "line": 44, "confidence": "low"}, {"file": "components\\ui\\EnhancedProgress.tsx", "name": "XPProgress", "type": "named", "line": 311, "confidence": "low"}, {"file": "components\\ui\\EnhancedProgress.tsx", "name": "LearningProgress", "type": "named", "line": 324, "confidence": "low"}, {"file": "components\\ui\\EnhancedProgress.tsx", "name": "AchievementProgress", "type": "named", "line": 336, "confidence": "low"}, {"file": "components\\ui\\ErrorHandling.tsx", "name": "Error<PERSON>ou<PERSON><PERSON>", "type": "default", "line": 321, "confidence": "low"}, {"file": "components\\ui\\ErrorMessage.tsx", "name": "ErrorMessageProps", "type": "named", "line": 24, "confidence": "low"}, {"file": "components\\ui\\ErrorMessage.tsx", "name": "ErrorMessage", "type": "named", "line": 70, "confidence": "low"}, {"file": "components\\ui\\ErrorMessage.tsx", "name": "ErrorToast", "type": "named", "line": 265, "confidence": "low"}, {"file": "components\\ui\\ErrorMessage.tsx", "name": "InlineFormError", "type": "named", "line": 299, "confidence": "low"}, {"file": "components\\ui\\ErrorMessage.tsx", "name": "<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "type": "named", "line": 327, "confidence": "low"}, {"file": "components\\ui\\ErrorMessage.tsx", "name": "ErrorPage", "type": "named", "line": 353, "confidence": "low"}, {"file": "components\\ui\\ErrorTesting.tsx", "name": "ErrorTesting", "type": "named", "line": 24, "confidence": "low"}, {"file": "components\\ui\\FeatureState.tsx", "name": "FeatureStateComponent", "type": "named", "line": 35, "confidence": "low"}, {"file": "components\\ui\\FeatureState.tsx", "name": "FeatureGate", "type": "named", "line": 325, "confidence": "low"}, {"file": "components\\ui\\FeatureState.tsx", "name": "ComingSoonPlaceholder", "type": "named", "line": 370, "confidence": "low"}, {"file": "components\\ui\\FeatureState.tsx", "name": "BetaFeatureNotice", "type": "named", "line": 404, "confidence": "low"}, {"file": "components\\ui\\FeedbackIndicators.tsx", "name": "FeedbackIndicatorProps", "type": "named", "line": 10, "confidence": "low"}, {"file": "components\\ui\\FeedbackIndicators.tsx", "name": "FeedbackIndicator", "type": "named", "line": 75, "confidence": "low"}, {"file": "components\\ui\\FeedbackIndicators.tsx", "name": "SuccessIndicator", "type": "named", "line": 123, "confidence": "low"}, {"file": "components\\ui\\FeedbackIndicators.tsx", "name": "ErrorIndicator", "type": "named", "line": 172, "confidence": "low"}, {"file": "components\\ui\\FeedbackIndicators.tsx", "name": "InlineFeedback", "type": "named", "line": 192, "confidence": "low"}, {"file": "components\\ui\\FeedbackIndicators.tsx", "name": "ToastNotification", "type": "named", "line": 226, "confidence": "low"}, {"file": "components\\ui\\FeedbackIndicators.tsx", "name": "ProgressFeedback", "type": "named", "line": 289, "confidence": "low"}, {"file": "components\\ui\\FeedbackIndicators.tsx", "name": "GlassErrorFeedback", "type": "named", "line": 339, "confidence": "low"}, {"file": "components\\ui\\FeedbackIndicators.tsx", "name": "NeumorphismErrorFeedback", "type": "named", "line": 429, "confidence": "low"}, {"file": "components\\ui\\GlassCard.stories.tsx", "name": "<PERSON><PERSON><PERSON>", "type": "named", "line": 96, "confidence": "low"}, {"file": "components\\ui\\GlassCard.stories.tsx", "name": "HighBlur", "type": "named", "line": 113, "confidence": "low"}, {"file": "components\\ui\\GlassCard.stories.tsx", "name": "Subtle", "type": "named", "line": 132, "confidence": "low"}, {"file": "components\\ui\\GlassCard.stories.tsx", "name": "Interactive", "type": "named", "line": 151, "confidence": "low"}, {"file": "components\\ui\\GlassCard.stories.tsx", "name": "Achievement", "type": "named", "line": 173, "confidence": "low"}, {"file": "components\\ui\\GlassCard.stories.tsx", "name": "Notification", "type": "named", "line": 198, "confidence": "low"}, {"file": "components\\ui\\GlassCard.stories.tsx", "name": "HighContrast", "type": "named", "line": 221, "confidence": "low"}, {"file": "components\\ui\\GlassCard.stories.tsx", "name": "Mobile", "type": "named", "line": 255, "confidence": "low"}, {"file": "components\\ui\\GlassCard.stories.tsx", "name": "meta", "type": "default", "line": 90, "confidence": "low"}, {"file": "components\\ui\\Glassmorphism.tsx", "name": "GlassContainer", "type": "default", "line": 377, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "GlassmorphismButtonProps", "type": "named", "line": 9, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "GlassmorphismButton", "type": "named", "line": 54, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "NeumorphismButtonProps", "type": "named", "line": 105, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "NeumorphismButton", "type": "named", "line": 124, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "GlassPrimaryButton", "type": "named", "line": 160, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "GlassSecondaryButton", "type": "named", "line": 171, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "GlassSuccessButton", "type": "named", "line": 181, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "GlassDangerButton", "type": "named", "line": 192, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "GlassFloatingActionButton", "type": "named", "line": 204, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "GlassButtonGroup", "type": "named", "line": 228, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismButtons.tsx", "name": "ButtonShowcase", "type": "named", "line": 258, "confidence": "low"}, {"file": "components\\ui\\GlassmorphismModal.tsx", "name": "GlassmorphismModal", "type": "default", "line": 119, "confidence": "low"}, {"file": "components\\ui\\GlassNeumorphDemo.tsx", "name": "GlassNeumorphDemo", "type": "default", "line": 318, "confidence": "low"}, {"file": "components\\ui\\GSAPAnimations.tsx", "name": "GSAPTimelineAnimation", "type": "default", "line": 374, "confidence": "low"}, {"file": "components\\ui\\GSAPAnimations.tsx", "name": "GSAPTextAnimation", "type": "named", "line": 108, "confidence": "low"}, {"file": "components\\ui\\GSAPAnimations.tsx", "name": "GSAPMorphingShape", "type": "named", "line": 178, "confidence": "low"}, {"file": "components\\ui\\GSAPAnimations.tsx", "name": "GSAPScrollAnimation", "type": "named", "line": 237, "confidence": "low"}, {"file": "components\\ui\\GSAPAnimations.tsx", "name": "GSAPMagneticButton", "type": "named", "line": 320, "confidence": "low"}, {"file": "components\\ui\\input.tsx", "name": "InputProps", "type": "named", "line": 5, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "LazyLoadingWrapper", "type": "named", "line": 31, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "LazyCodeEditor", "type": "named", "line": 100, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "LazyVideoPlayer", "type": "named", "line": 113, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "LazyThreeVisualization", "type": "named", "line": 126, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "LazyAdvancedSettings", "type": "named", "line": 139, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "LazyCollaborationPanel", "type": "named", "line": 152, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "LazyAnalyticsDashboard", "type": "named", "line": 165, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "useDebouncedLoading", "type": "named", "line": 179, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "DebouncedLoading", "type": "named", "line": 206, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "ProgressiveLoading", "type": "named", "line": 252, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "IntersectionLoading", "type": "named", "line": 299, "confidence": "low"}, {"file": "components\\ui\\LazyLoadingComponents.tsx", "name": "NetworkAwareLoading", "type": "named", "line": 342, "confidence": "low"}, {"file": "components\\ui\\LoadingSkeletons.tsx", "name": "Skeleton", "type": "default", "line": 257, "confidence": "low"}, {"file": "components\\ui\\LoadingSkeletons.tsx", "name": "MonacoEditorSkeleton", "type": "named", "line": 60, "confidence": "low"}, {"file": "components\\ui\\LoadingSkeletons.tsx", "name": "DashboardCardSkeleton", "type": "named", "line": 86, "confidence": "low"}, {"file": "components\\ui\\LoadingSkeletons.tsx", "name": "LessonCardSkeleton", "type": "named", "line": 102, "confidence": "low"}, {"file": "components\\ui\\LoadingSkeletons.tsx", "name": "UserProfileSkeleton", "type": "named", "line": 123, "confidence": "low"}, {"file": "components\\ui\\LoadingSkeletons.tsx", "name": "ChatMessageSkeleton", "type": "named", "line": 150, "confidence": "low"}, {"file": "components\\ui\\LoadingSkeletons.tsx", "name": "AchievementCardSkeleton", "type": "named", "line": 167, "confidence": "low"}, {"file": "components\\ui\\LoadingSkeletons.tsx", "name": "NavigationSkeleton", "type": "named", "line": 185, "confidence": "low"}, {"file": "components\\ui\\LoadingSkeletons.tsx", "name": "TableSkeleton", "type": "named", "line": 208, "confidence": "low"}, {"file": "components\\ui\\LoadingSkeletons.tsx", "name": "PageSkeleton", "type": "named", "line": 240, "confidence": "low"}, {"file": "components\\ui\\LoadingSpinner.tsx", "name": "LoadingSpinnerProps", "type": "named", "line": 8, "confidence": "low"}, {"file": "components\\ui\\LoadingSpinner.tsx", "name": "LoadingSpinner", "type": "named", "line": 42, "confidence": "low"}, {"file": "components\\ui\\LoadingSpinner.tsx", "name": "ButtonLoadingSpinner", "type": "named", "line": 210, "confidence": "low"}, {"file": "components\\ui\\LoadingSpinner.tsx", "name": "PageLoadingSpinner", "type": "named", "line": 221, "confidence": "low"}, {"file": "components\\ui\\LoadingStates.tsx", "name": "LoadingSpinner", "type": "default", "line": 402, "confidence": "low"}, {"file": "components\\ui\\LottieAnimations.tsx", "name": "<PERSON>tiePlayer", "type": "default", "line": 419, "confidence": "low"}, {"file": "components\\ui\\LottieAnimations.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 223, "confidence": "low"}, {"file": "components\\ui\\LottieAnimations.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 305, "confidence": "low"}, {"file": "components\\ui\\LottieAnimations.tsx", "name": "LottieSuccess", "type": "named", "line": 335, "confidence": "low"}, {"file": "components\\ui\\LottieAnimations.tsx", "name": "<PERSON><PERSON>ScrollTrigger", "type": "named", "line": 367, "confidence": "low"}, {"file": "components\\ui\\Neumorphism.tsx", "name": "NeumorphicContainer", "type": "default", "line": 419, "confidence": "low"}, {"file": "components\\ui\\NotificationCenter.tsx", "name": "NotificationCenter", "type": "named", "line": 31, "confidence": "low"}, {"file": "components\\ui\\NotificationHistory.tsx", "name": "NotificationHistoryModal", "type": "named", "line": 33, "confidence": "low"}, {"file": "components\\ui\\NotificationPreferences.tsx", "name": "NotificationPreferencesModal", "type": "named", "line": 26, "confidence": "low"}, {"file": "components\\ui\\NotificationSystem.tsx", "name": "NotificationType", "type": "named", "line": 46, "confidence": "low"}, {"file": "components\\ui\\NotificationSystem.tsx", "name": "NotificationPosition", "type": "named", "line": 58, "confidence": "low"}, {"file": "components\\ui\\NotificationSystem.tsx", "name": "NotificationVariant", "type": "named", "line": 60, "confidence": "low"}, {"file": "components\\ui\\NotificationSystem.tsx", "name": "NotificationAction", "type": "named", "line": 62, "confidence": "low"}, {"file": "components\\ui\\NotificationSystem.tsx", "name": "NotificationMetadata", "type": "named", "line": 69, "confidence": "low"}, {"file": "components\\ui\\NotificationSystem.tsx", "name": "Notification", "type": "named", "line": 81, "confidence": "low"}, {"file": "components\\ui\\NotificationSystem.tsx", "name": "NotificationGroup", "type": "named", "line": 100, "confidence": "low"}, {"file": "components\\ui\\NotificationSystem.tsx", "name": "NotificationPreferences", "type": "named", "line": 110, "confidence": "low"}, {"file": "components\\ui\\NotificationSystem.tsx", "name": "NotificationProvider", "type": "named", "line": 263, "confidence": "low"}, {"file": "components\\ui\\NotificationSystem.tsx", "name": "useNotifications", "type": "named", "line": 806, "confidence": "low"}, {"file": "components\\ui\\Onboarding.tsx", "name": "useOnboarding", "type": "named", "line": 31, "confidence": "low"}, {"file": "components\\ui\\Onboarding.tsx", "name": "OnboardingProvider", "type": "default", "line": 533, "confidence": "low"}, {"file": "components\\ui\\Onboarding.tsx", "name": "WelcomeModal", "type": "named", "line": 392, "confidence": "low"}, {"file": "components\\ui\\Onboarding.tsx", "name": "HelpButton", "type": "named", "line": 519, "confidence": "low"}, {"file": "components\\ui\\OptimizedImage.tsx", "name": "OptimizedImage", "type": "default", "line": 292, "confidence": "low"}, {"file": "components\\ui\\OptimizedImage.tsx", "name": "OptimizedAvatar", "type": "named", "line": 208, "confidence": "low"}, {"file": "components\\ui\\OptimizedImage.tsx", "name": "HeroImage", "type": "named", "line": 263, "confidence": "low"}, {"file": "components\\ui\\PageTransition.tsx", "name": "PageTransition", "type": "default", "line": 289, "confidence": "low"}, {"file": "components\\ui\\SaveStatusIndicator.tsx", "name": "SaveStatusIndicator", "type": "named", "line": 27, "confidence": "low"}, {"file": "components\\ui\\SaveStatusIndicator.tsx", "name": "FloatingSaveStatus", "type": "named", "line": 218, "confidence": "low"}, {"file": "components\\ui\\SaveStatusIndicator.tsx", "name": "SaveProgressIndicator", "type": "named", "line": 294, "confidence": "low"}, {"file": "components\\ui\\SessionStatusIndicator.tsx", "name": "SessionStatusIndicator", "type": "named", "line": 27, "confidence": "low"}, {"file": "components\\ui\\SessionStatusIndicator.tsx", "name": "SessionStatusBadge", "type": "named", "line": 321, "confidence": "low"}, {"file": "components\\ui\\SkeletonLoader.tsx", "name": "Skeleton", "type": "named", "line": 18, "confidence": "low"}, {"file": "components\\ui\\SkeletonLoader.tsx", "name": "CourseCardSkeleton", "type": "named", "line": 82, "confidence": "low"}, {"file": "components\\ui\\SkeletonLoader.tsx", "name": "LessonContentSkeleton", "type": "named", "line": 123, "confidence": "low"}, {"file": "components\\ui\\SkeletonLoader.tsx", "name": "UserProfileSkeleton", "type": "named", "line": 178, "confidence": "low"}, {"file": "components\\ui\\SkeletonLoader.tsx", "name": "DashboardSkeleton", "type": "named", "line": 216, "confidence": "low"}, {"file": "components\\ui\\SkeletonLoader.tsx", "name": "ListSkeleton", "type": "named", "line": 285, "confidence": "low"}, {"file": "components\\ui\\SkeletonLoader.tsx", "name": "TableSkeleton", "type": "named", "line": 310, "confidence": "low"}, {"file": "components\\ui\\SkeletonLoader.tsx", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 339, "confidence": "low"}, {"file": "components\\ui\\slider.tsx", "name": "Slide<PERSON>", "type": "default", "line": 64, "confidence": "low"}, {"file": "components\\ui\\SmartSearch.tsx", "name": "SmartSearch", "type": "named", "line": 95, "confidence": "low"}, {"file": "components\\ui\\SVGAnimations.tsx", "name": "SVGLoadingSpinner", "type": "default", "line": 548, "confidence": "low"}, {"file": "components\\ui\\SVGAnimations.tsx", "name": "SVGCheckmark", "type": "named", "line": 69, "confidence": "low"}, {"file": "components\\ui\\SVGAnimations.tsx", "name": "SVGAnimatedArrow", "type": "named", "line": 121, "confidence": "low"}, {"file": "components\\ui\\SVGAnimations.tsx", "name": "SVGMorphingIcon", "type": "named", "line": 172, "confidence": "low"}, {"file": "components\\ui\\SVGAnimations.tsx", "name": "SVGProgressRing", "type": "named", "line": 210, "confidence": "low"}, {"file": "components\\ui\\SVGAnimations.tsx", "name": "SVGWave", "type": "named", "line": 282, "confidence": "low"}, {"file": "components\\ui\\SVGAnimations.tsx", "name": "SVGBlockchainIcon", "type": "named", "line": 348, "confidence": "low"}, {"file": "components\\ui\\SVGAnimations.tsx", "name": "SVGInteractiveButton", "type": "named", "line": 474, "confidence": "low"}, {"file": "components\\ui\\textarea.tsx", "name": "TextareaProps", "type": "named", "line": 5, "confidence": "low"}, {"file": "components\\ui\\ThreeJSComponents.tsx", "name": "BlockchainVisualization", "type": "default", "line": 451, "confidence": "low"}, {"file": "components\\ui\\ThreeJSComponents.tsx", "name": "ParticleBackground", "type": "named", "line": 194, "confidence": "low"}, {"file": "components\\ui\\ThreeJSComponents.tsx", "name": "Interactive3DCard", "type": "named", "line": 288, "confidence": "low"}, {"file": "components\\ui\\ThreeJSComponents.tsx", "name": "MorphingGeometry", "type": "named", "line": 364, "confidence": "low"}, {"file": "components\\ui\\ThreeJSComponents.tsx", "name": "SolanaVisualization", "type": "named", "line": 417, "confidence": "low"}, {"file": "components\\ui\\toaster.tsx", "name": "Toaster", "type": "named", "line": 13, "confidence": "low"}, {"file": "components\\ui\\Tooltip.tsx", "name": "<PERSON><PERSON><PERSON>", "type": "default", "line": 73, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "H1", "type": "named", "line": 16, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "H2", "type": "named", "line": 40, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "H3", "type": "named", "line": 64, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "H4", "type": "named", "line": 88, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "Paragraph", "type": "named", "line": 112, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "Lead", "type": "named", "line": 135, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "Large", "type": "named", "line": 158, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "Small", "type": "named", "line": 181, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "Muted", "type": "named", "line": 203, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "Code", "type": "named", "line": 227, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "Pre", "type": "named", "line": 242, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "List", "type": "named", "line": 258, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "ListItem", "type": "named", "line": 281, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "Blockquote", "type": "named", "line": 304, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "Table", "type": "named", "line": 328, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "TableHeader", "type": "named", "line": 344, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "TableBody", "type": "named", "line": 352, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "TableRow", "type": "named", "line": 360, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "TableHead", "type": "named", "line": 368, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "TableCell", "type": "named", "line": 382, "confidence": "low"}, {"file": "components\\ui\\Typography.tsx", "name": "ReadableContent", "type": "named", "line": 398, "confidence": "low"}, {"file": "components\\ui\\use-toast.tsx", "name": "reducer", "type": "named", "line": 76, "confidence": "low"}, {"file": "components\\ui\\UserAvatar.tsx", "name": "UserAvatar", "type": "named", "line": 34, "confidence": "low"}, {"file": "components\\ui\\VisualFeedbackSystem.tsx", "name": "VisualFeedbackProvider", "type": "named", "line": 45, "confidence": "low"}, {"file": "components\\ui\\VisualFeedbackSystem.tsx", "name": "useVisualFeedback", "type": "named", "line": 368, "confidence": "low"}, {"file": "components\\ui\\VisualFeedbackSystem.tsx", "name": "FeedbackInput", "type": "named", "line": 377, "confidence": "low"}, {"file": "components\\ui\\VisualFeedbackSystem.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 423, "confidence": "low"}, {"file": "components\\vcs\\VersionControlInterface.tsx", "name": "VersionControlInterfaceProps", "type": "named", "line": 26, "confidence": "low"}, {"file": "components\\vcs\\VersionControlInterface.tsx", "name": "VersionControlInterface", "type": "named", "line": 32, "confidence": "low"}, {"file": "components\\xp\\LevelUpCelebration.tsx", "name": "LevelUpData", "type": "named", "line": 20, "confidence": "low"}, {"file": "components\\xp\\LevelUpCelebration.tsx", "name": "LevelUpCelebration", "type": "named", "line": 47, "confidence": "low"}, {"file": "components\\xp\\LevelUpCelebration.tsx", "name": "LevelUpManager", "type": "named", "line": 413, "confidence": "low"}, {"file": "components\\xp\\LevelUpCelebration.tsx", "name": "useLevelUp", "type": "named", "line": 448, "confidence": "low"}, {"file": "components\\xp\\ProgressBar.tsx", "name": "ProgressBar", "type": "named", "line": 23, "confidence": "low"}, {"file": "components\\xp\\ProgressBar.tsx", "name": "LevelProgressBar", "type": "named", "line": 291, "confidence": "low"}, {"file": "components\\xp\\XPCounter.tsx", "name": "XPCounter", "type": "named", "line": 20, "confidence": "low"}, {"file": "components\\xp\\XPCounter.tsx", "name": "SessionXPSummary", "type": "named", "line": 226, "confidence": "low"}, {"file": "components\\xp\\XPCounter.tsx", "name": "CompactXPDisplay", "type": "named", "line": 289, "confidence": "low"}, {"file": "components\\xp\\XPNotification.tsx", "name": "XPGain", "type": "named", "line": 8, "confidence": "low"}, {"file": "components\\xp\\XPNotification.tsx", "name": "XPNotification", "type": "named", "line": 26, "confidence": "low"}, {"file": "components\\xp\\XPNotification.tsx", "name": "XPNotificationManager", "type": "named", "line": 232, "confidence": "low"}, {"file": "components\\xp\\XPNotification.tsx", "name": "useXPNotifications", "type": "named", "line": 274, "confidence": "low"}, {"file": "lib\\accessibility\\AccessibilityTester.ts", "name": "AccessibilityTestResult", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\accessibility\\AccessibilityTester.ts", "name": "AccessibilityTestOptions", "type": "named", "line": 21, "confidence": "low"}, {"file": "lib\\accessibility\\AccessibilityTester.ts", "name": "AccessibilityTester", "type": "named", "line": 30, "confidence": "low"}, {"file": "lib\\accessibility\\AccessibilityTester.ts", "name": "accessibilityTester", "type": "named", "line": 277, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "hexToRgb", "type": "named", "line": 7, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "rgbToHex", "type": "named", "line": 16, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "getLuminance", "type": "named", "line": 20, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "getContrastRatio", "type": "named", "line": 28, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "meetsWCAGAA", "type": "named", "line": 43, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "meetsWCAGAAA", "type": "named", "line": 48, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "accessibleColors", "type": "named", "line": 54, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "generateAccessibleVariation", "type": "named", "line": 100, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "generateFocusRing", "type": "named", "line": 139, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "textSizes", "type": "named", "line": 147, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "accessibleSpacing", "type": "named", "line": 160, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "respectsReducedMotion", "type": "named", "line": 180, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "getAnimationDuration", "type": "named", "line": 185, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "announceToScreenReader", "type": "named", "line": 190, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "trapFocus", "type": "named", "line": 208, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "simulateColorBlindness", "type": "named", "line": 241, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "testColorAccessibility", "type": "named", "line": 277, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "generateAccessibleScheme", "type": "named", "line": 295, "confidence": "low"}, {"file": "lib\\accessibility\\contrast-utils.ts", "name": "auditPageAccessibility", "type": "named", "line": 314, "confidence": "low"}, {"file": "lib\\accessibility\\EditorAccessibility.ts", "name": "EditorAccessibilityManager", "type": "named", "line": 23, "confidence": "low"}, {"file": "lib\\achievements\\data.ts", "name": "ACHIEVEMENTS", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\achievements\\data.ts", "name": "ACHIEVEMENT_CATEGORIES", "type": "named", "line": 362, "confidence": "low"}, {"file": "lib\\achievements\\data.ts", "name": "ACHIEVEMENT_RARITIES", "type": "named", "line": 401, "confidence": "low"}, {"file": "lib\\achievements\\data.ts", "name": "LEVEL_PROGRESSION", "type": "named", "line": 452, "confidence": "low"}, {"file": "lib\\achievements\\data.ts", "name": "getAchievementById", "type": "named", "line": 468, "confidence": "low"}, {"file": "lib\\achievements\\data.ts", "name": "getAchievementsByCategory", "type": "named", "line": 472, "confidence": "low"}, {"file": "lib\\achievements\\data.ts", "name": "getAchievementsByRarity", "type": "named", "line": 476, "confidence": "low"}, {"file": "lib\\achievements\\data.ts", "name": "getLevelInfo", "type": "named", "line": 480, "confidence": "low"}, {"file": "lib\\achievements\\data.ts", "name": "calculateAchievementProgress", "type": "named", "line": 505, "confidence": "low"}, {"file": "lib\\achievements\\manager.ts", "name": "AchievementManager", "type": "named", "line": 16, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementStatus", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementCategory", "type": "named", "line": 4, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementRarity", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementRequirement", "type": "named", "line": 7, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementReward", "type": "named", "line": 14, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "Achievement", "type": "named", "line": 25, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "UserAchievement", "type": "named", "line": 43, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementProgress", "type": "named", "line": 52, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementNotification", "type": "named", "line": 72, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementEvent", "type": "named", "line": 84, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementFilter", "type": "named", "line": 91, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementStats", "type": "named", "line": 100, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementConfig", "type": "named", "line": 116, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "LevelInfo", "type": "named", "line": 127, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "LeaderboardEntry", "type": "named", "line": 138, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "Leaderboard", "type": "named", "line": 149, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 161, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementManager", "type": "named", "line": 168, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementEventHandler", "type": "named", "line": 194, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "AchievementUnlockHandler", "type": "named", "line": 195, "confidence": "low"}, {"file": "lib\\achievements\\types.ts", "name": "ProgressUpdateHandler", "type": "named", "line": 196, "confidence": "low"}, {"file": "lib\\admin\\auditLogger.ts", "name": "AuditLogger", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\admin\\auditLogger.ts", "name": "auditLogger", "type": "named", "line": 404, "confidence": "low"}, {"file": "lib\\admin\\auditLogger.ts", "name": "auditActions", "type": "named", "line": 407, "confidence": "low"}, {"file": "lib\\admin\\auth.ts", "name": "AdminAuthManager", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\admin\\auth.ts", "name": "ADMIN_PERMISSIONS", "type": "named", "line": 304, "confidence": "low"}, {"file": "lib\\admin\\auth.ts", "name": "adminAuth", "type": "named", "line": 340, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminUser", "type": "named", "line": 1, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 24, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AuditLog", "type": "named", "line": 49, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminPermission", "type": "named", "line": 69, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminRole", "type": "named", "line": 77, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "CommunityReport", "type": "named", "line": 87, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "SecurityEvent", "type": "named", "line": 104, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminStats", "type": "named", "line": 119, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "BulkOperation", "type": "named", "line": 134, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminNotification", "type": "named", "line": 149, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "Admin<PERSON><PERSON><PERSON>", "type": "named", "line": 162, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminSort", "type": "named", "line": 169, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminPagination", "type": "named", "line": 174, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminSearchParams", "type": "named", "line": 181, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminActionResult", "type": "named", "line": 188, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminConfirmation", "type": "named", "line": 196, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminUndo", "type": "named", "line": 209, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "ContentVersion", "type": "named", "line": 220, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "ModerationAction", "type": "named", "line": 233, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "AdminDashboardWidget", "type": "named", "line": 250, "confidence": "low"}, {"file": "lib\\admin\\types.ts", "name": "SystemHealth", "type": "named", "line": 260, "confidence": "low"}, {"file": "lib\\ai\\LearningAssistant.ts", "name": "AIResponse", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\ai\\LearningAssistant.ts", "name": "LearningContext", "type": "named", "line": 21, "confidence": "low"}, {"file": "lib\\ai\\LearningAssistant.ts", "name": "LearningAssistant", "type": "named", "line": 39, "confidence": "low"}, {"file": "lib\\analysis\\SolidityCodeAnalyzer.ts", "name": "AnalysisIssue", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\analysis\\SolidityCodeAnalyzer.ts", "name": "OptimizationSuggestion", "type": "named", "line": 20, "confidence": "low"}, {"file": "lib\\analysis\\SolidityCodeAnalyzer.ts", "name": "SecurityVulnerability", "type": "named", "line": 34, "confidence": "low"}, {"file": "lib\\analysis\\SolidityCodeAnalyzer.ts", "name": "AnalysisResult", "type": "named", "line": 48, "confidence": "low"}, {"file": "lib\\analysis\\SolidityCodeAnalyzer.ts", "name": "SolidityCodeAnalyzer", "type": "named", "line": 74, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "buttonVariants", "type": "named", "line": 11, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "cardVariants", "type": "named", "line": 33, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "progressVariants", "type": "named", "line": 55, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "achievementVariants", "type": "named", "line": 69, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "spinnerVariants", "type": "named", "line": 97, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "notificationVariants", "type": "named", "line": 110, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "inputVariants", "type": "named", "line": 135, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "pageVariants", "type": "named", "line": 159, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "staggerVariants", "type": "named", "line": 184, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "staggerItemVariants", "type": "named", "line": 193, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "fabVariants", "type": "named", "line": 211, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "tooltipVariants", "type": "named", "line": 230, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "modalVariants", "type": "named", "line": 248, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "backdropVariants", "type": "named", "line": 273, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "codeEditorVariants", "type": "named", "line": 286, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "xpGainVariants", "type": "named", "line": 309, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "cursorVariants", "type": "named", "line": 328, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "attentionVariants", "type": "named", "line": 345, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "createSpringTransition", "type": "named", "line": 374, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "createEaseTransition", "type": "named", "line": 382, "confidence": "low"}, {"file": "lib\\animations\\micro-interactions.ts", "name": "animationPresets", "type": "named", "line": 388, "confidence": "low"}, {"file": "lib\\api\\auth.ts", "name": "JwtPayload", "type": "named", "line": 24, "confidence": "low"}, {"file": "lib\\api\\auth.ts", "name": "RefreshTokenPayload", "type": "named", "line": 35, "confidence": "low"}, {"file": "lib\\api\\auth.ts", "name": "AuthService", "type": "named", "line": 43, "confidence": "low"}, {"file": "lib\\api\\auth.ts", "name": "PasswordValidator", "type": "named", "line": 265, "confidence": "low"}, {"file": "lib\\api\\auth.ts", "name": "SECURITY_HEADERS", "type": "named", "line": 338, "confidence": "low"}, {"file": "lib\\api\\auth.ts", "name": "CORS_CONFIG", "type": "named", "line": 356, "confidence": "low"}, {"file": "lib\\api\\auth.ts", "name": "InputSanitizer", "type": "named", "line": 379, "confidence": "low"}, {"file": "lib\\api\\cache.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 134, "confidence": "low"}, {"file": "lib\\api\\cache.ts", "name": "cacheMiddleware", "type": "named", "line": 262, "confidence": "low"}, {"file": "lib\\api\\cache.ts", "name": "CachePatterns", "type": "named", "line": 325, "confidence": "low"}, {"file": "lib\\api\\cache.ts", "name": "CacheTTL", "type": "named", "line": 351, "confidence": "low"}, {"file": "lib\\api\\cache.ts", "name": "cache", "type": "named", "line": 360, "confidence": "low"}, {"file": "lib\\api\\cache.ts", "name": "invalidateUserCache", "type": "named", "line": 371, "confidence": "low"}, {"file": "lib\\api\\cache.ts", "name": "invalidate<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 375, "confidence": "low"}, {"file": "lib\\api\\cache.ts", "name": "invalidateCourseCache", "type": "named", "line": 379, "confidence": "low"}, {"file": "lib\\api\\documentation.ts", "name": "ApiDocumentationGenerator", "type": "named", "line": 78, "confidence": "low"}, {"file": "lib\\api\\documentation.ts", "name": "apiDocumentation", "type": "named", "line": 600, "confidence": "low"}, {"file": "lib\\api\\documentation.ts", "name": "generateApiDocumentation", "type": "named", "line": 603, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "ApiError", "type": "named", "line": 8, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "UnauthorizedError", "type": "named", "line": 39, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "ForbiddenError", "type": "named", "line": 45, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "InvalidTokenError", "type": "named", "line": 51, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "TokenExpiredError", "type": "named", "line": 57, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "ValidationError", "type": "named", "line": 64, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "InvalidInputError", "type": "named", "line": 70, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "Missing<PERSON>ieldE<PERSON>r", "type": "named", "line": 76, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "NotFoundError", "type": "named", "line": 89, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "AlreadyExistsError", "type": "named", "line": 100, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "ConflictError", "type": "named", "line": 112, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "RateLimitError", "type": "named", "line": 119, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "TooManyRequestsError", "type": "named", "line": 130, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "InternalServerError", "type": "named", "line": 142, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "DatabaseError", "type": "named", "line": 155, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "ServiceUnavailableError", "type": "named", "line": 168, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "FileTooLargeError", "type": "named", "line": 180, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "InvalidFileTypeError", "type": "named", "line": 191, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "UploadFailedError", "type": "named", "line": 202, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "FeatureDisabledError", "type": "named", "line": 214, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "FeatureNotAvailableError", "type": "named", "line": 225, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "InsufficientXPError", "type": "named", "line": 237, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "LessonNotCompletedError", "type": "named", "line": 248, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "CourseNotAccessibleError", "type": "named", "line": 259, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "AchievementAlreadyEarnedError", "type": "named", "line": 270, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "createValidationErrors", "type": "named", "line": 282, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "createNotFoundError", "type": "named", "line": 286, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "createAlreadyExistsError", "type": "named", "line": 291, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "isApiError", "type": "named", "line": 297, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "isOperationalError", "type": "named", "line": 301, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "isValidationError", "type": "named", "line": 305, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "isAuthenticationError", "type": "named", "line": 309, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "isAuthorizationError", "type": "named", "line": 315, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "isRateLimitError", "type": "named", "line": 319, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "isServerError", "type": "named", "line": 323, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "ErrorSeverity", "type": "named", "line": 330, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "getErrorSeverity", "type": "named", "line": 337, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "ErrorContext", "type": "named", "line": 354, "confidence": "low"}, {"file": "lib\\api\\errors.ts", "name": "createErrorContext", "type": "named", "line": 368, "confidence": "low"}, {"file": "lib\\api\\integration.ts", "name": "composeMiddleware", "type": "named", "line": 21, "confidence": "low"}, {"file": "lib\\api\\integration.ts", "name": "createApiRoute", "type": "named", "line": 31, "confidence": "low"}, {"file": "lib\\api\\integration.ts", "name": "trackApiError", "type": "named", "line": 280, "confidence": "low"}, {"file": "lib\\api\\integration.ts", "name": "createFallbackMiddleware", "type": "named", "line": 301, "confidence": "low"}, {"file": "lib\\api\\integration.ts", "name": "createAccessibilityMiddleware", "type": "named", "line": 336, "confidence": "low"}, {"file": "lib\\api\\integration.ts", "name": "createPerformanceMiddleware", "type": "named", "line": 369, "confidence": "low"}, {"file": "lib\\api\\integration.ts", "name": "createCompleteApiMiddleware", "type": "named", "line": 409, "confidence": "low"}, {"file": "lib\\api\\integration.ts", "name": "api", "type": "named", "line": 438, "confidence": "low"}, {"file": "lib\\api\\logger.ts", "name": "LogLevel", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\api\\logger.ts", "name": "LogEntry", "type": "named", "line": 13, "confidence": "low"}, {"file": "lib\\api\\logger.ts", "name": "LoggerConfig", "type": "named", "line": 24, "confidence": "low"}, {"file": "lib\\api\\logger.ts", "name": "<PERSON><PERSON>", "type": "named", "line": 44, "confidence": "low"}, {"file": "lib\\api\\logger.ts", "name": "AuditLogger", "type": "named", "line": 321, "confidence": "low"}, {"file": "lib\\api\\logger.ts", "name": "logger", "type": "named", "line": 400, "confidence": "low"}, {"file": "lib\\api\\logger.ts", "name": "auditLogger", "type": "named", "line": 401, "confidence": "low"}, {"file": "lib\\api\\logger.ts", "name": "logApiRequest", "type": "named", "line": 404, "confidence": "low"}, {"file": "lib\\api\\logger.ts", "name": "logApiResponse", "type": "named", "line": 413, "confidence": "low"}, {"file": "lib\\api\\logger.ts", "name": "logApiError", "type": "named", "line": 424, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "LogLevel", "type": "named", "line": 12, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "RequestContext", "type": "named", "line": 43, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "ResponseContext", "type": "named", "line": 58, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "LogEntry", "type": "named", "line": 67, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 361, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "logRequest", "type": "named", "line": 364, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "logResponse", "type": "named", "line": 365, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "logError", "type": "named", "line": 367, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "logInfo", "type": "named", "line": 369, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "log<PERSON>arn", "type": "named", "line": 371, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "logDebug", "type": "named", "line": 373, "confidence": "low"}, {"file": "lib\\api\\logging.ts", "name": "createLoggingMiddleware", "type": "named", "line": 377, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "MiddlewareContext", "type": "named", "line": 9, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "MiddlewareOptions", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "RequestLogger", "type": "named", "line": 37, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "corsMiddleware", "type": "named", "line": 75, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "responseMiddleware", "type": "named", "line": 228, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "withMiddleware", "type": "named", "line": 273, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "publicEndpoint", "type": "named", "line": 302, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "protectedEndpoint", "type": "named", "line": 310, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "adminEndpoint", "type": "named", "line": 320, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "authEndpoint", "type": "named", "line": 329, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "uploadEndpoint", "type": "named", "line": 338, "confidence": "low"}, {"file": "lib\\api\\middleware.ts", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 348, "confidence": "low"}, {"file": "lib\\api\\optimizedApiClient.ts", "name": "ApiRequestConfig", "type": "named", "line": 4, "confidence": "low"}, {"file": "lib\\api\\optimizedApiClient.ts", "name": "ApiResponse", "type": "named", "line": 16, "confidence": "low"}, {"file": "lib\\api\\optimizedApiClient.ts", "name": "apiClient", "type": "default", "line": 298, "confidence": "low"}, {"file": "lib\\api\\optimizedApiClient.ts", "name": "createQueryKey", "type": "named", "line": 235, "confidence": "low"}, {"file": "lib\\api\\optimizedApiClient.ts", "name": "useOptimizedQuery", "type": "named", "line": 241, "confidence": "low"}, {"file": "lib\\api\\optimizedApiClient.ts", "name": "useOptimizedMutation", "type": "named", "line": 268, "confidence": "low"}, {"file": "lib\\api\\rate-limiting.ts", "name": "RateLimitConfig", "type": "named", "line": 15, "confidence": "low"}, {"file": "lib\\api\\rate-limiting.ts", "name": "RateLimitStore", "type": "named", "line": 28, "confidence": "low"}, {"file": "lib\\api\\rate-limiting.ts", "name": "RedisRateLimitStore", "type": "named", "line": 36, "confidence": "low"}, {"file": "lib\\api\\rate-limiting.ts", "name": "MemoryRateLimitStore", "type": "named", "line": 105, "confidence": "low"}, {"file": "lib\\api\\rate-limiting.ts", "name": "RateLimiter", "type": "named", "line": 165, "confidence": "low"}, {"file": "lib\\api\\rate-limiting.ts", "name": "rateLimitConfigs", "type": "named", "line": 255, "confidence": "low"}, {"file": "lib\\api\\rate-limiting.ts", "name": "rateLimiters", "type": "named", "line": 346, "confidence": "low"}, {"file": "lib\\api\\rate-limiting.ts", "name": "withRateLimit", "type": "named", "line": 358, "confidence": "low"}, {"file": "lib\\api\\rateLimit.ts", "name": "RateLimitConfig", "type": "named", "line": 85, "confidence": "low"}, {"file": "lib\\api\\rateLimit.ts", "name": "RATE_LIMIT_CONFIGS", "type": "named", "line": 99, "confidence": "low"}, {"file": "lib\\api\\rateLimit.ts", "name": "RateLimiter", "type": "named", "line": 144, "confidence": "low"}, {"file": "lib\\api\\rateLimit.ts", "name": "RateLimitManager", "type": "named", "line": 234, "confidence": "low"}, {"file": "lib\\api\\rateLimit.ts", "name": "getRateLimitManager", "type": "named", "line": 327, "confidence": "low"}, {"file": "lib\\api\\rateLimit.ts", "name": "AdaptiveRateLimiter", "type": "named", "line": 361, "confidence": "low"}, {"file": "lib\\api\\rateLimit.ts", "name": "RateLimitMetrics", "type": "named", "line": 398, "confidence": "low"}, {"file": "lib\\api\\rateLimit.ts", "name": "RateLimitMonitor", "type": "named", "line": 406, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "ApiResponseBuilder", "type": "named", "line": 13, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "ApiException", "type": "named", "line": 158, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "ValidationException", "type": "named", "line": 170, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "UnauthorizedException", "type": "named", "line": 177, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "ForbiddenException", "type": "named", "line": 184, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "NotFoundException", "type": "named", "line": 191, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "ConflictException", "type": "named", "line": 198, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "RateLimitException", "type": "named", "line": 205, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "createPaginationMeta", "type": "named", "line": 213, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "sanitizeForResponse", "type": "named", "line": 230, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "filterByPermissions", "type": "named", "line": 245, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "addSecurityHeaders", "type": "named", "line": 263, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "addCorsHeaders", "type": "named", "line": 277, "confidence": "low"}, {"file": "lib\\api\\response.ts", "name": "addRateLimitHeaders", "type": "named", "line": 294, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "Id", "type": "named", "line": 78, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "Email", "type": "named", "line": 79, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "Password", "type": "named", "line": 80, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "Pagin<PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 81, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "SearchQuery", "type": "named", "line": 82, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "CreateUserRequest", "type": "named", "line": 85, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "UpdateUserRequest", "type": "named", "line": 86, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "UpdateUserProfileRequest", "type": "named", "line": 87, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "UpdateUserPreferencesRequest", "type": "named", "line": 88, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ChangePasswordRequest", "type": "named", "line": 89, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "LoginRequest", "type": "named", "line": 92, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "RegisterRequest", "type": "named", "line": 93, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ForgotPasswordRequest", "type": "named", "line": 94, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ResetPasswordRequest", "type": "named", "line": 95, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "RefreshTokenRequest", "type": "named", "line": 96, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "CreateLessonRequest", "type": "named", "line": 99, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "UpdateLessonRequest", "type": "named", "line": 100, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "PublishLessonRequest", "type": "named", "line": 101, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "CreateCourseRequest", "type": "named", "line": 104, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "UpdateCourseRequest", "type": "named", "line": 105, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "CreateProgressRequest", "type": "named", "line": 108, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "UpdateProgressRequest", "type": "named", "line": 109, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "CompleteProgressRequest", "type": "named", "line": 110, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "CreateAchievementRequest", "type": "named", "line": 113, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "UpdateAchievementRequest", "type": "named", "line": 114, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "LeaderboardFilters", "type": "named", "line": 117, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "StatsFilters", "type": "named", "line": 118, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "RateLimitConfig", "type": "named", "line": 121, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "FileUploadRequest", "type": "named", "line": 124, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ImageUploadRequest", "type": "named", "line": 125, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "SettingsUpdateRequest", "type": "named", "line": 128, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ProfileSettingsRequest", "type": "named", "line": 129, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "SecuritySettingsRequest", "type": "named", "line": 130, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "LearningSettingsRequest", "type": "named", "line": 131, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "EditorSettingsRequest", "type": "named", "line": 132, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "SearchRequest", "type": "named", "line": 135, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ErrorReportRequest", "type": "named", "line": 138, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "MetricsQuery", "type": "named", "line": 141, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "FeatureFlagQuery", "type": "named", "line": 144, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ApiResponse", "type": "named", "line": 147, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ValidationError", "type": "named", "line": 176, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ValidatedRequest", "type": "named", "line": 183, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "PaginatedResponse", "type": "named", "line": 188, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "SearchResponse", "type": "named", "line": 201, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ErrorMetricsResponse", "type": "named", "line": 210, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ApiMetricsResponse", "type": "named", "line": 225, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "FeatureFlagResponse", "type": "named", "line": 280, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "RateLimitInfo", "type": "named", "line": 292, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "FileUploadResponse", "type": "named", "line": 300, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "isApiResponse", "type": "named", "line": 312, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "isValidationError", "type": "named", "line": 322, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "isPaginatedResponse", "type": "named", "line": 332, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ApiEndpoint", "type": "named", "line": 345, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "ValidatedApiEndpoint", "type": "named", "line": 349, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "SchemaRegistry", "type": "named", "line": 354, "confidence": "low"}, {"file": "lib\\api\\schema-types.ts", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 422, "confidence": "low"}, {"file": "lib\\api\\security.ts", "name": "SecurityConfig", "type": "named", "line": 10, "confidence": "low"}, {"file": "lib\\api\\security.ts", "name": "SecurityMiddleware", "type": "named", "line": 88, "confidence": "low"}, {"file": "lib\\api\\security.ts", "name": "InputSanitizer", "type": "named", "line": 307, "confidence": "low"}, {"file": "lib\\api\\security.ts", "name": "securityMiddleware", "type": "named", "line": 372, "confidence": "low"}, {"file": "lib\\api\\security.ts", "name": "withSecurity", "type": "named", "line": 375, "confidence": "low"}, {"file": "lib\\api\\security.ts", "name": "withTimeout", "type": "named", "line": 381, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "TestRequestBuilder", "type": "named", "line": 10, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "TestResponseAssertions", "type": "named", "line": 70, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "TestApiClient", "type": "named", "line": 159, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "MockDataGenerator", "type": "named", "line": 214, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "TestDatabase", "type": "named", "line": 320, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "createTestResponse", "type": "named", "line": 394, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "createTestApiResponse", "type": "named", "line": 398, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "describeApiEndpoint", "type": "named", "line": 415, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "itShouldRequireAuth", "type": "named", "line": 426, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "itShouldValidateInput", "type": "named", "line": 437, "confidence": "low"}, {"file": "lib\\api\\testing.ts", "name": "itShouldRateLimit", "type": "named", "line": 450, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ApiResponse", "type": "named", "line": 2, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ApiError", "type": "named", "line": 11, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ValidationError", "type": "named", "line": 18, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ResponseMeta", "type": "named", "line": 25, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "PaginationMeta", "type": "named", "line": 34, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "Pagin<PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 44, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 51, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ApiErrorCode", "type": "named", "line": 60, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "HttpStatus", "type": "named", "line": 97, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ApiUser", "type": "named", "line": 114, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "UserProfile", "type": "named", "line": 127, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "UserPreferences", "type": "named", "line": 144, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "UserRole", "type": "named", "line": 154, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "UserStatus", "type": "named", "line": 161, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 169, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "LessonType", "type": "named", "line": 192, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "DifficultyLevel", "type": "named", "line": 200, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "LessonStatus", "type": "named", "line": 207, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ApiProgress", "type": "named", "line": 215, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ProgressStatus", "type": "named", "line": 233, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ApiCourse", "type": "named", "line": 241, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "CourseStatus", "type": "named", "line": 275, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ApiAchievement", "type": "named", "line": 283, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "AchievementCategory", "type": "named", "line": 298, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "AchievementType", "type": "named", "line": 306, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "AchievementRarity", "type": "named", "line": 312, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "AchievementRequirement", "type": "named", "line": 319, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ApiLog", "type": "named", "line": 326, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "RateLimitInfo", "type": "named", "line": 343, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "HealthCheck", "type": "named", "line": 351, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ServiceHealth", "type": "named", "line": 364, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "FileUploadRequest", "type": "named", "line": 372, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "FileUploadResponse", "type": "named", "line": 378, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "SearchRequest", "type": "named", "line": 390, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "SearchResult", "type": "named", "line": 397, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "SearchResponse", "type": "named", "line": 408, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "FeatureFlagResponse", "type": "named", "line": 416, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ErrorReportRequest", "type": "named", "line": 428, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ErrorMetricsResponse", "type": "named", "line": 441, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "SettingsUpdateRequest", "type": "named", "line": 455, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "LoginRequest", "type": "named", "line": 461, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "RegisterRequest", "type": "named", "line": 467, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "AuthResponse", "type": "named", "line": 474, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "RefreshTokenRequest", "type": "named", "line": 481, "confidence": "low"}, {"file": "lib\\api\\types.ts", "name": "ApiConfig", "type": "named", "line": 486, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "generateRequestId", "type": "named", "line": 12, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "getClientIP", "type": "named", "line": 19, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "createApiResponse", "type": "named", "line": 42, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "createApiError", "type": "named", "line": 59, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "createResponse", "type": "named", "line": 81, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "successResponse", "type": "named", "line": 106, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "errorResponse", "type": "named", "line": 119, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "validationErrorResponse", "type": "named", "line": 133, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "notFoundResponse", "type": "named", "line": 149, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "unauthorizedResponse", "type": "named", "line": 165, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "forbiddenResponse", "type": "named", "line": 181, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "rateLimitResponse", "type": "named", "line": 197, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "internalErrorResponse", "type": "named", "line": 216, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "parsePaginationParams", "type": "named", "line": 232, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "createPaginationMeta", "type": "named", "line": 254, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "sanitizeData", "type": "named", "line": 281, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "parseUserAgent", "type": "named", "line": 311, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "validate<PERSON><PERSON><PERSON>", "type": "named", "line": 337, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "methodNotAllowedResponse", "type": "named", "line": 347, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 366, "confidence": "low"}, {"file": "lib\\api\\utils.ts", "name": "createCorsHeaders", "type": "named", "line": 409, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "IdSchema", "type": "named", "line": 6, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "EmailSchema", "type": "named", "line": 7, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "PasswordSchema", "type": "named", "line": 8, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "PaginationSchema", "type": "named", "line": 13, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "SearchSchema", "type": "named", "line": 20, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "CreateUserSchema", "type": "named", "line": 29, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "UpdateUserSchema", "type": "named", "line": 36, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "UpdateUserProfileSchema", "type": "named", "line": 43, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "UpdateUserPreferencesSchema", "type": "named", "line": 53, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "ChangePasswordSchema", "type": "named", "line": 63, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "LoginSchema", "type": "named", "line": 73, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "RegisterSchema", "type": "named", "line": 79, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "ForgotPasswordSchema", "type": "named", "line": 92, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "ResetPasswordSchema", "type": "named", "line": 96, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "RefreshTokenSchema", "type": "named", "line": 105, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "CreateLessonSchema", "type": "named", "line": 110, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "UpdateLessonSchema", "type": "named", "line": 123, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "PublishLessonSchema", "type": "named", "line": 125, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "CreateCourseSchema", "type": "named", "line": 130, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "UpdateCourseSchema", "type": "named", "line": 144, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "CreateProgressSchema", "type": "named", "line": 147, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "UpdateProgressSchema", "type": "named", "line": 156, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "CompleteProgressSchema", "type": "named", "line": 163, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "CreateAchievementSchema", "type": "named", "line": 170, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "UpdateAchievementSchema", "type": "named", "line": 186, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "LeaderboardFiltersSchema", "type": "named", "line": 189, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "StatsFiltersSchema", "type": "named", "line": 199, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "validateSchema", "type": "named", "line": 210, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "validate<PERSON><PERSON>y", "type": "named", "line": 228, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "validateBody", "type": "named", "line": 247, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "sanitizeString", "type": "named", "line": 252, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "sanitizeObject", "type": "named", "line": 260, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "RateLimitConfigSchema", "type": "named", "line": 279, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "FileUploadSchema", "type": "named", "line": 288, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "ImageUploadSchema", "type": "named", "line": 295, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "SettingsUpdateSchema", "type": "named", "line": 303, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "ProfileSettingsSchema", "type": "named", "line": 308, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "SecuritySettingsSchema", "type": "named", "line": 320, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "LearningSettingsSchema", "type": "named", "line": 337, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "EditorSettingsSchema", "type": "named", "line": 353, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "SearchQuerySchema", "type": "named", "line": 367, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "ErrorReportSchema", "type": "named", "line": 377, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "MetricsQuerySchema", "type": "named", "line": 391, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "FeatureFlagQuerySchema", "type": "named", "line": 398, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "createValidationMiddleware", "type": "named", "line": 407, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "validateAndSanitize", "type": "named", "line": 482, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "createPaginatedSchema", "type": "named", "line": 561, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "createSearchableSchema", "type": "named", "line": 570, "confidence": "low"}, {"file": "lib\\api\\validation.ts", "name": "validateResponse", "type": "named", "line": 578, "confidence": "low"}, {"file": "lib\\auth\\config.ts", "name": "authOptions", "type": "named", "line": 10, "confidence": "low"}, {"file": "lib\\auth\\mock-auth.tsx", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 10, "confidence": "low"}, {"file": "lib\\auth\\mock-auth.tsx", "name": "MockAuthState", "type": "named", "line": 19, "confidence": "low"}, {"file": "lib\\auth\\mock-auth.tsx", "name": "MockAuthActions", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\auth\\mock-auth.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 75, "confidence": "low"}, {"file": "lib\\auth\\mock-auth.tsx", "name": "useMockAuth", "type": "named", "line": 175, "confidence": "low"}, {"file": "lib\\auth\\mock-auth.tsx", "name": "useMockPermissions", "type": "named", "line": 184, "confidence": "low"}, {"file": "lib\\auth\\mock-auth.tsx", "name": "useMockAuthStatus", "type": "named", "line": 259, "confidence": "low"}, {"file": "lib\\auth\\navigationGuard.ts", "name": "NavigationGuardConfig", "type": "named", "line": 6, "confidence": "low"}, {"file": "lib\\auth\\navigationGuard.ts", "name": "NavigationContext", "type": "named", "line": 16, "confidence": "low"}, {"file": "lib\\auth\\navigationGuard.ts", "name": "RedirectOptions", "type": "named", "line": 29, "confidence": "low"}, {"file": "lib\\auth\\navigationGuard.ts", "name": "NavigationGuard", "type": "named", "line": 36, "confidence": "low"}, {"file": "lib\\auth\\navigationGuard.ts", "name": "useNavigationGuard", "type": "named", "line": 374, "confidence": "low"}, {"file": "lib\\auth\\navigationGuard.ts", "name": "createNavigationContext", "type": "named", "line": 390, "confidence": "low"}, {"file": "lib\\auth\\password.ts", "name": "passwordSchema", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\auth\\password.ts", "name": "emailSchema", "type": "named", "line": 13, "confidence": "low"}, {"file": "lib\\auth\\password.ts", "name": "registrationSchema", "type": "named", "line": 18, "confidence": "low"}, {"file": "lib\\auth\\password.ts", "name": "loginSchema", "type": "named", "line": 31, "confidence": "low"}, {"file": "lib\\auth\\password.ts", "name": "PasswordUtils", "type": "named", "line": 37, "confidence": "low"}, {"file": "lib\\auth\\password.ts", "name": "RegistrationData", "type": "named", "line": 142, "confidence": "low"}, {"file": "lib\\auth\\password.ts", "name": "LoginData", "type": "named", "line": 143, "confidence": "low"}, {"file": "lib\\auth\\sessionManager.ts", "name": "SessionData", "type": "named", "line": 6, "confidence": "low"}, {"file": "lib\\auth\\sessionManager.ts", "name": "SessionConfig", "type": "named", "line": 19, "confidence": "low"}, {"file": "lib\\auth\\sessionManager.ts", "name": "SessionStatus", "type": "named", "line": 30, "confidence": "low"}, {"file": "lib\\auth\\sessionManager.ts", "name": "SessionEvent", "type": "named", "line": 40, "confidence": "low"}, {"file": "lib\\auth\\sessionManager.ts", "name": "Session<PERSON>anager", "type": "named", "line": 46, "confidence": "low"}, {"file": "lib\\blockchain\\Web3Provider.tsx", "name": "useWeb3", "type": "named", "line": 23, "confidence": "low"}, {"file": "lib\\blockchain\\Web3Provider.tsx", "name": "SUPPORTED_NETWORKS", "type": "named", "line": 32, "confidence": "low"}, {"file": "lib\\blockchain\\Web3Provider.tsx", "name": "Web3Provider", "type": "named", "line": 60, "confidence": "low"}, {"file": "lib\\collaboration\\AdvancedCollaborativeEditor.ts", "name": "CollaboratorInfo", "type": "named", "line": 13, "confidence": "low"}, {"file": "lib\\collaboration\\AdvancedCollaborativeEditor.ts", "name": "DocumentState", "type": "named", "line": 24, "confidence": "low"}, {"file": "lib\\collaboration\\AdvancedCollaborativeEditor.ts", "name": "ChangeEvent", "type": "named", "line": 32, "confidence": "low"}, {"file": "lib\\collaboration\\AdvancedCollaborativeEditor.ts", "name": "CursorEvent", "type": "named", "line": 40, "confidence": "low"}, {"file": "lib\\collaboration\\AdvancedCollaborativeEditor.ts", "name": "ConflictEvent", "type": "named", "line": 46, "confidence": "low"}, {"file": "lib\\collaboration\\AdvancedCollaborativeEditor.ts", "name": "AdvancedCollaborativeEditor", "type": "named", "line": 56, "confidence": "low"}, {"file": "lib\\collaboration\\CollaborationClient.ts", "name": "CollaborationUser", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\collaboration\\CollaborationClient.ts", "name": "CollaborationMessage", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\collaboration\\CollaborationClient.ts", "name": "CollaborationSession", "type": "named", "line": 34, "confidence": "low"}, {"file": "lib\\collaboration\\CollaborationClient.ts", "name": "ConnectionStatus", "type": "named", "line": 52, "confidence": "low"}, {"file": "lib\\collaboration\\CollaborationClient.ts", "name": "CollaborationClient", "type": "named", "line": 54, "confidence": "low"}, {"file": "lib\\collaboration\\CollaborativeEditor.ts", "name": "CursorDecoration", "type": "named", "line": 6, "confidence": "low"}, {"file": "lib\\collaboration\\CollaborativeEditor.ts", "name": "CollaborativeEditorOptions", "type": "named", "line": 22, "confidence": "low"}, {"file": "lib\\collaboration\\CollaborativeEditor.ts", "name": "CollaborativeEditor", "type": "named", "line": 33, "confidence": "low"}, {"file": "lib\\collaboration\\ConnectionManager.ts", "name": "ConnectionManager", "type": "named", "line": 28, "confidence": "low"}, {"file": "lib\\collaboration\\OperationalTransform.ts", "name": "Operation", "type": "named", "line": 4, "confidence": "low"}, {"file": "lib\\collaboration\\OperationalTransform.ts", "name": "CursorPosition", "type": "named", "line": 15, "confidence": "low"}, {"file": "lib\\collaboration\\OperationalTransform.ts", "name": "SelectionRange", "type": "named", "line": 21, "confidence": "low"}, {"file": "lib\\collaboration\\OperationalTransform.ts", "name": "TextOperation", "type": "named", "line": 27, "confidence": "low"}, {"file": "lib\\collaboration\\OperationalTransform.ts", "name": "ConflictResolution", "type": "named", "line": 51, "confidence": "low"}, {"file": "lib\\collaboration\\OperationalTransform.ts", "name": "OperationResult", "type": "named", "line": 58, "confidence": "low"}, {"file": "lib\\collaboration\\OperationalTransform.ts", "name": "OperationalTransform", "type": "named", "line": 66, "confidence": "low"}, {"file": "lib\\community\\leaderboard.ts", "name": "LeaderboardManager", "type": "named", "line": 14, "confidence": "low"}, {"file": "lib\\community\\leaderboard.ts", "name": "leaderboardManager", "type": "named", "line": 371, "confidence": "low"}, {"file": "lib\\community\\leaderboard.ts", "name": "LeaderboardUtils", "type": "named", "line": 374, "confidence": "low"}, {"file": "lib\\community\\statistics.ts", "name": "CommunityStatsManager", "type": "named", "line": 13, "confidence": "low"}, {"file": "lib\\community\\statistics.ts", "name": "communityStatsManager", "type": "named", "line": 330, "confidence": "low"}, {"file": "lib\\community\\statistics.ts", "name": "StatsUtils", "type": "named", "line": 333, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "LeaderboardUser", "type": "named", "line": 1, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "Badge", "type": "named", "line": 27, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "Achievement", "type": "named", "line": 37, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "LeaderboardCategory", "type": "named", "line": 47, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "LeaderboardFilters", "type": "named", "line": 57, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "LeaderboardResponse", "type": "named", "line": 68, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "CommunityStats", "type": "named", "line": 80, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "TrendingTopic", "type": "named", "line": 117, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "CommunityMilestone", "type": "named", "line": 127, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "StatsFilters", "type": "named", "line": 137, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "ExportOptions", "type": "named", "line": 150, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "RealTimeUpdate", "type": "named", "line": 160, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "WebSocketMessage", "type": "named", "line": 168, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "LeaderboardCache", "type": "named", "line": 176, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "CommunityConfig", "type": "named", "line": 183, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "UserProgress", "type": "named", "line": 214, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "LeaderboardEvent", "type": "named", "line": 225, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "CommunityFeatureFlags", "type": "named", "line": 239, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "AdminCommunityControls", "type": "named", "line": 251, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "CommunityError", "type": "named", "line": 262, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "CommunityErrorCode", "type": "named", "line": 269, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "PerformanceMetrics", "type": "named", "line": 281, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "LoadingState", "type": "named", "line": 292, "confidence": "low"}, {"file": "lib\\community\\types.ts", "name": "CommunityNotification", "type": "named", "line": 299, "confidence": "low"}, {"file": "lib\\community\\websocket.ts", "name": "CommunityWebSocket", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\community\\websocket.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 273, "confidence": "low"}, {"file": "lib\\community\\websocket.ts", "name": "RealTimeManager", "type": "named", "line": 330, "confidence": "low"}, {"file": "lib\\community\\websocket.ts", "name": "realTimeManager", "type": "named", "line": 406, "confidence": "low"}, {"file": "lib\\compiler\\SolidityCompiler.ts", "name": "CompilationResult", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\compiler\\SolidityCompiler.ts", "name": "SecurityIssue", "type": "named", "line": 14, "confidence": "low"}, {"file": "lib\\compiler\\SolidityCompiler.ts", "name": "SolidityCompiler", "type": "named", "line": 23, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "env", "type": "named", "line": 191, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "isProduction", "type": "named", "line": 194, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "isStaging", "type": "named", "line": 195, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "isDevelopment", "type": "named", "line": 196, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "isServer", "type": "named", "line": 197, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "isClient", "type": "named", "line": 198, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "features", "type": "named", "line": 201, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "betaFeatures", "type": "named", "line": 211, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "dbConfig", "type": "named", "line": 218, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "redisConfig", "type": "named", "line": 228, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "rateLimitConfig", "type": "named", "line": 235, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "aiConfig", "type": "named", "line": 245, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "socketConfig", "type": "named", "line": 264, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "securityConfig", "type": "named", "line": 277, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "monitoringConfig", "type": "named", "line": 293, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "validateCriticalConfig", "type": "named", "line": 311, "confidence": "low"}, {"file": "lib\\config\\environment.ts", "name": "Environment", "type": "named", "line": 354, "confidence": "low"}, {"file": "lib\\config\\secrets.ts", "name": "secretsManager", "type": "named", "line": 307, "confidence": "low"}, {"file": "lib\\config\\secrets.ts", "name": "getSecret", "type": "named", "line": 316, "confidence": "low"}, {"file": "lib\\config\\secrets.ts", "name": "getValidatedSecret", "type": "named", "line": 330, "confidence": "low"}, {"file": "lib\\config\\secrets.ts", "name": "checkSecretRotation", "type": "named", "line": 347, "confidence": "low"}, {"file": "lib\\config\\secrets.ts", "name": "initializeSecrets", "type": "named", "line": 375, "confidence": "low"}, {"file": "lib\\context\\CollaborationContext.tsx", "name": "CollaborationProvider", "type": "named", "line": 171, "confidence": "low"}, {"file": "lib\\context\\CollaborationContext.tsx", "name": "useCollaboration", "type": "named", "line": 416, "confidence": "low"}, {"file": "lib\\context\\LearningContext.tsx", "name": "useLearning", "type": "named", "line": 155, "confidence": "low"}, {"file": "lib\\context\\LearningContext.tsx", "name": "LearningProvider", "type": "named", "line": 163, "confidence": "low"}, {"file": "lib\\curriculum\\data.ts", "name": "SOLIDITY_LESSONS", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\curriculum\\data.ts", "name": "SOLIDITY_MODULES", "type": "named", "line": 185, "confidence": "low"}, {"file": "lib\\curriculum\\data.ts", "name": "LEARNING_PATHS", "type": "named", "line": 261, "confidence": "low"}, {"file": "lib\\curriculum\\data.ts", "name": "getModuleById", "type": "named", "line": 313, "confidence": "low"}, {"file": "lib\\curriculum\\data.ts", "name": "getLessonById", "type": "named", "line": 317, "confidence": "low"}, {"file": "lib\\curriculum\\data.ts", "name": "getLearningPathById", "type": "named", "line": 321, "confidence": "low"}, {"file": "lib\\curriculum\\data.ts", "name": "getModulesForPath", "type": "named", "line": 325, "confidence": "low"}, {"file": "lib\\curriculum\\data.ts", "name": "calculateModuleProgress", "type": "named", "line": 334, "confidence": "low"}, {"file": "lib\\curriculum\\data.ts", "name": "getNextAvailableLesson", "type": "named", "line": 348, "confidence": "low"}, {"file": "lib\\curriculum\\data.ts", "name": "checkPrerequisites", "type": "named", "line": 363, "confidence": "low"}, {"file": "lib\\curriculum\\manager.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 23, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "LessonStatus", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "ModuleStatus", "type": "named", "line": 4, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "DifficultyLevel", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "LessonType", "type": "named", "line": 6, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "Prerequisite", "type": "named", "line": 8, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "LessonProgress", "type": "named", "line": 16, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "Lesson", "type": "named", "line": 29, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "ModuleProgress", "type": "named", "line": 51, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "<PERSON><PERSON><PERSON>", "type": "named", "line": 65, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "LearningPath", "type": "named", "line": 88, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "UserCurriculumProgress", "type": "named", "line": 107, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "CurriculumRecommendation", "type": "named", "line": 136, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "LearningAnalytics", "type": "named", "line": 147, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "Cur<PERSON>ulum<PERSON><PERSON>er", "type": "named", "line": 175, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 187, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "ProgressUpdateHandler", "type": "named", "line": 227, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "CompletionHandler", "type": "named", "line": 228, "confidence": "low"}, {"file": "lib\\curriculum\\types.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 229, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "CleanupOperation", "type": "named", "line": 4, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "CleanupCategory", "type": "named", "line": 16, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "CleanupSeverity", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "CleanupOptions", "type": "named", "line": 33, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "CleanupResult", "type": "named", "line": 43, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "CleanupReport", "type": "named", "line": 55, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "BackupService", "type": "named", "line": 68, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "CleanupManager", "type": "named", "line": 218, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "SafetyUtils", "type": "named", "line": 452, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "cleanupManager", "type": "named", "line": 514, "confidence": "low"}, {"file": "lib\\database\\cleanup.ts", "name": "backupService", "type": "named", "line": 515, "confidence": "low"}, {"file": "lib\\database\\data-removal.ts", "name": "registerDataRemovalOperations", "type": "named", "line": 765, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "MaintenanceSchedule", "type": "named", "line": 8, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "CronSchedule", "type": "named", "line": 21, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "NotificationConfig", "type": "named", "line": 30, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "MaintenanceReport", "type": "named", "line": 38, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "SystemMetrics", "type": "named", "line": 50, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "TableStats", "type": "named", "line": 59, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "QueryStats", "type": "named", "line": 67, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "DiskUsage", "type": "named", "line": 74, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "MemoryUsage", "type": "named", "line": 81, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "MaintenanceScheduler", "type": "named", "line": 89, "confidence": "low"}, {"file": "lib\\database\\maintenance.ts", "name": "maintenanceScheduler", "type": "named", "line": 610, "confidence": "low"}, {"file": "lib\\database\\migrations.ts", "name": "Migration", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\database\\migrations.ts", "name": "MigrationStatus", "type": "named", "line": 19, "confidence": "low"}, {"file": "lib\\database\\migrations.ts", "name": "MigrationResult", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\database\\migrations.ts", "name": "MigrationPlan", "type": "named", "line": 36, "confidence": "low"}, {"file": "lib\\database\\migrations.ts", "name": "<PERSON><PERSON><PERSON>anager", "type": "named", "line": 101, "confidence": "low"}, {"file": "lib\\database\\migrations.ts", "name": "migrationManager", "type": "named", "line": 603, "confidence": "low"}, {"file": "lib\\database\\orphaned-data.ts", "name": "registerOrphanedDataOperations", "type": "named", "line": 572, "confidence": "low"}, {"file": "lib\\debugging\\SolidityDebugger.ts", "name": "BreakpointInfo", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\debugging\\SolidityDebugger.ts", "name": "VariableInfo", "type": "named", "line": 15, "confidence": "low"}, {"file": "lib\\debugging\\SolidityDebugger.ts", "name": "CallStackFrame", "type": "named", "line": 25, "confidence": "low"}, {"file": "lib\\debugging\\SolidityDebugger.ts", "name": "ExecutionState", "type": "named", "line": 36, "confidence": "low"}, {"file": "lib\\debugging\\SolidityDebugger.ts", "name": "DebugSession", "type": "named", "line": 49, "confidence": "low"}, {"file": "lib\\debugging\\SolidityDebugger.ts", "name": "StepResult", "type": "named", "line": 60, "confidence": "low"}, {"file": "lib\\debugging\\SolidityDebugger.ts", "name": "SolidityDebugger", "type": "named", "line": 71, "confidence": "low"}, {"file": "lib\\editor\\AdvancedEditorConfig.ts", "name": "AdvancedEditorConfig", "type": "named", "line": 23, "confidence": "low"}, {"file": "lib\\editor\\ErrorHighlighting.ts", "name": "ErrorHighlightingManager", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\editor\\MonacoSoliditySetup.ts", "name": "solidityTheme", "type": "named", "line": 15, "confidence": "low"}, {"file": "lib\\editor\\MonacoSoliditySetup.ts", "name": "MonacoSoliditySetup", "type": "named", "line": 86, "confidence": "low"}, {"file": "lib\\editor\\RealTimeSyntaxChecker.ts", "name": "RealTimeSyntaxChecker", "type": "named", "line": 11, "confidence": "low"}, {"file": "lib\\editor\\SolidityIntelliSense.ts", "name": "SoliditySymbol", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\editor\\SolidityIntelliSense.ts", "name": "CompletionContext", "type": "named", "line": 20, "confidence": "low"}, {"file": "lib\\editor\\SolidityIntelliSense.ts", "name": "SolidityIntelliSense", "type": "named", "line": 38, "confidence": "low"}, {"file": "lib\\editor\\SolidityLanguageDefinition.ts", "name": "solidityLanguageConfig", "type": "named", "line": 6, "confidence": "low"}, {"file": "lib\\editor\\SolidityLanguageDefinition.ts", "name": "solidity<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 44, "confidence": "low"}, {"file": "lib\\editor\\SolidityLanguageDefinition.ts", "name": "solidityCompletionProvider", "type": "named", "line": 283, "confidence": "low"}, {"file": "lib\\editor\\SolidityLanguageDefinition.ts", "name": "solidityHoverProvider", "type": "named", "line": 389, "confidence": "low"}, {"file": "lib\\editor\\SolidityLanguageDefinition.ts", "name": "soliditySignatureHelpProvider", "type": "named", "line": 462, "confidence": "low"}, {"file": "lib\\editor\\SoliditySemanticAnalyzer.ts", "name": "SolidityError", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\editor\\SoliditySemanticAnalyzer.ts", "name": "SoliditySymbol", "type": "named", "line": 17, "confidence": "low"}, {"file": "lib\\editor\\SoliditySemanticAnalyzer.ts", "name": "SolidityParameter", "type": "named", "line": 33, "confidence": "low"}, {"file": "lib\\editor\\SoliditySemanticAnalyzer.ts", "name": "AnalysisResult", "type": "named", "line": 39, "confidence": "low"}, {"file": "lib\\editor\\SoliditySemanticAnalyzer.ts", "name": "SoliditySemanticAnalyzer", "type": "named", "line": 50, "confidence": "low"}, {"file": "lib\\errors\\ErrorContext.tsx", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 123, "confidence": "low"}, {"file": "lib\\errors\\ErrorContext.tsx", "name": "useError", "type": "named", "line": 318, "confidence": "low"}, {"file": "lib\\errors\\ErrorContext.tsx", "name": "useAsyncError", "type": "named", "line": 327, "confidence": "low"}, {"file": "lib\\errors\\recovery.ts", "name": "RetryConfig", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\errors\\recovery.ts", "name": "OfflineConfig", "type": "named", "line": 14, "confidence": "low"}, {"file": "lib\\errors\\recovery.ts", "name": "ErrorAnalytics", "type": "named", "line": 23, "confidence": "low"}, {"file": "lib\\errors\\recovery.ts", "name": "RetryManager", "type": "named", "line": 38, "confidence": "low"}, {"file": "lib\\errors\\recovery.ts", "name": "OfflineManager", "type": "named", "line": 124, "confidence": "low"}, {"file": "lib\\errors\\recovery.ts", "name": "ErrorAnalyticsManager", "type": "named", "line": 268, "confidence": "low"}, {"file": "lib\\errors\\recovery.ts", "name": "UserErrorReporter", "type": "named", "line": 425, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "ErrorSeverity", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "Error<PERSON>ate<PERSON><PERSON>", "type": "named", "line": 4, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "ErrorContext", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "BaseError", "type": "named", "line": 7, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "ErrorAction", "type": "named", "line": 25, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "ApiError", "type": "named", "line": 33, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "FormError", "type": "named", "line": 43, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "NavigationError", "type": "named", "line": 51, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "<PERSON>th<PERSON><PERSON><PERSON>", "type": "named", "line": 58, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "UploadError", "type": "named", "line": 65, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "NetworkError", "type": "named", "line": 74, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "AppError", "type": "named", "line": 81, "confidence": "low"}, {"file": "lib\\errors\\types.ts", "name": "ErrorFactory", "type": "named", "line": 84, "confidence": "low"}, {"file": "lib\\features\\feature-flags.ts", "name": "UserRole", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\features\\feature-flags.ts", "name": "FeatureState", "type": "named", "line": 7, "confidence": "low"}, {"file": "lib\\features\\feature-flags.ts", "name": "FeatureFlag", "type": "named", "line": 9, "confidence": "low"}, {"file": "lib\\features\\feature-flags.ts", "name": "FEATURE_FLAGS", "type": "named", "line": 29, "confidence": "low"}, {"file": "lib\\features\\feature-flags.ts", "name": "isFeatureEnabled", "type": "named", "line": 180, "confidence": "low"}, {"file": "lib\\features\\feature-flags.ts", "name": "getFeatureInfo", "type": "named", "line": 247, "confidence": "low"}, {"file": "lib\\features\\feature-flags.ts", "name": "getUserFeatures", "type": "named", "line": 254, "confidence": "low"}, {"file": "lib\\features\\feature-flags.ts", "name": "getFeaturesByState", "type": "named", "line": 267, "confidence": "low"}, {"file": "lib\\features\\feature-flags.ts", "name": "areDependenciesMet", "type": "named", "line": 274, "confidence": "low"}, {"file": "lib\\features\\feature-flags.ts", "name": "getFeatureAccessReason", "type": "named", "line": 293, "confidence": "low"}, {"file": "lib\\forms\\form-handler.ts", "name": "FormField", "type": "named", "line": 7, "confidence": "low"}, {"file": "lib\\forms\\form-handler.ts", "name": "FormState", "type": "named", "line": 14, "confidence": "low"}, {"file": "lib\\forms\\form-handler.ts", "name": "FormConfig", "type": "named", "line": 21, "confidence": "low"}, {"file": "lib\\forms\\form-handler.ts", "name": "useForm", "type": "named", "line": 29, "confidence": "low"}, {"file": "lib\\forms\\form-handler.ts", "name": "formSchemas", "type": "named", "line": 261, "confidence": "low"}, {"file": "lib\\forms\\form-handler.ts", "name": "submitForm", "type": "named", "line": 299, "confidence": "low"}, {"file": "lib\\forms\\form-handler.ts", "name": "updateForm", "type": "named", "line": 316, "confidence": "low"}, {"file": "lib\\git\\CommitManager.ts", "name": "CommitManager", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\git\\CommitManager.ts", "name": "commit<PERSON>anager", "type": "named", "line": 395, "confidence": "low"}, {"file": "lib\\git\\GitIntegration.ts", "name": "GitIntegrationManager", "type": "named", "line": 39, "confidence": "low"}, {"file": "lib\\git\\GitIntegration.ts", "name": "gitIntegration", "type": "named", "line": 375, "confidence": "low"}, {"file": "lib\\hooks\\useAchievements.ts", "name": "UseAchievementsReturn", "type": "named", "line": 17, "confidence": "low"}, {"file": "lib\\hooks\\useAchievements.ts", "name": "useAchievements", "type": "named", "line": 39, "confidence": "low"}, {"file": "lib\\hooks\\useAchievements.ts", "name": "useAchievementProgress", "type": "named", "line": 226, "confidence": "low"}, {"file": "lib\\hooks\\useAchievements.ts", "name": "useAchievementNotifications", "type": "named", "line": 244, "confidence": "low"}, {"file": "lib\\hooks\\useAchievements.ts", "name": "useGamificationStats", "type": "named", "line": 266, "confidence": "low"}, {"file": "lib\\hooks\\useAdvancedCollaborativeEditor.ts", "name": "UseAdvancedCollaborativeEditorOptions", "type": "named", "line": 17, "confidence": "low"}, {"file": "lib\\hooks\\useAdvancedCollaborativeEditor.ts", "name": "CollaborativeEditorState", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\hooks\\useAdvancedCollaborativeEditor.ts", "name": "useAdvancedCollaborativeEditor", "type": "named", "line": 40, "confidence": "low"}, {"file": "lib\\hooks\\useApiData.ts", "name": "useUserProgress", "type": "named", "line": 98, "confidence": "low"}, {"file": "lib\\hooks\\useApiData.ts", "name": "useAchievements", "type": "named", "line": 158, "confidence": "low"}, {"file": "lib\\hooks\\useApiData.ts", "name": "useLearningPaths", "type": "named", "line": 254, "confidence": "low"}, {"file": "lib\\hooks\\useApiData.ts", "name": "useProjects", "type": "named", "line": 299, "confidence": "low"}, {"file": "lib\\hooks\\useApiData.ts", "name": "useCommunityStats", "type": "named", "line": 344, "confidence": "low"}, {"file": "lib\\hooks\\useAsyncButton.ts", "name": "AsyncButtonState", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\hooks\\useAsyncButton.ts", "name": "AsyncButtonOptions", "type": "named", "line": 11, "confidence": "low"}, {"file": "lib\\hooks\\useAsyncButton.ts", "name": "useAsyncButton", "type": "named", "line": 20, "confidence": "low"}, {"file": "lib\\hooks\\useAsyncButton.ts", "name": "useDebouncedAsyncButton", "type": "named", "line": 160, "confidence": "low"}, {"file": "lib\\hooks\\useAsyncButton.ts", "name": "useFormSubmitButton", "type": "named", "line": 194, "confidence": "low"}, {"file": "lib\\hooks\\useAsyncButton.ts", "name": "useApiButton", "type": "named", "line": 222, "confidence": "low"}, {"file": "lib\\hooks\\useAuth.ts", "name": "AuthUser", "type": "named", "line": 11, "confidence": "low"}, {"file": "lib\\hooks\\useAuth.ts", "name": "AuthState", "type": "named", "line": 19, "confidence": "low"}, {"file": "lib\\hooks\\useAuth.ts", "name": "AuthActions", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\hooks\\useAuth.ts", "name": "useAuth", "type": "named", "line": 38, "confidence": "low"}, {"file": "lib\\hooks\\useAuth.ts", "name": "usePermissions", "type": "named", "line": 384, "confidence": "low"}, {"file": "lib\\hooks\\useAuth.ts", "name": "useAuthStatus", "type": "named", "line": 464, "confidence": "low"}, {"file": "lib\\hooks\\useEnhancedKeyboardNavigation.ts", "name": "EnhancedKeyboardNavigationOptions", "type": "named", "line": 4, "confidence": "low"}, {"file": "lib\\hooks\\useEnhancedKeyboardNavigation.ts", "name": "useEnhancedKeyboardNavigation", "type": "named", "line": 17, "confidence": "low"}, {"file": "lib\\hooks\\useEnhancedKeyboardNavigation.ts", "name": "useModalKeyboardNavigation", "type": "named", "line": 247, "confidence": "low"}, {"file": "lib\\hooks\\useEnhancedKeyboardNavigation.ts", "name": "useDropdownKeyboardNavigation", "type": "named", "line": 262, "confidence": "low"}, {"file": "lib\\hooks\\useEnhancedKeyboardNavigation.ts", "name": "useFormKeyboardNavigation", "type": "named", "line": 295, "confidence": "low"}, {"file": "lib\\hooks\\useErrorRecovery.ts", "name": "useRetry", "type": "named", "line": 6, "confidence": "low"}, {"file": "lib\\hooks\\useErrorRecovery.ts", "name": "useOfflineDetection", "type": "named", "line": 99, "confidence": "low"}, {"file": "lib\\hooks\\useErrorRecovery.ts", "name": "useErrorAnalytics", "type": "named", "line": 154, "confidence": "low"}, {"file": "lib\\hooks\\useErrorRecovery.ts", "name": "useErrorReporting", "type": "named", "line": 190, "confidence": "low"}, {"file": "lib\\hooks\\useErrorRecovery.ts", "name": "useErrorHandler", "type": "named", "line": 235, "confidence": "low"}, {"file": "lib\\hooks\\useErrorRecovery.ts", "name": "useFormErrorHandler", "type": "named", "line": 326, "confidence": "low"}, {"file": "lib\\hooks\\useErrorRecovery.ts", "name": "useUploadErrorHandler", "type": "named", "line": 379, "confidence": "low"}, {"file": "lib\\hooks\\useFeatureFlags.tsx", "name": "useFeatureFlags", "type": "named", "line": 30, "confidence": "low"}, {"file": "lib\\hooks\\useFeatureFlags.tsx", "name": "useFeature", "type": "named", "line": 111, "confidence": "low"}, {"file": "lib\\hooks\\useFeatureFlags.tsx", "name": "useFeatures", "type": "named", "line": 127, "confidence": "low"}, {"file": "lib\\hooks\\useFeatureFlags.tsx", "name": "useBetaFeatures", "type": "named", "line": 147, "confidence": "low"}, {"file": "lib\\hooks\\useFeatureFlags.tsx", "name": "useComingSoonFeatures", "type": "named", "line": 162, "confidence": "low"}, {"file": "lib\\hooks\\useFeatureFlags.tsx", "name": "useDevelopmentFeatures", "type": "named", "line": 178, "confidence": "low"}, {"file": "lib\\hooks\\useFeatureFlags.tsx", "name": "FeatureFlagsProvider", "type": "named", "line": 200, "confidence": "low"}, {"file": "lib\\hooks\\useFeatureFlags.tsx", "name": "useFeatureFlagsContext", "type": "named", "line": 218, "confidence": "low"}, {"file": "lib\\hooks\\useFeatureFlags.tsx", "name": "useFeatureGate", "type": "named", "line": 227, "confidence": "low"}, {"file": "lib\\hooks\\useKeyboardNavigation.ts", "name": "KeyboardNavigationOptions", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\hooks\\useKeyboardNavigation.ts", "name": "useKeyboardNavigation", "type": "named", "line": 14, "confidence": "low"}, {"file": "lib\\hooks\\useKeyboardNavigation.ts", "name": "useModalFocus", "type": "named", "line": 223, "confidence": "low"}, {"file": "lib\\hooks\\useKeyboardNavigation.ts", "name": "useDropdownFocus", "type": "named", "line": 236, "confidence": "low"}, {"file": "lib\\hooks\\useKeyboardNavigation.ts", "name": "useFormFocus", "type": "named", "line": 258, "confidence": "low"}, {"file": "lib\\hooks\\useKeyboardNavigation.ts", "name": "useGridFocus", "type": "named", "line": 275, "confidence": "low"}, {"file": "lib\\hooks\\useLazyLoading.ts", "name": "LazyLoadingOptions", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\hooks\\useLazyLoading.ts", "name": "useIntersectionObserver", "type": "default", "line": 301, "confidence": "low"}, {"file": "lib\\hooks\\useLazyLoading.ts", "name": "useLazyComponent", "type": "named", "line": 89, "confidence": "low"}, {"file": "lib\\hooks\\useLazyLoading.ts", "name": "useLazyImage", "type": "named", "line": 120, "confidence": "low"}, {"file": "lib\\hooks\\useLazyLoading.ts", "name": "useLazyData", "type": "named", "line": 147, "confidence": "low"}, {"file": "lib\\hooks\\useLazyLoading.ts", "name": "useVirtualScrolling", "type": "named", "line": 178, "confidence": "low"}, {"file": "lib\\hooks\\useLazyLoading.ts", "name": "useProgressiveImage", "type": "named", "line": 225, "confidence": "low"}, {"file": "lib\\hooks\\useLazyLoading.ts", "name": "useLazyLoadingWithRetry", "type": "named", "line": 251, "confidence": "low"}, {"file": "lib\\hooks\\useNotificationIntegrations.ts", "name": "useErrorNotifications", "type": "named", "line": 11, "confidence": "low"}, {"file": "lib\\hooks\\useNotificationIntegrations.ts", "name": "useAuthNotifications", "type": "named", "line": 84, "confidence": "low"}, {"file": "lib\\hooks\\useNotificationIntegrations.ts", "name": "useGamificationNotifications", "type": "named", "line": 148, "confidence": "low"}, {"file": "lib\\hooks\\useNotificationIntegrations.ts", "name": "useCollaborationNotifications", "type": "named", "line": 201, "confidence": "low"}, {"file": "lib\\hooks\\useNotificationIntegrations.ts", "name": "useAITutoringNotifications", "type": "named", "line": 266, "confidence": "low"}, {"file": "lib\\hooks\\useNotificationIntegrations.ts", "name": "useSystemNotifications", "type": "named", "line": 356, "confidence": "low"}, {"file": "lib\\hooks\\useNotificationSocket.ts", "name": "useNotificationSocket", "type": "named", "line": 12, "confidence": "low"}, {"file": "lib\\hooks\\useRealTimeXP.ts", "name": "XPUpdate", "type": "named", "line": 10, "confidence": "low"}, {"file": "lib\\hooks\\useRealTimeXP.ts", "name": "LevelInfo", "type": "named", "line": 18, "confidence": "low"}, {"file": "lib\\hooks\\useRealTimeXP.ts", "name": "SessionXPData", "type": "named", "line": 28, "confidence": "low"}, {"file": "lib\\hooks\\useRealTimeXP.ts", "name": "useRealTimeXP", "type": "named", "line": 35, "confidence": "low"}, {"file": "lib\\hooks\\useRealTimeXP.ts", "name": "useRealTimeProgress", "type": "named", "line": 254, "confidence": "low"}, {"file": "lib\\hooks\\useRealTimeXP.ts", "name": "useXPSync", "type": "named", "line": 324, "confidence": "low"}, {"file": "lib\\hooks\\useSessionStatus.ts", "name": "SessionStatusHookReturn", "type": "named", "line": 9, "confidence": "low"}, {"file": "lib\\hooks\\useSessionStatus.ts", "name": "useSessionStatus", "type": "named", "line": 20, "confidence": "low"}, {"file": "lib\\hooks\\useSessionStatus.ts", "name": "useSessionWarnings", "type": "named", "line": 158, "confidence": "low"}, {"file": "lib\\hooks\\useSessionStatus.ts", "name": "useSessionSync", "type": "named", "line": 207, "confidence": "low"}, {"file": "lib\\hooks\\useSessionStatus.ts", "name": "useSessionAnalytics", "type": "named", "line": 236, "confidence": "low"}, {"file": "lib\\hooks\\useSettings.ts", "name": "UseSettingsOptions", "type": "named", "line": 19, "confidence": "low"}, {"file": "lib\\hooks\\useSettings.ts", "name": "UseSettingsReturn", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\hooks\\useSettings.ts", "name": "useSettings", "type": "named", "line": 64, "confidence": "low"}, {"file": "lib\\hooks\\useSolidityAnalyzer.ts", "name": "AnalyzerState", "type": "named", "line": 13, "confidence": "low"}, {"file": "lib\\hooks\\useSolidityAnalyzer.ts", "name": "UseAnalyzerOptions", "type": "named", "line": 20, "confidence": "low"}, {"file": "lib\\hooks\\useSolidityAnalyzer.ts", "name": "useSolidityAnalyzer", "type": "named", "line": 31, "confidence": "low"}, {"file": "lib\\hooks\\useSolidityDebugger.ts", "name": "DebuggerState", "type": "named", "line": 13, "confidence": "low"}, {"file": "lib\\hooks\\useSolidityDebugger.ts", "name": "UseDebuggerOptions", "type": "named", "line": 22, "confidence": "low"}, {"file": "lib\\hooks\\useSolidityDebugger.ts", "name": "useSolidityDebugger", "type": "named", "line": 32, "confidence": "low"}, {"file": "lib\\hooks\\useSolidityVersionControl.ts", "name": "VCSState", "type": "named", "line": 14, "confidence": "low"}, {"file": "lib\\hooks\\useSolidityVersionControl.ts", "name": "UseVCSOptions", "type": "named", "line": 26, "confidence": "low"}, {"file": "lib\\hooks\\useSolidityVersionControl.ts", "name": "useSolidityVersionControl", "type": "named", "line": 38, "confidence": "low"}, {"file": "lib\\hooks\\useSwipeGesture.ts", "name": "useSwipeGesture", "type": "named", "line": 12, "confidence": "low"}, {"file": "lib\\hooks\\useSwipeGesture.ts", "name": "useOutsideClick", "type": "named", "line": 92, "confidence": "low"}, {"file": "lib\\hooks\\useSwipeGesture.ts", "name": "useKeyboardNavigation", "type": "named", "line": 115, "confidence": "low"}, {"file": "lib\\hooks\\__tests__\\useSettings.test.ts", "name": "exportResult", "type": "named", "line": 347, "confidence": "low"}, {"file": "lib\\monitoring\\analytics.ts", "name": "analytics", "type": "named", "line": 560, "confidence": "low"}, {"file": "lib\\monitoring\\analytics.ts", "name": "useAnalytics", "type": "named", "line": 565, "confidence": "low"}, {"file": "lib\\monitoring\\apiPerformance.ts", "name": "APIMetrics", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\monitoring\\apiPerformance.ts", "name": "PerformanceThresholds", "type": "named", "line": 16, "confidence": "low"}, {"file": "lib\\monitoring\\apiPerformance.ts", "name": "apiPerformanceMonitor", "type": "default", "line": 439, "confidence": "low"}, {"file": "lib\\monitoring\\apiPerformance.ts", "name": "requestDeduplicator", "type": "named", "line": 255, "confidence": "low"}, {"file": "lib\\monitoring\\apiPerformance.ts", "name": "<PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 305, "confidence": "low"}, {"file": "lib\\monitoring\\apiPerformance.ts", "name": "OptimizedAPIClient", "type": "named", "line": 308, "confidence": "low"}, {"file": "lib\\monitoring\\apiPerformance.ts", "name": "apiClient", "type": "named", "line": 437, "confidence": "low"}, {"file": "lib\\monitoring\\error-tracking.ts", "name": "ErrorEvent", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\monitoring\\error-tracking.ts", "name": "ErrorMetrics", "type": "named", "line": 24, "confidence": "low"}, {"file": "lib\\monitoring\\error-tracking.ts", "name": "errorTracker", "type": "named", "line": 379, "confidence": "low"}, {"file": "lib\\monitoring\\error-tracking.ts", "name": "captureError", "type": "named", "line": 382, "confidence": "low"}, {"file": "lib\\monitoring\\error-tracking.ts", "name": "captureWarning", "type": "named", "line": 386, "confidence": "low"}, {"file": "lib\\monitoring\\error-tracking.ts", "name": "captureInfo", "type": "named", "line": 390, "confidence": "low"}, {"file": "lib\\monitoring\\error-tracking.ts", "name": "useErrorTracking", "type": "named", "line": 395, "confidence": "low"}, {"file": "lib\\monitoring\\errorTracking.ts", "name": "errorTracker", "type": "named", "line": 463, "confidence": "low"}, {"file": "lib\\monitoring\\errorTracking.ts", "name": "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 469, "confidence": "low"}, {"file": "lib\\monitoring\\errorTracking.ts", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "named", "line": 501, "confidence": "low"}, {"file": "lib\\monitoring\\logger.ts", "name": "logger", "type": "named", "line": 505, "confidence": "low"}, {"file": "lib\\monitoring\\logger.ts", "name": "withPerformanceLogging", "type": "named", "line": 510, "confidence": "low"}, {"file": "lib\\monitoring\\logger.ts", "name": "createRequestLogger", "type": "named", "line": 544, "confidence": "low"}, {"file": "lib\\performance\\PerformanceMonitor.ts", "name": "PerformanceMonitor", "type": "named", "line": 21, "confidence": "low"}, {"file": "lib\\performance\\PerformanceMonitor.ts", "name": "performanceMonitor", "type": "named", "line": 409, "confidence": "low"}, {"file": "lib\\prisma-client-wrapper.ts", "name": "prisma", "type": "default", "line": 221, "confidence": "low"}, {"file": "lib\\prisma.ts", "name": "prisma", "type": "named", "line": 7, "confidence": "low"}, {"file": "lib\\security\\config.ts", "name": "SECURITY_CONFIG", "type": "named", "line": 8, "confidence": "low"}, {"file": "lib\\security\\config.ts", "name": "SecurityValidators", "type": "named", "line": 144, "confidence": "low"}, {"file": "lib\\security\\config.ts", "name": "SecurityUtils", "type": "named", "line": 240, "confidence": "low"}, {"file": "lib\\security\\config.ts", "name": "SecurityConfig", "type": "named", "line": 311, "confidence": "low"}, {"file": "lib\\security\\headers.ts", "name": "applySecurityHeaders", "type": "named", "line": 196, "confidence": "low"}, {"file": "lib\\security\\headers.ts", "name": "handleCORS", "type": "named", "line": 222, "confidence": "low"}, {"file": "lib\\security\\headers.ts", "name": "applyCORSHeaders", "type": "named", "line": 268, "confidence": "low"}, {"file": "lib\\security\\headers.ts", "name": "createSecurityMiddleware", "type": "named", "line": 306, "confidence": "low"}, {"file": "lib\\security\\headers.ts", "name": "withSecurity", "type": "named", "line": 347, "confidence": "low"}, {"file": "lib\\security\\headers.ts", "name": "socketCORSConfig", "type": "named", "line": 386, "confidence": "low"}, {"file": "lib\\security\\headers.ts", "name": "validate<PERSON><PERSON><PERSON>", "type": "named", "line": 398, "confidence": "low"}, {"file": "lib\\security\\headers.ts", "name": "generateSecurityReport", "type": "named", "line": 420, "confidence": "low"}, {"file": "lib\\security\\middleware.ts", "name": "createSecurityMiddleware", "type": "named", "line": 23, "confidence": "low"}, {"file": "lib\\security\\middleware.ts", "name": "authSecurityMiddleware", "type": "named", "line": 401, "confidence": "low"}, {"file": "lib\\security\\middleware.ts", "name": "apiSecurityMiddleware", "type": "named", "line": 410, "confidence": "low"}, {"file": "lib\\security\\middleware.ts", "name": "uploadSecurityMiddleware", "type": "named", "line": 419, "confidence": "low"}, {"file": "lib\\security\\rateLimiting.ts", "name": "rateLimiter", "type": "named", "line": 288, "confidence": "low"}, {"file": "lib\\security\\rateLimiting.ts", "name": "rateLimitConfigs", "type": "named", "line": 293, "confidence": "low"}, {"file": "lib\\security\\rateLimiting.ts", "name": "withRateLimit", "type": "named", "line": 379, "confidence": "low"}, {"file": "lib\\security\\session.ts", "name": "sessionSecurity", "type": "named", "line": 293, "confidence": "low"}, {"file": "lib\\security\\session.ts", "name": "getClientInfo", "type": "named", "line": 298, "confidence": "low"}, {"file": "lib\\security\\session.ts", "name": "validateCSRF", "type": "named", "line": 395, "confidence": "low"}, {"file": "lib\\security\\session.ts", "name": "createSessionMiddleware", "type": "named", "line": 413, "confidence": "low"}, {"file": "lib\\security\\session.ts", "name": "getSecureCookieOptions", "type": "named", "line": 429, "confidence": "low"}, {"file": "lib\\security\\session.ts", "name": "detectSuspiciousActivity", "type": "named", "line": 448, "confidence": "low"}, {"file": "lib\\security\\validation.ts", "name": "commonSchemas", "type": "named", "line": 11, "confidence": "low"}, {"file": "lib\\security\\validation.ts", "name": "sanitize", "type": "named", "line": 101, "confidence": "low"}, {"file": "lib\\security\\validation.ts", "name": "validate", "type": "named", "line": 169, "confidence": "low"}, {"file": "lib\\security\\validation.ts", "name": "validateRequest", "type": "named", "line": 325, "confidence": "low"}, {"file": "lib\\security\\validation.ts", "name": "sanitizeAndValidate", "type": "named", "line": 345, "confidence": "low"}, {"file": "lib\\security\\validation.ts", "name": "apiSchemas", "type": "named", "line": 372, "confidence": "low"}, {"file": "lib\\services\\SettingsService.ts", "name": "SettingsService", "type": "named", "line": 17, "confidence": "low"}, {"file": "lib\\socket\\client.ts", "name": "useSocket", "type": "named", "line": 69, "confidence": "low"}, {"file": "lib\\socket\\client.ts", "name": "useCollaborationSessions", "type": "named", "line": 329, "confidence": "low"}, {"file": "lib\\socket\\NotificationSocketService.ts", "name": "SocketNotificationEvent", "type": "named", "line": 6, "confidence": "low"}, {"file": "lib\\socket\\NotificationSocketService.ts", "name": "SocketCollaborationEvent", "type": "named", "line": 20, "confidence": "low"}, {"file": "lib\\socket\\NotificationSocketService.ts", "name": "SocketGamificationEvent", "type": "named", "line": 32, "confidence": "low"}, {"file": "lib\\socket\\NotificationSocketService.ts", "name": "SocketSystemEvent", "type": "named", "line": 45, "confidence": "low"}, {"file": "lib\\socket\\NotificationSocketService.ts", "name": "SocketEvent", "type": "named", "line": 57, "confidence": "low"}, {"file": "lib\\socket\\NotificationSocketService.ts", "name": "NotificationSocketService", "type": "named", "line": 66, "confidence": "low"}, {"file": "lib\\socket\\server.ts", "name": "NextApiResponseServerIO", "type": "named", "line": 6, "confidence": "low"}, {"file": "lib\\socket\\server.ts", "name": "initializeSocket", "type": "named", "line": 81, "confidence": "low"}, {"file": "lib\\socket\\SocketProvider.tsx", "name": "useSocket", "type": "named", "line": 23, "confidence": "low"}, {"file": "lib\\socket\\SocketProvider.tsx", "name": "SocketProvider", "type": "named", "line": 31, "confidence": "low"}, {"file": "lib\\storage\\CodePersistence.ts", "name": "codePersistence", "type": "named", "line": 294, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "UXTestScenario", "type": "named", "line": 9, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "UXTestStep", "type": "named", "line": 21, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "NetworkSimulator", "type": "named", "line": 31, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "LoadingStateTestUtils", "type": "named", "line": 64, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "ErrorBoundaryTestUtils", "type": "named", "line": 130, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "ToastTestUtils", "type": "named", "line": 192, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "NavigationTestUtils", "type": "named", "line": 247, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "AccessibilityTestUtils", "type": "named", "line": 301, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "PerformanceTestUtils", "type": "named", "line": 348, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "UX_TEST_SCENARIOS", "type": "named", "line": 413, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "IntegrationTestUtils", "type": "named", "line": 455, "confidence": "low"}, {"file": "lib\\testing\\ux-testing.ts", "name": "UXTestRunner", "type": "named", "line": 506, "confidence": "low"}, {"file": "lib\\theme\\ThemeProvider.tsx", "name": "ThemeProvider", "type": "named", "line": 24, "confidence": "low"}, {"file": "lib\\theme\\ThemeProvider.tsx", "name": "useTheme", "type": "named", "line": 145, "confidence": "low"}, {"file": "lib\\theme\\ThemeProvider.tsx", "name": "ThemeToggle", "type": "named", "line": 154, "confidence": "low"}, {"file": "lib\\theme\\ThemeProvider.tsx", "name": "EyeComfortSettings", "type": "named", "line": 237, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "AccessibilityIssue", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "AccessibilityReport", "type": "named", "line": 11, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "checkColorContrast", "type": "named", "line": 22, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "checkAriaLabels", "type": "named", "line": 48, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "checkKeyboardAccessibility", "type": "named", "line": 104, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "checkFocusManagement", "type": "named", "line": 150, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "checkTouchTargets", "type": "named", "line": 174, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "auditAccessibility", "type": "named", "line": 195, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "generateAccessibilityReport", "type": "named", "line": 239, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "announceToScreenReader", "type": "named", "line": 275, "confidence": "low"}, {"file": "lib\\utils\\accessibility.ts", "name": "announceFocusChange", "type": "named", "line": 291, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "ImageOptimizationConfig", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "OPTIMIZATION_PRESETS", "type": "named", "line": 12, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "generateResponsiveImageSources", "type": "named", "line": 51, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "generateBlurDataURL", "type": "named", "line": 61, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "supportsWebP", "type": "named", "line": 76, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "supportsAVIF", "type": "named", "line": 86, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "preloadImage", "type": "named", "line": 104, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "preloadCSS", "type": "named", "line": 115, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "LazyImageLoader", "type": "named", "line": 129, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "preloadFont", "type": "named", "line": 184, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "extractCriticalCSS", "type": "named", "line": 197, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "PerformanceMetrics", "type": "named", "line": 223, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "measureWebVitals", "type": "named", "line": 231, "confidence": "low"}, {"file": "lib\\utils\\assetOptimization.ts", "name": "setCacheHeaders", "type": "named", "line": 265, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "CriticalCSSConfig", "type": "named", "line": 3, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "extractCriticalCSS", "type": "named", "line": 11, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "minifyCSS", "type": "named", "line": 77, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "getUnusedSelectors", "type": "named", "line": 92, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "preloadCriticalFonts", "type": "named", "line": 138, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "optimizeFontDisplay", "type": "named", "line": 164, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "GLASSMORPHISM_CRITICAL_CSS", "type": "named", "line": 178, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "CSSLoader", "type": "named", "line": 202, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "measureCSSPerformance", "type": "named", "line": 247, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "deferNonCriticalCSS", "type": "named", "line": 287, "confidence": "low"}, {"file": "lib\\utils\\cssOptimization.ts", "name": "CSS_OPTIMIZATION_CONFIG", "type": "named", "line": 310, "confidence": "low"}, {"file": "lib\\utils\\fallback-integration.ts", "name": "applyAccessibilitySettings", "type": "named", "line": 12, "confidence": "low"}, {"file": "lib\\utils\\fallback-integration.ts", "name": "get<PERSON>allback<PERSON><PERSON>nt", "type": "named", "line": 56, "confidence": "low"}, {"file": "lib\\utils\\fallback-integration.ts", "name": "trackFallbackUsage", "type": "named", "line": 153, "confidence": "low"}, {"file": "lib\\utils\\fallback-integration.ts", "name": "generateContextualHelp", "type": "named", "line": 179, "confidence": "low"}, {"file": "lib\\utils\\fallback-integration.ts", "name": "SmartRetry", "type": "named", "line": 251, "confidence": "low"}, {"file": "lib\\utils\\fallback-integration.ts", "name": "OfflineDataManager", "type": "named", "line": 300, "confidence": "low"}, {"file": "lib\\utils\\fallback-integration.ts", "name": "smartRetry", "type": "named", "line": 381, "confidence": "low"}, {"file": "lib\\utils\\fallback-integration.ts", "name": "offlineData", "type": "named", "line": 382, "confidence": "low"}, {"file": "lib\\utils\\redirects.ts", "name": "RedirectRule", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\utils\\redirects.ts", "name": "RedirectSuggestion", "type": "named", "line": 12, "confidence": "low"}, {"file": "lib\\utils\\redirects.ts", "name": "generateRedirectSuggestions", "type": "named", "line": 147, "confidence": "low"}, {"file": "lib\\utils\\redirects.ts", "name": "shouldAutoRedirect", "type": "named", "line": 246, "confidence": "low"}, {"file": "lib\\utils\\redirects.ts", "name": "NotFoundEvent", "type": "named", "line": 265, "confidence": "low"}, {"file": "lib\\utils\\redirects.ts", "name": "trackNotFoundError", "type": "named", "line": 273, "confidence": "low"}, {"file": "lib\\utils\\redirects.ts", "name": "getContextualSuggestions", "type": "named", "line": 307, "confidence": "low"}, {"file": "lib\\utils.ts", "name": "cn", "type": "named", "line": 4, "confidence": "low"}, {"file": "lib\\vcs\\SolidityVersionControl.ts", "name": "Commit", "type": "named", "line": 5, "confidence": "low"}, {"file": "lib\\vcs\\SolidityVersionControl.ts", "name": "FileChange", "type": "named", "line": 24, "confidence": "low"}, {"file": "lib\\vcs\\SolidityVersionControl.ts", "name": "DiffHunk", "type": "named", "line": 33, "confidence": "low"}, {"file": "lib\\vcs\\SolidityVersionControl.ts", "name": "DiffLine", "type": "named", "line": 41, "confidence": "low"}, {"file": "lib\\vcs\\SolidityVersionControl.ts", "name": "Branch", "type": "named", "line": 48, "confidence": "low"}, {"file": "lib\\vcs\\SolidityVersionControl.ts", "name": "MergeRequest", "type": "named", "line": 58, "confidence": "low"}, {"file": "lib\\vcs\\SolidityVersionControl.ts", "name": "ConflictInfo", "type": "named", "line": 78, "confidence": "low"}, {"file": "lib\\vcs\\SolidityVersionControl.ts", "name": "Repository", "type": "named", "line": 87, "confidence": "low"}, {"file": "lib\\vcs\\SolidityVersionControl.ts", "name": "SolidityVersionControl", "type": "named", "line": 103, "confidence": "low"}, {"file": "hooks\\useAutoSave.ts", "name": "useAutoSave", "type": "named", "line": 26, "confidence": "low"}, {"file": "hooks\\useAutoSave.ts", "name": "useCodeSessions", "type": "named", "line": 215, "confidence": "low"}, {"file": "hooks\\useCollaborationConnection.ts", "name": "useCollaborationConnection", "type": "named", "line": 40, "confidence": "low"}, {"file": "hooks\\useGitIntegration.ts", "name": "useGitIntegration", "type": "named", "line": 28, "confidence": "low"}, {"file": "hooks\\useGitIntegration.ts", "name": "useAutoGit", "type": "named", "line": 246, "confidence": "low"}, {"file": "hooks\\useLessonProgress.ts", "name": "useLessonProgress", "type": "named", "line": 50, "confidence": "low"}, {"file": "hooks\\useLoadingState.ts", "name": "useLoadingState", "type": "default", "line": 229, "confidence": "low"}, {"file": "hooks\\useLoadingState.ts", "name": "useMultipleLoadingStates", "type": "named", "line": 148, "confidence": "low"}, {"file": "hooks\\useLoadingState.ts", "name": "useDebouncedLoading", "type": "named", "line": 196, "confidence": "low"}, {"file": "hooks\\usePerformance.ts", "name": "useDebounce", "type": "named", "line": 4, "confidence": "low"}, {"file": "hooks\\usePerformance.ts", "name": "useThrottle", "type": "named", "line": 21, "confidence": "low"}, {"file": "hooks\\usePerformance.ts", "name": "useIntersectionObserver", "type": "named", "line": 45, "confidence": "low"}, {"file": "hooks\\usePerformance.ts", "name": "useVirtualScroll", "type": "named", "line": 89, "confidence": "low"}, {"file": "hooks\\usePerformance.ts", "name": "useMemoWithDeps", "type": "named", "line": 122, "confidence": "low"}, {"file": "hooks\\usePerformance.ts", "name": "usePerformanceMonitor", "type": "named", "line": 151, "confidence": "low"}, {"file": "hooks\\usePerformance.ts", "name": "useLazyImage", "type": "named", "line": 190, "confidence": "low"}, {"file": "hooks\\usePerformance.ts", "name": "useBatchUpdates", "type": "named", "line": 228, "confidence": "low"}, {"file": "hooks\\usePerformance.ts", "name": "useResourcePreloader", "type": "named", "line": 261, "confidence": "low"}, {"file": "hooks\\usePerformance.ts", "name": "useMemoryMonitor", "type": "named", "line": 323, "confidence": "low"}, {"file": "hooks\\useProgress.ts", "name": "useProgress", "type": "named", "line": 5, "confidence": "low"}, {"file": "hooks\\useUserPresence.ts", "name": "useUserPresence", "type": "named", "line": 37, "confidence": "low"}, {"file": "hooks\\useUserPresence.ts", "name": "useUserColors", "type": "named", "line": 290, "confidence": "low"}, {"file": "types\\global.d.ts", "name": "GoogleGenerativeAI", "type": "named", "line": 16, "confidence": "low"}, {"file": "types\\global.d.ts", "name": "UserRole", "type": "named", "line": 40, "confidence": "low"}, {"file": "types\\global.d.ts", "name": "PrismaClient", "type": "named", "line": 47, "confidence": "low"}, {"file": "types\\settings.ts", "name": "UserProfile", "type": "named", "line": 1, "confidence": "low"}, {"file": "types\\settings.ts", "name": "SecuritySettings", "type": "named", "line": 20, "confidence": "low"}, {"file": "types\\settings.ts", "name": "NotificationSettings", "type": "named", "line": 32, "confidence": "low"}, {"file": "types\\settings.ts", "name": "LearningPreferences", "type": "named", "line": 56, "confidence": "low"}, {"file": "types\\settings.ts", "name": "EditorPreferences", "type": "named", "line": 80, "confidence": "low"}, {"file": "types\\settings.ts", "name": "CollaborationPreferences", "type": "named", "line": 100, "confidence": "low"}, {"file": "types\\settings.ts", "name": "AccessibilitySettings", "type": "named", "line": 113, "confidence": "low"}, {"file": "types\\settings.ts", "name": "PrivacySettings", "type": "named", "line": 124, "confidence": "low"}, {"file": "types\\settings.ts", "name": "UserSettings", "type": "named", "line": 135, "confidence": "low"}, {"file": "types\\settings.ts", "name": "SettingsUpdateRequest", "type": "named", "line": 146, "confidence": "low"}, {"file": "types\\settings.ts", "name": "SettingsValidationError", "type": "named", "line": 153, "confidence": "low"}, {"file": "types\\settings.ts", "name": "SettingsUpdateResponse", "type": "named", "line": 159, "confidence": "low"}, {"file": "types\\settings.ts", "name": "AuditLogEntry", "type": "named", "line": 167, "confidence": "low"}, {"file": "types\\settings.ts", "name": "ActiveSession", "type": "named", "line": 180, "confidence": "low"}, {"file": "types\\settings.ts", "name": "PasswordRequirements", "type": "named", "line": 199, "confidence": "low"}, {"file": "types\\settings.ts", "name": "TwoFactorSetup", "type": "named", "line": 209, "confidence": "low"}, {"file": "types\\settings.ts", "name": "DataExportRequest", "type": "named", "line": 216, "confidence": "low"}, {"file": "types\\settings.ts", "name": "AccountDeletionRequest", "type": "named", "line": 227, "confidence": "low"}, {"file": "types\\settings.ts", "name": "DEFAULT_USER_SETTINGS", "type": "named", "line": 238, "confidence": "low"}, {"file": "services\\geminiService.ts", "name": "initializeChatForModule", "type": "named", "line": 274, "confidence": "low"}, {"file": "services\\geminiService.ts", "name": "sendMessageToGeminiChat", "type": "named", "line": 309, "confidence": "low"}, {"file": "services\\geminiService.ts", "name": "generateDiagramForConcept", "type": "named", "line": 341, "confidence": "low"}, {"file": "services\\geminiService.ts", "name": "getTopicExplanation", "type": "named", "line": 393, "confidence": "low"}]}, "organization": {"issues": ["Route api missing page.tsx", "Route auth missing page.tsx", "Route ping missing page.tsx"], "recommendations": ["Consider adding layout.tsx to achievements for consistent structure", "Consider adding layout.tsx to admin for consistent structure", "Consider adding layout.tsx to auth for consistent structure", "Consider adding layout.tsx to auth-testing for consistent structure", "Consider adding layout.tsx to button-testing for consistent structure", "Consider adding layout.tsx to code for consistent structure", "Consider adding layout.tsx to collaborate for consistent structure", "Consider adding layout.tsx to community for consistent structure", "Consider adding layout.tsx to contact for consistent structure", "Consider adding layout.tsx to cookies for consistent structure", "Consider adding layout.tsx to dashboard for consistent structure", "Consider adding layout.tsx to documentation for consistent structure", "Consider adding layout.tsx to error-testing for consistent structure", "Consider adding layout.tsx to examples for consistent structure", "Consider adding layout.tsx to instructor for consistent structure", "Consider adding layout.tsx to learn for consistent structure", "Consider adding layout.tsx to mentor for consistent structure", "Consider adding layout.tsx to offline for consistent structure", "Consider adding layout.tsx to ping for consistent structure", "Consider adding layout.tsx to privacy for consistent structure", "Consider adding layout.tsx to profile for consistent structure", "Consider adding layout.tsx to session-expired for consistent structure", "Consider adding layout.tsx to settings for consistent structure", "Consider adding layout.tsx to terms for consistent structure", "Consider adding layout.tsx to tutorials for consistent structure", "Consider adding layout.tsx to unauthorized for consistent structure", "Consider adding index.ts to components for cleaner imports", "Consider adding index.ts to lib for cleaner imports", "Consider adding index.ts to hooks for cleaner imports", "Consider adding index.ts to utils for cleaner imports", "Consider adding index.ts to types for cleaner imports"]}, "potentialSavings": {"files": 144, "estimatedSizeKB": 1420}}