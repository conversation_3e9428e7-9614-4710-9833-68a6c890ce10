# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file and rename to .env.production for production deployment
# Set these variables in your deployment platform (Vercel, Railway, etc.)
# =============================================================================

# =============================================================================
# DATABASE CONFIGURATION (REQUIRED)
# =============================================================================
# Supabase PostgreSQL Database URL
# Format: postgresql://postgres:[PASSWORD]@db.[PROJECT-REF].supabase.co:5432/postgres
DATABASE_URL="*********************************************************************************/postgres"

# =============================================================================
# AUTHENTICATION CONFIGURATION (REQUIRED)
# =============================================================================
# Generate with: openssl rand -base64 32
NEXTAUTH_SECRET="YOUR_PRODUCTION_NEXTAUTH_SECRET_32_CHARS_MIN"
NEXTAUTH_URL="https://your-domain.vercel.app"

# GitHub OAuth (Create at: https://github.com/settings/applications/new)
GITHUB_CLIENT_ID="your_github_client_id"
GITHUB_CLIENT_SECRET="your_github_client_secret"

# Google OAuth (Create at: https://console.cloud.google.com/apis/credentials)
GOOGLE_CLIENT_ID="your_google_client_id"
GOOGLE_CLIENT_SECRET="your_google_client_secret"

# =============================================================================
# AI SERVICES CONFIGURATION (REQUIRED)
# =============================================================================
# Google Gemini API Key (Get from: https://makersuite.google.com/app/apikey)
GEMINI_API_KEY="your_gemini_api_key"

# =============================================================================
# OPTIONAL SERVICES (RECOMMENDED FOR PRODUCTION)
# =============================================================================
# Redis for session storage and caching
# Upstash Redis: https://upstash.com/
REDIS_URL="redis://default:password@host:port"

# Socket.io for real-time collaboration
NEXT_PUBLIC_SOCKET_URL="https://your-socket-server.com"

# =============================================================================
# MONITORING & ANALYTICS (OPTIONAL)
# =============================================================================
# Sentry for error tracking
SENTRY_DSN="https://<EMAIL>/project-id"

# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV="production"
NEXT_PUBLIC_APP_URL="https://your-domain.vercel.app"
NEXT_PUBLIC_APP_NAME="Solidity Learning Platform"

# =============================================================================
# FEATURE FLAGS
# =============================================================================
FEATURE_AI_TUTORING=true
FEATURE_COLLABORATION=true
FEATURE_CODE_COMPILATION=true
FEATURE_BLOCKCHAIN_INTEGRATION=true
FEATURE_GAMIFICATION=true
FEATURE_SOCIAL_FEATURES=true

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS origins (comma-separated)
CORS_ORIGINS="https://your-domain.vercel.app,https://your-custom-domain.com"
