#!/usr/bin/env node

/**
 * TypeScript Configuration Optimizer
 * Optimizes tsconfig.json for better performance and stricter type checking
 */

const fs = require('fs');
const path = require('path');

class TSConfigOptimizer {
  constructor() {
    this.configPath = 'tsconfig.json';
    this.backupPath = 'tsconfig.backup.json';
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
  }

  createBackup() {
    if (fs.existsSync(this.configPath)) {
      fs.copyFileSync(this.configPath, this.backupPath);
      this.log('Created backup of tsconfig.json');
    }
  }

  getOptimizedConfig() {
    return {
      compilerOptions: {
        // Target and module settings
        target: "ES2022",
        lib: ["DOM", "DOM.Iterable", "ES2022"],
        allowJs: true,
        skipLibCheck: true,
        strict: true,
        noEmit: true,
        esModuleInterop: true,
        module: "esnext",
        moduleResolution: "bundler",
        resolveJsonModule: true,
        isolatedModules: true,
        jsx: "preserve",
        incremental: true,
        
        // Path mapping for cleaner imports
        baseUrl: ".",
        paths: {
          "@/*": ["./*"],
          "@/components/*": ["./components/*"],
          "@/lib/*": ["./lib/*"],
          "@/hooks/*": ["./hooks/*"],
          "@/types/*": ["./types/*"],
          "@/utils/*": ["./utils/*"],
          "@/app/*": ["./app/*"],
          "@/public/*": ["./public/*"]
        },
        
        // Strict type checking options
        noUnusedLocals: true,
        noUnusedParameters: true,
        exactOptionalPropertyTypes: true,
        noImplicitReturns: true,
        noFallthroughCasesInSwitch: true,
        noUncheckedIndexedAccess: true,
        noImplicitOverride: true,
        
        // Advanced options
        forceConsistentCasingInFileNames: true,
        allowSyntheticDefaultImports: true,
        verbatimModuleSyntax: false,
        
        // Performance optimizations
        assumeChangesOnlyAffectDirectDependencies: true,
        
        // Plugin configurations
        plugins: [
          {
            name: "next"
          }
        ]
      },
      
      // Include patterns
      include: [
        "next-env.d.ts",
        "**/*.ts",
        "**/*.tsx",
        ".next/types/**/*.ts"
      ],
      
      // Exclude patterns for better performance
      exclude: [
        "node_modules",
        ".next",
        "out",
        "dist",
        "build",
        "coverage",
        "**/*.test.ts",
        "**/*.test.tsx",
        "**/*.spec.ts",
        "**/*.spec.tsx",
        "scripts",
        "docs",
        "public",
        "cleanup-backup"
      ],
      
      // TypeScript project references for better performance
      references: [],
      
      // Compiler performance settings
      watchOptions: {
        watchFile: "useFsEvents",
        watchDirectory: "useFsEvents",
        fallbackPolling: "dynamicPriority",
        synchronousWatchDirectory: true,
        excludeDirectories: ["**/node_modules", "**/.git", "**/.next"]
      },
      
      // Type acquisition settings
      typeAcquisition: {
        enable: false,
        include: [],
        exclude: []
      }
    };
  }

  mergeWithExisting() {
    let existingConfig = {};
    
    if (fs.existsSync(this.configPath)) {
      try {
        const content = fs.readFileSync(this.configPath, 'utf8');
        existingConfig = JSON.parse(content);
        this.log('Loaded existing tsconfig.json');
      } catch (error) {
        this.log(`Error reading existing config: ${error.message}`, 'warn');
      }
    }

    const optimizedConfig = this.getOptimizedConfig();
    
    // Merge configurations, prioritizing optimized settings
    const mergedConfig = {
      ...existingConfig,
      compilerOptions: {
        ...existingConfig.compilerOptions,
        ...optimizedConfig.compilerOptions
      },
      include: optimizedConfig.include,
      exclude: [
        ...(existingConfig.exclude || []),
        ...optimizedConfig.exclude
      ].filter((item, index, arr) => arr.indexOf(item) === index), // Remove duplicates
      watchOptions: optimizedConfig.watchOptions,
      typeAcquisition: optimizedConfig.typeAcquisition
    };

    return mergedConfig;
  }

  validateConfig(config) {
    const issues = [];

    // Check for required settings
    if (!config.compilerOptions) {
      issues.push('Missing compilerOptions');
    }

    if (!config.compilerOptions?.strict) {
      issues.push('Strict mode is not enabled');
    }

    if (!config.compilerOptions?.noUnusedLocals) {
      issues.push('noUnusedLocals is not enabled');
    }

    if (!config.compilerOptions?.paths) {
      issues.push('Path mapping is not configured');
    }

    // Check for performance settings
    if (!config.watchOptions) {
      issues.push('Watch options are not configured for optimal performance');
    }

    return issues;
  }

  generateReport(oldConfig, newConfig) {
    const report = {
      timestamp: new Date().toISOString(),
      changes: {
        compilerOptions: {},
        newSettings: [],
        optimizations: []
      },
      performance: {
        stricterTypeChecking: true,
        betterPathResolution: true,
        optimizedWatching: true,
        fasterIncrementalBuilds: true
      },
      recommendations: [
        'Run "npm run type-check" to verify all types are correct',
        'Consider enabling additional strict flags gradually',
        'Monitor build performance after changes',
        'Update IDE settings to use workspace TypeScript version'
      ]
    };

    // Compare compiler options
    const oldOptions = oldConfig.compilerOptions || {};
    const newOptions = newConfig.compilerOptions || {};

    Object.keys(newOptions).forEach(key => {
      if (oldOptions[key] !== newOptions[key]) {
        report.changes.compilerOptions[key] = {
          old: oldOptions[key],
          new: newOptions[key]
        };
      }
    });

    // Identify new settings
    Object.keys(newOptions).forEach(key => {
      if (!(key in oldOptions)) {
        report.changes.newSettings.push(key);
      }
    });

    // Performance optimizations
    if (newConfig.watchOptions && !oldConfig.watchOptions) {
      report.changes.optimizations.push('Added watch options for better performance');
    }

    if (newConfig.compilerOptions.incremental && !oldOptions.incremental) {
      report.changes.optimizations.push('Enabled incremental compilation');
    }

    return report;
  }

  run() {
    this.log('Starting TypeScript configuration optimization...');

    // Create backup
    this.createBackup();

    // Load and merge configurations
    const oldConfig = fs.existsSync(this.configPath) 
      ? JSON.parse(fs.readFileSync(this.configPath, 'utf8'))
      : {};
    
    const newConfig = this.mergeWithExisting();

    // Validate configuration
    const issues = this.validateConfig(newConfig);
    if (issues.length > 0) {
      this.log('Configuration issues found:', 'warn');
      issues.forEach(issue => this.log(`  - ${issue}`, 'warn'));
    }

    // Write optimized configuration
    fs.writeFileSync(this.configPath, JSON.stringify(newConfig, null, 2));
    this.log('Updated tsconfig.json with optimized settings');

    // Generate and save report
    const report = this.generateReport(oldConfig, newConfig);
    const reportPath = `tsconfig-optimization-report-${Date.now()}.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    // Display summary
    console.log('\n=== TYPESCRIPT OPTIMIZATION REPORT ===');
    console.log(`Compiler options changed: ${Object.keys(report.changes.compilerOptions).length}`);
    console.log(`New settings added: ${report.changes.newSettings.length}`);
    console.log(`Performance optimizations: ${report.changes.optimizations.length}`);
    
    console.log('\nKey improvements:');
    console.log('✓ Stricter type checking enabled');
    console.log('✓ Better path resolution configured');
    console.log('✓ Optimized watch mode settings');
    console.log('✓ Faster incremental builds enabled');
    
    console.log('\nNext steps:');
    report.recommendations.forEach(rec => {
      console.log(`  - ${rec}`);
    });

    console.log(`\nBackup saved as: ${this.backupPath}`);
    console.log(`Detailed report saved as: ${reportPath}`);

    this.log('TypeScript optimization completed successfully');
  }
}

// Run the optimizer
if (require.main === module) {
  const optimizer = new TSConfigOptimizer();
  optimizer.run();
}

module.exports = TSConfigOptimizer;
