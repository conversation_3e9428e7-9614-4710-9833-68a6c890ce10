name: Production Deployment

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
    types: [opened, synchronize, reopened, ready_for_review]

env:
  NODE_VERSION: '20'
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

jobs:
  # Quality Gates
  quality-gates:
    name: Quality Gates
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Type checking
        run: npm run type-check

      - name: Linting
        run: npm run lint:check

      - name: Security audit
        run: npm audit --audit-level=high
        continue-on-error: true

      - name: Run tests with coverage
        run: npm run test:coverage
        env:
          NODE_ENV: test

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

      - name: Bundle analysis
        run: npm run analyze
        env:
          ANALYZE: true

      - name: Check bundle size
        run: |
          npm run build
          npx bundlesize
        continue-on-error: true

  # Accessibility Testing
  accessibility-tests:
    name: Accessibility Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Start application
        run: npm start &
        env:
          NODE_ENV: production

      - name: Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000

      - name: Run accessibility tests
        run: npm run test:accessibility

      - name: Run Lighthouse CI
        run: |
          npm install -g @lhci/cli@0.12.x
          lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

  # Performance Testing
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event.pull_request.draft == false
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Start application
        run: npm start &
        env:
          NODE_ENV: production

      - name: Wait for application
        run: npx wait-on http://localhost:3000 --timeout 60000

      - name: Run performance tests
        run: npm run test:performance

      - name: Run Core Web Vitals audit
        run: |
          npx lighthouse http://localhost:3000 \
            --output=json \
            --output-path=lighthouse-report.json \
            --chrome-flags="--headless --no-sandbox"

      - name: Upload Lighthouse report
        uses: actions/upload-artifact@v3
        with:
          name: lighthouse-report
          path: lighthouse-report.json

  # Preview Deployment (for PRs)
  preview-deployment:
    name: Preview Deployment
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' && github.event.pull_request.draft == false
    needs: [quality-gates, accessibility-tests, performance-tests]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel environment information
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build project artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}

      - name: Deploy to Vercel
        id: deploy
        run: |
          url=$(vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }})
          echo "preview_url=$url" >> $GITHUB_OUTPUT

      - name: Comment PR with preview URL
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `🚀 Preview deployment ready!\n\n**Preview URL:** ${{ steps.deploy.outputs.preview_url }}\n\nThis preview will be automatically updated when you push new commits to this PR.`
            })

  # Production Deployment (for main branch)
  production-deployment:
    name: Production Deployment
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [quality-gates, accessibility-tests, performance-tests]
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run production deployment script
        run: node scripts/production-deployment.js
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          DATABASE_URL: ${{ secrets.DATABASE_URL }}
          NEXTAUTH_SECRET: ${{ secrets.NEXTAUTH_SECRET }}
          NEXTAUTH_URL: ${{ secrets.NEXTAUTH_URL }}
          NEXT_PUBLIC_GA_ID: ${{ secrets.NEXT_PUBLIC_GA_ID }}
          SENTRY_DSN: ${{ secrets.SENTRY_DSN }}
          NEXT_PUBLIC_HOTJAR_ID: ${{ secrets.NEXT_PUBLIC_HOTJAR_ID }}

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Pull Vercel environment information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}

      - name: Build project artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}

      - name: Deploy to Vercel
        id: deploy
        run: |
          url=$(vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }})
          echo "production_url=$url" >> $GITHUB_OUTPUT

      - name: Post-deployment verification
        run: |
          # Wait for deployment to be ready
          sleep 30
          
          # Run health checks
          curl -f ${{ steps.deploy.outputs.production_url }}/api/health || exit 1
          
          # Run critical user journey tests
          npm run test:e2e:production
        env:
          PRODUCTION_URL: ${{ steps.deploy.outputs.production_url }}

      - name: Run post-deployment Lighthouse audit
        run: |
          npx lighthouse ${{ steps.deploy.outputs.production_url }} \
            --output=json \
            --output-path=production-lighthouse-report.json \
            --chrome-flags="--headless --no-sandbox"

      - name: Notify deployment success
        uses: actions/github-script@v6
        with:
          script: |
            github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sha: context.sha,
              state: 'success',
              target_url: '${{ steps.deploy.outputs.production_url }}',
              description: 'Production deployment successful',
              context: 'deployment/production'
            })

      - name: Upload production artifacts
        uses: actions/upload-artifact@v3
        with:
          name: production-artifacts
          path: |
            production-lighthouse-report.json
            deployment-report-*.json

  # Post-deployment monitoring
  post-deployment-monitoring:
    name: Post-deployment Monitoring
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [production-deployment]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup monitoring
        run: |
          # Setup Sentry release
          curl -sL https://sentry.io/get-cli/ | bash
          sentry-cli releases new ${{ github.sha }}
          sentry-cli releases set-commits ${{ github.sha }} --auto
          sentry-cli releases finalize ${{ github.sha }}
        env:
          SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_AUTH_TOKEN }}
          SENTRY_ORG: ${{ secrets.SENTRY_ORG }}
          SENTRY_PROJECT: ${{ secrets.SENTRY_PROJECT }}

      - name: Trigger uptime monitoring
        run: |
          # Ping uptime monitoring service
          curl -X POST "${{ secrets.UPTIME_WEBHOOK_URL }}" \
            -H "Content-Type: application/json" \
            -d '{"event": "deployment", "status": "success", "url": "${{ needs.production-deployment.outputs.production_url }}"}'
        continue-on-error: true

      - name: Update deployment status
        run: |
          echo "✅ Production deployment completed successfully"
          echo "🌐 Production URL: ${{ needs.production-deployment.outputs.production_url }}"
          echo "📊 Monitoring and analytics are active"
