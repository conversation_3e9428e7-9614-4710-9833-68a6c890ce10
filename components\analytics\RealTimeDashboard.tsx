'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  BarChart3, 
  Users, 
  TrendingUp, 
  Eye, 
  MousePointer, 
  Clock,
  Target,
  Zap,
  Activity,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface RealTimeMetrics {
  activeUsers: number;
  conversionRate: number;
  bounceRate: number;
  avgSessionDuration: number;
  pageViews: number;
  exitIntentTriggers: number;
  scrollEngagement: number;
  trialSignups: number;
  lastUpdated: Date;
}

interface ConversionFunnel {
  landing: number;
  trialSignup: number;
  firstLesson: number;
  paidConversion: number;
  conversionRates: {
    landingToTrial: number;
    trialToLesson: number;
    lessonToPaid: number;
    overallConversion: number;
  };
}

interface UserEngagementData {
  heatmapClicks: Array<{ x: number; y: number; intensity: number }>;
  scrollDepth: number[];
  timeOnPage: number[];
  interactionEvents: Array<{ type: string; timestamp: number; element: string }>;
}

// Real-time metrics hook
export function useRealTimeMetrics() {
  const [metrics, setMetrics] = useState<RealTimeMetrics>({
    activeUsers: 0,
    conversionRate: 0,
    bounceRate: 0,
    avgSessionDuration: 0,
    pageViews: 0,
    exitIntentTriggers: 0,
    scrollEngagement: 0,
    trialSignups: 0,
    lastUpdated: new Date()
  });

  const [isConnected, setIsConnected] = useState(false);

  useEffect(() => {
    // Simulate real-time data updates
    const interval = setInterval(() => {
      setMetrics(prev => ({
        activeUsers: Math.max(0, prev.activeUsers + Math.floor(Math.random() * 6) - 2),
        conversionRate: Math.max(0, Math.min(100, prev.conversionRate + (Math.random() - 0.5) * 2)),
        bounceRate: Math.max(0, Math.min(100, prev.bounceRate + (Math.random() - 0.5) * 3)),
        avgSessionDuration: Math.max(0, prev.avgSessionDuration + (Math.random() - 0.5) * 30),
        pageViews: prev.pageViews + Math.floor(Math.random() * 5),
        exitIntentTriggers: prev.exitIntentTriggers + Math.floor(Math.random() * 2),
        scrollEngagement: Math.max(0, Math.min(100, prev.scrollEngagement + (Math.random() - 0.5) * 5)),
        trialSignups: prev.trialSignups + (Math.random() < 0.1 ? 1 : 0),
        lastUpdated: new Date()
      }));
    }, 5000);

    setIsConnected(true);

    return () => {
      clearInterval(interval);
      setIsConnected(false);
    };
  }, []);

  return { metrics, isConnected };
}

// Conversion funnel hook
export function useConversionFunnel() {
  const [funnel, setFunnel] = useState<ConversionFunnel>({
    landing: 1000,
    trialSignup: 250,
    firstLesson: 180,
    paidConversion: 45,
    conversionRates: {
      landingToTrial: 25,
      trialToLesson: 72,
      lessonToPaid: 25,
      overallConversion: 4.5
    }
  });

  useEffect(() => {
    // Update funnel data periodically
    const interval = setInterval(() => {
      setFunnel(prev => {
        const newLanding = prev.landing + Math.floor(Math.random() * 10);
        const newTrialSignup = prev.trialSignup + Math.floor(Math.random() * 3);
        const newFirstLesson = prev.firstLesson + Math.floor(Math.random() * 2);
        const newPaidConversion = prev.paidConversion + (Math.random() < 0.2 ? 1 : 0);

        return {
          landing: newLanding,
          trialSignup: newTrialSignup,
          firstLesson: newFirstLesson,
          paidConversion: newPaidConversion,
          conversionRates: {
            landingToTrial: (newTrialSignup / newLanding) * 100,
            trialToLesson: (newFirstLesson / newTrialSignup) * 100,
            lessonToPaid: (newPaidConversion / newFirstLesson) * 100,
            overallConversion: (newPaidConversion / newLanding) * 100
          }
        };
      });
    }, 10000);

    return () => clearInterval(interval);
  }, []);

  return funnel;
}

// Metric card component
function MetricCard({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  format = 'number',
  className 
}: {
  title: string;
  value: number;
  change?: number;
  icon: React.ComponentType<{ className?: string }>;
  format?: 'number' | 'percentage' | 'duration' | 'currency';
  className?: string;
}) {
  const formatValue = (val: number) => {
    switch (format) {
      case 'percentage':
        return `${val.toFixed(1)}%`;
      case 'duration':
        return `${Math.floor(val / 60)}:${(val % 60).toString().padStart(2, '0')}`;
      case 'currency':
        return `$${val.toLocaleString()}`;
      default:
        return val.toLocaleString();
    }
  };

  const getChangeIcon = () => {
    if (!change) return null;
    if (change > 0) return <ArrowUp className="w-3 h-3 text-green-400" />;
    if (change < 0) return <ArrowDown className="w-3 h-3 text-red-400" />;
    return <Minus className="w-3 h-3 text-gray-400" />;
  };

  const getChangeColor = () => {
    if (!change) return 'text-gray-400';
    if (change > 0) return 'text-green-400';
    if (change < 0) return 'text-red-400';
    return 'text-gray-400';
  };

  return (
    <motion.div
      className={cn(
        'bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6',
        className
      )}
      whileHover={{ scale: 1.02 }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="p-2 bg-blue-500/20 rounded-lg">
          <Icon className="w-5 h-5 text-blue-400" />
        </div>
        {change !== undefined && (
          <div className={cn('flex items-center space-x-1 text-sm', getChangeColor())}>
            {getChangeIcon()}
            <span>{Math.abs(change).toFixed(1)}%</span>
          </div>
        )}
      </div>
      
      <div>
        <div className="text-2xl font-bold text-white mb-1">
          {formatValue(value)}
        </div>
        <div className="text-gray-400 text-sm">{title}</div>
      </div>
    </motion.div>
  );
}

// Conversion funnel visualization
function ConversionFunnelChart({ funnel }: { funnel: ConversionFunnel }) {
  const steps = [
    { label: 'Landing Page', value: funnel.landing, color: 'bg-blue-500' },
    { label: 'Trial Signup', value: funnel.trialSignup, color: 'bg-green-500' },
    { label: 'First Lesson', value: funnel.firstLesson, color: 'bg-yellow-500' },
    { label: 'Paid Conversion', value: funnel.paidConversion, color: 'bg-purple-500' }
  ];

  const maxValue = Math.max(...steps.map(step => step.value));

  return (
    <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
      <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
        <Target className="w-5 h-5" />
        <span>Conversion Funnel</span>
      </h3>
      
      <div className="space-y-4">
        {steps.map((step, index) => {
          const width = (step.value / maxValue) * 100;
          const conversionRate = index > 0 ? (step.value / steps[index - 1].value) * 100 : 100;
          
          return (
            <motion.div
              key={step.label}
              className="space-y-2"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              <div className="flex items-center justify-between text-sm">
                <span className="text-white font-medium">{step.label}</span>
                <div className="flex items-center space-x-4">
                  <span className="text-gray-400">{step.value.toLocaleString()}</span>
                  {index > 0 && (
                    <span className="text-blue-400">{conversionRate.toFixed(1)}%</span>
                  )}
                </div>
              </div>
              
              <div className="relative h-8 bg-gray-800 rounded-lg overflow-hidden">
                <motion.div
                  className={cn('h-full rounded-lg', step.color)}
                  initial={{ width: 0 }}
                  animate={{ width: `${width}%` }}
                  transition={{ duration: 0.8, delay: index * 0.2 }}
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <span className="text-white text-xs font-medium">
                    {step.value.toLocaleString()}
                  </span>
                </div>
              </div>
            </motion.div>
          );
        })}
      </div>
      
      <div className="mt-6 pt-4 border-t border-white/10">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-400">Overall Conversion:</span>
            <span className="text-green-400 font-medium ml-2">
              {funnel.conversionRates.overallConversion.toFixed(1)}%
            </span>
          </div>
          <div>
            <span className="text-gray-400">Best Step:</span>
            <span className="text-blue-400 font-medium ml-2">
              Trial → Lesson ({funnel.conversionRates.trialToLesson.toFixed(1)}%)
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

// Real-time activity feed
function ActivityFeed() {
  const [activities, setActivities] = useState<Array<{
    id: string;
    type: string;
    message: string;
    timestamp: Date;
    icon: React.ComponentType<{ className?: string }>;
  }>>([]);

  useEffect(() => {
    const generateActivity = () => {
      const activityTypes = [
        { type: 'signup', message: 'New user signed up for trial', icon: Users },
        { type: 'conversion', message: 'Trial user converted to paid', icon: Target },
        { type: 'engagement', message: 'User completed first lesson', icon: Zap },
        { type: 'exit_intent', message: 'Exit intent triggered and recovered', icon: Activity }
      ];

      const randomActivity = activityTypes[Math.floor(Math.random() * activityTypes.length)];
      
      setActivities(prev => [
        {
          id: Date.now().toString(),
          ...randomActivity,
          timestamp: new Date()
        },
        ...prev.slice(0, 9) // Keep only last 10 activities
      ]);
    };

    // Generate initial activities
    for (let i = 0; i < 5; i++) {
      setTimeout(generateActivity, i * 1000);
    }

    // Continue generating activities
    const interval = setInterval(generateActivity, 8000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6">
      <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
        <Activity className="w-5 h-5" />
        <span>Live Activity</span>
      </h3>
      
      <div className="space-y-3 max-h-80 overflow-y-auto">
        <AnimatePresence>
          {activities.map((activity) => (
            <motion.div
              key={activity.id}
              className="flex items-start space-x-3 p-3 bg-white/5 rounded-lg"
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="p-1 bg-blue-500/20 rounded">
                <activity.icon className="w-3 h-3 text-blue-400" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-white text-sm">{activity.message}</p>
                <p className="text-gray-400 text-xs">
                  {activity.timestamp.toLocaleTimeString()}
                </p>
              </div>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}

// Main dashboard component
export function RealTimeDashboard({ className }: { className?: string }) {
  const { metrics, isConnected } = useRealTimeMetrics();
  const funnel = useConversionFunnel();

  return (
    <div className={cn('space-y-6', className)}>
      {/* Connection Status */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-white">Real-Time Analytics</h2>
        <div className="flex items-center space-x-2">
          <div className={cn(
            'w-2 h-2 rounded-full',
            isConnected ? 'bg-green-400' : 'bg-red-400'
          )} />
          <span className="text-gray-400 text-sm">
            {isConnected ? 'Live' : 'Disconnected'}
          </span>
          <span className="text-gray-500 text-xs">
            Updated {metrics.lastUpdated.toLocaleTimeString()}
          </span>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Active Users"
          value={metrics.activeUsers}
          change={2.3}
          icon={Users}
        />
        <MetricCard
          title="Conversion Rate"
          value={metrics.conversionRate}
          change={-0.8}
          icon={Target}
          format="percentage"
        />
        <MetricCard
          title="Bounce Rate"
          value={metrics.bounceRate}
          change={-1.2}
          icon={TrendingUp}
          format="percentage"
        />
        <MetricCard
          title="Avg Session"
          value={metrics.avgSessionDuration}
          change={5.4}
          icon={Clock}
          format="duration"
        />
      </div>

      {/* Secondary Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Page Views"
          value={metrics.pageViews}
          icon={Eye}
        />
        <MetricCard
          title="Exit Intent Triggers"
          value={metrics.exitIntentTriggers}
          icon={MousePointer}
        />
        <MetricCard
          title="Scroll Engagement"
          value={metrics.scrollEngagement}
          icon={Activity}
          format="percentage"
        />
        <MetricCard
          title="Trial Signups"
          value={metrics.trialSignups}
          change={12.5}
          icon={Zap}
        />
      </div>

      {/* Funnel and Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <ConversionFunnelChart funnel={funnel} />
        <ActivityFeed />
      </div>
    </div>
  );
}
