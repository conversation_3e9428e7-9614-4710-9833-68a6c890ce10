'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Save, 
  Mail, 
  Github, 
  Chrome, 
  Users, 
  Clock, 
  Star, 
  Zap,
  X,
  CheckCircle,
  AlertCircle,
  TrendingUp
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AccountPrompt {
  trigger: 'step-completion' | 'time-based' | 'engagement-score';
  stepNumber?: number;
  timeThreshold?: number;
  engagementThreshold?: number;
  message: string;
  urgencyLevel: 'low' | 'medium' | 'high';
  incentive?: string;
  socialProof?: string;
}

interface OAuthProvider {
  id: 'github' | 'google' | 'discord';
  name: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  enabled: boolean;
}

interface StrategicAccountCreationProps {
  currentStep: number;
  totalSteps: number;
  timeElapsed: number;
  totalXP: number;
  achievements: number;
  onAccountCreated?: (method: string, email?: string) => void;
  onGuestContinue?: (email?: string) => void;
  onDismiss?: () => void;
  className?: string;
}

/**
 * Strategic account creation component with urgency messaging and social proof
 */
export function StrategicAccountCreation({
  currentStep,
  totalSteps,
  timeElapsed,
  totalXP,
  achievements,
  onAccountCreated,
  onGuestContinue,
  onDismiss,
  className = ''
}: StrategicAccountCreationProps) {
  const [showPrompt, setShowPrompt] = useState(false);
  const [currentPrompt, setCurrentPrompt] = useState<AccountPrompt | null>(null);
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [mode, setMode] = useState<'prompt' | 'signup' | 'guest'>('prompt');

  const oauthProviders: OAuthProvider[] = [
    {
      id: 'github',
      name: 'GitHub',
      icon: Github,
      color: 'bg-gray-800 hover:bg-gray-700 border-gray-600',
      enabled: true
    },
    {
      id: 'google',
      name: 'Google',
      icon: Chrome,
      color: 'bg-blue-600 hover:bg-blue-700 border-blue-500',
      enabled: true
    }
  ];

  const accountPrompts: AccountPrompt[] = [
    {
      trigger: 'step-completion',
      stepNumber: 2,
      message: "You're making great progress!",
      urgencyLevel: 'low',
      incentive: 'Save your progress and unlock exclusive features',
      socialProof: 'Join 10,000+ developers learning Solidity'
    },
    {
      trigger: 'step-completion',
      stepNumber: 4,
      message: "Don't lose your amazing progress!",
      urgencyLevel: 'medium',
      incentive: 'Get personalized learning recommendations',
      socialProof: '95% of users who create accounts complete more courses'
    },
    {
      trigger: 'time-based',
      timeThreshold: 120000, // 2 minutes
      message: 'You\'re clearly engaged!',
      urgencyLevel: 'medium',
      incentive: 'Unlock advanced features and track your journey',
      socialProof: 'Developers with accounts learn 3x faster'
    },
    {
      trigger: 'engagement-score',
      engagementThreshold: 200,
      message: 'You\'re a natural at this!',
      urgencyLevel: 'high',
      incentive: 'Join our community of expert developers',
      socialProof: 'Top 10% of learners create accounts within 3 minutes'
    }
  ];

  // Check for prompt triggers
  useEffect(() => {
    const checkTriggers = () => {
      for (const prompt of accountPrompts) {
        if (prompt.trigger === 'step-completion' && currentStep === prompt.stepNumber) {
          setCurrentPrompt(prompt);
          setShowPrompt(true);
          break;
        } else if (prompt.trigger === 'time-based' && timeElapsed >= (prompt.timeThreshold || 0)) {
          setCurrentPrompt(prompt);
          setShowPrompt(true);
          break;
        } else if (prompt.trigger === 'engagement-score' && totalXP >= (prompt.engagementThreshold || 0)) {
          setCurrentPrompt(prompt);
          setShowPrompt(true);
          break;
        }
      }
    };

    checkTriggers();
  }, [currentStep, timeElapsed, totalXP]);

  const handleOAuthSignup = async (provider: OAuthProvider) => {
    setIsLoading(true);
    try {
      // Simulate OAuth flow
      await new Promise(resolve => setTimeout(resolve, 1500));
      onAccountCreated?.(provider.id);
      setShowPrompt(false);
    } catch (error) {
      console.error('OAuth signup failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmailSignup = async () => {
    if (!email.trim()) return;
    
    setIsLoading(true);
    try {
      // Simulate email signup
      await new Promise(resolve => setTimeout(resolve, 1000));
      onAccountCreated?.('email', email);
      setShowPrompt(false);
    } catch (error) {
      console.error('Email signup failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGuestContinue = () => {
    onGuestContinue?.(email || undefined);
    setShowPrompt(false);
  };

  const getUrgencyColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'from-red-500 to-orange-500';
      case 'medium':
        return 'from-yellow-500 to-orange-500';
      default:
        return 'from-blue-500 to-purple-500';
    }
  };

  const getUrgencyBorder = (level: string) => {
    switch (level) {
      case 'high':
        return 'border-red-500/50';
      case 'medium':
        return 'border-yellow-500/50';
      default:
        return 'border-blue-500/50';
    }
  };

  if (!showPrompt || !currentPrompt) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <motion.div
          className={cn(
            'bg-gray-900 border-2 rounded-xl p-6 max-w-md w-full shadow-2xl',
            getUrgencyBorder(currentPrompt.urgencyLevel),
            className
          )}
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
        >
          {/* Header */}
          <div className="flex items-start justify-between mb-4">
            <div className="flex-1">
              <div className={cn(
                'inline-flex items-center px-3 py-1 rounded-full text-xs font-bold text-white mb-2',
                `bg-gradient-to-r ${getUrgencyColor(currentPrompt.urgencyLevel)}`
              )}>
                <Save className="w-3 h-3 mr-1" />
                Save Your Progress
              </div>
              <h3 className="text-xl font-bold text-white">
                {currentPrompt.message}
              </h3>
            </div>
            <button
              onClick={() => setShowPrompt(false)}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Progress Stats */}
          <div className="grid grid-cols-3 gap-4 mb-6 p-4 bg-gray-800/50 rounded-lg">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-400">{totalXP}</div>
              <div className="text-xs text-gray-400">XP Earned</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">{achievements}</div>
              <div className="text-xs text-gray-400">Achievements</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-400">
                {Math.round((currentStep / totalSteps) * 100)}%
              </div>
              <div className="text-xs text-gray-400">Complete</div>
            </div>
          </div>

          {/* Incentive */}
          {currentPrompt.incentive && (
            <div className="mb-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-blue-400" />
                <span className="text-blue-300 text-sm font-medium">
                  {currentPrompt.incentive}
                </span>
              </div>
            </div>
          )}

          {/* Social Proof */}
          {currentPrompt.socialProof && (
            <div className="mb-6 p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-4 h-4 text-green-400" />
                <span className="text-green-300 text-sm">
                  {currentPrompt.socialProof}
                </span>
              </div>
            </div>
          )}

          {mode === 'prompt' && (
            <div className="space-y-4">
              {/* OAuth Providers */}
              <div className="space-y-2">
                {oauthProviders.filter(p => p.enabled).map((provider) => (
                  <motion.button
                    key={provider.id}
                    onClick={() => handleOAuthSignup(provider)}
                    disabled={isLoading}
                    className={cn(
                      'w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg font-medium transition-colors border',
                      provider.color,
                      isLoading && 'opacity-50 cursor-not-allowed'
                    )}
                    whileHover={!isLoading ? { scale: 1.02 } : {}}
                    whileTap={!isLoading ? { scale: 0.98 } : {}}
                  >
                    {isLoading ? (
                      <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <provider.icon className="w-5 h-5" />
                    )}
                    <span>Continue with {provider.name}</span>
                  </motion.button>
                ))}
              </div>

              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-600"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-gray-900 text-gray-400">or</span>
                </div>
              </div>

              {/* Email Option */}
              <button
                onClick={() => setMode('signup')}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 hover:bg-purple-700 rounded-lg font-medium transition-colors"
              >
                <Mail className="w-5 h-5" />
                <span>Sign up with Email</span>
              </button>

              {/* Guest Option */}
              <button
                onClick={() => setMode('guest')}
                className="w-full text-center text-gray-400 hover:text-white transition-colors text-sm"
              >
                Continue as guest
              </button>
            </div>
          )}

          {mode === 'signup' && (
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                />
              </div>
              
              <button
                onClick={handleEmailSignup}
                disabled={!email.trim() || isLoading}
                className="w-full flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg font-medium transition-colors"
              >
                {isLoading ? (
                  <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <CheckCircle className="w-5 h-5" />
                )}
                <span>Create Account</span>
              </button>

              <button
                onClick={() => setMode('prompt')}
                className="w-full text-center text-gray-400 hover:text-white transition-colors text-sm"
              >
                ← Back to options
              </button>
            </div>
          )}

          {mode === 'guest' && (
            <div className="space-y-4">
              <div className="text-center">
                <AlertCircle className="w-12 h-12 text-yellow-400 mx-auto mb-3" />
                <h4 className="text-lg font-semibold text-white mb-2">
                  Continue as Guest?
                </h4>
                <p className="text-gray-300 text-sm mb-4">
                  You'll lose your progress when you close the browser. 
                  We can send you a reminder email to continue later.
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Email (Optional)
                </label>
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => setMode('prompt')}
                  className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg font-medium transition-colors"
                >
                  Back
                </button>
                <button
                  onClick={handleGuestContinue}
                  className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 rounded-lg font-medium transition-colors"
                >
                  Continue
                </button>
              </div>
            </div>
          )}

          {/* Urgency Timer */}
          {currentPrompt.urgencyLevel === 'high' && (
            <div className="mt-4 p-2 bg-red-500/10 border border-red-500/30 rounded-lg">
              <div className="flex items-center justify-center space-x-2 text-red-300 text-xs">
                <Clock className="w-3 h-3" />
                <span>Limited time: Save your progress now!</span>
              </div>
            </div>
          )}
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
