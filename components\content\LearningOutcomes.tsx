'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Target,
  TrendingUp,
  Award,
  Code,
  Briefcase,
  DollarSign,
  Users,
  CheckCircle,
  Star,
  ArrowRight,
  ExternalLink,
  BookOpen,
  Zap,
  Shield,
  Globe,
  Building,
  ChevronDown,
  ChevronUp,
  Play,
  Download
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface LearningOutcome {
  id: string;
  title: string;
  description: string;
  category: 'technical' | 'practical' | 'professional' | 'certification';
  level: 'beginner' | 'intermediate' | 'advanced';
  skills: string[];
  measurableGoals: string[];
  timeToAchieve: string;
  prerequisites?: string[];
}

interface SkillProgression {
  skill: string;
  category: 'core' | 'advanced' | 'specialized';
  levels: {
    beginner: {
      description: string;
      capabilities: string[];
      projects: string[];
    };
    intermediate: {
      description: string;
      capabilities: string[];
      projects: string[];
    };
    advanced: {
      description: string;
      capabilities: string[];
      projects: string[];
    };
  };
}

interface ProjectExample {
  id: string;
  title: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: string;
  technologies: string[];
  learningObjectives: string[];
  deliverables: string[];
  portfolioValue: number; // 1-10 scale
  industryRelevance: string[];
  demoUrl?: string;
  codeUrl?: string;
}

interface CareerOutcome {
  role: string;
  level: 'entry' | 'mid' | 'senior';
  salaryRange: {
    min: number;
    max: number;
    currency: string;
    location: string;
  };
  skills: string[];
  responsibilities: string[];
  companies: string[];
  growthPath: string[];
  timeToRole: string;
  demandLevel: 'high' | 'very-high' | 'extreme';
}

interface Certification {
  id: string;
  name: string;
  issuer: string;
  type: 'completion' | 'competency' | 'professional';
  description: string;
  requirements: string[];
  benefits: string[];
  industryRecognition: string[];
  blockchainVerified: boolean;
  linkedInIntegration: boolean;
  estimatedValue: string;
}

// Mock data
const learningOutcomes: LearningOutcome[] = [
  {
    id: 'smart-contract-development',
    title: 'Smart Contract Development Mastery',
    description: 'Build, deploy, and maintain production-ready smart contracts on Ethereum and compatible networks',
    category: 'technical',
    level: 'intermediate',
    skills: ['Solidity', 'Smart Contract Security', 'Gas Optimization', 'Testing', 'Deployment'],
    measurableGoals: [
      'Write 10+ smart contracts with zero security vulnerabilities',
      'Deploy contracts to mainnet with <$50 gas costs',
      'Achieve 95%+ test coverage on all contracts',
      'Complete security audit checklist for all projects'
    ],
    timeToAchieve: '8-12 weeks',
    prerequisites: ['Basic programming knowledge', 'Blockchain fundamentals']
  },
  {
    id: 'defi-protocol-design',
    title: 'DeFi Protocol Architecture',
    description: 'Design and implement complex DeFi protocols including DEXs, lending platforms, and yield farming',
    category: 'technical',
    level: 'advanced',
    skills: ['DeFi Protocols', 'Liquidity Management', 'Tokenomics', 'Oracle Integration', 'Cross-chain'],
    measurableGoals: [
      'Build a functional DEX with AMM capabilities',
      'Implement lending protocol with liquidation mechanisms',
      'Design tokenomics for sustainable yield farming',
      'Integrate price oracles with 99.9% uptime'
    ],
    timeToAchieve: '16-20 weeks',
    prerequisites: ['Smart Contract Development Mastery', 'Advanced Solidity']
  },
  {
    id: 'blockchain-security',
    title: 'Blockchain Security Specialist',
    description: 'Identify, prevent, and mitigate security vulnerabilities in smart contracts and DeFi protocols',
    category: 'technical',
    level: 'advanced',
    skills: ['Security Auditing', 'Vulnerability Assessment', 'Formal Verification', 'Incident Response'],
    measurableGoals: [
      'Complete 20+ security audits with detailed reports',
      'Identify and prevent 50+ common vulnerabilities',
      'Implement formal verification for critical contracts',
      'Respond to security incidents within 1 hour'
    ],
    timeToAchieve: '12-16 weeks',
    prerequisites: ['Smart Contract Development Mastery', 'Security Fundamentals']
  }
];

const skillProgressions: SkillProgression[] = [
  {
    skill: 'Solidity Programming',
    category: 'core',
    levels: {
      beginner: {
        description: 'Learn syntax, basic contracts, and fundamental concepts',
        capabilities: [
          'Write simple smart contracts',
          'Understand data types and variables',
          'Implement basic functions and modifiers',
          'Deploy contracts to testnets'
        ],
        projects: ['Hello World Contract', 'Simple Token', 'Basic Voting System']
      },
      intermediate: {
        description: 'Master advanced features, patterns, and best practices',
        capabilities: [
          'Implement complex contract interactions',
          'Use inheritance and interfaces effectively',
          'Optimize gas usage and performance',
          'Handle errors and edge cases'
        ],
        projects: ['ERC-20 Token with Features', 'NFT Marketplace', 'Multi-signature Wallet']
      },
      advanced: {
        description: 'Architect scalable systems and lead development teams',
        capabilities: [
          'Design upgradeable contract architectures',
          'Implement cross-chain functionality',
          'Lead security audits and reviews',
          'Mentor junior developers'
        ],
        projects: ['DeFi Protocol Suite', 'Cross-chain Bridge', 'DAO Governance System']
      }
    }
  }
];

const projectExamples: ProjectExample[] = [
  {
    id: 'defi-lending-platform',
    title: 'DeFi Lending Platform',
    description: 'A complete lending and borrowing platform with collateralization, liquidation, and yield farming',
    difficulty: 'advanced',
    duration: '6-8 weeks',
    technologies: ['Solidity', 'React', 'Web3.js', 'IPFS', 'Chainlink Oracles'],
    learningObjectives: [
      'Implement complex DeFi mechanics',
      'Integrate external price feeds',
      'Handle liquidation scenarios',
      'Design sustainable tokenomics'
    ],
    deliverables: [
      'Smart contract suite with full test coverage',
      'Frontend application with Web3 integration',
      'Technical documentation and API docs',
      'Security audit report'
    ],
    portfolioValue: 9,
    industryRelevance: ['DeFi', 'Banking', 'Fintech', 'Investment Management'],
    demoUrl: 'https://demo.defi-platform.com',
    codeUrl: 'https://github.com/user/defi-lending'
  },
  {
    id: 'nft-marketplace',
    title: 'NFT Marketplace with Royalties',
    description: 'Full-featured NFT marketplace with minting, trading, auctions, and creator royalties',
    difficulty: 'intermediate',
    duration: '4-5 weeks',
    technologies: ['Solidity', 'Next.js', 'IPFS', 'OpenSea API', 'MetaMask'],
    learningObjectives: [
      'Implement ERC-721 and ERC-1155 standards',
      'Build marketplace functionality',
      'Handle royalty distributions',
      'Integrate with IPFS for metadata'
    ],
    deliverables: [
      'NFT smart contracts with royalty support',
      'Marketplace frontend with wallet integration',
      'IPFS integration for metadata storage',
      'Admin dashboard for platform management'
    ],
    portfolioValue: 8,
    industryRelevance: ['Digital Art', 'Gaming', 'Entertainment', 'Collectibles'],
    demoUrl: 'https://demo.nft-marketplace.com',
    codeUrl: 'https://github.com/user/nft-marketplace'
  }
];

const careerOutcomes: CareerOutcome[] = [
  {
    role: 'Blockchain Developer',
    level: 'entry',
    salaryRange: { min: 80000, max: 120000, currency: 'USD', location: 'United States' },
    skills: ['Solidity', 'Web3.js', 'Smart Contracts', 'JavaScript', 'Git'],
    responsibilities: [
      'Develop and test smart contracts',
      'Integrate blockchain with frontend applications',
      'Participate in code reviews and testing',
      'Collaborate with senior developers on architecture'
    ],
    companies: ['ConsenSys', 'Chainlink', 'Polygon', 'Alchemy', 'OpenSea'],
    growthPath: ['Senior Blockchain Developer', 'Lead Developer', 'Blockchain Architect'],
    timeToRole: '3-6 months',
    demandLevel: 'very-high'
  },
  {
    role: 'DeFi Protocol Engineer',
    level: 'mid',
    salaryRange: { min: 120000, max: 180000, currency: 'USD', location: 'United States' },
    skills: ['Advanced Solidity', 'DeFi Protocols', 'Security', 'Tokenomics', 'Oracle Integration'],
    responsibilities: [
      'Design and implement DeFi protocols',
      'Conduct security reviews and audits',
      'Optimize gas usage and performance',
      'Research and implement new DeFi innovations'
    ],
    companies: ['Uniswap', 'Aave', 'Compound', 'MakerDAO', 'Curve Finance'],
    growthPath: ['Senior Protocol Engineer', 'Protocol Architect', 'Head of Engineering'],
    timeToRole: '8-12 months',
    demandLevel: 'extreme'
  },
  {
    role: 'Smart Contract Security Auditor',
    level: 'mid',
    salaryRange: { min: 100000, max: 160000, currency: 'USD', location: 'Remote' },
    skills: ['Security Auditing', 'Formal Verification', 'Vulnerability Assessment', 'Incident Response'],
    responsibilities: [
      'Conduct comprehensive security audits',
      'Identify and document vulnerabilities',
      'Provide remediation recommendations',
      'Stay updated on latest attack vectors'
    ],
    companies: ['OpenZeppelin', 'Trail of Bits', 'ConsenSys Diligence', 'Quantstamp'],
    growthPath: ['Senior Security Auditor', 'Security Team Lead', 'Chief Security Officer'],
    timeToRole: '6-10 months',
    demandLevel: 'extreme'
  }
];

const certifications: Certification[] = [
  {
    id: 'solidity-developer-certified',
    name: 'Certified Solidity Developer',
    issuer: 'Solidity Learning Platform',
    type: 'competency',
    description: 'Comprehensive certification covering smart contract development, security, and best practices',
    requirements: [
      'Complete all core Solidity courses',
      'Pass practical coding assessment',
      'Submit portfolio of 5+ projects',
      'Complete security audit simulation'
    ],
    benefits: [
      'Industry-recognized credential',
      'Direct access to hiring partners',
      'Priority consideration for blockchain roles',
      'Lifetime certification validity'
    ],
    industryRecognition: ['ConsenSys', 'Chainlink', 'Polygon', 'Alchemy', 'OpenSea'],
    blockchainVerified: true,
    linkedInIntegration: true,
    estimatedValue: '$5,000 salary increase'
  },
  {
    id: 'defi-specialist-certified',
    name: 'DeFi Protocol Specialist',
    issuer: 'Solidity Learning Platform',
    type: 'professional',
    description: 'Advanced certification for DeFi protocol development and architecture',
    requirements: [
      'Hold Certified Solidity Developer certification',
      'Complete advanced DeFi specialization',
      'Build and deploy live DeFi protocol',
      'Pass comprehensive DeFi assessment'
    ],
    benefits: [
      'Recognition as DeFi expert',
      'Access to exclusive DeFi job opportunities',
      'Invitation to industry conferences',
      'Mentorship opportunities'
    ],
    industryRecognition: ['Uniswap', 'Aave', 'Compound', 'MakerDAO', 'Curve Finance'],
    blockchainVerified: true,
    linkedInIntegration: true,
    estimatedValue: '$15,000 salary increase'
  }
];

export function LearningOutcomes({ className }: { className?: string }) {
  const [activeTab, setActiveTab] = useState<'outcomes' | 'skills' | 'projects' | 'careers' | 'certifications'>('outcomes');
  const [expandedOutcome, setExpandedOutcome] = useState<string | null>(null);
  const [selectedSkill, setSelectedSkill] = useState<string | null>(null);
  const [selectedProject, setSelectedProject] = useState<string | null>(null);

  const tabs = [
    { id: 'outcomes', label: 'Learning Outcomes', icon: Target },
    { id: 'skills', label: 'Skill Progression', icon: TrendingUp },
    { id: 'projects', label: 'Portfolio Projects', icon: Code },
    { id: 'careers', label: 'Career Outcomes', icon: Briefcase },
    { id: 'certifications', label: 'Certifications', icon: Award }
  ];

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'intermediate':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'advanced':
        return 'text-red-400 bg-red-500/20 border-red-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'technical':
        return 'text-blue-400 bg-blue-500/20 border-blue-500/30';
      case 'practical':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'professional':
        return 'text-purple-400 bg-purple-500/20 border-purple-500/30';
      case 'certification':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  return (
    <div className={cn('max-w-6xl mx-auto', className)}>
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          className="text-3xl md:text-4xl font-bold text-white mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          What You'll Learn & Achieve
        </motion.h2>
        <motion.p
          className="text-gray-300 text-lg max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          Comprehensive learning outcomes with measurable goals, real-world projects,
          and clear career progression paths in blockchain development
        </motion.p>
      </div>

      {/* Tab Navigation */}
      <motion.div
        className="flex flex-wrap justify-center gap-2 mb-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={cn(
                'flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border',
                activeTab === tab.id
                  ? 'bg-blue-600 text-white border-blue-500'
                  : 'bg-white/10 text-gray-300 hover:bg-white/20 border-white/20'
              )}
            >
              <Icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          );
        })}
      </motion.div>

      {/* Tab Content */}
      <AnimatePresence mode="wait">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
        >
          {/* Learning Outcomes Tab */}
          {activeTab === 'outcomes' && (
            <div className="space-y-6">
              {learningOutcomes.map((outcome, index) => (
                <motion.div
                  key={outcome.id}
                  className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-xl font-semibold text-white">{outcome.title}</h3>
                          <div className={cn('px-2 py-1 rounded text-xs font-medium border', getDifficultyColor(outcome.level))}>
                            {outcome.level}
                          </div>
                          <div className={cn('px-2 py-1 rounded text-xs font-medium border', getCategoryColor(outcome.category))}>
                            {outcome.category}
                          </div>
                        </div>
                        <p className="text-gray-300 leading-relaxed">{outcome.description}</p>
                      </div>
                      <div className="text-right ml-4">
                        <div className="text-sm text-gray-400 mb-1">Time to Achieve</div>
                        <div className="font-semibold text-blue-400">{outcome.timeToAchieve}</div>
                      </div>
                    </div>

                    {/* Skills */}
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-white mb-2">Key Skills</h4>
                      <div className="flex flex-wrap gap-2">
                        {outcome.skills.map((skill) => (
                          <span
                            key={skill}
                            className="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded-full border border-blue-500/30"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Measurable Goals */}
                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-white mb-3">Measurable Goals</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {outcome.measurableGoals.map((goal, goalIndex) => (
                          <div key={goalIndex} className="flex items-start space-x-2">
                            <Target className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-300 text-sm">{goal}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Prerequisites */}
                    {outcome.prerequisites && outcome.prerequisites.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-white mb-2">Prerequisites</h4>
                        <div className="flex flex-wrap gap-2">
                          {outcome.prerequisites.map((prereq) => (
                            <span
                              key={prereq}
                              className="px-2 py-1 bg-gray-500/20 text-gray-300 text-xs rounded border border-gray-500/30"
                            >
                              {prereq}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* Skills Progression Tab */}
          {activeTab === 'skills' && (
            <div className="space-y-8">
              {skillProgressions.map((skillProg, index) => (
                <motion.div
                  key={skillProg.skill}
                  className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-xl font-semibold text-white">{skillProg.skill}</h3>
                    <div className={cn(
                      'px-3 py-1 rounded text-sm font-medium border',
                      skillProg.category === 'core' ? 'text-blue-400 bg-blue-500/20 border-blue-500/30' :
                      skillProg.category === 'advanced' ? 'text-purple-400 bg-purple-500/20 border-purple-500/30' :
                      'text-yellow-400 bg-yellow-500/20 border-yellow-500/30'
                    )}>
                      {skillProg.category}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {Object.entries(skillProg.levels).map(([level, data]) => (
                      <div key={level} className="bg-white/5 rounded-lg p-4 border border-white/10">
                        <div className="flex items-center space-x-2 mb-3">
                          <h4 className={cn(
                            'font-semibold capitalize',
                            level === 'beginner' ? 'text-green-400' :
                            level === 'intermediate' ? 'text-yellow-400' :
                            'text-red-400'
                          )}>
                            {level}
                          </h4>
                        </div>

                        <p className="text-gray-300 text-sm mb-4">{data.description}</p>

                        <div className="mb-4">
                          <h5 className="text-xs font-medium text-white mb-2">Capabilities</h5>
                          <ul className="space-y-1">
                            {data.capabilities.map((capability, capIndex) => (
                              <li key={capIndex} className="flex items-start space-x-2 text-xs">
                                <CheckCircle className="w-3 h-3 text-green-400 mt-0.5 flex-shrink-0" />
                                <span className="text-gray-300">{capability}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        <div>
                          <h5 className="text-xs font-medium text-white mb-2">Example Projects</h5>
                          <div className="space-y-1">
                            {data.projects.map((project, projIndex) => (
                              <div key={projIndex} className="text-xs text-blue-300">
                                {project}
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* Portfolio Projects Tab */}
          {activeTab === 'projects' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {projectExamples.map((project, index) => (
                <motion.div
                  key={project.id}
                  className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-white mb-2">{project.title}</h3>
                        <div className="flex items-center space-x-2 mb-2">
                          <div className={cn('px-2 py-1 rounded text-xs font-medium border', getDifficultyColor(project.difficulty))}>
                            {project.difficulty}
                          </div>
                          <div className="text-xs text-gray-400">{project.duration}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-1 mb-1">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={cn(
                                'w-3 h-3',
                                i < project.portfolioValue / 2
                                  ? 'text-yellow-400 fill-current'
                                  : 'text-gray-600'
                              )}
                            />
                          ))}
                        </div>
                        <div className="text-xs text-gray-400">Portfolio Value</div>
                      </div>
                    </div>

                    <p className="text-gray-300 text-sm mb-4 leading-relaxed">{project.description}</p>

                    {/* Technologies */}
                    <div className="mb-4">
                      <h4 className="text-xs font-medium text-white mb-2">Technologies</h4>
                      <div className="flex flex-wrap gap-1">
                        {project.technologies.map((tech) => (
                          <span
                            key={tech}
                            className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded border border-purple-500/30"
                          >
                            {tech}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Learning Objectives */}
                    <div className="mb-4">
                      <h4 className="text-xs font-medium text-white mb-2">Learning Objectives</h4>
                      <ul className="space-y-1">
                        {project.learningObjectives.slice(0, 3).map((objective, objIndex) => (
                          <li key={objIndex} className="flex items-start space-x-2 text-xs">
                            <Target className="w-3 h-3 text-blue-400 mt-0.5 flex-shrink-0" />
                            <span className="text-gray-300">{objective}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Industry Relevance */}
                    <div className="mb-4">
                      <h4 className="text-xs font-medium text-white mb-2">Industry Relevance</h4>
                      <div className="flex flex-wrap gap-1">
                        {project.industryRelevance.map((industry) => (
                          <span
                            key={industry}
                            className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded border border-green-500/30"
                          >
                            {industry}
                          </span>
                        ))}
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-2">
                      {project.demoUrl && (
                        <EnhancedButton
                          size="sm"
                          className="bg-blue-600 hover:bg-blue-700 text-white flex-1"
                        >
                          <Play className="w-3 h-3 mr-1" />
                          Live Demo
                        </EnhancedButton>
                      )}
                      {project.codeUrl && (
                        <EnhancedButton
                          size="sm"
                          variant="ghost"
                          className="text-gray-400 hover:text-white border-gray-600 flex-1"
                        >
                          <Code className="w-3 h-3 mr-1" />
                          View Code
                        </EnhancedButton>
                      )}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* Career Outcomes Tab */}
          {activeTab === 'careers' && (
            <div className="space-y-6">
              {careerOutcomes.map((career, index) => (
                <motion.div
                  key={career.role}
                  className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Role Info */}
                    <div className="lg:col-span-2">
                      <div className="flex items-center space-x-3 mb-4">
                        <h3 className="text-xl font-semibold text-white">{career.role}</h3>
                        <div className={cn(
                          'px-2 py-1 rounded text-xs font-medium border',
                          career.level === 'entry' ? 'text-green-400 bg-green-500/20 border-green-500/30' :
                          career.level === 'mid' ? 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30' :
                          'text-red-400 bg-red-500/20 border-red-500/30'
                        )}>
                          {career.level} level
                        </div>
                        <div className={cn(
                          'px-2 py-1 rounded text-xs font-medium border',
                          career.demandLevel === 'high' ? 'text-blue-400 bg-blue-500/20 border-blue-500/30' :
                          career.demandLevel === 'very-high' ? 'text-purple-400 bg-purple-500/20 border-purple-500/30' :
                          'text-red-400 bg-red-500/20 border-red-500/30'
                        )}>
                          {career.demandLevel.replace('-', ' ')} demand
                        </div>
                      </div>

                      {/* Required Skills */}
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-white mb-2">Required Skills</h4>
                        <div className="flex flex-wrap gap-2">
                          {career.skills.map((skill) => (
                            <span
                              key={skill}
                              className="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded border border-blue-500/30"
                            >
                              {skill}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Responsibilities */}
                      <div className="mb-4">
                        <h4 className="text-sm font-medium text-white mb-2">Key Responsibilities</h4>
                        <ul className="space-y-1">
                          {career.responsibilities.map((responsibility, respIndex) => (
                            <li key={respIndex} className="flex items-start space-x-2 text-sm">
                              <CheckCircle className="w-3 h-3 text-green-400 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-300">{responsibility}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Companies */}
                      <div>
                        <h4 className="text-sm font-medium text-white mb-2">Hiring Companies</h4>
                        <div className="flex flex-wrap gap-2">
                          {career.companies.map((company) => (
                            <span
                              key={company}
                              className="px-2 py-1 bg-purple-500/20 text-purple-300 text-xs rounded border border-purple-500/30"
                            >
                              {company}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Salary & Growth */}
                    <div className="space-y-4">
                      <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                        <div className="flex items-center space-x-2 mb-2">
                          <DollarSign className="w-4 h-4 text-green-400" />
                          <h4 className="font-medium text-white">Salary Range</h4>
                        </div>
                        <div className="text-2xl font-bold text-green-400 mb-1">
                          ${career.salaryRange.min.toLocaleString()} - ${career.salaryRange.max.toLocaleString()}
                        </div>
                        <div className="text-xs text-gray-400">{career.salaryRange.location}</div>
                      </div>

                      <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                        <div className="flex items-center space-x-2 mb-2">
                          <TrendingUp className="w-4 h-4 text-blue-400" />
                          <h4 className="font-medium text-white">Time to Role</h4>
                        </div>
                        <div className="text-lg font-semibold text-blue-400">{career.timeToRole}</div>
                      </div>

                      <div className="bg-white/5 rounded-lg p-4 border border-white/10">
                        <h4 className="font-medium text-white mb-2">Growth Path</h4>
                        <div className="space-y-1">
                          {career.growthPath.map((step, stepIndex) => (
                            <div key={stepIndex} className="flex items-center space-x-2 text-sm">
                              <ArrowRight className="w-3 h-3 text-gray-400" />
                              <span className="text-gray-300">{step}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {/* Certifications Tab */}
          {activeTab === 'certifications' && (
            <div className="space-y-6">
              {certifications.map((cert, index) => (
                <motion.div
                  key={cert.id}
                  className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-xl font-semibold text-white">{cert.name}</h3>
                          <div className={cn(
                            'px-2 py-1 rounded text-xs font-medium border',
                            cert.type === 'completion' ? 'text-green-400 bg-green-500/20 border-green-500/30' :
                            cert.type === 'competency' ? 'text-blue-400 bg-blue-500/20 border-blue-500/30' :
                            'text-purple-400 bg-purple-500/20 border-purple-500/30'
                          )}>
                            {cert.type}
                          </div>
                        </div>
                        <div className="text-gray-400 text-sm mb-2">Issued by {cert.issuer}</div>
                        <p className="text-gray-300 leading-relaxed">{cert.description}</p>
                      </div>
                      <div className="text-right ml-4">
                        <div className="text-lg font-bold text-green-400">{cert.estimatedValue}</div>
                        <div className="text-xs text-gray-400">Estimated Value</div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                      {/* Requirements */}
                      <div>
                        <h4 className="text-sm font-medium text-white mb-2">Requirements</h4>
                        <ul className="space-y-1">
                          {cert.requirements.map((req, reqIndex) => (
                            <li key={reqIndex} className="flex items-start space-x-2 text-xs">
                              <CheckCircle className="w-3 h-3 text-blue-400 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-300">{req}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Benefits */}
                      <div>
                        <h4 className="text-sm font-medium text-white mb-2">Benefits</h4>
                        <ul className="space-y-1">
                          {cert.benefits.map((benefit, benefitIndex) => (
                            <li key={benefitIndex} className="flex items-start space-x-2 text-xs">
                              <Star className="w-3 h-3 text-yellow-400 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-300">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Industry Recognition */}
                      <div>
                        <h4 className="text-sm font-medium text-white mb-2">Recognized By</h4>
                        <div className="space-y-1">
                          {cert.industryRecognition.map((company) => (
                            <div key={company} className="text-xs text-purple-300">
                              {company}
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Features */}
                      <div>
                        <h4 className="text-sm font-medium text-white mb-2">Features</h4>
                        <div className="space-y-2">
                          {cert.blockchainVerified && (
                            <div className="flex items-center space-x-2 text-xs">
                              <Shield className="w-3 h-3 text-green-400" />
                              <span className="text-gray-300">Blockchain Verified</span>
                            </div>
                          )}
                          {cert.linkedInIntegration && (
                            <div className="flex items-center space-x-2 text-xs">
                              <ExternalLink className="w-3 h-3 text-blue-400" />
                              <span className="text-gray-300">LinkedIn Integration</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-between items-center pt-4 border-t border-white/10">
                      <div className="text-sm text-gray-400">
                        Lifetime validity • Industry recognized • Portfolio enhancement
                      </div>
                      <EnhancedButton
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                        size="sm"
                      >
                        Start Certification Path
                      </EnhancedButton>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </AnimatePresence>

      {/* CTA Section */}
      <motion.div
        className="mt-12 text-center bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg p-8 border border-blue-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.8 }}
      >
        <h3 className="text-2xl font-bold text-white mb-4">Ready to Achieve These Outcomes?</h3>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          Join thousands of developers who have successfully achieved these learning outcomes
          and advanced their careers in blockchain development.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <EnhancedButton
            className="bg-blue-600 hover:bg-blue-700 text-white"
            touchTarget
          >
            <Play className="w-4 h-4 mr-2" />
            Start Learning Journey
          </EnhancedButton>
          <EnhancedButton
            variant="ghost"
            className="text-blue-400 hover:text-blue-300 border-blue-400/30"
            touchTarget
          >
            <Download className="w-4 h-4 mr-2" />
            Download Learning Guide
          </EnhancedButton>
        </div>
      </motion.div>
    </div>
  );
}