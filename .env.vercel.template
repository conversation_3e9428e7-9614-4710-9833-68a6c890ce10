# Vercel Environment Variables Template
# Copy these to your Vercel dashboard: Settings → Environment Variables

# Authentication (REQUIRED)
NEXTAUTH_URL=https://your-app.vercel.app
NEXTAUTH_SECRET=your-secure-secret-key-min-32-characters-long

# Database (REQUIRED)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# OAuth Providers (OPTIONAL)
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# AI Integration (OPTIONAL)
GEMINI_API_KEY=your-gemini-api-key

# Monitoring (OPTIONAL)
WAKATIME_API_KEY=your-wakatime-api-key
