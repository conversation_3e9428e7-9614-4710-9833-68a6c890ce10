#!/usr/bin/env node

/**
 * Simplified Accessibility Audit Runner
 * Uses <PERSON><PERSON> and axe-core to perform comprehensive accessibility testing
 */

const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

// Test configuration
const AUDIT_CONFIG = {
  baseUrl: 'http://localhost:3000',
  pages: [
    { path: '/', name: 'Homepage' },
    { path: '/dashboard', name: 'Dashboard' },
    { path: '/learn', name: 'Learn Page' },
    { path: '/code', name: 'Code Editor' },
    { path: '/auth/login', name: 'Login Page' },
    { path: '/auth/register', name: 'Register Page' },
  ],
  timeout: 30000,
  viewport: { width: 1280, height: 720 },
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

// Check if server is running
async function checkServer() {
  try {
    const browser = await chromium.launch({ headless: true });
    const page = await browser.newPage();
    await page.goto(AUDIT_CONFIG.baseUrl, { timeout: 5000 });
    await browser.close();
    return true;
  } catch (error) {
    return false;
  }
}

// Run axe accessibility audit
async function runAxeAudit(page, url) {
  try {
    // Inject axe-core
    await page.addScriptTag({
      url: 'https://unpkg.com/axe-core@4.10.3/axe.min.js'
    });

    // Wait for axe to load
    await page.waitForFunction(() => typeof window.axe !== 'undefined');

    // Run axe audit
    const results = await page.evaluate(async () => {
      return new Promise((resolve, reject) => {
        window.axe.run(document, {
          tags: ['wcag2a', 'wcag2aa', 'wcag21aa', 'best-practice'],
          rules: {
            'color-contrast': { enabled: true },
            'keyboard-navigation': { enabled: true },
            'aria-labels': { enabled: true },
            'focus-indicators': { enabled: true },
            'heading-order': { enabled: true },
            'image-alt': { enabled: true },
            'label': { enabled: true },
            'link-name': { enabled: true },
            'page-has-heading-one': { enabled: true },
            'html-has-lang': { enabled: true }
          }
        }, (err, results) => {
          if (err) reject(err);
          else resolve(results);
        });
      });
    });

    return {
      url,
      violations: results.violations,
      passes: results.passes,
      incomplete: results.incomplete,
      summary: {
        violationCount: results.violations.length,
        passCount: results.passes.length,
        incompleteCount: results.incomplete.length
      }
    };
  } catch (error) {
    logError(`Axe audit failed for ${url}: ${error.message}`);
    return null;
  }
}

// Test keyboard navigation
async function testKeyboardNavigation(page) {
  try {
    const results = {
      focusableElements: 0,
      tabOrder: [],
      issues: []
    };

    // Find focusable elements
    const focusableCount = await page.evaluate(() => {
      const selector = 'a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])';
      return document.querySelectorAll(selector).length;
    });

    results.focusableElements = focusableCount;

    // Test tab navigation (limited to prevent infinite loops)
    const maxTabs = Math.min(focusableCount, 10);
    for (let i = 0; i < maxTabs; i++) {
      await page.keyboard.press('Tab');
      
      const focusedElement = await page.evaluate(() => {
        const focused = document.activeElement;
        return {
          tagName: focused.tagName,
          id: focused.id || null,
          text: focused.textContent?.trim().substring(0, 30) || null
        };
      });
      
      results.tabOrder.push(focusedElement);
    }

    if (results.focusableElements === 0) {
      results.issues.push('No focusable elements found');
    }

    return results;
  } catch (error) {
    logError(`Keyboard navigation test failed: ${error.message}`);
    return null;
  }
}

// Test color contrast
async function testColorContrast(page) {
  try {
    const contrastIssues = await page.evaluate(() => {
      const issues = [];
      const elements = document.querySelectorAll('*');
      
      for (let i = 0; i < Math.min(elements.length, 100); i++) {
        const el = elements[i];
        const style = window.getComputedStyle(el);
        const color = style.color;
        const backgroundColor = style.backgroundColor;
        
        if (color && backgroundColor && el.textContent?.trim()) {
          // Simple contrast check (would need proper contrast calculation in production)
          if (color === backgroundColor) {
            issues.push({
              element: el.tagName,
              text: el.textContent.trim().substring(0, 30),
              issue: 'Text and background colors are identical'
            });
          }
        }
      }
      
      return issues.slice(0, 10); // Limit results
    });

    return contrastIssues;
  } catch (error) {
    logError(`Color contrast test failed: ${error.message}`);
    return [];
  }
}

// Generate accessibility report
function generateReport(auditResults) {
  logHeader('Accessibility Audit Results');
  
  let totalViolations = 0;
  let totalPasses = 0;
  let criticalIssues = [];
  let allIssues = [];
  
  auditResults.forEach(result => {
    if (!result) return;
    
    const { url, name, axeResults, keyboardResults, contrastResults } = result;
    
    log(`\n🔍 ${name} (${url})`, 'bright');
    log('-'.repeat(50), 'cyan');
    
    if (axeResults) {
      totalViolations += axeResults.summary.violationCount;
      totalPasses += axeResults.summary.passCount;
      
      log(`Violations: ${axeResults.summary.violationCount}`, 
          axeResults.summary.violationCount === 0 ? 'green' : 'red');
      log(`Passes: ${axeResults.summary.passCount}`, 'green');
      
      // Log critical violations
      axeResults.violations.forEach(violation => {
        if (violation.impact === 'critical' || violation.impact === 'serious') {
          criticalIssues.push({
            page: name,
            type: 'axe-violation',
            impact: violation.impact,
            rule: violation.id,
            description: violation.description,
            help: violation.help
          });
          
          logError(`  ${violation.impact.toUpperCase()}: ${violation.description}`);
        }
      });
    }
    
    if (keyboardResults) {
      log(`Focusable Elements: ${keyboardResults.focusableElements}`, 'blue');
      
      keyboardResults.issues.forEach(issue => {
        allIssues.push({
          page: name,
          type: 'keyboard',
          issue
        });
        logWarning(`  Keyboard: ${issue}`);
      });
    }
    
    if (contrastResults && contrastResults.length > 0) {
      log(`Color Contrast Issues: ${contrastResults.length}`, 'yellow');
      
      contrastResults.forEach(issue => {
        allIssues.push({
          page: name,
          type: 'color-contrast',
          issue: issue.issue
        });
      });
    }
  });
  
  // Overall summary
  log('\n' + '='.repeat(60), 'cyan');
  log('Accessibility Summary', 'bright');
  log('='.repeat(60), 'cyan');
  
  log(`Total Violations: ${totalViolations}`, totalViolations === 0 ? 'green' : 'red');
  log(`Total Passes: ${totalPasses}`, 'green');
  log(`Critical Issues: ${criticalIssues.length}`, criticalIssues.length === 0 ? 'green' : 'red');
  log(`All Issues: ${allIssues.length}`, allIssues.length === 0 ? 'green' : 'yellow');
  
  // Critical issues summary
  if (criticalIssues.length > 0) {
    log('\n🚨 Critical Accessibility Issues:', 'red');
    criticalIssues.forEach((issue, index) => {
      log(`\n${index + 1}. ${issue.page}: ${issue.description}`, 'red');
      if (issue.help) {
        log(`   💡 ${issue.help}`, 'cyan');
      }
    });
  }
  
  const success = totalViolations === 0 && criticalIssues.length === 0;
  
  log('\n' + '='.repeat(60), 'cyan');
  if (success) {
    logSuccess('🎉 All accessibility tests passed!');
  } else {
    logError('❌ Accessibility issues found. Please address critical issues.');
  }
  log('='.repeat(60), 'cyan');
  
  return {
    success,
    totalViolations,
    totalPasses,
    criticalIssues,
    allIssues,
    results: auditResults
  };
}

// Main execution
async function main() {
  logHeader('Solidity Learning Platform - Accessibility Audit');
  
  // Check if server is running
  logInfo('Checking if development server is running...');
  const serverRunning = await checkServer();
  
  if (!serverRunning) {
    logError('Development server is not running at http://localhost:3000');
    logInfo('Please start the server with: npm run dev');
    process.exit(1);
  }
  
  logSuccess('Development server is running');
  
  const browser = await chromium.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-dev-shm-usage']
  });
  
  try {
    const auditResults = [];
    
    for (const { path: pagePath, name } of AUDIT_CONFIG.pages) {
      const url = `${AUDIT_CONFIG.baseUrl}${pagePath}`;
      logInfo(`Testing ${name} (${url})`);
      
      const page = await browser.newPage();
      await page.setViewportSize(AUDIT_CONFIG.viewport);
      
      try {
        await page.goto(url, { 
          waitUntil: 'networkidle',
          timeout: AUDIT_CONFIG.timeout 
        });
        
        // Run all accessibility tests
        const [axeResults, keyboardResults, contrastResults] = await Promise.all([
          runAxeAudit(page, url),
          testKeyboardNavigation(page),
          testColorContrast(page)
        ]);
        
        auditResults.push({
          url,
          name,
          axeResults,
          keyboardResults,
          contrastResults
        });
        
        logSuccess(`✅ ${name} accessibility tests completed`);
      } catch (error) {
        logError(`❌ ${name} accessibility tests failed: ${error.message}`);
      } finally {
        await page.close();
      }
    }
    
    // Generate report
    const summary = generateReport(auditResults);
    
    // Save detailed report
    const reportDir = path.join(process.cwd(), 'reports');
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportPath = path.join(reportDir, `accessibility-audit-${timestamp}.json`);
    
    fs.writeFileSync(reportPath, JSON.stringify({
      timestamp: new Date().toISOString(),
      summary,
      config: AUDIT_CONFIG
    }, null, 2));
    
    logInfo(`Detailed report saved to: ${reportPath}`);
    
    // Exit with appropriate code
    process.exit(summary.success ? 0 : 1);
    
  } catch (error) {
    logError(`Accessibility audit failed: ${error.message}`);
    process.exit(1);
  } finally {
    await browser.close();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch((error) => {
    logError(`Accessibility audit runner failed: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runAxeAudit, testKeyboardNavigation, testColorContrast };
