# =============================================================================
# DEVELOPMENT ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains development-specific environment variables
# These values are optimized for local development
# =============================================================================

# Application Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
DEBUG=true
VERBOSE_LOGGING=true
ENABLE_QUERY_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=false

# Database Configuration
DATABASE_URL="postgresql://postgres:password@localhost:5432/solidity_learning_dev?schema=public"
DATABASE_POOL_MIN=1
DATABASE_POOL_MAX=5
DATABASE_POOL_TIMEOUT=10000

# Redis Configuration
REDIS_URL="redis://localhost:6379"
REDIS_DB=0

# Authentication
NEXTAUTH_URL=http://localhost:3000
SESSION_COOKIE_SECURE=false
SESSION_COOKIE_SAME_SITE=lax

# Socket.io Configuration
SOCKET_IO_PORT=3001
SOCKET_IO_CORS_ORIGINS="http://localhost:3000"
SOCKET_IO_MAX_CONNECTIONS=50

# Rate Limiting (Relaxed for Development)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
API_RATE_LIMIT_PER_MINUTE=300
AUTH_RATE_LIMIT_PER_MINUTE=20
COLLABORATION_RATE_LIMIT_PER_MINUTE=100

# AI Services (Development Limits)
AI_REQUESTS_PER_MINUTE=30
AI_REQUESTS_PER_HOUR=500
AI_REQUESTS_PER_DAY=2000

# Email Configuration (Development - Use Mailtrap or similar)
SMTP_HOST="smtp.mailtrap.io"
SMTP_PORT=2525
SMTP_SECURE=false
SMTP_USER="your-mailtrap-user"
SMTP_PASSWORD="your-mailtrap-password"

# File Storage (Local Development)
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,text/plain,application/json"

# Blockchain (Testnet Only)
ETHEREUM_RPC_URL="https://sepolia.infura.io/v3/your-project-id"
ETHEREUM_TESTNET_RPC_URL="https://sepolia.infura.io/v3/your-project-id"

# Feature Flags (All Enabled for Development)
FEATURE_AI_TUTORING=true
FEATURE_COLLABORATION=true
FEATURE_CODE_COMPILATION=true
FEATURE_BLOCKCHAIN_INTEGRATION=true
FEATURE_GAMIFICATION=true
FEATURE_SOCIAL_FEATURES=true
FEATURE_ADVANCED_ANALYTICS=true

# Beta Features (Enabled for Testing)
BETA_VOICE_CHAT=true
BETA_VIDEO_COLLABORATION=true
BETA_AI_CODE_REVIEW=true

# Monitoring (Disabled for Development)
# SENTRY_DSN=""
# NEXT_PUBLIC_GA_MEASUREMENT_ID=""

# Security (Relaxed for Development)
CONTENT_SECURITY_POLICY_REPORT_URI=""
HSTS_MAX_AGE=0

# Caching
CACHE_TTL=300
LOG_LEVEL=debug
