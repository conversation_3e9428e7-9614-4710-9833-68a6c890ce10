'use client';

import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { Users, BookOpen, Code, Trophy, TrendingUp, Star } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Represents a single user metric with display properties
 */
interface UserMetric {
  /** Unique identifier for the metric */
  id: string;
  /** Display label for the metric */
  label: string;
  /** Numeric value of the metric */
  value: number;
  /** Suffix to append to the value (e.g., '+', '%') */
  suffix: string;
  /** Icon component to display with the metric */
  icon: React.ComponentType<{ className?: string }>;
  /** Tailwind color class for the metric */
  color: string;
  /** Detailed description of what the metric represents */
  description: string;
}

/**
 * Props for the DynamicUserMetrics component
 */
interface DynamicUserMetricsProps {
  /** Additional CSS classes to apply to the container */
  className?: string;
  /** Whether to animate the counter values on scroll into view */
  animated?: boolean;
  /** Whether to show detailed descriptions on hover */
  showDescription?: boolean;
}

/**
 * Dynamic User Metrics Component
 *
 * Displays real-time platform statistics with animated counters that trigger
 * when the component comes into view. Supports accessibility features including
 * reduced motion preferences and screen reader announcements.
 *
 * @component
 * @example
 * ```tsx
 * <DynamicUserMetrics
 *   animated={true}
 *   showDescription={false}
 *   className="my-8"
 * />
 * ```
 *
 * @param props - Component props
 * @param props.className - Additional CSS classes
 * @param props.animated - Enable counter animations (default: true)
 * @param props.showDescription - Show metric descriptions on hover (default: false)
 * @returns JSX element containing animated metric cards
 */
export function DynamicUserMetrics({ 
  className = '', 
  animated = true,
  showDescription = false 
}: DynamicUserMetricsProps) {
  const [metrics, setMetrics] = useState<UserMetric[]>([
    {
      id: 'learners',
      label: 'Active Learners',
      value: 0,
      suffix: '+',
      icon: Users,
      color: 'text-blue-400',
      description: 'Developers currently learning Solidity'
    },
    {
      id: 'courses',
      label: 'Courses',
      value: 0,
      suffix: '+',
      icon: BookOpen,
      color: 'text-green-400',
      description: 'Comprehensive learning modules'
    },
    {
      id: 'projects',
      label: 'Projects Built',
      value: 0,
      suffix: '+',
      icon: Code,
      color: 'text-purple-400',
      description: 'Smart contracts deployed'
    },
    {
      id: 'success',
      label: 'Success Rate',
      value: 0,
      suffix: '%',
      icon: Trophy,
      color: 'text-yellow-400',
      description: 'Course completion rate'
    }
  ]);

  const [isVisible, setIsVisible] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const countersRef = useRef<(HTMLSpanElement | null)[]>([]);

  // Target values for animation
  const targetValues = [10247, 52, 25834, 94];

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          animateCounters();
        }
      },
      { threshold: 0.3 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible]);

  const animateCounters = () => {
    if (!animated) {
      setMetrics(prev => prev.map((metric, index) => ({
        ...metric,
        value: targetValues[index]
      })));
      return;
    }

    countersRef.current.forEach((counter, index) => {
      if (counter) {
        gsap.to(metrics[index], {
          value: targetValues[index],
          duration: 2.5,
          delay: index * 0.2,
          ease: "power2.out",
          onUpdate: function() {
            const currentValue = Math.round(this.targets()[0].value);
            counter.textContent = currentValue.toLocaleString();
          }
        });
      }
    });
  };

  // Simulate real-time updates
  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setMetrics(prev => prev.map((metric, index) => {
        // Small random increments to simulate real-time activity
        const increment = Math.floor(Math.random() * 3);
        return {
          ...metric,
          value: metric.value + increment
        };
      }));
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [isVisible]);

  return (
    <motion.div
      ref={containerRef}
      className={cn(
        'grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto',
        className
      )}
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1, duration: 0.8 }}
      role="region"
      aria-label="Platform statistics"
    >
      {metrics.map((metric, index) => {
        const IconComponent = metric.icon;
        
        return (
          <motion.div
            key={metric.id}
            className="text-center group"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 1.2 + index * 0.1, duration: 0.6 }}
            whileHover={{ scale: 1.05 }}
          >
            <div className="glass p-4 rounded-xl border border-white/10 hover:border-white/20 transition-all duration-300 backdrop-blur-md bg-white/5">
              <div className="flex flex-col items-center space-y-2">
                <div className={cn(
                  'w-8 h-8 rounded-full flex items-center justify-center',
                  'bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-sm',
                  'group-hover:scale-110 transition-transform duration-300'
                )}>
                  <IconComponent className={cn('w-4 h-4', metric.color)} />
                </div>
                
                <div className="space-y-1">
                  <div className="flex items-center justify-center space-x-1">
                    <span
                      ref={el => countersRef.current[index] = el}
                      className={cn(
                        'text-2xl md:text-3xl font-bold font-mono tabular-nums',
                        metric.color,
                        'drop-shadow-lg'
                      )}
                      aria-live="polite"
                    >
                      {metric.value.toLocaleString()}
                    </span>
                    <span className={cn('text-lg font-bold', metric.color)}>
                      {metric.suffix}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-300 font-medium">
                    {metric.label}
                  </div>
                  
                  {showDescription && (
                    <div className="text-xs text-gray-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      {metric.description}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        );
      })}
    </motion.div>
  );
}

/**
 * Compact User Metrics Component
 *
 * A condensed version of the user metrics display suitable for smaller spaces
 * like headers or sidebars. Shows key metrics in a horizontal layout.
 *
 * @component
 * @example
 * ```tsx
 * <CompactUserMetrics className="mb-4" />
 * ```
 *
 * @param props - Component props
 * @param props.className - Additional CSS classes to apply
 * @returns JSX element with compact metrics display
 */
export function CompactUserMetrics({ className = '' }: { className?: string }) {
  return (
    <motion.div
      className={cn(
        'flex items-center justify-center space-x-8 text-center',
        className
      )}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ delay: 1.5, duration: 0.6 }}
    >
      <div className="flex items-center space-x-2">
        <Users className="w-4 h-4 text-blue-400" />
        <span className="text-lg font-bold text-blue-400">10,000+</span>
        <span className="text-sm text-gray-300">learners</span>
      </div>
      
      <div className="flex items-center space-x-2">
        <Star className="w-4 h-4 text-yellow-400" />
        <span className="text-lg font-bold text-yellow-400">94%</span>
        <span className="text-sm text-gray-300">success</span>
      </div>
      
      <div className="flex items-center space-x-2">
        <TrendingUp className="w-4 h-4 text-green-400" />
        <span className="text-lg font-bold text-green-400">25k+</span>
        <span className="text-sm text-gray-300">projects</span>
      </div>
    </motion.div>
  );
}
