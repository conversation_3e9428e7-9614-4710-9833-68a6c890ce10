'use client';

import React, { useState, useEffect, useCallback, createContext, useContext } from 'react';

interface ExitIntentAnalytics {
  totalTriggers: number;
  conversions: number;
  conversionRate: number;
  offerPerformance: Record<string, { views: number; conversions: number; rate: number }>;
  userSegmentPerformance: Record<string, { triggers: number; conversions: number; rate: number }>;
  timeToConversion: number[];
  dismissalReasons: Record<string, number>;
  abTestResults: Record<string, { variant: string; conversions: number; views: number; rate: number }>;
}

interface ABTestVariants {
  currentVariant: string;
  variants: Record<string, { name: string; weight: number; config?: any }>;
}

interface ExitIntentSettings {
  enabled: boolean;
  maxTriggersPerSession: number;
  minTimeOnSite: number;
  cooldownPeriod: number;
  respectDoNotTrack: boolean;
  accessibilityMode: boolean;
  debugMode: boolean;
}

// Hook for exit intent analytics and optimization
export function useExitIntentAnalytics() {
  const [analytics, setAnalytics] = useState<ExitIntentAnalytics>({
    totalTriggers: 0,
    conversions: 0,
    conversionRate: 0,
    offerPerformance: {},
    userSegmentPerformance: {},
    timeToConversion: [],
    dismissalReasons: {},
    abTestResults: {}
  });

  const [abTestVariants, setAbTestVariants] = useState<ABTestVariants>({
    currentVariant: 'A',
    variants: {
      A: { name: 'Standard Offer', weight: 50 },
      B: { name: 'Urgency Focus', weight: 50 }
    }
  });

  useEffect(() => {
    // Load analytics from localStorage
    const savedAnalytics = localStorage.getItem('exit_intent_analytics');
    if (savedAnalytics) {
      try {
        setAnalytics(JSON.parse(savedAnalytics));
      } catch (error) {
        console.error('Failed to load exit intent analytics:', error);
      }
    }

    // Load A/B test configuration
    const savedABTest = localStorage.getItem('exit_intent_ab_test');
    if (savedABTest) {
      try {
        setAbTestVariants(JSON.parse(savedABTest));
      } catch (error) {
        console.error('Failed to load A/B test configuration:', error);
      }
    }
  }, []);

  const trackTrigger = useCallback((offerId: string, userSegment: string, variant?: string) => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        totalTriggers: prev.totalTriggers + 1,
        offerPerformance: {
          ...prev.offerPerformance,
          [offerId]: {
            views: (prev.offerPerformance[offerId]?.views || 0) + 1,
            conversions: prev.offerPerformance[offerId]?.conversions || 0,
            rate: 0 // Will be calculated
          }
        },
        userSegmentPerformance: {
          ...prev.userSegmentPerformance,
          [userSegment]: {
            triggers: (prev.userSegmentPerformance[userSegment]?.triggers || 0) + 1,
            conversions: prev.userSegmentPerformance[userSegment]?.conversions || 0,
            rate: 0 // Will be calculated
          }
        },
        abTestResults: variant ? {
          ...prev.abTestResults,
          [variant]: {
            variant,
            views: (prev.abTestResults[variant]?.views || 0) + 1,
            conversions: prev.abTestResults[variant]?.conversions || 0,
            rate: 0 // Will be calculated
          }
        } : prev.abTestResults
      };

      // Recalculate rates
      Object.keys(newAnalytics.offerPerformance).forEach(id => {
        const offer = newAnalytics.offerPerformance[id];
        offer.rate = offer.views > 0 ? (offer.conversions / offer.views) * 100 : 0;
      });

      Object.keys(newAnalytics.userSegmentPerformance).forEach(segment => {
        const segmentData = newAnalytics.userSegmentPerformance[segment];
        segmentData.rate = segmentData.triggers > 0 ? (segmentData.conversions / segmentData.triggers) * 100 : 0;
      });

      Object.keys(newAnalytics.abTestResults).forEach(testVariant => {
        const testData = newAnalytics.abTestResults[testVariant];
        testData.rate = testData.views > 0 ? (testData.conversions / testData.views) * 100 : 0;
      });

      newAnalytics.conversionRate = newAnalytics.totalTriggers > 0 ? (newAnalytics.conversions / newAnalytics.totalTriggers) * 100 : 0;

      localStorage.setItem('exit_intent_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });

    // Track with external analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exit_intent_trigger', {
        offer_id: offerId,
        user_segment: userSegment,
        variant: variant || 'default',
        timestamp: new Date().toISOString()
      });
    }
  }, []);

  const trackConversion = useCallback((offerId: string, userSegment: string, timeToConversion: number, variant?: string) => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        conversions: prev.conversions + 1,
        timeToConversion: [...prev.timeToConversion, timeToConversion],
        offerPerformance: {
          ...prev.offerPerformance,
          [offerId]: {
            views: prev.offerPerformance[offerId]?.views || 0,
            conversions: (prev.offerPerformance[offerId]?.conversions || 0) + 1,
            rate: 0 // Will be calculated
          }
        },
        userSegmentPerformance: {
          ...prev.userSegmentPerformance,
          [userSegment]: {
            triggers: prev.userSegmentPerformance[userSegment]?.triggers || 0,
            conversions: (prev.userSegmentPerformance[userSegment]?.conversions || 0) + 1,
            rate: 0 // Will be calculated
          }
        },
        abTestResults: variant ? {
          ...prev.abTestResults,
          [variant]: {
            variant,
            views: prev.abTestResults[variant]?.views || 0,
            conversions: (prev.abTestResults[variant]?.conversions || 0) + 1,
            rate: 0 // Will be calculated
          }
        } : prev.abTestResults
      };

      // Recalculate rates
      Object.keys(newAnalytics.offerPerformance).forEach(id => {
        const offer = newAnalytics.offerPerformance[id];
        offer.rate = offer.views > 0 ? (offer.conversions / offer.views) * 100 : 0;
      });

      Object.keys(newAnalytics.userSegmentPerformance).forEach(segment => {
        const segmentData = newAnalytics.userSegmentPerformance[segment];
        segmentData.rate = segmentData.triggers > 0 ? (segmentData.conversions / segmentData.triggers) * 100 : 0;
      });

      Object.keys(newAnalytics.abTestResults).forEach(testVariant => {
        const testData = newAnalytics.abTestResults[testVariant];
        testData.rate = testData.views > 0 ? (testData.conversions / testData.views) * 100 : 0;
      });

      newAnalytics.conversionRate = newAnalytics.totalTriggers > 0 ? (newAnalytics.conversions / newAnalytics.totalTriggers) * 100 : 0;

      localStorage.setItem('exit_intent_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });

    // Track with external analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exit_intent_conversion', {
        offer_id: offerId,
        user_segment: userSegment,
        time_to_conversion: timeToConversion,
        variant: variant || 'default',
        timestamp: new Date().toISOString()
      });
    }
  }, []);

  const trackDismissal = useCallback((reason: string, offerId?: string, variant?: string) => {
    setAnalytics(prev => {
      const newAnalytics = {
        ...prev,
        dismissalReasons: {
          ...prev.dismissalReasons,
          [reason]: (prev.dismissalReasons[reason] || 0) + 1
        }
      };

      localStorage.setItem('exit_intent_analytics', JSON.stringify(newAnalytics));
      return newAnalytics;
    });

    // Track with external analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exit_intent_dismissal', {
        reason,
        offer_id: offerId || 'unknown',
        variant: variant || 'default',
        timestamp: new Date().toISOString()
      });
    }
  }, []);

  const getABTestVariant = useCallback(() => {
    const random = Math.random() * 100;
    let cumulative = 0;
    
    for (const [variant, config] of Object.entries(abTestVariants.variants)) {
      cumulative += config.weight;
      if (random <= cumulative) {
        return variant;
      }
    }
    
    return 'A'; // Fallback
  }, [abTestVariants]);

  const updateABTestConfig = useCallback((variants: ABTestVariants['variants']) => {
    const newConfig = { ...abTestVariants, variants };
    setAbTestVariants(newConfig);
    localStorage.setItem('exit_intent_ab_test', JSON.stringify(newConfig));
  }, [abTestVariants]);

  const getOptimalOffer = useCallback((userSegment: string) => {
    const segmentPerformance = analytics.userSegmentPerformance[userSegment];
    if (!segmentPerformance) return null;

    // Find best performing offer for this segment
    const bestOffer = Object.entries(analytics.offerPerformance)
      .sort(([,a], [,b]) => b.rate - a.rate)
      .find(([offerId]) => analytics.offerPerformance[offerId].views >= 10); // Minimum sample size

    return bestOffer ? bestOffer[0] : null;
  }, [analytics]);

  const getConversionInsights = useCallback(() => {
    const avgTimeToConversion = analytics.timeToConversion.length > 0 
      ? analytics.timeToConversion.reduce((sum, time) => sum + time, 0) / analytics.timeToConversion.length 
      : 0;

    const bestPerformingSegment = Object.entries(analytics.userSegmentPerformance)
      .sort(([,a], [,b]) => b.rate - a.rate)[0];

    const mostDismissedReason = Object.entries(analytics.dismissalReasons)
      .sort(([,a], [,b]) => b - a)[0];

    return {
      avgTimeToConversion,
      bestPerformingSegment: bestPerformingSegment ? {
        segment: bestPerformingSegment[0],
        rate: bestPerformingSegment[1].rate
      } : null,
      mostDismissedReason: mostDismissedReason ? {
        reason: mostDismissedReason[0],
        count: mostDismissedReason[1]
      } : null,
      totalRevenue: analytics.conversions * 49, // Assuming $49 average conversion value
      revenuePerVisitor: analytics.totalTriggers > 0 ? (analytics.conversions * 49) / analytics.totalTriggers : 0
    };
  }, [analytics]);

  return {
    analytics,
    abTestVariants,
    trackTrigger,
    trackConversion,
    trackDismissal,
    getABTestVariant,
    updateABTestConfig,
    getOptimalOffer,
    getConversionInsights
  };
}

// Context for exit intent state
const ExitIntentContext = createContext<{
  isExitIntentActive: boolean;
  setIsExitIntentActive: (active: boolean) => void;
  globalSettings: ExitIntentSettings;
  updateSettings: (settings: Partial<ExitIntentSettings>) => void;
  analytics: ReturnType<typeof useExitIntentAnalytics>;
} | null>(null);

// Exit Intent Provider for global state management
export function ExitIntentProvider({ children }: { children: React.ReactNode }) {
  const [isExitIntentActive, setIsExitIntentActive] = useState(false);
  const [globalSettings, setGlobalSettings] = useState<ExitIntentSettings>({
    enabled: true,
    maxTriggersPerSession: 2,
    minTimeOnSite: 30000, // 30 seconds
    cooldownPeriod: 60000, // 1 minute
    respectDoNotTrack: true,
    accessibilityMode: false,
    debugMode: false
  });

  const analytics = useExitIntentAnalytics();

  useEffect(() => {
    // Load global settings
    const savedSettings = localStorage.getItem('exit_intent_settings');
    if (savedSettings) {
      try {
        setGlobalSettings(JSON.parse(savedSettings));
      } catch (error) {
        console.error('Failed to load exit intent settings:', error);
      }
    }

    // Check for accessibility preferences
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      setGlobalSettings(prev => ({ ...prev, accessibilityMode: true }));
    }

    // Respect Do Not Track
    if (navigator.doNotTrack === '1' && globalSettings.respectDoNotTrack) {
      setGlobalSettings(prev => ({ ...prev, enabled: false }));
    }
  }, [globalSettings.respectDoNotTrack]);

  const updateSettings = useCallback((newSettings: Partial<ExitIntentSettings>) => {
    const updated = { ...globalSettings, ...newSettings };
    setGlobalSettings(updated);
    localStorage.setItem('exit_intent_settings', JSON.stringify(updated));
  }, [globalSettings]);

  const contextValue = {
    isExitIntentActive,
    setIsExitIntentActive,
    globalSettings,
    updateSettings,
    analytics
  };

  return (
    <ExitIntentContext.Provider value={contextValue}>
      {children}
    </ExitIntentContext.Provider>
  );
}

export function useExitIntentContext() {
  const context = useContext(ExitIntentContext);
  if (!context) {
    throw new Error('useExitIntentContext must be used within ExitIntentProvider');
  }
  return context;
}
