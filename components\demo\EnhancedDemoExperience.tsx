'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

// Import all the enhanced components
import { TourManager } from '@/components/onboarding/TourManager';
import { SolidityCompiler } from '@/components/hero/SolidityCompiler';
import { CompilationErrorDisplay } from '@/components/hero/CompilationErrorDisplay';
import { ProgressTrackingProvider, useProgressTracking } from '@/components/progress/ProgressTrackingSystem';
import { HeroProgressIndicator } from '@/components/progress/HeroProgressIndicator';
import { SaveProgressSystem } from '@/components/auth/SaveProgressSystem';
import { InteractiveMiniDemo } from '@/components/hero/InteractiveMiniDemo';

interface EnhancedDemoExperienceProps {
  className?: string;
  enableTour?: boolean;
  enableProgressTracking?: boolean;
  enableSavePrompts?: boolean;
  enableRealTimeCompilation?: boolean;
  userVisitCount?: number;
}

function DemoContent({
  className,
  enableTour = true,
  enableProgressTracking = true,
  enableSavePrompts = true,
  enableRealTimeCompilation = true,
  userVisitCount = 1
}: EnhancedDemoExperienceProps) {
  const [compilationResult, setCompilationResult] = useState<any>(null);
  const [compilationState, setCompilationState] = useState<any>(null);
  const [demoProgress, setDemoProgress] = useState({
    hasCompiled: false,
    hasDeployed: false,
    hasInteracted: false,
    completionPercentage: 0
  });

  const { updateProgress, addXP } = useProgressTracking();
  const demoRef = useRef<HTMLDivElement>(null);

  // Track demo interactions and trigger events
  useEffect(() => {
    const trackDemoEvent = (type: string, data?: any) => {
      // Dispatch custom events for save progress system
      window.dispatchEvent(new CustomEvent('progress_event', {
        detail: { type, data }
      }));

      // Update progress tracking
      switch (type) {
        case 'compilation':
          if (!demoProgress.hasCompiled) {
            updateProgress('first-compilation', 'compilation');
            setDemoProgress(prev => ({ ...prev, hasCompiled: true }));
          }
          break;
        case 'deployment':
          if (!demoProgress.hasDeployed) {
            updateProgress('first-deployment', 'deployment');
            setDemoProgress(prev => ({ ...prev, hasDeployed: true }));
          }
          break;
        case 'demo_complete':
          updateProgress('demo-completed', 'achievement');
          break;
      }
    };

    // Set up demo event tracking
    window.trackDemoEvent = trackDemoEvent;

    return () => {
      delete window.trackDemoEvent;
    };
  }, [updateProgress, demoProgress]);

  const handleCompilationResult = (result: any) => {
    setCompilationResult(result);
    
    if (result.success) {
      // Track successful compilation
      window.trackDemoEvent?.('compilation', result);
      addXP(25, 'Successful compilation in demo');
    }
  };

  const handleCompilationStateChange = (state: any) => {
    setCompilationState(state);
  };

  const handleDemoComplete = () => {
    window.trackDemoEvent?.('demo_complete');
    setDemoProgress(prev => ({ ...prev, completionPercentage: 100 }));
  };

  const handleDeployment = () => {
    window.trackDemoEvent?.('deployment');
    addXP(50, 'Contract deployment in demo');
  };

  return (
    <div className={cn('relative', className)} ref={demoRef}>
      {/* Tour System */}
      {enableTour && (
        <TourManager
          enableAutoStart={true}
          userVisitCount={userVisitCount}
          onTourComplete={(tourId) => {
            updateProgress('platform-tour', 'tour');
            addXP(100, 'Completed platform tour');
          }}
        />
      )}

      {/* Progress Indicator */}
      {enableProgressTracking && (
        <HeroProgressIndicator
          position="top-right"
          variant="compact"
          showExpandedView={true}
          autoHide={false}
        />
      )}

      {/* Enhanced Interactive Demo */}
      <div className="space-y-8" data-tour="demo-section">
        {/* Demo Header */}
        <div className="text-center">
          <motion.h2
            className="text-3xl md:text-4xl font-bold text-white mb-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Interactive Solidity Demo
          </motion.h2>
          <motion.p
            className="text-gray-300 text-lg max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Experience the power of our platform with real-time compilation, deployment, and comprehensive learning tools.
          </motion.p>
        </div>

        {/* Main Demo Interface */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Code Editor Section */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <InteractiveMiniDemo
              onComplete={handleDemoComplete}
              onDeploy={handleDeployment}
              enableGamification={enableProgressTracking}
              showProgressIndicator={true}
              data-tour="code-editor"
            />
          </motion.div>

          {/* Compilation & Results Section */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
          >
            {/* Enhanced Compiler */}
            <SolidityCompiler
              code={`// Welcome to Solidity Learning Platform!
pragma solidity ^0.8.0;

contract HelloWorld {
    string public message;
    
    constructor() {
        message = "Hello, Blockchain World!";
    }
    
    function updateMessage(string memory _newMessage) public {
        message = _newMessage;
    }
    
    function getMessage() public view returns (string memory) {
        return message;
    }
}`}
              onCompilationResult={handleCompilationResult}
              onCompilationStateChange={handleCompilationStateChange}
              enableSoundEffects={false}
              enableRealTimeValidation={enableRealTimeCompilation}
              showCompilationMetrics={true}
              data-tour="compile-button"
            />

            {/* Error Display */}
            {compilationResult?.errors && (
              <CompilationErrorDisplay
                errors={compilationResult.errors}
                sourceCode={compilationResult.sourceCode || ''}
                enableQuickFix={true}
                showLineNumbers={true}
              />
            )}

            {/* Deployment Section */}
            {compilationResult?.success && (
              <motion.div
                className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4 }}
                data-tour="deploy-section"
              >
                <h3 className="text-lg font-semibold text-white mb-4">Deploy to Testnet</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-400">Gas Estimate:</span>
                      <span className="text-white ml-2">{compilationResult.gasEstimate?.toLocaleString()}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Deployment Cost:</span>
                      <span className="text-white ml-2">${(compilationResult.deploymentCost / 1000000).toFixed(4)}</span>
                    </div>
                  </div>
                  
                  <button
                    onClick={handleDeployment}
                    className="w-full bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-all duration-200 transform hover:scale-105"
                  >
                    Deploy to Sepolia Testnet
                  </button>
                </div>
              </motion.div>
            )}
          </motion.div>
        </div>

        {/* Demo Progress Indicator */}
        <motion.div
          className="bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
        >
          <h3 className="text-lg font-semibold text-white mb-4">Demo Progress</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Code Compilation</span>
              <span className={cn(
                'text-sm font-medium',
                demoProgress.hasCompiled ? 'text-green-400' : 'text-gray-400'
              )}>
                {demoProgress.hasCompiled ? '✓ Complete' : 'Pending'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Contract Deployment</span>
              <span className={cn(
                'text-sm font-medium',
                demoProgress.hasDeployed ? 'text-green-400' : 'text-gray-400'
              )}>
                {demoProgress.hasDeployed ? '✓ Complete' : 'Pending'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-gray-300">Demo Completion</span>
              <span className={cn(
                'text-sm font-medium',
                demoProgress.completionPercentage === 100 ? 'text-green-400' : 'text-gray-400'
              )}>
                {demoProgress.completionPercentage === 100 ? '✓ Complete' : `${demoProgress.completionPercentage}%`}
              </span>
            </div>
          </div>
          
          <div className="w-full bg-gray-700 rounded-full h-2 mt-4">
            <motion.div
              className="h-full bg-gradient-to-r from-blue-500 to-green-500 rounded-full"
              initial={{ width: 0 }}
              animate={{ width: `${demoProgress.completionPercentage}%` }}
              transition={{ duration: 0.5 }}
            />
          </div>
        </motion.div>
      </div>

      {/* Save Progress System */}
      {enableSavePrompts && (
        <SaveProgressSystem
          triggerEvents={['compilation', 'achievement', 'demo_complete', 'time_spent', 'milestone']}
          showSocialProof={true}
          enableUrgency={true}
          guestSessionDuration={60} // 1 hour
        />
      )}
    </div>
  );
}

// Main component with provider wrapper
export function EnhancedDemoExperience(props: EnhancedDemoExperienceProps) {
  return (
    <ProgressTrackingProvider>
      <DemoContent {...props} />
    </ProgressTrackingProvider>
  );
}

// Analytics tracking for success metrics
export function useDemoAnalytics() {
  const [metrics, setMetrics] = useState({
    demoStarted: false,
    demoCompleted: false,
    compilationAttempts: 0,
    successfulCompilations: 0,
    deploymentAttempts: 0,
    successfulDeployments: 0,
    timeSpent: 0,
    accountCreated: false
  });

  useEffect(() => {
    const startTime = Date.now();

    const trackMetric = (metric: string, value?: any) => {
      setMetrics(prev => ({
        ...prev,
        [metric]: value !== undefined ? value : true,
        timeSpent: Math.round((Date.now() - startTime) / 1000)
      }));
    };

    // Listen for demo events
    const handleDemoEvent = (event: CustomEvent) => {
      const { type, data } = event.detail;
      
      switch (type) {
        case 'compilation':
          trackMetric('compilationAttempts', metrics.compilationAttempts + 1);
          if (data?.success) {
            trackMetric('successfulCompilations', metrics.successfulCompilations + 1);
          }
          break;
        case 'deployment':
          trackMetric('deploymentAttempts', metrics.deploymentAttempts + 1);
          if (data?.success) {
            trackMetric('successfulDeployments', metrics.successfulDeployments + 1);
          }
          break;
        case 'demo_complete':
          trackMetric('demoCompleted');
          break;
      }
    };

    window.addEventListener('progress_event', handleDemoEvent as EventListener);
    trackMetric('demoStarted');

    return () => {
      window.removeEventListener('progress_event', handleDemoEvent as EventListener);
    };
  }, []);

  return metrics;
}
