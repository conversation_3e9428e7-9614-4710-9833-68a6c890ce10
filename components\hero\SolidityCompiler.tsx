'use client';

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Zap,
  Shield,
  TrendingUp,
  Clock,
  DollarSign,
  Code,
  ExternalLink,
  FileText,
  Fuel
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CompilationError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning';
  suggestion?: string;
  fixable?: boolean;
}

interface CompilationWarning {
  line: number;
  message: string;
  type: 'gas' | 'security' | 'style' | 'deprecation';
}

interface SecurityWarning {
  type: 'reentrancy' | 'overflow' | 'access-control' | 'gas-limit';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  recommendation: string;
  line?: number;
}

interface CompilationResult {
  success: boolean;
  bytecode?: string;
  abi?: any[];
  gasEstimate?: number;
  errors?: CompilationError[];
  warnings?: CompilationWarning[];
  optimizationTips?: string[];
  securityWarnings?: SecurityWarning[];
  deploymentCost?: number;
  executionCost?: number;
  compilationTime?: number;
  contractSize?: number;
  gasOptimizationScore?: number;
}

interface CompilationState {
  status: 'idle' | 'compiling' | 'success' | 'error' | 'warning';
  progress: number;
  stage: string;
  startTime?: number;
  endTime?: number;
}

interface SolidityCompilerProps {
  code: string;
  onCompilationResult?: (result: CompilationResult) => void;
  onCompilationStateChange?: (state: CompilationState) => void;
  autoCompile?: boolean;
  showGasEstimation?: boolean;
  showSecurityAnalysis?: boolean;
  enableSoundEffects?: boolean;
  enableRealTimeValidation?: boolean;
  showCompilationMetrics?: boolean;
  className?: string;
}

/**
 * Real-time Solidity compiler with feedback system
 */
export function SolidityCompiler({
  code,
  onCompilationResult,
  onCompilationStateChange,
  autoCompile = true,
  showGasEstimation = true,
  showSecurityAnalysis = true,
  enableSoundEffects = false,
  enableRealTimeValidation = true,
  showCompilationMetrics = true,
  className = ''
}: SolidityCompilerProps) {
  const [isCompiling, setIsCompiling] = useState(false);
  const [result, setResult] = useState<CompilationResult | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [compilationState, setCompilationState] = useState<CompilationState>({
    status: 'idle',
    progress: 0,
    stage: 'Ready to compile'
  });

  const soundRef = React.useRef<HTMLAudioElement>(null);
  const errorSoundRef = React.useRef<HTMLAudioElement>(null);

  // Enhanced compilation function with real-time progress
  const compileCode = useCallback(async (sourceCode: string): Promise<CompilationResult> => {
    const startTime = Date.now();

    // Update compilation state with progress
    const updateProgress = (progress: number, stage: string) => {
      const newState: CompilationState = {
        status: 'compiling',
        progress,
        stage,
        startTime
      };
      setCompilationState(newState);
      if (onCompilationStateChange) {
        onCompilationStateChange(newState);
      }
    };

    // Simulate compilation stages with progress updates
    updateProgress(10, 'Parsing source code...');
    await new Promise(resolve => setTimeout(resolve, 200));

    updateProgress(30, 'Analyzing syntax...');
    await new Promise(resolve => setTimeout(resolve, 200));

    updateProgress(50, 'Type checking...');
    await new Promise(resolve => setTimeout(resolve, 200));

    updateProgress(70, 'Generating bytecode...');
    await new Promise(resolve => setTimeout(resolve, 200));

    updateProgress(90, 'Optimizing gas usage...');
    await new Promise(resolve => setTimeout(resolve, 200));

    // Mock analysis based on code content
    const hasBasicContract = sourceCode.includes('contract');
    const hasConstructor = sourceCode.includes('constructor');
    const hasPublicFunction = sourceCode.includes('public');
    const hasPragma = sourceCode.includes('pragma');

    const errors: CompilationError[] = [];
    const warnings: CompilationWarning[] = [];
    const securityWarnings: SecurityWarning[] = [];
    const optimizationTips: string[] = [];

    // Basic validation
    if (!hasPragma) {
      errors.push({
        line: 1,
        column: 1,
        message: 'Missing pragma directive',
        severity: 'error',
        suggestion: 'Add pragma solidity ^0.8.0; at the top of your file',
        fixable: true
      });
    }

    if (!hasBasicContract) {
      errors.push({
        line: 1,
        column: 1,
        message: 'No contract found',
        severity: 'error',
        suggestion: 'Define a contract using the contract keyword'
      });
    }

    // Warnings
    if (hasPublicFunction && !sourceCode.includes('require(')) {
      warnings.push({
        line: 10,
        message: 'Consider adding input validation with require()',
        type: 'security'
      });
    }

    if (sourceCode.includes('uint ') && !sourceCode.includes('uint256')) {
      warnings.push({
        line: 4,
        message: 'Consider using uint256 instead of uint for clarity',
        type: 'style'
      });
    }

    // Security analysis
    if (sourceCode.includes('call(') || sourceCode.includes('.call')) {
      securityWarnings.push({
        type: 'reentrancy',
        severity: 'high',
        message: 'Potential reentrancy vulnerability detected',
        recommendation: 'Use the checks-effects-interactions pattern or reentrancy guards',
        line: 15
      });
    }

    if (sourceCode.includes('tx.origin')) {
      securityWarnings.push({
        type: 'access-control',
        severity: 'critical',
        message: 'Use of tx.origin for authorization is dangerous',
        recommendation: 'Use msg.sender instead of tx.origin for access control',
        line: 12
      });
    }

    // Optimization tips
    if (sourceCode.includes('string memory')) {
      optimizationTips.push('Consider using bytes32 instead of string for fixed-length data to save gas');
    }

    if (sourceCode.includes('for (')) {
      optimizationTips.push('Be cautious with loops - they can consume significant gas');
    }

    const success = errors.length === 0;
    const gasEstimate = success ? Math.floor(Math.random() * 200000) + 100000 : 0;
    const endTime = Date.now();
    const compilationTime = endTime - startTime;
    const contractSize = success ? Math.floor(sourceCode.length * 0.8) : 0;
    const gasOptimizationScore = success ? Math.floor(Math.random() * 30) + 70 : 0;

    // Final progress update
    updateProgress(100, success ? 'Compilation successful!' : 'Compilation failed');

    // Update final state
    const finalState: CompilationState = {
      status: success ? (warnings.length > 0 ? 'warning' : 'success') : 'error',
      progress: 100,
      stage: success ? 'Ready for deployment' : 'Fix errors to continue',
      startTime,
      endTime
    };
    setCompilationState(finalState);
    if (onCompilationStateChange) {
      onCompilationStateChange(finalState);
    }

    return {
      success,
      bytecode: success ? '0x608060405234801561001057600080fd5b50...' : undefined,
      abi: success ? [
        {
          "inputs": [],
          "name": "message",
          "outputs": [{"internalType": "string", "name": "", "type": "string"}],
          "stateMutability": "view",
          "type": "function"
        }
      ] : undefined,
      gasEstimate,
      errors: errors.length > 0 ? errors : undefined,
      warnings: warnings.length > 0 ? warnings : undefined,
      securityWarnings: securityWarnings.length > 0 ? securityWarnings : undefined,
      optimizationTips: optimizationTips.length > 0 ? optimizationTips : undefined,
      deploymentCost: success ? gasEstimate * 20 : undefined, // Mock gas price
      executionCost: success ? Math.floor(gasEstimate * 0.3) : undefined,
      compilationTime,
      contractSize,
      gasOptimizationScore
    };
  }, []);

  const handleCompile = async () => {
    if (!code.trim()) return;

    setIsCompiling(true);
    try {
      const compilationResult = await compileCode(code);
      setResult(compilationResult);

      // Play sound effects
      if (enableSoundEffects) {
        if (compilationResult.success) {
          soundRef.current?.play().catch(() => {});
        } else {
          errorSoundRef.current?.play().catch(() => {});
        }
      }

      onCompilationResult?.(compilationResult);
    } catch (error) {
      console.error('Compilation failed:', error);

      // Update state for unexpected errors
      const errorState: CompilationState = {
        status: 'error',
        progress: 0,
        stage: 'Compilation failed unexpectedly'
      };
      setCompilationState(errorState);
      if (onCompilationStateChange) {
        onCompilationStateChange(errorState);
      }
    } finally {
      setIsCompiling(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'high':
        return 'text-orange-400 bg-orange-500/20 border-orange-500/30';
      case 'medium':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      case 'low':
        return 'text-blue-400 bg-blue-500/20 border-blue-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Compile Button with Real-time Status */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-white">Compilation Results</h3>
          <motion.button
            onClick={handleCompile}
            disabled={isCompiling || !code.trim()}
            className={cn(
              'flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors relative overflow-hidden',
              isCompiling || !code.trim()
                ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                : compilationState.status === 'success'
                ? 'bg-green-600 hover:bg-green-700 text-white'
                : compilationState.status === 'error'
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            )}
            whileHover={!isCompiling ? { scale: 1.02 } : {}}
            whileTap={!isCompiling ? { scale: 0.98 } : {}}
            data-tour="compile-button"
          >
            {/* Progress bar background */}
            {isCompiling && (
              <motion.div
                className="absolute inset-0 bg-white/20"
                initial={{ width: '0%' }}
                animate={{ width: `${compilationState.progress}%` }}
                transition={{ duration: 0.3 }}
              />
            )}

            <div className="relative z-10 flex items-center space-x-2">
              {isCompiling ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>{compilationState.stage}</span>
                </>
              ) : compilationState.status === 'success' ? (
                <>
                  <CheckCircle className="w-4 h-4" />
                  <span>Compiled Successfully</span>
                </>
              ) : compilationState.status === 'error' ? (
                <>
                  <AlertCircle className="w-4 h-4" />
                  <span>Fix Errors & Retry</span>
                </>
              ) : (
                <>
                  <Code className="w-4 h-4" />
                  <span>Compile Contract</span>
                </>
              )}
            </div>
          </motion.button>
        </div>

        {/* Real-time Progress Indicator */}
        {isCompiling && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="bg-gray-800 rounded-lg p-3 border border-gray-700"
          >
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-300">{compilationState.stage}</span>
              <span className="text-sm text-gray-400">{compilationState.progress}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-blue-500 to-green-500"
                initial={{ width: '0%' }}
                animate={{ width: `${compilationState.progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </motion.div>
        )}

        {/* Compilation Metrics */}
        {showCompilationMetrics && result && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs"
          >
            {result.compilationTime && (
              <div className="bg-gray-800 rounded-lg p-2 border border-gray-700">
                <div className="flex items-center space-x-1 mb-1">
                  <Clock className="w-3 h-3 text-blue-400" />
                  <span className="text-gray-400">Time</span>
                </div>
                <span className="text-white font-medium">{result.compilationTime}ms</span>
              </div>
            )}
            {result.contractSize && (
              <div className="bg-gray-800 rounded-lg p-2 border border-gray-700">
                <div className="flex items-center space-x-1 mb-1">
                  <FileText className="w-3 h-3 text-green-400" />
                  <span className="text-gray-400">Size</span>
                </div>
                <span className="text-white font-medium">{result.contractSize} bytes</span>
              </div>
            )}
            {result.gasOptimizationScore && (
              <div className="bg-gray-800 rounded-lg p-2 border border-gray-700">
                <div className="flex items-center space-x-1 mb-1">
                  <Zap className="w-3 h-3 text-yellow-400" />
                  <span className="text-gray-400">Optimization</span>
                </div>
                <span className="text-white font-medium">{result.gasOptimizationScore}%</span>
              </div>
            )}
            {result.gasEstimate && (
              <div className="bg-gray-800 rounded-lg p-2 border border-gray-700">
                <div className="flex items-center space-x-1 mb-1">
                  <Fuel className="w-3 h-3 text-orange-400" />
                  <span className="text-gray-400">Gas</span>
                </div>
                <span className="text-white font-medium">{result.gasEstimate.toLocaleString()}</span>
              </div>
            )}
          </motion.div>
        )}
      </div>

      {/* Compilation Results */}
      <AnimatePresence>
        {result && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-4"
          >
            {/* Success/Error Summary */}
            <div className={cn(
              'p-4 rounded-lg border',
              result.success 
                ? 'bg-green-500/10 border-green-500/30' 
                : 'bg-red-500/10 border-red-500/30'
            )}>
              <div className="flex items-center space-x-2">
                {result.success ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <AlertCircle className="w-5 h-5 text-red-400" />
                )}
                <span className={cn(
                  'font-medium',
                  result.success ? 'text-green-300' : 'text-red-300'
                )}>
                  {result.success ? 'Compilation Successful!' : 'Compilation Failed'}
                </span>
              </div>
              
              {result.success && showGasEstimation && result.gasEstimate && (
                <div className="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div className="flex items-center space-x-2">
                    <Zap className="w-4 h-4 text-yellow-400" />
                    <span className="text-gray-300">Gas: {result.gasEstimate.toLocaleString()}</span>
                  </div>
                  {result.deploymentCost && (
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300">Deploy: ~${(result.deploymentCost / 1000000).toFixed(4)}</span>
                    </div>
                  )}
                  {result.executionCost && (
                    <div className="flex items-center space-x-2">
                      <Clock className="w-4 h-4 text-blue-400" />
                      <span className="text-gray-300">Execute: {result.executionCost.toLocaleString()} gas</span>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Errors */}
            {result.errors && result.errors.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-red-400 font-medium flex items-center space-x-2">
                  <AlertCircle className="w-4 h-4" />
                  <span>Errors ({result.errors.length})</span>
                </h4>
                {result.errors.map((error, index) => (
                  <div key={index} className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="text-red-300 font-medium">Line {error.line}: {error.message}</p>
                        {error.suggestion && (
                          <p className="text-gray-300 text-sm mt-1">💡 {error.suggestion}</p>
                        )}
                      </div>
                      {error.fixable && (
                        <button className="text-blue-400 hover:text-blue-300 text-sm">
                          Auto-fix
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Warnings */}
            {result.warnings && result.warnings.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-yellow-400 font-medium flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4" />
                  <span>Warnings ({result.warnings.length})</span>
                </h4>
                {result.warnings.map((warning, index) => (
                  <div key={index} className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-3">
                    <p className="text-yellow-300">Line {warning.line}: {warning.message}</p>
                    <span className="text-xs text-gray-400 capitalize">{warning.type}</span>
                  </div>
                ))}
              </div>
            )}

            {/* Security Warnings */}
            {showSecurityAnalysis && result.securityWarnings && result.securityWarnings.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-orange-400 font-medium flex items-center space-x-2">
                  <Shield className="w-4 h-4" />
                  <span>Security Analysis ({result.securityWarnings.length})</span>
                </h4>
                {result.securityWarnings.map((warning, index) => (
                  <div key={index} className={cn('rounded-lg p-3 border', getSeverityColor(warning.severity))}>
                    <div className="flex items-start justify-between">
                      <div>
                        <p className="font-medium">{warning.message}</p>
                        <p className="text-sm mt-1 opacity-80">💡 {warning.recommendation}</p>
                        {warning.line && (
                          <p className="text-xs mt-1 opacity-60">Line {warning.line}</p>
                        )}
                      </div>
                      <span className="text-xs px-2 py-1 rounded uppercase font-bold">
                        {warning.severity}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Optimization Tips */}
            {result.optimizationTips && result.optimizationTips.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-blue-400 font-medium flex items-center space-x-2">
                  <TrendingUp className="w-4 h-4" />
                  <span>Optimization Tips</span>
                </h4>
                <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-3">
                  <ul className="space-y-1">
                    {result.optimizationTips.map((tip, index) => (
                      <li key={index} className="text-blue-300 text-sm flex items-start space-x-2">
                        <span className="text-blue-400 mt-1">•</span>
                        <span>{tip}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            )}

            {/* Deployment Simulation */}
            {result.success && (
              <div className="bg-purple-500/10 border border-purple-500/30 rounded-lg p-4">
                <h4 className="text-purple-400 font-medium mb-3 flex items-center space-x-2">
                  <ExternalLink className="w-4 h-4" />
                  <span>Ready for Deployment</span>
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-300 mb-2">Contract successfully compiled and ready for deployment to:</p>
                    <ul className="space-y-1 text-purple-300">
                      <li>• Sepolia Testnet (Recommended)</li>
                      <li>• Goerli Testnet</li>
                      <li>• Local Development Network</li>
                    </ul>
                  </div>
                  <div>
                    <p className="text-gray-300 mb-2">Estimated costs:</p>
                    <ul className="space-y-1 text-purple-300">
                      <li>• Deployment: ~{result.deploymentCost?.toLocaleString()} gas</li>
                      <li>• Function calls: ~{result.executionCost?.toLocaleString()} gas</li>
                      <li>• Total: ~$0.05 USD (current gas prices)</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Sound Effects */}
      {enableSoundEffects && (
        <>
          <audio ref={soundRef} preload="auto">
            <source src="/sounds/compilation-success.mp3" type="audio/mpeg" />
            <source src="/sounds/compilation-success.wav" type="audio/wav" />
          </audio>
          <audio ref={errorSoundRef} preload="auto">
            <source src="/sounds/compilation-error.mp3" type="audio/mpeg" />
            <source src="/sounds/compilation-error.wav" type="audio/wav" />
          </audio>
        </>
      )}
    </div>
  );
}
