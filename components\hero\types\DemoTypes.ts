/**
 * Enhanced Interactive Demo Types
 * Comprehensive type definitions for the advanced InteractiveMiniDemo component
 */

import { ReactNode } from 'react';

/**
 * Learning objective for each demo step
 */
export interface LearningObjective {
  id: string;
  title: string;
  description: string;
  completed: boolean;
}

/**
 * Interactive tooltip configuration
 */
export interface InteractiveTooltip {
  id: string;
  target: string; // CSS selector or element ID
  content: ReactNode;
  position: 'top' | 'bottom' | 'left' | 'right';
  delay?: number;
  persistent?: boolean;
}

/**
 * Voice-over narration configuration
 */
export interface VoiceOverConfig {
  enabled: boolean;
  autoPlay: boolean;
  speed: number; // 0.5 to 2.0
  voice?: string;
  language: string;
}

/**
 * Keyboard shortcut definition
 */
export interface KeyboardShortcut {
  key: string;
  description: string;
  action: () => void;
  modifier?: 'ctrl' | 'alt' | 'shift';
}

/**
 * Code snippet with enhanced features
 */
export interface CodeSnippet {
  id: string;
  language: 'solidity' | 'javascript' | 'typescript';
  content: string;
  filename: string;
  editable: boolean;
  copyable: boolean;
  highlights?: number[]; // line numbers to highlight
  annotations?: CodeAnnotation[];
}

/**
 * Code annotation for tooltips and explanations
 */
export interface CodeAnnotation {
  line: number;
  column?: number;
  type: 'info' | 'warning' | 'error' | 'tip';
  title: string;
  content: string;
  link?: string;
}

/**
 * Compilation result from Solidity compiler
 */
export interface CompilationResult {
  success: boolean;
  bytecode?: string;
  abi?: any[];
  gasEstimate?: number;
  errors?: CompilationError[];
  warnings?: CompilationWarning[];
  optimizationTips?: string[];
  securityWarnings?: SecurityWarning[];
}

/**
 * Compilation error details
 */
export interface CompilationError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning';
  suggestion?: string;
  fixable?: boolean;
}

/**
 * Compilation warning details
 */
export interface CompilationWarning {
  line: number;
  message: string;
  type: 'gas' | 'security' | 'style' | 'deprecation';
}

/**
 * Security analysis warning
 */
export interface SecurityWarning {
  type: 'reentrancy' | 'overflow' | 'access-control' | 'gas-limit';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  recommendation: string;
  line?: number;
}

/**
 * Achievement badge configuration
 */
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  xpReward: number;
  unlocked: boolean;
  unlockedAt?: Date;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

/**
 * Progress tracking data
 */
export interface ProgressData {
  currentStep: number;
  completedSteps: Set<number>;
  totalXP: number;
  achievements: Achievement[];
  startTime: Date;
  lastActiveTime: Date;
  completionPercentage: number;
  estimatedTimeRemaining: number;
}

/**
 * User session data for progress persistence
 */
export interface UserSession {
  id: string;
  isGuest: boolean;
  email?: string;
  progress: ProgressData;
  preferences: {
    voiceOver: VoiceOverConfig;
    autoAdvance: boolean;
    showTooltips: boolean;
    animationSpeed: number;
  };
}

/**
 * Enhanced demo step configuration
 */
export interface EnhancedDemoStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  duration: number;
  autoAdvance?: boolean;
  learningObjectives: LearningObjective[];
  content: ReactNode;
  codeSnippets?: CodeSnippet[];
  tooltips?: InteractiveTooltip[];
  voiceOverText?: string;
  successCriteria: string[];
  xpReward: number;
  achievements?: string[]; // Achievement IDs that can be unlocked
}

/**
 * Analytics event data
 */
export interface AnalyticsEvent {
  event: string;
  category: 'engagement' | 'completion' | 'error' | 'conversion';
  label?: string;
  value?: number;
  customParameters?: Record<string, any>;
}

/**
 * Account creation prompt configuration
 */
export interface AccountPrompt {
  trigger: 'step-completion' | 'time-based' | 'engagement-score';
  stepNumber?: number;
  timeThreshold?: number;
  engagementThreshold?: number;
  message: string;
  urgencyLevel: 'low' | 'medium' | 'high';
  incentive?: string;
  socialProof?: string;
}

/**
 * OAuth provider configuration
 */
export interface OAuthProvider {
  id: 'github' | 'google' | 'discord';
  name: string;
  icon: string;
  color: string;
  enabled: boolean;
}

/**
 * Enhanced InteractiveMiniDemo props
 */
export interface EnhancedInteractiveMiniDemoProps {
  className?: string;
  triggerText?: string;
  autoStart?: boolean;
  trackAnalytics?: boolean;
  respectReducedMotion?: boolean;
  
  // Enhanced features
  enableVoiceOver?: boolean;
  enableTooltips?: boolean;
  enableGamification?: boolean;
  enableAccountPrompts?: boolean;
  enableCompilation?: boolean;
  
  // Configuration
  voiceOverConfig?: Partial<VoiceOverConfig>;
  accountPrompts?: AccountPrompt[];
  oauthProviders?: OAuthProvider[];
  
  // Callbacks
  onStepComplete?: (stepId: string, xpEarned: number) => void;
  onAchievementUnlocked?: (achievement: Achievement) => void;
  onAccountCreationPrompt?: (prompt: AccountPrompt) => void;
  onCompilationResult?: (result: CompilationResult) => void;
  onProgressSave?: (progress: ProgressData) => void;
  onAnalyticsEvent?: (event: AnalyticsEvent) => void;
}

/**
 * Demo state management
 */
export interface DemoState {
  isOpen: boolean;
  currentStep: number;
  isPlaying: boolean;
  isPaused: boolean;
  timeRemaining: number;
  totalDuration: number;
  progress: ProgressData;
  session: UserSession;
  compilationResults: Map<string, CompilationResult>;
  activeTooltips: Set<string>;
  showKeyboardShortcuts: boolean;
  showAccountPrompt: boolean;
  currentPrompt?: AccountPrompt;
}

/**
 * Leaderboard entry
 */
export interface LeaderboardEntry {
  rank: number;
  username: string;
  avatar?: string;
  xp: number;
  completionTime: number;
  achievements: number;
  isCurrentUser?: boolean;
}

/**
 * Demo completion statistics
 */
export interface CompletionStats {
  totalUsers: number;
  averageCompletionTime: number;
  completionRate: number;
  topAchievements: Achievement[];
  recentCompletions: LeaderboardEntry[];
}
