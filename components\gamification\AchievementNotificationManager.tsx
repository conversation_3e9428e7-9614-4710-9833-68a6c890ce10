'use client';

import React, { useState, useEffect } from 'react';
import { AnimatePresence } from 'framer-motion';
import { Achievement } from '@/lib/achievements/types';
import { MicroAchievement } from './MicroAchievements';
import { AchievementCelebration, AchievementToast } from './AchievementCelebration';

export interface AchievementNotification {
  id: string;
  type: 'major' | 'micro' | 'toast';
  achievement: Achievement | MicroAchievement;
  timestamp: Date;
  priority: number; // Higher number = higher priority
}

interface AchievementNotificationManagerProps {
  className?: string;
  maxToasts?: number;
  toastDuration?: number;
  onShare?: (achievement: Achievement | MicroAchievement, platform: string) => void;
}

export function AchievementNotificationManager({
  className,
  maxToasts = 3,
  toastDuration = 5000,
  onShare
}: AchievementNotificationManagerProps) {
  const [notifications, setNotifications] = useState<AchievementNotification[]>([]);
  const [currentCelebration, setCurrentCelebration] = useState<AchievementNotification | null>(null);
  const [toastQueue, setToastQueue] = useState<AchievementNotification[]>([]);

  // Function to add new achievement notification
  const addAchievementNotification = (
    achievement: Achievement | MicroAchievement,
    type: 'major' | 'micro' | 'toast' = 'toast',
    priority: number = 1
  ) => {
    const notification: AchievementNotification = {
      id: `achievement_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      achievement,
      timestamp: new Date(),
      priority
    };

    // Determine notification type based on achievement properties
    if (type === 'toast') {
      // Check if this should be a major celebration instead
      if ('rarity' in achievement) {
        if (achievement.rarity === 'legendary' || achievement.rarity === 'epic') {
          notification.type = 'major';
          notification.priority = 10;
        } else if (achievement.rarity === 'rare') {
          notification.priority = 5;
        }
      }
    }

    setNotifications(prev => [...prev, notification]);
  };

  // Process notification queue
  useEffect(() => {
    if (notifications.length === 0) return;

    // Sort by priority (highest first)
    const sortedNotifications = [...notifications].sort((a, b) => b.priority - a.priority);
    const highestPriority = sortedNotifications[0];

    // Handle major celebrations first
    if (highestPriority.type === 'major' && !currentCelebration) {
      setCurrentCelebration(highestPriority);
      setNotifications(prev => prev.filter(n => n.id !== highestPriority.id));
      return;
    }

    // Handle toast notifications
    const toastNotifications = sortedNotifications.filter(n => n.type === 'toast' || n.type === 'micro');
    if (toastNotifications.length > 0 && toastQueue.length < maxToasts) {
      const nextToast = toastNotifications[0];
      setToastQueue(prev => [...prev, nextToast]);
      setNotifications(prev => prev.filter(n => n.id !== nextToast.id));
    }
  }, [notifications, currentCelebration, toastQueue.length, maxToasts]);

  // Remove toast from queue
  const removeToast = (notificationId: string) => {
    setToastQueue(prev => prev.filter(n => n.id !== notificationId));
  };

  // Handle celebration close
  const handleCelebrationClose = () => {
    setCurrentCelebration(null);
  };

  // Handle celebration click (for toasts)
  const handleToastClick = (notification: AchievementNotification) => {
    // Remove from toast queue and show as major celebration
    removeToast(notification.id);
    setCurrentCelebration(notification);
  };

  // Handle sharing
  const handleShare = (platform: string) => {
    if (currentCelebration && onShare) {
      onShare(currentCelebration.achievement, platform);
    }
  };

  // Expose function globally for easy access
  useEffect(() => {
    (window as any).addAchievementNotification = addAchievementNotification;
    
    return () => {
      delete (window as any).addAchievementNotification;
    };
  }, []);

  return (
    <div className={className}>
      {/* Major Achievement Celebration */}
      <AnimatePresence>
        {currentCelebration && (
          <AchievementCelebration
            achievement={currentCelebration.achievement as Achievement}
            onClose={handleCelebrationClose}
            onShare={handleShare}
            showConfetti={true}
          />
        )}
      </AnimatePresence>

      {/* Toast Notifications */}
      <div className="fixed top-4 right-4 z-40 space-y-2">
        <AnimatePresence>
          {toastQueue.map((notification, index) => (
            <div
              key={notification.id}
              style={{ 
                transform: `translateY(${index * 10}px)`,
                zIndex: 40 - index
              }}
            >
              <AchievementToast
                achievement={notification.achievement as Achievement}
                onClose={() => removeToast(notification.id)}
                onClick={() => handleToastClick(notification)}
                duration={toastDuration}
              />
            </div>
          ))}
        </AnimatePresence>
      </div>
    </div>
  );
}

// Hook for triggering achievement notifications
export function useAchievementNotifications() {
  const triggerAchievementNotification = (
    achievement: Achievement | MicroAchievement,
    type: 'major' | 'micro' | 'toast' = 'toast',
    priority: number = 1
  ) => {
    if (typeof window !== 'undefined' && (window as any).addAchievementNotification) {
      (window as any).addAchievementNotification(achievement, type, priority);
    }
  };

  // Convenience methods for different types of achievements
  const triggerMajorAchievement = (achievement: Achievement) => {
    triggerAchievementNotification(achievement, 'major', 10);
  };

  const triggerMicroAchievement = (achievement: MicroAchievement) => {
    triggerAchievementNotification(achievement, 'micro', 3);
  };

  const triggerToastAchievement = (achievement: Achievement) => {
    triggerAchievementNotification(achievement, 'toast', 5);
  };

  // Trigger achievement based on rarity
  const triggerAchievementByRarity = (achievement: Achievement) => {
    if ('rarity' in achievement) {
      switch (achievement.rarity) {
        case 'legendary':
          triggerMajorAchievement(achievement);
          break;
        case 'epic':
          triggerMajorAchievement(achievement);
          break;
        case 'rare':
          triggerToastAchievement(achievement);
          break;
        default:
          triggerToastAchievement(achievement);
      }
    } else {
      triggerToastAchievement(achievement);
    }
  };

  return {
    triggerAchievementNotification,
    triggerMajorAchievement,
    triggerMicroAchievement,
    triggerToastAchievement,
    triggerAchievementByRarity
  };
}

// Achievement Sound Manager
export class AchievementSoundManager {
  private static instance: AchievementSoundManager;
  private audioContext: AudioContext | null = null;
  private soundEnabled: boolean = true;

  private constructor() {
    // Initialize audio context on first user interaction
    if (typeof window !== 'undefined') {
      document.addEventListener('click', this.initAudioContext.bind(this), { once: true });
    }
  }

  static getInstance(): AchievementSoundManager {
    if (!AchievementSoundManager.instance) {
      AchievementSoundManager.instance = new AchievementSoundManager();
    }
    return AchievementSoundManager.instance;
  }

  private initAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Web Audio API not supported');
    }
  }

  setSoundEnabled(enabled: boolean) {
    this.soundEnabled = enabled;
  }

  playAchievementSound(type: 'major' | 'micro' | 'toast' = 'toast') {
    if (!this.soundEnabled || !this.audioContext) return;

    try {
      const oscillator = this.audioContext.createOscillator();
      const gainNode = this.audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      // Different sounds for different achievement types
      switch (type) {
        case 'major':
          // Triumphant chord progression
          this.playChord([523.25, 659.25, 783.99], 1.5); // C-E-G major chord
          break;
        case 'micro':
          // Quick positive beep
          oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
          oscillator.type = 'sine';
          gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
          gainNode.gain.linearRampToValueAtTime(0.1, this.audioContext.currentTime + 0.01);
          gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.3);
          oscillator.start(this.audioContext.currentTime);
          oscillator.stop(this.audioContext.currentTime + 0.3);
          break;
        case 'toast':
          // Pleasant notification sound
          oscillator.frequency.setValueAtTime(659.25, this.audioContext.currentTime);
          oscillator.frequency.linearRampToValueAtTime(783.99, this.audioContext.currentTime + 0.1);
          oscillator.type = 'triangle';
          gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
          gainNode.gain.linearRampToValueAtTime(0.08, this.audioContext.currentTime + 0.01);
          gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.5);
          oscillator.start(this.audioContext.currentTime);
          oscillator.stop(this.audioContext.currentTime + 0.5);
          break;
      }
    } catch (error) {
      console.warn('Failed to play achievement sound:', error);
    }
  }

  private playChord(frequencies: number[], duration: number) {
    if (!this.audioContext) return;

    frequencies.forEach((freq, index) => {
      const oscillator = this.audioContext!.createOscillator();
      const gainNode = this.audioContext!.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(this.audioContext!.destination);
      
      oscillator.frequency.setValueAtTime(freq, this.audioContext!.currentTime);
      oscillator.type = 'triangle';
      
      const volume = 0.05 / frequencies.length; // Reduce volume for multiple oscillators
      gainNode.gain.setValueAtTime(0, this.audioContext!.currentTime);
      gainNode.gain.linearRampToValueAtTime(volume, this.audioContext!.currentTime + 0.1);
      gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext!.currentTime + duration);
      
      oscillator.start(this.audioContext!.currentTime + index * 0.1);
      oscillator.stop(this.audioContext!.currentTime + duration);
    });
  }
}

// Hook for achievement sounds
export function useAchievementSounds() {
  const soundManager = AchievementSoundManager.getInstance();

  const playAchievementSound = (type: 'major' | 'micro' | 'toast' = 'toast') => {
    soundManager.playAchievementSound(type);
  };

  const setSoundEnabled = (enabled: boolean) => {
    soundManager.setSoundEnabled(enabled);
  };

  return {
    playAchievementSound,
    setSoundEnabled
  };
}
