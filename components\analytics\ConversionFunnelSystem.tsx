'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  TrendingDown, 
  Users, 
  Target, 
  Zap, 
  MessageSquare,
  Star,
  ThumbsUp,
  ThumbsDown,
  AlertCircle,
  CheckCircle,
  X,
  Send
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface FunnelStep {
  id: string;
  name: string;
  description: string;
  users: number;
  conversions: number;
  conversionRate: number;
  dropOffRate: number;
  avgTimeSpent: number;
  commonExitPoints: string[];
  improvementOpportunities: string[];
}

interface DropOffAnalysis {
  stepId: string;
  stepName: string;
  dropOffCount: number;
  dropOffRate: number;
  reasons: Array<{
    reason: string;
    percentage: number;
    impact: 'high' | 'medium' | 'low';
  }>;
  recommendations: Array<{
    action: string;
    expectedImpact: number;
    effort: 'low' | 'medium' | 'high';
  }>;
}

interface FeedbackWidget {
  id: string;
  type: 'rating' | 'nps' | 'feedback' | 'survey';
  trigger: {
    event: string;
    delay?: number;
    conditions?: Record<string, any>;
  };
  content: {
    title: string;
    description?: string;
    questions: Array<{
      id: string;
      type: 'rating' | 'text' | 'choice' | 'scale';
      question: string;
      options?: string[];
      required: boolean;
    }>;
  };
  styling: {
    position: 'bottom-right' | 'bottom-left' | 'center' | 'top-banner';
    theme: 'light' | 'dark';
    size: 'compact' | 'standard' | 'expanded';
  };
}

interface UserFeedback {
  id: string;
  userId: string;
  widgetId: string;
  responses: Record<string, any>;
  sentiment: 'positive' | 'neutral' | 'negative';
  category: 'ux' | 'content' | 'technical' | 'feature_request' | 'bug';
  priority: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  context: {
    page: string;
    userAgent: string;
    sessionDuration: number;
    previousActions: string[];
  };
}

// Conversion Funnel Hook
export function useConversionFunnel() {
  const [funnelData, setFunnelData] = useState<FunnelStep[]>([
    {
      id: 'awareness',
      name: 'Landing Page Visit',
      description: 'User visits the landing page',
      users: 10000,
      conversions: 10000,
      conversionRate: 100,
      dropOffRate: 0,
      avgTimeSpent: 45,
      commonExitPoints: [],
      improvementOpportunities: ['Improve page load speed', 'Optimize hero message']
    },
    {
      id: 'interest',
      name: 'Content Engagement',
      description: 'User scrolls and engages with content',
      users: 7500,
      conversions: 7500,
      conversionRate: 75,
      dropOffRate: 25,
      avgTimeSpent: 120,
      commonExitPoints: ['Hero section', 'Pricing section'],
      improvementOpportunities: ['Add more compelling CTAs', 'Improve value proposition clarity']
    },
    {
      id: 'trial',
      name: 'Trial Signup',
      description: 'User signs up for free trial',
      users: 2250,
      conversions: 2250,
      conversionRate: 30,
      dropOffRate: 70,
      avgTimeSpent: 180,
      commonExitPoints: ['Signup form', 'Social login'],
      improvementOpportunities: ['Simplify signup process', 'Add social proof']
    },
    {
      id: 'activation',
      name: 'First Lesson Completion',
      description: 'User completes their first lesson',
      users: 1575,
      conversions: 1575,
      conversionRate: 70,
      dropOffRate: 30,
      avgTimeSpent: 900,
      commonExitPoints: ['Code editor', 'Video tutorial'],
      improvementOpportunities: ['Improve onboarding flow', 'Add progress indicators']
    },
    {
      id: 'retention',
      name: 'Week 1 Engagement',
      description: 'User returns and engages in week 1',
      users: 1102,
      conversions: 1102,
      conversionRate: 70,
      dropOffRate: 30,
      avgTimeSpent: 1800,
      commonExitPoints: ['Lesson 3', 'Practice exercises'],
      improvementOpportunities: ['Send engagement emails', 'Add gamification']
    },
    {
      id: 'revenue',
      name: 'Paid Conversion',
      description: 'User converts to paid subscription',
      users: 331,
      conversions: 331,
      conversionRate: 30,
      dropOffRate: 70,
      avgTimeSpent: 2400,
      commonExitPoints: ['Pricing page', 'Payment form'],
      improvementOpportunities: ['Optimize pricing strategy', 'Add payment options']
    }
  ]);

  const [dropOffAnalysis, setDropOffAnalysis] = useState<DropOffAnalysis[]>([]);

  useEffect(() => {
    // Generate drop-off analysis
    const analysis = funnelData.map((step, index) => {
      if (index === 0) return null; // Skip first step

      const prevStep = funnelData[index - 1];
      const dropOffCount = prevStep.users - step.users;
      const dropOffRate = (dropOffCount / prevStep.users) * 100;

      return {
        stepId: step.id,
        stepName: step.name,
        dropOffCount,
        dropOffRate,
        reasons: [
          { reason: 'Unclear value proposition', percentage: 35, impact: 'high' as const },
          { reason: 'Too many form fields', percentage: 25, impact: 'medium' as const },
          { reason: 'Technical issues', percentage: 20, impact: 'high' as const },
          { reason: 'Price concerns', percentage: 20, impact: 'medium' as const }
        ],
        recommendations: [
          { action: 'Simplify signup process', expectedImpact: 15, effort: 'medium' as const },
          { action: 'Add social proof elements', expectedImpact: 10, effort: 'low' as const },
          { action: 'Improve page performance', expectedImpact: 12, effort: 'high' as const }
        ]
      };
    }).filter(Boolean) as DropOffAnalysis[];

    setDropOffAnalysis(analysis);
  }, [funnelData]);

  const updateFunnelData = useCallback((stepId: string, updates: Partial<FunnelStep>) => {
    setFunnelData(prev => prev.map(step => 
      step.id === stepId ? { ...step, ...updates } : step
    ));
  }, []);

  return {
    funnelData,
    dropOffAnalysis,
    updateFunnelData
  };
}

// Feedback Widget Hook
export function useFeedbackWidget() {
  const [activeWidget, setActiveWidget] = useState<FeedbackWidget | null>(null);
  const [feedbackHistory, setFeedbackHistory] = useState<UserFeedback[]>([]);
  const [isVisible, setIsVisible] = useState(false);

  const widgets: FeedbackWidget[] = [
    {
      id: 'post-lesson-nps',
      type: 'nps',
      trigger: {
        event: 'lesson_completed',
        delay: 2000
      },
      content: {
        title: 'How likely are you to recommend our platform?',
        questions: [
          {
            id: 'nps_score',
            type: 'scale',
            question: 'On a scale of 0-10, how likely are you to recommend us?',
            required: true
          },
          {
            id: 'nps_reason',
            type: 'text',
            question: 'What\'s the main reason for your score?',
            required: false
          }
        ]
      },
      styling: {
        position: 'bottom-right',
        theme: 'dark',
        size: 'standard'
      }
    },
    {
      id: 'trial-extension-feedback',
      type: 'feedback',
      trigger: {
        event: 'trial_extension_offered',
        conditions: { engagement_score: { min: 70 } }
      },
      content: {
        title: 'Help us improve your experience',
        description: 'Quick feedback to make your learning journey better',
        questions: [
          {
            id: 'satisfaction',
            type: 'rating',
            question: 'How satisfied are you with the platform so far?',
            required: true
          },
          {
            id: 'improvement',
            type: 'choice',
            question: 'What would improve your experience most?',
            options: ['More interactive content', 'Better explanations', 'More practice exercises', 'Faster loading'],
            required: true
          },
          {
            id: 'additional_feedback',
            type: 'text',
            question: 'Any additional feedback?',
            required: false
          }
        ]
      },
      styling: {
        position: 'center',
        theme: 'dark',
        size: 'expanded'
      }
    }
  ];

  const triggerWidget = useCallback((event: string, context?: any) => {
    const widget = widgets.find(w => w.trigger.event === event);
    if (!widget) return;

    // Check conditions
    if (widget.trigger.conditions) {
      const meetsConditions = Object.entries(widget.trigger.conditions).every(([key, condition]) => {
        if (typeof condition === 'object' && condition.min !== undefined) {
          return context?.[key] >= condition.min;
        }
        return context?.[key] === condition;
      });

      if (!meetsConditions) return;
    }

    // Apply delay if specified
    const delay = widget.trigger.delay || 0;
    setTimeout(() => {
      setActiveWidget(widget);
      setIsVisible(true);
    }, delay);
  }, [widgets]);

  const submitFeedback = useCallback((responses: Record<string, any>) => {
    if (!activeWidget) return;

    const feedback: UserFeedback = {
      id: Date.now().toString(),
      userId: 'current-user', // Would come from auth context
      widgetId: activeWidget.id,
      responses,
      sentiment: calculateSentiment(responses),
      category: categorizeFeedback(responses),
      priority: calculatePriority(responses),
      timestamp: new Date(),
      context: {
        page: window.location.pathname,
        userAgent: navigator.userAgent,
        sessionDuration: Date.now() - parseInt(sessionStorage.getItem('session_start') || '0'),
        previousActions: JSON.parse(sessionStorage.getItem('user_actions') || '[]')
      }
    };

    setFeedbackHistory(prev => [...prev, feedback]);
    setIsVisible(false);
    setActiveWidget(null);

    // Track with analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'feedback_submitted', {
        widget_id: activeWidget.id,
        sentiment: feedback.sentiment,
        category: feedback.category,
        priority: feedback.priority
      });
    }
  }, [activeWidget]);

  const dismissWidget = useCallback(() => {
    setIsVisible(false);
    setActiveWidget(null);
  }, []);

  return {
    activeWidget,
    isVisible,
    feedbackHistory,
    triggerWidget,
    submitFeedback,
    dismissWidget
  };
}

// Helper functions
function calculateSentiment(responses: Record<string, any>): 'positive' | 'neutral' | 'negative' {
  // Simple sentiment calculation based on ratings and NPS scores
  const npsScore = responses.nps_score;
  const satisfaction = responses.satisfaction;

  if (npsScore !== undefined) {
    if (npsScore >= 9) return 'positive';
    if (npsScore <= 6) return 'negative';
    return 'neutral';
  }

  if (satisfaction !== undefined) {
    if (satisfaction >= 4) return 'positive';
    if (satisfaction <= 2) return 'negative';
    return 'neutral';
  }

  return 'neutral';
}

function categorizeFeedback(responses: Record<string, any>): 'ux' | 'content' | 'technical' | 'feature_request' | 'bug' {
  // Simple categorization based on response content
  const improvement = responses.improvement;
  const feedback = responses.additional_feedback?.toLowerCase() || '';

  if (feedback.includes('bug') || feedback.includes('error') || feedback.includes('broken')) {
    return 'bug';
  }

  if (improvement === 'More interactive content' || improvement === 'More practice exercises') {
    return 'content';
  }

  if (improvement === 'Faster loading' || feedback.includes('slow') || feedback.includes('performance')) {
    return 'technical';
  }

  if (feedback.includes('feature') || feedback.includes('add') || feedback.includes('would like')) {
    return 'feature_request';
  }

  return 'ux';
}

function calculatePriority(responses: Record<string, any>): 'low' | 'medium' | 'high' | 'critical' {
  const sentiment = calculateSentiment(responses);
  const category = categorizeFeedback(responses);

  if (category === 'bug' && sentiment === 'negative') return 'critical';
  if (category === 'technical' && sentiment === 'negative') return 'high';
  if (sentiment === 'negative') return 'medium';
  if (category === 'feature_request') return 'low';

  return 'medium';
}

// Funnel Visualization Component
export function FunnelVisualization({ className }: { className?: string }) {
  const { funnelData, dropOffAnalysis } = useConversionFunnel();

  return (
    <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 p-6', className)}>
      <h3 className="text-lg font-semibold text-white mb-6 flex items-center space-x-2">
        <TrendingDown className="w-5 h-5" />
        <span>Conversion Funnel Analysis</span>
      </h3>

      <div className="space-y-6">
        {funnelData.map((step, index) => {
          const isFirst = index === 0;
          const dropOff = dropOffAnalysis.find(d => d.stepId === step.id);

          return (
            <motion.div
              key={step.id}
              className="relative"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
            >
              {/* Funnel Step */}
              <div className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="text-white font-medium">{step.name}</h4>
                    <p className="text-gray-400 text-sm">{step.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-white">{step.users.toLocaleString()}</div>
                    <div className="text-gray-400 text-sm">users</div>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 text-center">
                  <div>
                    <div className="text-lg font-bold text-blue-400">{step.conversionRate.toFixed(1)}%</div>
                    <div className="text-gray-400 text-xs">Conversion Rate</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-yellow-400">{Math.floor(step.avgTimeSpent / 60)}:{(step.avgTimeSpent % 60).toString().padStart(2, '0')}</div>
                    <div className="text-gray-400 text-xs">Avg Time</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-red-400">{step.dropOffRate.toFixed(1)}%</div>
                    <div className="text-gray-400 text-xs">Drop-off Rate</div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mt-4">
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-blue-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${step.conversionRate}%` }}
                    />
                  </div>
                </div>
              </div>

              {/* Drop-off Analysis */}
              {dropOff && (
                <motion.div
                  className="mt-4 bg-red-500/10 border border-red-500/30 rounded-lg p-4"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  transition={{ duration: 0.3 }}
                >
                  <h5 className="text-red-400 font-medium mb-3 flex items-center space-x-2">
                    <AlertCircle className="w-4 h-4" />
                    <span>Drop-off Analysis: {dropOff.dropOffCount.toLocaleString()} users ({dropOff.dropOffRate.toFixed(1)}%)</span>
                  </h5>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    <div>
                      <h6 className="text-white text-sm font-medium mb-2">Top Reasons</h6>
                      <div className="space-y-2">
                        {dropOff.reasons.map((reason, idx) => (
                          <div key={idx} className="flex items-center justify-between text-sm">
                            <span className="text-gray-300">{reason.reason}</span>
                            <span className={cn(
                              'font-medium',
                              reason.impact === 'high' ? 'text-red-400' :
                              reason.impact === 'medium' ? 'text-yellow-400' :
                              'text-green-400'
                            )}>
                              {reason.percentage}%
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h6 className="text-white text-sm font-medium mb-2">Recommendations</h6>
                      <div className="space-y-2">
                        {dropOff.recommendations.map((rec, idx) => (
                          <div key={idx} className="text-sm">
                            <div className="text-gray-300">{rec.action}</div>
                            <div className="flex items-center space-x-2 text-xs">
                              <span className="text-green-400">+{rec.expectedImpact}% impact</span>
                              <span className={cn(
                                rec.effort === 'low' ? 'text-green-400' :
                                rec.effort === 'medium' ? 'text-yellow-400' :
                                'text-red-400'
                              )}>
                                {rec.effort} effort
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </motion.div>
          );
        })}
      </div>
    </div>
  );
}
