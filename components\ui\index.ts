// Export all UI components
export * from './Accessibility';
export * from './badge';
export * from './Branding';
export * from './button';
export * from './EnhancedButton';
export * from './ErrorMessage';
export * from './ErrorTesting';
export * from './FeedbackIndicators';
export * from './LoadingSpinner';
export * from './card';
export * from './dialog';
export * from './dropdown-menu';
export * from './ErrorHandling';
export * from './LoadingStates';
export * from './Onboarding';
export * from './PageTransition';
export * from './progress';
export * from './toast';
export * from './Tooltip';
export * from './slider';
export * from './checkbox';
export * from './textarea';
export * from './input';
export * from './label';

// Glassmorphism & Neumorphism components
export * from './Glassmorphism';
export * from './Neumorphism';
export * from './AdvancedAnimations';

// Default exports
export { default as SkipLink } from './Accessibility';
export { Badge } from './badge';
export { default as Logo } from './Branding';
export { Button } from './button';
export { Card } from './card';
export { Dialog } from './dialog';
export { DropdownMenu } from './dropdown-menu';
export { default as ErrorBoundary } from './ErrorHandling';
export { default as LoadingSpinner } from './LoadingStates';
export { default as OnboardingProvider } from './Onboarding';
export { default as PageTransition } from './PageTransition';
export { Progress } from './progress';
export { default as Tooltip } from './Tooltip';
export { Slider } from './slider';
export { Checkbox } from './checkbox';
export { Textarea } from './textarea';
export { Input } from './input';
export { Label } from './label';

// Advanced Animation Components
export * from './GSAPAnimations';
export * from './LottieAnimations';
export * from './ThreeJSComponents';
export * from './SVGAnimations';
export * from './AnimationShowcase';

// Glassmorphism & Neumorphism default exports
export { default as GlassContainer } from './Glassmorphism';
export { default as NeumorphicContainer } from './Neumorphism';
export { default as GlassNeumorphDemo } from './GlassNeumorphDemo';

// Learning Platform Components
export { default as InteractiveCodeEditor } from '../learning/InteractiveCodeEditor';
export { default as GamificationSystem } from '../learning/GamificationSystem';
export { default as StructuredCurriculum } from '../learning/StructuredCurriculum';
export { default as ProjectBasedLearning } from '../learning/ProjectBasedLearning';
export { default as ComprehensiveLearningPlatform } from '../learning/ComprehensiveLearningPlatform';
