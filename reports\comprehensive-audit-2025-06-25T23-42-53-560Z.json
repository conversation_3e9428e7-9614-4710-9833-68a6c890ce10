{"timestamp": "2025-06-25T23:42:53.558Z", "summary": {"bundleAnalysis": {"buildExists": true, "bundleSizes": {"javascript": 17320}, "budgetViolations": [{"type": "script", "actual": 17320, "budget": 400, "excess": 16920}], "recommendations": [{"type": "javascript", "priority": "high", "issue": "JavaScript bundle size exceeds 400KB", "suggestion": "Implement code splitting, remove unused dependencies, and use dynamic imports"}]}, "dependencyAnalysis": {"totalDependencies": 76, "devDependencies": 23, "heavyDependencies": ["monaco-editor", "three", "@google/genai", "framer-motion", "react-syntax-highlighter"], "securityIssues": [], "recommendations": [{"type": "performance", "priority": "medium", "issue": "High number of dependencies (76)", "suggestion": "Audit dependencies and remove unused packages"}]}, "accessibilityAnalysis": {"wcagCompliance": "tools-available", "issues": [], "recommendations": []}}, "overallScore": 55, "criticalIssues": [], "recommendations": [{"type": "javascript", "priority": "high", "issue": "JavaScript bundle size exceeds 400KB", "suggestion": "Implement code splitting, remove unused dependencies, and use dynamic imports"}, {"type": "performance", "priority": "medium", "issue": "High number of dependencies (76)", "suggestion": "Audit dependencies and remove unused packages"}]}