{"config": {"configFile": "C:\\Users\\<USER>\\OneDrive\\Desktop\\learn_sol\\playwright.config.ts", "rootDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "C:\\Users\\<USER>\\OneDrive\\Desktop\\learn_sol\\__tests__\\e2e\\global-setup.ts", "globalTeardown": "C:\\Users\\<USER>\\OneDrive\\Desktop\\learn_sol\\__tests__\\e2e\\global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "test-results/html-report"}], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/junit.xml"}], ["line", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "setup", "name": "setup", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["/.*\\.setup\\.ts/"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "performance", "name": "performance", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["/.*\\.perf\\.ts/"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "security", "name": "security", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["/.*\\.security\\.ts/"], "timeout": 60000}, {"outputDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "api", "name": "api", "testDir": "C:/Users/<USER>/OneDrive/Desktop/learn_sol/__tests__/e2e", "testIgnore": [], "testMatch": ["/.*\\.api\\.ts/"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000, "env": {"NODE_ENV": "test"}}}, "suites": [], "errors": [{"message": "Error: page.goto: Protocol error (Page.navigate): Cannot navigate to invalid URL\nCall log:\n\u001b[2m  - navigating to \"/\", waiting until \"load\"\u001b[22m\n", "stack": "Error: page.goto: Protocol error (Page.navigate): Cannot navigate to invalid URL\nCall log:\n\u001b[2m  - navigating to \"/\", waiting until \"load\"\u001b[22m\n\n    at setupTestUsers (C:\\Users\\<USER>\\OneDrive\\Desktop\\learn_sol\\__tests__\\e2e\\global-setup.ts:40:16)\n    at globalSetup (C:\\Users\\<USER>\\OneDrive\\Desktop\\learn_sol\\__tests__\\e2e\\global-setup.ts:16:5)", "location": {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\learn_sol\\__tests__\\e2e\\global-setup.ts", "column": 16, "line": 40}, "snippet": "\u001b[90m   at \u001b[39mglobal-setup.ts:40\n\n\u001b[0m \u001b[90m 38 |\u001b[39m   \u001b[36mtry\u001b[39m {\n \u001b[90m 39 |\u001b[39m     \u001b[90m// Navigate to the application\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 40 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'/'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 41 |\u001b[39m\n \u001b[90m 42 |\u001b[39m     \u001b[90m// Test users to create\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[36mconst\u001b[39m testUsers \u001b[33m=\u001b[39m [\u001b[0m"}], "stats": {"startTime": "2025-06-25T23:39:51.042Z", "duration": 1877.955, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}