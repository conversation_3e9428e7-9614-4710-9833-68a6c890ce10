'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Save, 
  Clock, 
  Users, 
  Trophy, 
  Star, 
  AlertTriangle,
  CheckCircle,
  X,
  Github,
  Mail,
  Chrome,
  Shield,
  Zap,
  TrendingUp,
  Award,
  Target
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useProgressTracking } from '@/components/progress/ProgressTrackingSystem';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface SaveProgressSystemProps {
  className?: string;
  triggerEvents?: ('compilation' | 'achievement' | 'demo_complete' | 'time_spent' | 'milestone')[];
  showSocialProof?: boolean;
  enableUrgency?: boolean;
  guestSessionDuration?: number; // in minutes
}

interface GuestSession {
  startTime: Date;
  lastActivity: Date;
  progress: any;
  expiresAt: Date;
  warningShown: boolean;
  finalWarningShown: boolean;
}

export function SaveProgressSystem({
  className,
  triggerEvents = ['compilation', 'achievement', 'demo_complete', 'time_spent'],
  showSocialProof = true,
  enableUrgency = true,
  guestSessionDuration = 60 // 1 hour default
}: SaveProgressSystemProps) {
  const [showSavePrompt, setShowSavePrompt] = useState(false);
  const [showAccountModal, setShowAccountModal] = useState(false);
  const [guestSession, setGuestSession] = useState<GuestSession | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [triggerReason, setTriggerReason] = useState<string>('');

  const { userProgress, saveProgress } = useProgressTracking();

  // Initialize guest session
  useEffect(() => {
    const checkAuthStatus = () => {
      // Check if user is authenticated (implement your auth logic here)
      const authToken = localStorage.getItem('auth_token');
      setIsAuthenticated(!!authToken);
      
      if (!authToken) {
        initializeGuestSession();
      }
    };

    checkAuthStatus();
  }, []);

  const initializeGuestSession = useCallback(() => {
    const existingSession = localStorage.getItem('guest_session');
    
    if (existingSession) {
      try {
        const session = JSON.parse(existingSession);
        const expiresAt = new Date(session.expiresAt);
        
        if (expiresAt > new Date()) {
          setGuestSession({
            ...session,
            startTime: new Date(session.startTime),
            lastActivity: new Date(session.lastActivity),
            expiresAt
          });
        } else {
          // Session expired, create new one
          createNewGuestSession();
        }
      } catch (error) {
        createNewGuestSession();
      }
    } else {
      createNewGuestSession();
    }
  }, [guestSessionDuration]);

  const createNewGuestSession = () => {
    const now = new Date();
    const expiresAt = new Date(now.getTime() + guestSessionDuration * 60 * 1000);
    
    const newSession: GuestSession = {
      startTime: now,
      lastActivity: now,
      progress: userProgress,
      expiresAt,
      warningShown: false,
      finalWarningShown: false
    };
    
    setGuestSession(newSession);
    localStorage.setItem('guest_session', JSON.stringify(newSession));
  };

  // Update guest session activity
  const updateGuestActivity = useCallback(() => {
    if (guestSession && !isAuthenticated) {
      const updatedSession = {
        ...guestSession,
        lastActivity: new Date(),
        progress: userProgress
      };
      
      setGuestSession(updatedSession);
      localStorage.setItem('guest_session', JSON.stringify(updatedSession));
    }
  }, [guestSession, userProgress, isAuthenticated]);

  // Timer for guest session expiration
  useEffect(() => {
    if (!guestSession || isAuthenticated) return;

    const updateTimer = () => {
      const now = new Date();
      const timeLeft = guestSession.expiresAt.getTime() - now.getTime();
      
      if (timeLeft <= 0) {
        setTimeRemaining('Expired');
        if (!showSavePrompt) {
          triggerSavePrompt('session_expired');
        }
        return;
      }

      const minutes = Math.floor(timeLeft / (1000 * 60));
      const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);
      
      setTimeRemaining(`${minutes}:${seconds.toString().padStart(2, '0')}`);

      // Show warnings
      if (timeLeft <= 5 * 60 * 1000 && !guestSession.finalWarningShown) {
        // 5 minutes left - final warning
        setGuestSession(prev => prev ? { ...prev, finalWarningShown: true } : null);
        triggerSavePrompt('final_warning');
      } else if (timeLeft <= 15 * 60 * 1000 && !guestSession.warningShown) {
        // 15 minutes left - first warning
        setGuestSession(prev => prev ? { ...prev, warningShown: true } : null);
        triggerSavePrompt('warning');
      }
    };

    updateTimer();
    const interval = setInterval(updateTimer, 1000);

    return () => clearInterval(interval);
  }, [guestSession, isAuthenticated, showSavePrompt]);

  // Trigger save prompt based on events
  const triggerSavePrompt = useCallback((reason: string) => {
    if (isAuthenticated) return;
    
    setTriggerReason(reason);
    setShowSavePrompt(true);
    
    // Auto-hide after 10 seconds unless it's a warning
    if (!reason.includes('warning') && reason !== 'session_expired') {
      setTimeout(() => {
        setShowSavePrompt(false);
      }, 10000);
    }
  }, [isAuthenticated]);

  // Listen for trigger events
  useEffect(() => {
    const handleTriggerEvent = (event: CustomEvent) => {
      const { type, data } = event.detail;
      
      if (triggerEvents.includes(type)) {
        updateGuestActivity();
        
        switch (type) {
          case 'compilation':
            triggerSavePrompt('successful_compilation');
            break;
          case 'achievement':
            triggerSavePrompt('achievement_unlocked');
            break;
          case 'demo_complete':
            triggerSavePrompt('demo_completed');
            break;
          case 'milestone':
            triggerSavePrompt('milestone_reached');
            break;
        }
      }
    };

    window.addEventListener('progress_event', handleTriggerEvent as EventListener);
    
    return () => {
      window.removeEventListener('progress_event', handleTriggerEvent as EventListener);
    };
  }, [triggerEvents, updateGuestActivity, triggerSavePrompt]);

  // Time spent trigger
  useEffect(() => {
    if (!guestSession || isAuthenticated) return;

    const checkTimeSpent = () => {
      const timeSpent = Date.now() - guestSession.startTime.getTime();
      const minutes = Math.floor(timeSpent / (1000 * 60));
      
      // Trigger at 5, 15, and 30 minutes
      if ([5, 15, 30].includes(minutes)) {
        triggerSavePrompt('time_spent');
      }
    };

    const interval = setInterval(checkTimeSpent, 60000); // Check every minute
    return () => clearInterval(interval);
  }, [guestSession, isAuthenticated, triggerSavePrompt]);

  const getPromptMessage = () => {
    switch (triggerReason) {
      case 'successful_compilation':
        return {
          title: '🎉 Great job compiling!',
          message: 'Save your progress to keep building on your success.',
          urgency: 'medium'
        };
      case 'achievement_unlocked':
        return {
          title: '🏆 Achievement unlocked!',
          message: 'Don\'t lose your achievements - save your progress now.',
          urgency: 'high'
        };
      case 'demo_completed':
        return {
          title: '✅ Demo completed!',
          message: 'You\'re making great progress. Save it to continue your journey.',
          urgency: 'medium'
        };
      case 'milestone_reached':
        return {
          title: '🎯 Milestone reached!',
          message: 'You\'ve hit an important milestone. Secure your progress!',
          urgency: 'high'
        };
      case 'time_spent':
        return {
          title: '⏰ You\'ve been learning for a while',
          message: 'Save your progress so you don\'t lose your hard work.',
          urgency: 'medium'
        };
      case 'warning':
        return {
          title: '⚠️ Session expiring soon',
          message: `Only ${timeRemaining} left! Save your progress now.`,
          urgency: 'high'
        };
      case 'final_warning':
        return {
          title: '🚨 Last chance!',
          message: `Your session expires in ${timeRemaining}. Save now or lose your progress!`,
          urgency: 'critical'
        };
      case 'session_expired':
        return {
          title: '❌ Session expired',
          message: 'Your guest session has expired. Create an account to restore your progress.',
          urgency: 'critical'
        };
      default:
        return {
          title: '💾 Save your progress',
          message: 'Create an account to keep your learning progress safe.',
          urgency: 'medium'
        };
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical':
        return 'from-red-600 to-red-800 border-red-500';
      case 'high':
        return 'from-orange-600 to-red-600 border-orange-500';
      case 'medium':
        return 'from-blue-600 to-purple-600 border-blue-500';
      default:
        return 'from-gray-600 to-gray-800 border-gray-500';
    }
  };

  const handleCreateAccount = (provider: 'email' | 'github' | 'google') => {
    // Implement account creation logic here
    console.log(`Creating account with ${provider}`);
    setShowAccountModal(true);
  };

  const promptData = getPromptMessage();

  if (isAuthenticated) {
    return null; // Don't show for authenticated users
  }

  return (
    <div className={className}>
      {/* Save Progress Prompt */}
      <AnimatePresence>
        {showSavePrompt && (
          <motion.div
            className="fixed bottom-4 right-4 z-50 max-w-sm"
            initial={{ opacity: 0, x: 100, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 100, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <div className={cn(
              'bg-gradient-to-br backdrop-blur-md rounded-lg border shadow-xl p-4',
              getUrgencyColor(promptData.urgency)
            )}>
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h3 className="font-semibold text-white text-sm mb-1">
                    {promptData.title}
                  </h3>
                  <p className="text-white/90 text-xs leading-relaxed">
                    {promptData.message}
                  </p>
                </div>
                <button
                  onClick={() => setShowSavePrompt(false)}
                  className="text-white/60 hover:text-white transition-colors ml-2"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>

              {/* Progress Preview */}
              <div className="bg-white/10 rounded-lg p-3 mb-3">
                <div className="grid grid-cols-3 gap-3 text-center">
                  <div>
                    <div className="text-lg font-bold text-white">{userProgress.level}</div>
                    <div className="text-xs text-white/70">Level</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-white">{userProgress.totalXP}</div>
                    <div className="text-xs text-white/70">XP</div>
                  </div>
                  <div>
                    <div className="text-lg font-bold text-white">{userProgress.completedMilestones.length}</div>
                    <div className="text-xs text-white/70">Achievements</div>
                  </div>
                </div>
              </div>

              {/* Social Proof */}
              {showSocialProof && (
                <div className="bg-white/10 rounded-lg p-2 mb-3">
                  <div className="flex items-center space-x-2 text-xs text-white/80">
                    <Users className="w-3 h-3" />
                    <span>Join 10,000+ developers who saved their progress</span>
                  </div>
                </div>
              )}

              {/* Session Timer */}
              {guestSession && timeRemaining && (
                <div className="flex items-center justify-center space-x-2 mb-3 p-2 bg-white/10 rounded-lg">
                  <Clock className="w-4 h-4 text-white" />
                  <span className="text-sm text-white font-medium">
                    Session expires in: {timeRemaining}
                  </span>
                </div>
              )}

              {/* Action Buttons */}
              <div className="space-y-2">
                <EnhancedButton
                  onClick={() => setShowAccountModal(true)}
                  className="w-full bg-white hover:bg-gray-100 text-gray-900 font-medium"
                  touchTarget
                >
                  <Save className="w-4 h-4 mr-2" />
                  Save My Progress
                </EnhancedButton>
                
                <div className="grid grid-cols-3 gap-2">
                  <EnhancedButton
                    onClick={() => handleCreateAccount('github')}
                    variant="ghost"
                    size="sm"
                    className="text-white border-white/20 hover:bg-white/10"
                  >
                    <Github className="w-4 h-4" />
                  </EnhancedButton>
                  <EnhancedButton
                    onClick={() => handleCreateAccount('google')}
                    variant="ghost"
                    size="sm"
                    className="text-white border-white/20 hover:bg-white/10"
                  >
                    <Chrome className="w-4 h-4" />
                  </EnhancedButton>
                  <EnhancedButton
                    onClick={() => handleCreateAccount('email')}
                    variant="ghost"
                    size="sm"
                    className="text-white border-white/20 hover:bg-white/10"
                  >
                    <Mail className="w-4 h-4" />
                  </EnhancedButton>
                </div>
              </div>

              {/* Benefits */}
              <div className="mt-3 text-xs text-white/70">
                <div className="flex items-center space-x-1 mb-1">
                  <CheckCircle className="w-3 h-3" />
                  <span>Keep your progress forever</span>
                </div>
                <div className="flex items-center space-x-1 mb-1">
                  <CheckCircle className="w-3 h-3" />
                  <span>Access from any device</span>
                </div>
                <div className="flex items-center space-x-1">
                  <CheckCircle className="w-3 h-3" />
                  <span>Join the leaderboard</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Account Creation Modal */}
      <AnimatePresence>
        {showAccountModal && (
          <motion.div
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setShowAccountModal(false)}
          >
            <motion.div
              className="bg-white rounded-lg shadow-2xl max-w-md w-full overflow-hidden"
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.8, opacity: 0 }}
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-bold">Save Your Progress</h2>
                  <button
                    onClick={() => setShowAccountModal(false)}
                    className="text-white/80 hover:text-white transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>
                <p className="text-blue-100 text-sm mt-2">
                  Create an account to keep your learning progress safe and access exclusive features.
                </p>
              </div>

              {/* Content */}
              <div className="p-6">
                {/* Progress Summary */}
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <h3 className="font-semibold text-gray-900 mb-3">Your Progress So Far</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{userProgress.level}</div>
                      <div className="text-sm text-gray-600">Level Reached</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">{userProgress.totalXP}</div>
                      <div className="text-sm text-gray-600">XP Earned</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">{userProgress.completedMilestones.length}</div>
                      <div className="text-sm text-gray-600">Achievements</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-orange-600">{userProgress.currentStreak}</div>
                      <div className="text-sm text-gray-600">Day Streak</div>
                    </div>
                  </div>
                </div>

                {/* Account Creation Options */}
                <div className="space-y-3">
                  <EnhancedButton
                    onClick={() => handleCreateAccount('github')}
                    className="w-full bg-gray-900 hover:bg-gray-800 text-white"
                    touchTarget
                  >
                    <Github className="w-5 h-5 mr-3" />
                    Continue with GitHub
                  </EnhancedButton>

                  <EnhancedButton
                    onClick={() => handleCreateAccount('google')}
                    className="w-full bg-white hover:bg-gray-50 text-gray-900 border border-gray-300"
                    touchTarget
                  >
                    <Chrome className="w-5 h-5 mr-3" />
                    Continue with Google
                  </EnhancedButton>

                  <EnhancedButton
                    onClick={() => handleCreateAccount('email')}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                    touchTarget
                  >
                    <Mail className="w-5 h-5 mr-3" />
                    Continue with Email
                  </EnhancedButton>
                </div>

                {/* Benefits */}
                <div className="mt-6 space-y-3">
                  <h4 className="font-semibold text-gray-900">What you get:</h4>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-3">
                      <Shield className="w-5 h-5 text-green-500" />
                      <span className="text-sm text-gray-700">Progress saved forever</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Trophy className="w-5 h-5 text-yellow-500" />
                      <span className="text-sm text-gray-700">Access to leaderboards</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Award className="w-5 h-5 text-purple-500" />
                      <span className="text-sm text-gray-700">Exclusive achievements</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Target className="w-5 h-5 text-blue-500" />
                      <span className="text-sm text-gray-700">Personalized learning paths</span>
                    </div>
                  </div>
                </div>

                {/* Social Proof */}
                {showSocialProof && (
                  <div className="mt-6 p-4 bg-blue-50 rounded-lg">
                    <div className="flex items-center space-x-2 text-blue-800">
                      <Users className="w-5 h-5" />
                      <span className="font-medium">Join 10,000+ developers</span>
                    </div>
                    <p className="text-blue-700 text-sm mt-1">
                      Who have saved their progress and accelerated their Solidity learning journey.
                    </p>
                  </div>
                )}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
