'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

export interface UserSkillLevel {
  level: 'beginner' | 'intermediate' | 'advanced';
  programmingExperience: boolean;
  blockchainKnowledge: boolean;
  preferredPace: 'relaxed' | 'standard' | 'intensive';
}

// Skill Assessment Modal for Personalization
export function SkillAssessmentModal({
  isOpen,
  onClose,
  onComplete
}: {
  isOpen: boolean;
  onClose: () => void;
  onComplete: (skillLevel: UserSkillLevel) => void;
}) {
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState({
    programmingExperience: null as boolean | null,
    blockchainKnowledge: null as boolean | null,
    preferredPace: null as 'relaxed' | 'standard' | 'intensive' | null,
    timeAvailable: null as string | null,
    goals: [] as string[]
  });

  const questions = [
    {
      id: 'programming',
      title: 'Programming Experience',
      question: 'Do you have prior programming experience?',
      type: 'boolean',
      options: [
        { 
          value: true, 
          label: 'Yes, I have programming experience', 
          description: 'Familiar with variables, functions, and basic programming concepts',
          icon: '💻'
        },
        { 
          value: false, 
          label: 'No, I\'m new to programming', 
          description: 'This will be my first programming language',
          icon: '🌱'
        }
      ]
    },
    {
      id: 'blockchain',
      title: 'Blockchain Knowledge',
      question: 'How familiar are you with blockchain technology?',
      type: 'boolean',
      options: [
        { 
          value: true, 
          label: 'I understand blockchain basics', 
          description: 'Know about blocks, transactions, and decentralization',
          icon: '⛓️'
        },
        { 
          value: false, 
          label: 'Blockchain is new to me', 
          description: 'I want to learn from the ground up',
          icon: '🔍'
        }
      ]
    },
    {
      id: 'pace',
      title: 'Learning Pace',
      question: 'What learning pace works best for you?',
      type: 'pace',
      options: [
        { 
          value: 'relaxed', 
          label: 'Relaxed (1-2 hours/day)', 
          description: 'I prefer to take my time and learn thoroughly',
          icon: '🐢'
        },
        { 
          value: 'standard', 
          label: 'Standard (2-3 hours/day)', 
          description: 'Balanced approach with steady progress',
          icon: '🚶'
        },
        { 
          value: 'intensive', 
          label: 'Intensive (3-4 hours/day)', 
          description: 'I want to learn as quickly as possible',
          icon: '🏃'
        }
      ]
    }
  ];

  const currentQuestion = questions[currentStep];

  const handleAnswer = (value: any) => {
    setAnswers(prev => ({
      ...prev,
      [currentQuestion.id === 'programming' ? 'programmingExperience' :
       currentQuestion.id === 'blockchain' ? 'blockchainKnowledge' :
       'preferredPace']: value
    }));
  };

  const handleNext = () => {
    if (currentStep < questions.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete assessment
      const skillLevel: UserSkillLevel = {
        level: answers.programmingExperience ? 'intermediate' : 'beginner',
        programmingExperience: answers.programmingExperience || false,
        blockchainKnowledge: answers.blockchainKnowledge || false,
        preferredPace: answers.preferredPace || 'standard'
      };
      onComplete(skillLevel);
      onClose();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const getCurrentAnswer = () => {
    switch (currentQuestion.id) {
      case 'programming':
        return answers.programmingExperience;
      case 'blockchain':
        return answers.blockchainKnowledge;
      case 'pace':
        return answers.preferredPace;
      default:
        return null;
    }
  };

  const isAnswered = getCurrentAnswer() !== null;

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-gray-900 rounded-lg shadow-2xl max-w-2xl w-full border border-gray-700"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="p-6 border-b border-gray-700">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-xl font-semibold text-white">Personalize Your Learning Path</h3>
                <p className="text-gray-400 text-sm mt-1">
                  Step {currentStep + 1} of {questions.length} • This helps us customize your experience
                </p>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-800 rounded-lg"
                aria-label="Close assessment"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            {/* Progress Bar */}
            <div className="w-full bg-gray-700 rounded-full h-2 mt-4">
              <motion.div
                className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${((currentStep + 1) / questions.length) * 100}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="mb-8 text-center">
                  <h4 className="text-2xl font-bold text-white mb-3">
                    {currentQuestion.title}
                  </h4>
                  <p className="text-gray-300 text-lg">
                    {currentQuestion.question}
                  </p>
                </div>

                <div className="space-y-4">
                  {currentQuestion.options.map((option) => (
                    <motion.button
                      key={option.value.toString()}
                      onClick={() => handleAnswer(option.value)}
                      className={cn(
                        'w-full p-6 rounded-lg border text-left transition-all duration-200 group',
                        getCurrentAnswer() === option.value
                          ? 'border-blue-500 bg-blue-500/20 text-blue-300 shadow-lg shadow-blue-500/25'
                          : 'border-gray-600 bg-gray-800/50 text-gray-300 hover:border-gray-500 hover:bg-gray-800 hover:shadow-lg'
                      )}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex items-start space-x-4">
                        <div className="text-3xl">{option.icon}</div>
                        <div className="flex-1">
                          <div className="font-semibold text-lg mb-2 group-hover:text-white transition-colors">
                            {option.label}
                          </div>
                          <div className="text-sm opacity-75 leading-relaxed">
                            {option.description}
                          </div>
                        </div>
                        <div className={cn(
                          'w-5 h-5 rounded-full border-2 flex items-center justify-center transition-all',
                          getCurrentAnswer() === option.value
                            ? 'border-blue-500 bg-blue-500'
                            : 'border-gray-500 group-hover:border-gray-400'
                        )}>
                          {getCurrentAnswer() === option.value && (
                            <motion.div
                              className="w-2 h-2 bg-white rounded-full"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ duration: 0.2 }}
                            />
                          )}
                        </div>
                      </div>
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            </AnimatePresence>
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-gray-700 flex justify-between">
            <EnhancedButton
              onClick={handlePrevious}
              disabled={currentStep === 0}
              variant="ghost"
              className="text-gray-400 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="w-4 h-4 mr-1" />
              Previous
            </EnhancedButton>
            
            <EnhancedButton
              onClick={handleNext}
              disabled={!isAnswered}
              className="bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {currentStep === questions.length - 1 ? 'Complete Assessment' : 'Next'}
              <ChevronRight className="w-4 h-4 ml-1" />
            </EnhancedButton>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

// Hook for learning path personalization
export function useLearningPathPersonalization() {
  const [userSkillLevel, setUserSkillLevel] = useState<UserSkillLevel | null>(null);

  useEffect(() => {
    // Load saved skill level
    const saved = localStorage.getItem('user_skill_level');
    if (saved) {
      try {
        setUserSkillLevel(JSON.parse(saved));
      } catch (error) {
        console.error('Failed to load skill level:', error);
      }
    }
  }, []);

  const updateSkillLevel = (skillLevel: UserSkillLevel) => {
    setUserSkillLevel(skillLevel);
    localStorage.setItem('user_skill_level', JSON.stringify(skillLevel));
  };

  const getPersonalizedTimeEstimate = (baseTime: string, skillLevel: UserSkillLevel) => {
    let hours = parseFloat(baseTime);
    
    // Adjust based on programming experience
    if (skillLevel.programmingExperience) {
      hours = Math.max(1, hours * 0.8);
    }

    // Adjust based on preferred pace
    if (skillLevel.preferredPace === 'intensive') {
      hours = hours * 1.3;
    } else if (skillLevel.preferredPace === 'relaxed') {
      hours = hours * 0.7;
    }

    return `${Math.round(hours * 10) / 10} hours`;
  };

  const getPersonalizedDifficulty = (baseDifficulty: string, skillLevel: UserSkillLevel) => {
    if (skillLevel.programmingExperience && baseDifficulty === 'beginner') {
      return 'easy';
    }
    return baseDifficulty;
  };

  const getRecommendedStartingPoint = (skillLevel: UserSkillLevel) => {
    if (skillLevel.programmingExperience && skillLevel.blockchainKnowledge) {
      return 'week-2'; // Skip some basics
    } else if (skillLevel.programmingExperience) {
      return 'day-2'; // Skip environment setup
    }
    return 'day-1'; // Start from the beginning
  };

  return {
    userSkillLevel,
    updateSkillLevel,
    getPersonalizedTimeEstimate,
    getPersonalizedDifficulty,
    getRecommendedStartingPoint
  };
}

// Personalization results component
export function PersonalizationResults({ 
  skillLevel, 
  className 
}: { 
  skillLevel: UserSkillLevel; 
  className?: string; 
}) {
  const getPersonalizedMessage = () => {
    if (skillLevel.programmingExperience && skillLevel.blockchainKnowledge) {
      return {
        title: "Perfect! You're ready for advanced concepts",
        description: "We'll focus on Solidity-specific features and advanced smart contract patterns.",
        timeReduction: "25% faster completion",
        startingPoint: "Week 2: Advanced Solidity"
      };
    } else if (skillLevel.programmingExperience) {
      return {
        title: "Great! Your programming background will help",
        description: "We'll skip basic programming concepts and focus on blockchain-specific knowledge.",
        timeReduction: "20% faster completion",
        startingPoint: "Day 2: Smart Contract Basics"
      };
    } else {
      return {
        title: "Welcome! We'll start from the fundamentals",
        description: "Our comprehensive curriculum will teach you both programming and blockchain concepts.",
        timeReduction: "Standard timeline",
        startingPoint: "Day 1: Programming & Blockchain Basics"
      };
    }
  };

  const message = getPersonalizedMessage();

  return (
    <motion.div
      className={cn('bg-gradient-to-r from-green-600/20 to-blue-600/20 rounded-lg p-6 border border-green-500/30', className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="flex items-start space-x-4">
        <div className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center flex-shrink-0">
          <span className="text-2xl">🎯</span>
        </div>
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-white mb-2">{message.title}</h3>
          <p className="text-gray-300 mb-4">{message.description}</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="bg-white/10 rounded-lg p-3">
              <div className="font-medium text-green-400 mb-1">Time Optimization</div>
              <div className="text-gray-300">{message.timeReduction}</div>
            </div>
            <div className="bg-white/10 rounded-lg p-3">
              <div className="font-medium text-blue-400 mb-1">Starting Point</div>
              <div className="text-gray-300">{message.startingPoint}</div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
