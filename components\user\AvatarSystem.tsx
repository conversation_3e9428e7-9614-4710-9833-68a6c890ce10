'use client';

import React, { useState, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  User, 
  Camera, 
  Upload, 
  Shuffle, 
  Check, 
  X, 
  Edit3,
  Crown,
  Star,
  Trophy,
  Zap,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface UserProfile {
  id: string;
  name: string;
  username: string;
  email: string;
  avatar: {
    type: 'initials' | 'upload' | 'generated';
    url?: string;
    initials?: string;
    backgroundColor?: string;
    style?: 'circle' | 'rounded' | 'square';
  };
  level: number;
  xp: number;
  achievements: string[];
  joinedAt: Date;
  location?: string;
  bio?: string;
  skills: string[];
  isVerified: boolean;
  isPremium: boolean;
  socialLinks?: {
    github?: string;
    linkedin?: string;
    twitter?: string;
  };
}

interface AvatarProps {
  user: UserProfile;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  showStatus?: boolean;
  showLevel?: boolean;
  showBadges?: boolean;
  className?: string;
  onClick?: () => void;
  editable?: boolean;
}

// Avatar size configurations
const avatarSizes = {
  xs: 'w-6 h-6 text-xs',
  sm: 'w-8 h-8 text-sm',
  md: 'w-10 h-10 text-sm',
  lg: 'w-12 h-12 text-base',
  xl: 'w-16 h-16 text-lg',
  '2xl': 'w-20 h-20 text-xl'
};

const badgeSizes = {
  xs: 'w-3 h-3',
  sm: 'w-3 h-3',
  md: 'w-4 h-4',
  lg: 'w-4 h-4',
  xl: 'w-5 h-5',
  '2xl': 'w-6 h-6'
};

// Generate avatar colors based on name
function generateAvatarColor(name: string): string {
  const colors = [
    'bg-gradient-to-br from-blue-500 to-blue-600',
    'bg-gradient-to-br from-green-500 to-green-600',
    'bg-gradient-to-br from-purple-500 to-purple-600',
    'bg-gradient-to-br from-pink-500 to-pink-600',
    'bg-gradient-to-br from-indigo-500 to-indigo-600',
    'bg-gradient-to-br from-red-500 to-red-600',
    'bg-gradient-to-br from-yellow-500 to-yellow-600',
    'bg-gradient-to-br from-teal-500 to-teal-600'
  ];
  
  const hash = name.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
  return colors[hash % colors.length];
}

// Generate initials from name
function generateInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);
}

// Main Avatar component
export function Avatar({
  user,
  size = 'md',
  showStatus = false,
  showLevel = false,
  showBadges = false,
  className,
  onClick,
  editable = false
}: AvatarProps) {
  const [isHovered, setIsHovered] = useState(false);

  const renderAvatarContent = () => {
    switch (user.avatar.type) {
      case 'upload':
        return (
          <img
            src={user.avatar.url}
            alt={user.name}
            className="w-full h-full object-cover"
          />
        );
      case 'generated':
        return (
          <img
            src={user.avatar.url}
            alt={user.name}
            className="w-full h-full object-cover"
          />
        );
      case 'initials':
      default:
        return (
          <span className={cn('font-semibold text-white', size === 'xs' ? 'text-xs' : '')}>
            {user.avatar.initials || generateInitials(user.name)}
          </span>
        );
    }
  };

  const getAvatarBackground = () => {
    if (user.avatar.type === 'upload' || user.avatar.type === 'generated') {
      return 'bg-gray-200';
    }
    return user.avatar.backgroundColor || generateAvatarColor(user.name);
  };

  return (
    <div 
      className={cn('relative inline-block', className)}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Main Avatar */}
      <motion.div
        className={cn(
          'relative flex items-center justify-center rounded-full overflow-hidden cursor-pointer',
          avatarSizes[size],
          getAvatarBackground(),
          onClick && 'hover:ring-2 hover:ring-blue-400 hover:ring-offset-2 hover:ring-offset-gray-900',
          'transition-all duration-200'
        )}
        onClick={onClick}
        whileHover={{ scale: onClick ? 1.05 : 1 }}
        whileTap={{ scale: onClick ? 0.95 : 1 }}
      >
        {renderAvatarContent()}
        
        {/* Edit overlay */}
        {editable && isHovered && (
          <motion.div
            className="absolute inset-0 bg-black/50 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <Camera className="w-4 h-4 text-white" />
          </motion.div>
        )}
      </motion.div>

      {/* Status indicator */}
      {showStatus && (
        <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-400 border-2 border-gray-900 rounded-full" />
      )}

      {/* Level badge */}
      {showLevel && (
        <motion.div
          className="absolute -top-1 -right-1 bg-gradient-to-r from-yellow-500 to-orange-500 text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center px-1"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2 }}
        >
          {user.level}
        </motion.div>
      )}

      {/* Badges */}
      {showBadges && (
        <div className="absolute -top-1 -left-1 flex space-x-1">
          {user.isPremium && (
            <Crown className={cn('text-yellow-400', badgeSizes[size])} />
          )}
          {user.isVerified && (
            <Star className={cn('text-blue-400', badgeSizes[size])} />
          )}
        </div>
      )}
    </div>
  );
}

// Avatar customization modal
export function AvatarCustomization({
  user,
  isOpen,
  onClose,
  onSave
}: {
  user: UserProfile;
  isOpen: boolean;
  onClose: () => void;
  onSave: (avatar: UserProfile['avatar']) => void;
}) {
  const [selectedType, setSelectedType] = useState<'initials' | 'upload' | 'generated'>(user.avatar.type);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [selectedColor, setSelectedColor] = useState(user.avatar.backgroundColor || generateAvatarColor(user.name));
  const [selectedStyle, setSelectedStyle] = useState<'circle' | 'rounded' | 'square'>(user.avatar.style || 'circle');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const colors = [
    'bg-gradient-to-br from-blue-500 to-blue-600',
    'bg-gradient-to-br from-green-500 to-green-600',
    'bg-gradient-to-br from-purple-500 to-purple-600',
    'bg-gradient-to-br from-pink-500 to-pink-600',
    'bg-gradient-to-br from-indigo-500 to-indigo-600',
    'bg-gradient-to-br from-red-500 to-red-600',
    'bg-gradient-to-br from-yellow-500 to-yellow-600',
    'bg-gradient-to-br from-teal-500 to-teal-600'
  ];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setUploadedImage(e.target?.result as string);
        setSelectedType('upload');
      };
      reader.readAsDataURL(file);
    }
  };

  const generateRandomAvatar = () => {
    // In a real app, this would call an avatar generation service
    const avatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.username}&backgroundColor=random`;
    setUploadedImage(avatarUrl);
    setSelectedType('generated');
  };

  const handleSave = () => {
    const newAvatar: UserProfile['avatar'] = {
      type: selectedType,
      url: uploadedImage || undefined,
      initials: generateInitials(user.name),
      backgroundColor: selectedColor,
      style: selectedStyle
    };
    onSave(newAvatar);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/80 backdrop-blur-sm"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        onClick={onClose}
      >
        <motion.div
          className="bg-gray-900 rounded-lg shadow-2xl max-w-md w-full border border-gray-700"
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.8, opacity: 0 }}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="p-6 border-b border-gray-700">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-semibold text-white">Customize Avatar</h3>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Preview */}
            <div className="text-center">
              <div className="inline-block relative">
                <Avatar
                  user={{
                    ...user,
                    avatar: {
                      type: selectedType,
                      url: uploadedImage || undefined,
                      initials: generateInitials(user.name),
                      backgroundColor: selectedColor,
                      style: selectedStyle
                    }
                  }}
                  size="2xl"
                  showLevel={true}
                  showBadges={true}
                />
              </div>
              <p className="text-gray-400 text-sm mt-2">Preview</p>
            </div>

            {/* Avatar Type Selection */}
            <div>
              <h4 className="text-white font-medium mb-3">Avatar Type</h4>
              <div className="grid grid-cols-3 gap-3">
                <button
                  onClick={() => setSelectedType('initials')}
                  className={cn(
                    'p-3 rounded-lg border text-center transition-colors',
                    selectedType === 'initials'
                      ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                      : 'border-gray-600 text-gray-400 hover:border-gray-500'
                  )}
                >
                  <User className="w-6 h-6 mx-auto mb-1" />
                  <span className="text-xs">Initials</span>
                </button>
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className={cn(
                    'p-3 rounded-lg border text-center transition-colors',
                    selectedType === 'upload'
                      ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                      : 'border-gray-600 text-gray-400 hover:border-gray-500'
                  )}
                >
                  <Upload className="w-6 h-6 mx-auto mb-1" />
                  <span className="text-xs">Upload</span>
                </button>
                <button
                  onClick={generateRandomAvatar}
                  className={cn(
                    'p-3 rounded-lg border text-center transition-colors',
                    selectedType === 'generated'
                      ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                      : 'border-gray-600 text-gray-400 hover:border-gray-500'
                  )}
                >
                  <Shuffle className="w-6 h-6 mx-auto mb-1" />
                  <span className="text-xs">Generate</span>
                </button>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>

            {/* Color Selection (for initials) */}
            {selectedType === 'initials' && (
              <div>
                <h4 className="text-white font-medium mb-3">Background Color</h4>
                <div className="grid grid-cols-4 gap-3">
                  {colors.map((color) => (
                    <button
                      key={color}
                      onClick={() => setSelectedColor(color)}
                      className={cn(
                        'w-12 h-12 rounded-full border-2 transition-all',
                        color,
                        selectedColor === color
                          ? 'border-white scale-110'
                          : 'border-gray-600 hover:border-gray-400'
                      )}
                    >
                      {selectedColor === color && (
                        <Check className="w-4 h-4 text-white mx-auto" />
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Style Selection */}
            <div>
              <h4 className="text-white font-medium mb-3">Style</h4>
              <div className="grid grid-cols-3 gap-3">
                {(['circle', 'rounded', 'square'] as const).map((style) => (
                  <button
                    key={style}
                    onClick={() => setSelectedStyle(style)}
                    className={cn(
                      'p-3 rounded-lg border text-center transition-colors capitalize',
                      selectedStyle === style
                        ? 'border-blue-500 bg-blue-500/20 text-blue-400'
                        : 'border-gray-600 text-gray-400 hover:border-gray-500'
                    )}
                  >
                    {style}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="p-6 border-t border-gray-700 flex justify-end space-x-3">
            <EnhancedButton
              onClick={onClose}
              variant="ghost"
              className="text-gray-400 hover:text-white"
            >
              Cancel
            </EnhancedButton>
            <EnhancedButton
              onClick={handleSave}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Save Changes
            </EnhancedButton>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

// User profile card
export function UserProfileCard({ 
  user, 
  variant = 'full',
  className 
}: { 
  user: UserProfile; 
  variant?: 'full' | 'compact' | 'minimal';
  className?: string;
}) {
  const [showAvatarCustomization, setShowAvatarCustomization] = useState(false);

  const handleAvatarSave = (newAvatar: UserProfile['avatar']) => {
    // In a real app, this would update the user profile
    console.log('Saving new avatar:', newAvatar);
  };

  if (variant === 'minimal') {
    return (
      <div className={cn('flex items-center space-x-3', className)}>
        <Avatar user={user} size="md" showStatus={true} />
        <div>
          <div className="font-medium text-white">{user.name}</div>
          <div className="text-gray-400 text-sm">Level {user.level}</div>
        </div>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10', className)}>
        <div className="flex items-center space-x-4">
          <Avatar 
            user={user} 
            size="lg" 
            showLevel={true} 
            showBadges={true}
            editable={true}
            onClick={() => setShowAvatarCustomization(true)}
          />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <h3 className="font-semibold text-white">{user.name}</h3>
              {user.isVerified && <Star className="w-4 h-4 text-blue-400" />}
              {user.isPremium && <Crown className="w-4 h-4 text-yellow-400" />}
            </div>
            <p className="text-gray-400 text-sm">@{user.username}</p>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <span className="text-blue-400">Level {user.level}</span>
              <span className="text-green-400">{user.xp.toLocaleString()} XP</span>
              <span className="text-purple-400">{user.achievements.length} achievements</span>
            </div>
          </div>
        </div>

        <AvatarCustomization
          user={user}
          isOpen={showAvatarCustomization}
          onClose={() => setShowAvatarCustomization(false)}
          onSave={handleAvatarSave}
        />
      </div>
    );
  }

  return (
    <div className={cn('bg-white/5 backdrop-blur-sm rounded-lg p-6 border border-white/10', className)}>
      {/* Header */}
      <div className="flex items-start justify-between mb-6">
        <div className="flex items-center space-x-4">
          <Avatar 
            user={user} 
            size="xl" 
            showLevel={true} 
            showBadges={true}
            editable={true}
            onClick={() => setShowAvatarCustomization(true)}
          />
          <div>
            <div className="flex items-center space-x-2">
              <h2 className="text-xl font-bold text-white">{user.name}</h2>
              {user.isVerified && <Star className="w-5 h-5 text-blue-400" />}
              {user.isPremium && <Crown className="w-5 h-5 text-yellow-400" />}
            </div>
            <p className="text-gray-400">@{user.username}</p>
            {user.location && (
              <p className="text-gray-500 text-sm">{user.location}</p>
            )}
          </div>
        </div>
        <button className="text-gray-400 hover:text-white transition-colors">
          <Settings className="w-5 h-5" />
        </button>
      </div>

      {/* Bio */}
      {user.bio && (
        <p className="text-gray-300 mb-6">{user.bio}</p>
      )}

      {/* Stats */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-400">{user.level}</div>
          <div className="text-gray-400 text-sm">Level</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-400">{user.xp.toLocaleString()}</div>
          <div className="text-gray-400 text-sm">XP</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-400">{user.achievements.length}</div>
          <div className="text-gray-400 text-sm">Achievements</div>
        </div>
      </div>

      {/* Skills */}
      {user.skills.length > 0 && (
        <div className="mb-6">
          <h3 className="font-semibold text-white mb-3">Skills</h3>
          <div className="flex flex-wrap gap-2">
            {user.skills.map((skill) => (
              <span
                key={skill}
                className="px-3 py-1 bg-blue-500/20 text-blue-300 text-sm rounded-full border border-blue-500/30"
              >
                {skill}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Recent Achievements */}
      <div>
        <h3 className="font-semibold text-white mb-3">Recent Achievements</h3>
        <div className="space-y-2">
          {user.achievements.slice(0, 3).map((achievement) => (
            <div key={achievement} className="flex items-center space-x-3 p-2 bg-white/5 rounded-lg">
              <Trophy className="w-4 h-4 text-yellow-400" />
              <span className="text-gray-300 text-sm">{achievement}</span>
            </div>
          ))}
        </div>
      </div>

      <AvatarCustomization
        user={user}
        isOpen={showAvatarCustomization}
        onClose={() => setShowAvatarCustomization(false)}
        onSave={handleAvatarSave}
      />
    </div>
  );
}
