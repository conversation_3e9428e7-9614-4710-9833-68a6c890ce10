'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SkeletonProps {
  className?: string;
  animate?: boolean;
  'data-testid'?: string;
}

// Base skeleton component with accessibility support
function Skeleton({ className, animate = true, 'data-testid': testId }: SkeletonProps) {
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false;

  const shouldAnimate = animate && !prefersReducedMotion;

  return (
    <div
      className={cn(
        'bg-gray-200 dark:bg-gray-700 rounded',
        shouldAnimate && 'animate-pulse',
        className
      )}
      data-testid={testId}
      role="status"
      aria-label="Loading content"
      aria-live="polite"
    />
  );
}

// Hero Section Skeleton
export function HeroSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('w-full', className)} data-testid="hero-skeleton">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center max-w-4xl mx-auto">
          {/* Title skeleton */}
          <div className="mb-6 space-y-3">
            <Skeleton className="h-12 w-3/4 mx-auto" data-testid="hero-title-skeleton" />
            <Skeleton className="h-12 w-2/3 mx-auto" />
          </div>
          
          {/* Description skeleton */}
          <div className="mb-8 space-y-2">
            <Skeleton className="h-6 w-5/6 mx-auto" data-testid="hero-description-skeleton" />
            <Skeleton className="h-6 w-4/5 mx-auto" />
            <Skeleton className="h-6 w-3/4 mx-auto" />
          </div>
          
          {/* CTA buttons skeleton */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Skeleton className="h-14 w-48" data-testid="hero-cta-primary-skeleton" />
            <Skeleton className="h-14 w-40" data-testid="hero-cta-secondary-skeleton" />
          </div>
          
          {/* Stats skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="text-center">
                <Skeleton className="h-8 w-24 mx-auto mb-2" data-testid={`hero-stat-${i}-skeleton`} />
                <Skeleton className="h-4 w-32 mx-auto" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Learning Path Preview Skeleton
export function LearningPathSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('w-full', className)} data-testid="learning-path-skeleton">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <Skeleton className="h-10 w-64 mx-auto mb-4" data-testid="learning-path-title-skeleton" />
          <Skeleton className="h-6 w-96 mx-auto" />
        </div>
        
        {/* Week cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((week) => (
            <div key={week} className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center mb-4">
                <Skeleton className="h-8 w-8 rounded-full mr-3" />
                <Skeleton className="h-6 w-20" />
              </div>
              <Skeleton className="h-5 w-full mb-3" />
              <Skeleton className="h-4 w-4/5 mb-4" />
              
              {/* Progress bar */}
              <div className="mb-4">
                <Skeleton className="h-2 w-full rounded-full" />
              </div>
              
              {/* Milestone indicators */}
              <div className="space-y-2">
                {[1, 2, 3].map((milestone) => (
                  <div key={milestone} className="flex items-center">
                    <Skeleton className="h-4 w-4 rounded-full mr-2" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// FAQ Section Skeleton
export function FAQSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('w-full', className)} data-testid="faq-skeleton">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-8">
          <Skeleton className="h-10 w-80 mx-auto mb-4" />
          <Skeleton className="h-6 w-96 mx-auto" />
        </div>
        
        {/* Search bar */}
        <div className="max-w-2xl mx-auto mb-8">
          <Skeleton className="h-12 w-full rounded-lg" data-testid="faq-search-skeleton" />
        </div>
        
        {/* Category filters */}
        <div className="flex flex-wrap gap-2 justify-center mb-8">
          {[1, 2, 3, 4, 5].map((i) => (
            <Skeleton key={i} className="h-8 w-24 rounded-full" />
          ))}
        </div>
        
        {/* FAQ items */}
        <div className="max-w-4xl mx-auto space-y-4">
          {[1, 2, 3, 4, 5].map((i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-6 w-6" />
              </div>
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-5/6" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Comparison Table Skeleton
export function ComparisonTableSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('w-full', className)} data-testid="comparison-table-skeleton">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-8">
          <Skeleton className="h-10 w-64 mx-auto mb-4" />
          <Skeleton className="h-6 w-80 mx-auto" />
        </div>
        
        {/* Table */}
        <div className="max-w-6xl mx-auto overflow-x-auto">
          <div className="min-w-full bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            {/* Header row */}
            <div className="grid grid-cols-4 gap-4 p-6 border-b border-gray-200 dark:border-gray-700">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-6 w-32" />
            </div>
            
            {/* Feature rows */}
            {[1, 2, 3, 4, 5, 6].map((row) => (
              <div key={row} className="grid grid-cols-4 gap-4 p-6 border-b border-gray-200 dark:border-gray-700 last:border-b-0">
                <Skeleton className="h-5 w-full" />
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-5 w-16" />
                <Skeleton className="h-5 w-16" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Conversion Components Skeleton
export function ConversionSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('w-full', className)} data-testid="conversion-skeleton">
      <div className="container mx-auto px-4 py-12">
        {/* Trial signup form */}
        <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <Skeleton className="h-8 w-48 mx-auto mb-6" />
          
          <div className="space-y-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        </div>
        
        {/* Urgency timer */}
        <div className="text-center mb-8">
          <Skeleton className="h-6 w-64 mx-auto mb-4" />
          <div className="flex justify-center space-x-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="text-center">
                <Skeleton className="h-12 w-12 mx-auto mb-2" />
                <Skeleton className="h-4 w-8 mx-auto" />
              </div>
            ))}
          </div>
        </div>
        
        {/* Social proof elements */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <div key={i} className="text-center">
              <Skeleton className="h-16 w-16 rounded-full mx-auto mb-4" />
              <Skeleton className="h-5 w-32 mx-auto mb-2" />
              <Skeleton className="h-4 w-24 mx-auto" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Interactive Code Editor Skeleton
export function CodeEditorSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('w-full', className)} data-testid="code-editor-skeleton">
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Toolbar */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-2">
            <Skeleton className="h-8 w-8" />
            <Skeleton className="h-6 w-24" />
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-8 w-20" />
            <Skeleton className="h-8 w-16" />
            <Skeleton className="h-8 w-8" />
          </div>
        </div>
        
        {/* Editor area */}
        <div className="p-4 bg-gray-50 dark:bg-gray-900">
          <div className="space-y-2">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((line) => (
              <div key={line} className="flex items-center">
                <Skeleton className="h-4 w-6 mr-4" />
                <Skeleton className={cn(
                  'h-4',
                  line % 3 === 0 ? 'w-3/4' : line % 2 === 0 ? 'w-1/2' : 'w-5/6'
                )} />
              </div>
            ))}
          </div>
        </div>
        
        {/* Output panel */}
        <div className="border-t border-gray-200 dark:border-gray-700 p-4">
          <Skeleton className="h-6 w-32 mb-4" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-4/5" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </div>
      </div>
    </div>
  );
}

// Analytics Dashboard Skeleton
export function AnalyticsDashboardSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn('w-full', className)} data-testid="analytics-dashboard-skeleton">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-48" />
          <div className="flex items-center space-x-2">
            <Skeleton className="h-8 w-24" />
            <Skeleton className="h-8 w-20" />
          </div>
        </div>
        
        {/* Metric cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
              <div className="flex items-center justify-between mb-4">
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-4 w-4" />
              </div>
              <Skeleton className="h-8 w-20 mb-2" />
              <Skeleton className="h-4 w-32 mb-4" />
              <div className="flex items-center">
                <Skeleton className="h-4 w-4 mr-2" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
          ))}
        </div>
        
        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <Skeleton className="h-6 w-32 mb-6" />
            <Skeleton className="h-64 w-full" />
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
            <Skeleton className="h-6 w-40 mb-6" />
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
        
        {/* Real-time indicators */}
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-6">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-24" />
          </div>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center justify-between">
                <div className="flex items-center">
                  <Skeleton className="h-8 w-8 rounded-full mr-3" />
                  <div>
                    <Skeleton className="h-4 w-32 mb-1" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

// Loading wrapper component
interface LoadingWrapperProps {
  isLoading: boolean;
  skeleton: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  transitionDuration?: number;
}

export function LoadingWrapper({ 
  isLoading, 
  skeleton, 
  children, 
  className,
  transitionDuration = 200 
}: LoadingWrapperProps) {
  return (
    <div className={className}>
      {isLoading ? (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: transitionDuration / 1000 }}
        >
          {skeleton}
        </motion.div>
      ) : (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: transitionDuration / 1000 }}
        >
          {children}
        </motion.div>
      )}
    </div>
  );
}

// Hook for managing loading states
export function useSkeletonLoading(initialState = true) {
  const [isLoading, setIsLoading] = React.useState(initialState);
  
  const startLoading = React.useCallback(() => setIsLoading(true), []);
  const stopLoading = React.useCallback(() => setIsLoading(false), []);
  
  return {
    isLoading,
    startLoading,
    stopLoading,
    setIsLoading
  };
}

export default {
  HeroSkeleton,
  LearningPathSkeleton,
  FAQSkeleton,
  ComparisonTableSkeleton,
  ConversionSkeleton,
  CodeEditorSkeleton,
  AnalyticsDashboardSkeleton,
  LoadingWrapper,
  useSkeletonLoading
};
