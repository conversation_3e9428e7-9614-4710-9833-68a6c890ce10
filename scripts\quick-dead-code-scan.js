#!/usr/bin/env node

/**
 * Quick Dead Code Scanner
 * Fast analysis of unused files and basic organization issues
 */

const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  sourceExtensions: ['.ts', '.tsx', '.js', '.jsx'],
  assetExtensions: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico'],
  excludeDirs: ['node_modules', '.next', '.git', 'dist', 'build', 'coverage'],
  sourceDirs: ['app', 'components', 'lib', 'hooks', 'utils', 'types', 'services'],
  assetDirs: ['public']
};

// Colors
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Get files recursively
function getFiles(dir, extensions) {
  const files = [];
  
  if (!fs.existsSync(dir)) return files;
  
  function scan(currentDir) {
    try {
      const items = fs.readdirSync(currentDir);
      
      items.forEach(item => {
        if (CONFIG.excludeDirs.includes(item)) return;
        
        const fullPath = path.join(currentDir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          scan(fullPath);
        } else if (extensions.some(ext => item.endsWith(ext))) {
          files.push({
            path: fullPath,
            relative: path.relative(process.cwd(), fullPath),
            name: item,
            size: stat.size,
            modified: stat.mtime
          });
        }
      });
    } catch (error) {
      // Skip unreadable directories
    }
  }
  
  scan(dir);
  return files;
}

// Quick import analysis
function findImports(content) {
  const imports = [];
  
  // Simple regex for imports
  const importRegex = /import.*?from\s+['"`]([^'"`]+)['"`]/g;
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    imports.push(match[1]);
  }
  
  // Dynamic imports
  const dynamicImportRegex = /import\s*\(\s*['"`]([^'"`]+)['"`]\s*\)/g;
  while ((match = dynamicImportRegex.exec(content)) !== null) {
    imports.push(match[1]);
  }
  
  return imports;
}

// Analyze unused files
function analyzeUnusedFiles() {
  log('\n🔍 Analyzing Unused Files...', 'cyan');
  
  const allSourceFiles = [];
  const allAssetFiles = [];
  
  // Collect source files
  CONFIG.sourceDirs.forEach(dir => {
    const files = getFiles(dir, CONFIG.sourceExtensions);
    allSourceFiles.push(...files);
  });
  
  // Collect asset files
  CONFIG.assetDirs.forEach(dir => {
    const files = getFiles(dir, CONFIG.assetExtensions);
    allAssetFiles.push(...files);
  });
  
  log(`Found ${allSourceFiles.length} source files`, 'blue');
  log(`Found ${allAssetFiles.length} asset files`, 'blue');
  
  // Build import map
  const allImports = new Set();
  const fileContents = new Map();
  
  allSourceFiles.forEach(file => {
    try {
      const content = fs.readFileSync(file.path, 'utf8');
      fileContents.set(file.relative, content);
      
      const imports = findImports(content);
      imports.forEach(imp => allImports.add(imp));
    } catch (error) {
      // Skip unreadable files
    }
  });
  
  // Find potentially unused source files
  const unusedSourceFiles = allSourceFiles.filter(file => {
    const relativePath = file.relative.replace(/\\/g, '/');
    const withoutExt = relativePath.replace(/\.(ts|tsx|js|jsx)$/, '');
    
    // Check if imported
    const isImported = Array.from(allImports).some(imp => {
      const normalizedImp = imp.replace(/\\/g, '/');
      return normalizedImp.includes(file.name.replace(/\.(ts|tsx|js|jsx)$/, '')) ||
             normalizedImp.includes(relativePath) ||
             normalizedImp.includes(withoutExt);
    });
    
    // Skip Next.js special files
    const isSpecialFile = file.name === 'page.tsx' || 
                         file.name === 'layout.tsx' || 
                         file.name === 'loading.tsx' || 
                         file.name === 'error.tsx' ||
                         file.name === 'not-found.tsx' ||
                         file.name === 'middleware.ts' ||
                         file.relative.includes('api/') ||
                         file.name === 'globals.css' ||
                         file.name === 'providers.tsx';
    
    return !isImported && !isSpecialFile;
  });
  
  // Find potentially unused assets
  const unusedAssetFiles = allAssetFiles.filter(file => {
    const fileName = file.name;
    const isReferenced = Array.from(fileContents.values()).some(content => 
      content.includes(fileName) || content.includes(file.relative.replace(/\\/g, '/'))
    );
    
    return !isReferenced;
  });
  
  // Display results
  if (unusedSourceFiles.length > 0) {
    log(`\n⚠️  Found ${unusedSourceFiles.length} potentially unused source files:`, 'yellow');
    unusedSourceFiles.slice(0, 10).forEach(file => {
      log(`  ${file.relative} (${Math.round(file.size / 1024)}KB)`, 'yellow');
    });
    if (unusedSourceFiles.length > 10) {
      log(`  ... and ${unusedSourceFiles.length - 10} more`, 'yellow');
    }
  } else {
    log('✅ No unused source files found', 'green');
  }
  
  if (unusedAssetFiles.length > 0) {
    log(`\n⚠️  Found ${unusedAssetFiles.length} potentially unused asset files:`, 'yellow');
    unusedAssetFiles.slice(0, 10).forEach(file => {
      log(`  ${file.relative} (${Math.round(file.size / 1024)}KB)`, 'yellow');
    });
    if (unusedAssetFiles.length > 10) {
      log(`  ... and ${unusedAssetFiles.length - 10} more`, 'yellow');
    }
  } else {
    log('✅ No unused asset files found', 'green');
  }
  
  return {
    unusedSourceFiles,
    unusedAssetFiles,
    totalSourceFiles: allSourceFiles.length,
    totalAssetFiles: allAssetFiles.length
  };
}

// Analyze directory structure
function analyzeDirectoryStructure() {
  log('\n🏗️  Analyzing Directory Structure...', 'cyan');
  
  const issues = [];
  const recommendations = [];
  
  // Check app routes
  const appDir = 'app';
  if (fs.existsSync(appDir)) {
    const routes = fs.readdirSync(appDir).filter(item => {
      const itemPath = path.join(appDir, item);
      return fs.existsSync(itemPath) && fs.statSync(itemPath).isDirectory();
    });
    
    routes.forEach(route => {
      const routePath = path.join(appDir, route);
      try {
        const files = fs.readdirSync(routePath);
        
        if (!files.some(f => f.startsWith('page.'))) {
          issues.push(`Route ${route} missing page file`);
        }
      } catch (error) {
        // Skip unreadable directories
      }
    });
  }
  
  // Check for index files
  const dirsToCheck = ['components', 'lib', 'hooks', 'utils'];
  dirsToCheck.forEach(dir => {
    if (fs.existsSync(dir)) {
      const hasIndex = fs.existsSync(path.join(dir, 'index.ts')) || 
                     fs.existsSync(path.join(dir, 'index.tsx'));
      
      if (!hasIndex) {
        recommendations.push(`Add index.ts to ${dir} for cleaner imports`);
      }
    }
  });
  
  // Check component naming
  const componentsDir = 'components';
  if (fs.existsSync(componentsDir)) {
    try {
      const componentDirs = fs.readdirSync(componentsDir).filter(item => {
        const itemPath = path.join(componentsDir, item);
        return fs.statSync(itemPath).isDirectory();
      });
      
      componentDirs.forEach(dir => {
        if (!/^[A-Z][a-zA-Z]*$/.test(dir) && !/^[a-z]+(-[a-z]+)*$/.test(dir)) {
          issues.push(`Component directory ${dir} doesn't follow naming convention`);
        }
      });
    } catch (error) {
      // Skip if can't read components directory
    }
  }
  
  if (issues.length > 0) {
    log('\n⚠️  Directory structure issues:', 'yellow');
    issues.forEach(issue => log(`  ${issue}`, 'yellow'));
  }
  
  if (recommendations.length > 0) {
    log('\n💡 Directory structure recommendations:', 'blue');
    recommendations.forEach(rec => log(`  ${rec}`, 'blue'));
  }
  
  if (issues.length === 0 && recommendations.length === 0) {
    log('✅ Directory structure looks good', 'green');
  }
  
  return { issues, recommendations };
}

// Generate summary report
function generateSummaryReport(unusedFilesResult, structureResult) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: unusedFilesResult.totalSourceFiles + unusedFilesResult.totalAssetFiles,
      unusedFiles: unusedFilesResult.unusedSourceFiles.length + unusedFilesResult.unusedAssetFiles.length,
      structureIssues: structureResult.issues.length,
      recommendations: structureResult.recommendations.length
    },
    unusedFiles: {
      source: unusedFilesResult.unusedSourceFiles.map(f => ({
        path: f.relative,
        size: f.size
      })),
      assets: unusedFilesResult.unusedAssetFiles.map(f => ({
        path: f.relative,
        size: f.size
      }))
    },
    structure: {
      issues: structureResult.issues,
      recommendations: structureResult.recommendations
    },
    potentialSavings: {
      files: unusedFilesResult.unusedSourceFiles.length + unusedFilesResult.unusedAssetFiles.length,
      sizeKB: Math.round(
        (unusedFilesResult.unusedSourceFiles.reduce((sum, f) => sum + f.size, 0) +
         unusedFilesResult.unusedAssetFiles.reduce((sum, f) => sum + f.size, 0)) / 1024
      )
    }
  };
  
  // Save report
  const reportDir = 'reports';
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportPath = path.join(reportDir, `quick-dead-code-scan-${timestamp}.json`);
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log(`\n📊 Analysis Summary:`, 'cyan');
  log(`Total files: ${report.summary.totalFiles}`, 'blue');
  log(`Potentially unused: ${report.summary.unusedFiles}`, 'yellow');
  log(`Structure issues: ${report.summary.structureIssues}`, 'yellow');
  log(`Potential savings: ${report.potentialSavings.sizeKB}KB`, 'green');
  log(`\n📄 Report saved: ${reportPath}`, 'green');
  
  return report;
}

// Main execution
function main() {
  log('🔍 Quick Dead Code Scanner - Solidity Learning Platform', 'cyan');
  
  try {
    const unusedFilesResult = analyzeUnusedFiles();
    const structureResult = analyzeDirectoryStructure();
    const report = generateSummaryReport(unusedFilesResult, structureResult);
    
    const hasIssues = report.summary.unusedFiles > 0 || report.summary.structureIssues > 0;
    
    log('\n' + '='.repeat(50), 'cyan');
    if (hasIssues) {
      log('⚠️  Issues found. Review the report for details.', 'yellow');
    } else {
      log('✅ No significant issues found!', 'green');
    }
    log('='.repeat(50), 'cyan');
    
    process.exit(hasIssues ? 1 : 0);
    
  } catch (error) {
    log(`❌ Analysis failed: ${error.message}`, 'red');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}
