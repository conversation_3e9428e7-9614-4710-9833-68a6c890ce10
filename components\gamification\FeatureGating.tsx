'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Lock, 
  Crown, 
  Zap, 
  Star, 
  Info,
  ChevronRight,
  Gift,
  Target,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { GlassCard } from '@/components/ui/Glassmorphism';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import { LevelInfo } from './LevelProgressionRoadmap';

export interface FeatureGateConfig {
  featureId: string;
  name: string;
  description: string;
  requiredLevel: number;
  icon?: React.ComponentType<{ className?: string }>;
  category: 'editor' | 'analysis' | 'collaboration' | 'advanced' | 'premium';
  previewAvailable?: boolean;
  comingSoon?: boolean;
}

interface FeatureGateProps {
  feature: FeatureGateConfig;
  currentLevel: number;
  currentXP: number;
  levels: LevelInfo[];
  children: React.ReactNode;
  showUpgradePrompt?: boolean;
  onUpgradeClick?: () => void;
  className?: string;
}

export function FeatureGate({
  feature,
  currentLevel,
  currentXP,
  levels,
  children,
  showUpgradePrompt = true,
  onUpgradeClick,
  className
}: FeatureGateProps) {
  const [showDetails, setShowDetails] = useState(false);
  
  const isUnlocked = currentLevel >= feature.requiredLevel;
  const requiredLevelInfo = levels.find(l => l.level === feature.requiredLevel);
  const xpNeeded = requiredLevelInfo ? Math.max(requiredLevelInfo.xpTotal - currentXP, 0) : 0;

  if (isUnlocked) {
    return <div className={className}>{children}</div>;
  }

  const getCategoryColor = () => {
    switch (feature.category) {
      case 'editor':
        return 'from-blue-500 to-cyan-500';
      case 'analysis':
        return 'from-green-500 to-emerald-500';
      case 'collaboration':
        return 'from-purple-500 to-pink-500';
      case 'advanced':
        return 'from-orange-500 to-red-500';
      case 'premium':
        return 'from-yellow-500 to-orange-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getCategoryIcon = () => {
    if (feature.icon) return feature.icon;
    
    switch (feature.category) {
      case 'premium':
        return Crown;
      case 'advanced':
        return Star;
      default:
        return Lock;
    }
  };

  const Icon = getCategoryIcon();

  return (
    <div className={cn('relative', className)}>
      {/* Locked Content Overlay */}
      <div className="relative">
        {/* Blurred/Disabled Content */}
        <div className="pointer-events-none opacity-30 blur-sm select-none">
          {children}
        </div>

        {/* Lock Overlay */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="absolute inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm rounded-lg"
        >
          <GlassCard className="p-6 max-w-sm w-full mx-4 text-center">
            <div className="space-y-4">
              {/* Feature Icon */}
              <div className={cn(
                'w-16 h-16 rounded-full flex items-center justify-center mx-auto',
                'bg-gradient-to-br',
                getCategoryColor(),
                'shadow-lg'
              )}>
                <Icon className="w-8 h-8 text-white" />
              </div>

              {/* Feature Info */}
              <div>
                <h3 className="text-lg font-bold text-white mb-2">{feature.name}</h3>
                <p className="text-sm text-gray-300 mb-4">{feature.description}</p>
                
                {feature.comingSoon ? (
                  <div className="inline-flex items-center space-x-2 px-3 py-1 bg-purple-500/20 border border-purple-500/30 rounded-full">
                    <Star className="w-4 h-4 text-purple-400" />
                    <span className="text-sm font-medium text-purple-300">Coming Soon</span>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="inline-flex items-center space-x-2 px-3 py-1 bg-yellow-500/20 border border-yellow-500/30 rounded-full">
                      <Crown className="w-4 h-4 text-yellow-400" />
                      <span className="text-sm font-medium text-yellow-300">
                        Level {feature.requiredLevel} Required
                      </span>
                    </div>
                    
                    <div className="text-xs text-gray-400">
                      {xpNeeded > 0 ? (
                        <>Need {xpNeeded.toLocaleString()} more XP</>
                      ) : (
                        <>Almost there! Complete a few more lessons</>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="space-y-2">
                {showUpgradePrompt && !feature.comingSoon && (
                  <EnhancedButton
                    onClick={onUpgradeClick}
                    className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600"
                    touchTarget
                  >
                    <Zap className="w-4 h-4 mr-2" />
                    Level Up to Unlock
                  </EnhancedButton>
                )}
                
                <EnhancedButton
                  onClick={() => setShowDetails(true)}
                  variant="outline"
                  size="sm"
                  className="w-full"
                >
                  <Info className="w-4 h-4 mr-2" />
                  Learn More
                </EnhancedButton>
                
                {feature.previewAvailable && (
                  <EnhancedButton
                    variant="ghost"
                    size="sm"
                    className="w-full text-gray-400 hover:text-white"
                  >
                    <Target className="w-4 h-4 mr-2" />
                    Preview Feature
                  </EnhancedButton>
                )}
              </div>
            </div>
          </GlassCard>
        </motion.div>
      </div>

      {/* Feature Details Modal */}
      <AnimatePresence>
        {showDetails && (
          <FeatureDetailsModal
            feature={feature}
            requiredLevelInfo={requiredLevelInfo}
            xpNeeded={xpNeeded}
            onClose={() => setShowDetails(false)}
            onUpgradeClick={onUpgradeClick}
          />
        )}
      </AnimatePresence>
    </div>
  );
}

// Feature Details Modal
interface FeatureDetailsModalProps {
  feature: FeatureGateConfig;
  requiredLevelInfo?: LevelInfo;
  xpNeeded: number;
  onClose: () => void;
  onUpgradeClick?: () => void;
}

function FeatureDetailsModal({
  feature,
  requiredLevelInfo,
  xpNeeded,
  onClose,
  onUpgradeClick
}: FeatureDetailsModalProps) {
  const getCategoryColor = () => {
    switch (feature.category) {
      case 'editor':
        return 'from-blue-500 to-cyan-500';
      case 'analysis':
        return 'from-green-500 to-emerald-500';
      case 'collaboration':
        return 'from-purple-500 to-pink-500';
      case 'advanced':
        return 'from-orange-500 to-red-500';
      case 'premium':
        return 'from-yellow-500 to-orange-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const Icon = feature.icon || Lock;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
        className="max-w-lg w-full max-h-[90vh] overflow-y-auto"
      >
        <GlassCard className="p-8 border-2 border-yellow-500/30">
          {/* Close Button */}
          <EnhancedButton
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="absolute top-4 right-4 w-8 h-8 p-0 text-gray-400 hover:text-white"
          >
            <X className="w-4 h-4" />
          </EnhancedButton>

          <div className="space-y-6">
            {/* Header */}
            <div className="text-center">
              <div className={cn(
                'w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4',
                'bg-gradient-to-br',
                getCategoryColor(),
                'shadow-lg'
              )}>
                <Icon className="w-10 h-10 text-white" />
              </div>
              
              <h2 className="text-2xl font-bold text-white mb-2">{feature.name}</h2>
              <div className="inline-flex items-center space-x-2 px-3 py-1 bg-gray-500/20 border border-gray-500/30 rounded-full">
                <span className="text-sm font-medium text-gray-300 capitalize">{feature.category}</span>
              </div>
            </div>

            {/* Description */}
            <div className="text-center">
              <p className="text-gray-300 leading-relaxed">{feature.description}</p>
            </div>

            {/* Requirements */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">Unlock Requirements</h3>
              
              <div className="p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
                <div className="flex items-center space-x-3">
                  <Crown className="w-6 h-6 text-yellow-400" />
                  <div>
                    <div className="font-semibold text-yellow-300">
                      Level {feature.requiredLevel} Required
                    </div>
                    {requiredLevelInfo && (
                      <div className="text-sm text-yellow-200">
                        {requiredLevelInfo.title} - {requiredLevelInfo.xpTotal.toLocaleString()} XP
                      </div>
                    )}
                  </div>
                </div>
                
                {xpNeeded > 0 && (
                  <div className="mt-3 pt-3 border-t border-yellow-500/20">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-yellow-200">XP needed:</span>
                      <span className="font-semibold text-yellow-300">
                        {xpNeeded.toLocaleString()}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Level Rewards Preview */}
            {requiredLevelInfo && requiredLevelInfo.rewards.length > 0 && (
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-white flex items-center">
                  <Gift className="w-5 h-5 mr-2 text-purple-400" />
                  Level {feature.requiredLevel} Rewards
                </h3>
                
                <div className="space-y-2">
                  {requiredLevelInfo.rewards.slice(0, 3).map((reward, index) => {
                    const RewardIcon = reward.icon;
                    return (
                      <div
                        key={index}
                        className="flex items-center space-x-3 p-3 bg-white/5 border border-white/10 rounded-lg"
                      >
                        <div className="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center">
                          <RewardIcon className="w-4 h-4 text-purple-400" />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-white">{reward.name}</div>
                          <div className="text-xs text-gray-400">{reward.description}</div>
                        </div>
                      </div>
                    );
                  })}
                  
                  {requiredLevelInfo.rewards.length > 3 && (
                    <div className="text-center text-sm text-gray-400">
                      +{requiredLevelInfo.rewards.length - 3} more rewards
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              {!feature.comingSoon && onUpgradeClick && (
                <EnhancedButton
                  onClick={onUpgradeClick}
                  className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600"
                  touchTarget
                >
                  <Zap className="w-4 h-4 mr-2" />
                  Start Learning to Unlock
                  <ChevronRight className="w-4 h-4 ml-2" />
                </EnhancedButton>
              )}
              
              <EnhancedButton
                onClick={onClose}
                variant="outline"
                className="w-full"
              >
                Close
              </EnhancedButton>
            </div>
          </div>
        </GlassCard>
      </motion.div>
    </motion.div>
  );
}

// Hook for feature gating
export function useFeatureGating(currentLevel: number) {
  const isFeatureUnlocked = (requiredLevel: number) => {
    return currentLevel >= requiredLevel;
  };

  const getFeatureStatus = (feature: FeatureGateConfig) => {
    return {
      isUnlocked: isFeatureUnlocked(feature.requiredLevel),
      levelsToGo: Math.max(feature.requiredLevel - currentLevel, 0)
    };
  };

  return {
    isFeatureUnlocked,
    getFeatureStatus
  };
}

// Default feature configurations
export const defaultFeatures: FeatureGateConfig[] = [
  {
    featureId: 'advanced_debugger',
    name: 'Advanced Debugger',
    description: 'Step-by-step debugging with variable inspection and call stack visualization',
    requiredLevel: 5,
    category: 'editor',
    previewAvailable: true
  },
  {
    featureId: 'ai_code_review',
    name: 'AI Code Review',
    description: 'Get intelligent feedback and suggestions from our AI assistant',
    requiredLevel: 8,
    category: 'analysis',
    previewAvailable: true
  },
  {
    featureId: 'real_time_collaboration',
    name: 'Real-time Collaboration',
    description: 'Code together with other developers in real-time',
    requiredLevel: 12,
    category: 'collaboration'
  },
  {
    featureId: 'custom_themes',
    name: 'Custom Themes',
    description: 'Personalize your coding environment with custom themes and layouts',
    requiredLevel: 15,
    category: 'premium',
    icon: Crown
  },
  {
    featureId: 'advanced_analytics',
    name: 'Advanced Analytics',
    description: 'Detailed insights into your learning progress and coding patterns',
    requiredLevel: 20,
    category: 'advanced'
  }
];
