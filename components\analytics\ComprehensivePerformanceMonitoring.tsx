'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Activity, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  Zap,
  TrendingUp,
  TrendingDown,
  Monitor,
  Wifi,
  HardDrive,
  Cpu,
  BarChart3
} from 'lucide-react';

// Core Web Vitals and Performance Metrics
interface PerformanceMetrics {
  // Core Web Vitals
  lcp: number; // Largest Contentful Paint
  fid: number; // First Input Delay
  cls: number; // Cumulative Layout Shift
  fcp: number; // First Contentful Paint
  ttfb: number; // Time to First Byte
  
  // Additional metrics
  domContentLoaded: number;
  loadComplete: number;
  resourceCount: number;
  transferSize: number;
  
  // Custom metrics
  timeToInteractive: number;
  speedIndex: number;
  totalBlockingTime: number;
}

interface ErrorReport {
  id: string;
  message: string;
  stack?: string;
  timestamp: Date;
  url: string;
  userAgent: string;
  userId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context?: any;
}

interface PerformanceAlert {
  id: string;
  type: 'performance' | 'error' | 'availability';
  severity: 'warning' | 'critical';
  message: string;
  metric?: string;
  value?: number;
  threshold?: number;
  timestamp: Date;
  resolved: boolean;
}

interface ComprehensivePerformanceMonitoringProps {
  className?: string;
  enableRealTimeMonitoring?: boolean;
  enableErrorTracking?: boolean;
  enableAlerts?: boolean;
  sentryDsn?: string;
  performanceThresholds?: {
    lcp: number;
    fid: number;
    cls: number;
    ttfb: number;
  };
}

const defaultThresholds = {
  lcp: 2500, // 2.5 seconds
  fid: 100,  // 100 milliseconds
  cls: 0.1,  // 0.1
  ttfb: 600  // 600 milliseconds
};

export function ComprehensivePerformanceMonitoring({
  className,
  enableRealTimeMonitoring = true,
  enableErrorTracking = true,
  enableAlerts = true,
  sentryDsn,
  performanceThresholds = defaultThresholds
}: ComprehensivePerformanceMonitoringProps) {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [errors, setErrors] = useState<ErrorReport[]>([]);
  const [alerts, setAlerts] = useState<PerformanceAlert[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [connectionInfo, setConnectionInfo] = useState<any>(null);
  const observerRef = useRef<PerformanceObserver | null>(null);

  // Initialize performance monitoring
  useEffect(() => {
    if (!enableRealTimeMonitoring) return;

    const initializeMonitoring = () => {
      setIsMonitoring(true);
      
      // Collect initial metrics
      collectPerformanceMetrics();
      
      // Set up performance observer
      if ('PerformanceObserver' in window) {
        observerRef.current = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          processPerformanceEntries(entries);
        });

        // Observe different types of performance entries
        try {
          observerRef.current.observe({ entryTypes: ['navigation', 'paint', 'largest-contentful-paint', 'first-input', 'layout-shift'] });
        } catch (error) {
          console.warn('Some performance entry types not supported:', error);
        }
      }

      // Monitor network information
      if ('connection' in navigator) {
        setConnectionInfo((navigator as any).connection);
        (navigator as any).connection.addEventListener('change', () => {
          setConnectionInfo((navigator as any).connection);
        });
      }
    };

    initializeMonitoring();

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
      setIsMonitoring(false);
    };
  }, [enableRealTimeMonitoring]);

  // Error tracking setup
  useEffect(() => {
    if (!enableErrorTracking) return;

    const handleError = (event: ErrorEvent) => {
      const errorReport: ErrorReport = {
        id: `error-${Date.now()}-${Math.random()}`,
        message: event.message,
        stack: event.error?.stack,
        timestamp: new Date(),
        url: event.filename || window.location.href,
        userAgent: navigator.userAgent,
        severity: determineSeverity(event.error),
        context: {
          lineno: event.lineno,
          colno: event.colno,
          page: window.location.pathname
        }
      };

      setErrors(prev => [errorReport, ...prev.slice(0, 99)]); // Keep last 100 errors
      
      if (enableAlerts) {
        createAlert('error', errorReport.severity === 'critical' ? 'critical' : 'warning', 
                   `JavaScript Error: ${errorReport.message}`);
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const errorReport: ErrorReport = {
        id: `promise-${Date.now()}-${Math.random()}`,
        message: `Unhandled Promise Rejection: ${event.reason}`,
        timestamp: new Date(),
        url: window.location.href,
        userAgent: navigator.userAgent,
        severity: 'medium',
        context: {
          reason: event.reason,
          page: window.location.pathname
        }
      };

      setErrors(prev => [errorReport, ...prev.slice(0, 99)]);
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);

    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, [enableErrorTracking, enableAlerts]);

  const collectPerformanceMetrics = useCallback(() => {
    if (!('performance' in window)) return;

    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const paint = performance.getEntriesByType('paint');
    const resources = performance.getEntriesByType('resource');

    const fcp = paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0;
    
    const newMetrics: PerformanceMetrics = {
      lcp: 0, // Will be updated by observer
      fid: 0, // Will be updated by observer
      cls: 0, // Will be updated by observer
      fcp,
      ttfb: navigation?.responseStart - navigation?.requestStart || 0,
      domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.navigationStart || 0,
      loadComplete: navigation?.loadEventEnd - navigation?.navigationStart || 0,
      resourceCount: resources.length,
      transferSize: resources.reduce((sum, resource) => sum + (resource as any).transferSize || 0, 0),
      timeToInteractive: 0, // Calculated separately
      speedIndex: 0, // Calculated separately
      totalBlockingTime: 0 // Calculated separately
    };

    setMetrics(newMetrics);
    checkPerformanceThresholds(newMetrics);
  }, []);

  const processPerformanceEntries = useCallback((entries: PerformanceEntry[]) => {
    entries.forEach(entry => {
      switch (entry.entryType) {
        case 'largest-contentful-paint':
          setMetrics(prev => prev ? { ...prev, lcp: entry.startTime } : null);
          break;
        case 'first-input':
          const fidEntry = entry as PerformanceEventTiming;
          setMetrics(prev => prev ? { ...prev, fid: fidEntry.processingStart - fidEntry.startTime } : null);
          break;
        case 'layout-shift':
          const clsEntry = entry as any;
          if (!clsEntry.hadRecentInput) {
            setMetrics(prev => prev ? { ...prev, cls: prev.cls + clsEntry.value } : null);
          }
          break;
      }
    });
  }, []);

  const checkPerformanceThresholds = useCallback((metrics: PerformanceMetrics) => {
    if (!enableAlerts) return;

    const checks = [
      { metric: 'lcp', value: metrics.lcp, threshold: performanceThresholds.lcp, name: 'Largest Contentful Paint' },
      { metric: 'fid', value: metrics.fid, threshold: performanceThresholds.fid, name: 'First Input Delay' },
      { metric: 'cls', value: metrics.cls, threshold: performanceThresholds.cls, name: 'Cumulative Layout Shift' },
      { metric: 'ttfb', value: metrics.ttfb, threshold: performanceThresholds.ttfb, name: 'Time to First Byte' }
    ];

    checks.forEach(check => {
      if (check.value > check.threshold) {
        createAlert('performance', 'warning', 
                   `${check.name} (${check.value.toFixed(2)}) exceeds threshold (${check.threshold})`,
                   check.metric, check.value, check.threshold);
      }
    });
  }, [enableAlerts, performanceThresholds]);

  const createAlert = useCallback((type: PerformanceAlert['type'], severity: PerformanceAlert['severity'], 
                                  message: string, metric?: string, value?: number, threshold?: number) => {
    const alert: PerformanceAlert = {
      id: `alert-${Date.now()}-${Math.random()}`,
      type,
      severity,
      message,
      metric,
      value,
      threshold,
      timestamp: new Date(),
      resolved: false
    };

    setAlerts(prev => [alert, ...prev.slice(0, 49)]); // Keep last 50 alerts

    // Auto-resolve warnings after 5 minutes
    if (severity === 'warning') {
      setTimeout(() => {
        setAlerts(prev => prev.map(a => a.id === alert.id ? { ...a, resolved: true } : a));
      }, 5 * 60 * 1000);
    }
  }, []);

  const resolveAlert = useCallback((alertId: string) => {
    setAlerts(prev => prev.map(alert => 
      alert.id === alertId ? { ...alert, resolved: true } : alert
    ));
  }, []);

  const getMetricStatus = (metric: keyof PerformanceMetrics, value: number) => {
    const thresholds = {
      lcp: { good: 2500, poor: 4000 },
      fid: { good: 100, poor: 300 },
      cls: { good: 0.1, poor: 0.25 },
      fcp: { good: 1800, poor: 3000 },
      ttfb: { good: 600, poor: 1500 }
    };

    const threshold = thresholds[metric as keyof typeof thresholds];
    if (!threshold) return 'unknown';

    if (value <= threshold.good) return 'good';
    if (value <= threshold.poor) return 'needs-improvement';
    return 'poor';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-500';
      case 'needs-improvement': return 'text-yellow-500';
      case 'poor': return 'text-red-500';
      default: return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <CheckCircle className="w-4 h-4" />;
      case 'needs-improvement': return <AlertTriangle className="w-4 h-4" />;
      case 'poor': return <AlertTriangle className="w-4 h-4" />;
      default: return <Monitor className="w-4 h-4" />;
    }
  };

  const determineSeverity = (error: Error): ErrorReport['severity'] => {
    if (!error) return 'low';
    
    const message = error.message?.toLowerCase() || '';
    
    if (message.includes('network') || message.includes('fetch')) return 'medium';
    if (message.includes('syntax') || message.includes('reference')) return 'high';
    if (message.includes('security') || message.includes('cors')) return 'critical';
    
    return 'low';
  };

  if (!metrics && isMonitoring) {
    return (
      <div className={cn('flex items-center justify-center p-8', className)}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500" />
        <span className="ml-3 text-gray-600 dark:text-gray-400">Collecting performance data...</span>
      </div>
    );
  }

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Performance Monitoring
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Real-time performance metrics and error tracking
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <div className={cn(
            'flex items-center space-x-2 px-3 py-1 rounded-full text-sm',
            isMonitoring ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
          )}>
            <Activity className="w-4 h-4" />
            <span>{isMonitoring ? 'Monitoring' : 'Stopped'}</span>
          </div>
        </div>
      </div>

      {/* Alerts */}
      {alerts.filter(alert => !alert.resolved).length > 0 && (
        <div className="space-y-2">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Active Alerts</h3>
          <AnimatePresence>
            {alerts.filter(alert => !alert.resolved).slice(0, 5).map(alert => (
              <motion.div
                key={alert.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                className={cn(
                  'flex items-center justify-between p-4 rounded-lg border',
                  alert.severity === 'critical' 
                    ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                    : 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
                )}
              >
                <div className="flex items-center space-x-3">
                  <AlertTriangle className={cn(
                    'w-5 h-5',
                    alert.severity === 'critical' ? 'text-red-500' : 'text-yellow-500'
                  )} />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {alert.message}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {alert.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
                
                <button
                  onClick={() => resolveAlert(alert.id)}
                  className="px-3 py-1 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  Resolve
                </button>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Core Web Vitals */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            { key: 'lcp', name: 'Largest Contentful Paint', value: metrics.lcp, unit: 'ms', icon: <Monitor className="w-5 h-5" /> },
            { key: 'fid', name: 'First Input Delay', value: metrics.fid, unit: 'ms', icon: <Zap className="w-5 h-5" /> },
            { key: 'cls', name: 'Cumulative Layout Shift', value: metrics.cls, unit: '', icon: <BarChart3 className="w-5 h-5" /> },
            { key: 'fcp', name: 'First Contentful Paint', value: metrics.fcp, unit: 'ms', icon: <Clock className="w-5 h-5" /> }
          ].map(metric => {
            const status = getMetricStatus(metric.key as keyof PerformanceMetrics, metric.value);
            return (
              <div key={metric.key} className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    <div className="text-blue-600 dark:text-blue-400">
                      {metric.icon}
                    </div>
                  </div>
                  <div className={getStatusColor(status)}>
                    {getStatusIcon(status)}
                  </div>
                </div>
                
                <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
                  {metric.value.toFixed(metric.unit === '' ? 3 : 0)}
                  <span className="text-sm font-normal text-gray-500 ml-1">{metric.unit}</span>
                </div>
                
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {metric.name}
                </div>
                
                <div className={cn('text-xs mt-2', getStatusColor(status))}>
                  {status.replace('-', ' ').toUpperCase()}
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Connection Info */}
      {connectionInfo && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Network Information</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Connection Type</div>
              <div className="font-medium text-gray-900 dark:text-white">{connectionInfo.effectiveType}</div>
            </div>
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Downlink</div>
              <div className="font-medium text-gray-900 dark:text-white">{connectionInfo.downlink} Mbps</div>
            </div>
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">RTT</div>
              <div className="font-medium text-gray-900 dark:text-white">{connectionInfo.rtt} ms</div>
            </div>
            <div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Save Data</div>
              <div className="font-medium text-gray-900 dark:text-white">{connectionInfo.saveData ? 'On' : 'Off'}</div>
            </div>
          </div>
        </div>
      )}

      {/* Recent Errors */}
      {errors.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Recent Errors</h3>
          <div className="space-y-3">
            {errors.slice(0, 5).map(error => (
              <div key={error.id} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-900/50 rounded-lg">
                <div className={cn(
                  'w-2 h-2 rounded-full mt-2',
                  error.severity === 'critical' ? 'bg-red-500' :
                  error.severity === 'high' ? 'bg-orange-500' :
                  error.severity === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                )} />
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
                    {error.message}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {error.timestamp.toLocaleString()} • {error.url}
                  </div>
                </div>
                <div className={cn(
                  'text-xs px-2 py-1 rounded',
                  error.severity === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                  error.severity === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400' :
                  error.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                  'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400'
                )}>
                  {error.severity}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default ComprehensivePerformanceMonitoring;
