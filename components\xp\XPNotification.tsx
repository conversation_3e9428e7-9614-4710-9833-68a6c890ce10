'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Zap, Star, Trophy, Target, BookOpen, Code, Award, Flame, TrendingUp, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import GamificationAnalytics from '@/lib/analytics/GamificationAnalytics';
import OfflineProgressStorage from '@/lib/storage/OfflineProgressStorage';

export interface XPGain {
  id: string;
  amount: number;
  source: string;
  description: string;
  timestamp: Date;
  position?: { x: number; y: number };
  color?: string;
  icon?: React.ComponentType<any>;
  multiplier?: number;
  streak?: number;
  isBonus?: boolean;
  soundEnabled?: boolean;
}

interface XPNotificationProps {
  xpGain: XPGain;
  onComplete: () => void;
  duration?: number;
  className?: string;
  enableSound?: boolean;
}

export function XPNotification({
  xpGain,
  onComplete,
  duration = 3000,
  className,
  enableSound = true
}: XPNotificationProps) {
  const [isVisible, setIsVisible] = useState(true);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [analytics] = useState(() => GamificationAnalytics.getInstance());
  const [storage] = useState(() => OfflineProgressStorage.getInstance());

  // Play sound effect on mount and track performance
  useEffect(() => {
    const startTime = performance.now();

    if (enableSound && xpGain.soundEnabled !== false) {
      playXPSound();
    }

    // Track XP notification performance
    analytics.trackXPGained(xpGain.amount, xpGain.source, {
      renderTime: performance.now() - startTime,
      position: xpGain.position,
      hasMultiplier: !!xpGain.multiplier,
      hasStreak: !!xpGain.streak
    });

    // Save XP to offline storage
    storage.addXP(xpGain.amount, xpGain.source);
  }, [enableSound, xpGain, analytics, storage]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(false);
      setTimeout(onComplete, 500); // Wait for exit animation
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onComplete]);

  const playXPSound = () => {
    try {
      // Create different sounds based on XP amount and type
      const frequency = Math.min(800 + (xpGain.amount * 2), 1200);
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
      oscillator.type = xpGain.isBonus ? 'triangle' : 'sine';

      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(0.1, audioContext.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 0.3);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.3);
    } catch (error) {
      // Fallback for browsers that don't support Web Audio API
      console.log('XP sound effect not supported');
    }
  };

  const getSourceIcon = () => {
    if (xpGain.icon) return xpGain.icon;

    switch (xpGain.source) {
      case 'lesson':
        return BookOpen;
      case 'quiz':
        return Target;
      case 'project':
        return Code;
      case 'achievement':
        return Trophy;
      case 'streak':
        return Flame;
      case 'bonus':
        return Star;
      case 'completion':
        return CheckCircle;
      case 'speed':
        return TrendingUp;
      default:
        return Zap;
    }
  };

  const getColorClass = () => {
    if (xpGain.color) return xpGain.color;

    switch (xpGain.source) {
      case 'lesson':
        return 'text-blue-400';
      case 'quiz':
        return 'text-green-400';
      case 'project':
        return 'text-purple-400';
      case 'achievement':
        return 'text-yellow-400';
      case 'streak':
        return 'text-orange-400';
      case 'bonus':
        return 'text-pink-400';
      case 'completion':
        return 'text-emerald-400';
      case 'speed':
        return 'text-cyan-400';
      default:
        return 'text-yellow-400';
    }
  };

  const getBgColorClass = () => {
    const colorClass = getColorClass();
    return colorClass.replace('text-', 'bg-').replace('-400', '-500/20');
  };

  const getBorderColorClass = () => {
    const colorClass = getColorClass();
    return colorClass.replace('text-', 'border-').replace('-400', '-500/30');
  };

  const SourceIcon = getSourceIcon();
  const colorClass = getColorClass();
  const bgColorClass = getBgColorClass();
  const borderColorClass = getBorderColorClass();

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{
            opacity: 0,
            scale: 0.3,
            y: 20,
            x: 0,
            rotate: -10
          }}
          animate={{
            opacity: 1,
            scale: xpGain.isBonus ? [1, 1.2, 1] : 1,
            y: -80,
            x: (Math.random() - 0.5) * 60,
            rotate: 0
          }}
          exit={{
            opacity: 0,
            scale: 0.5,
            y: -120,
            rotate: 10
          }}
          transition={{
            type: 'spring',
            stiffness: 400,
            damping: 20,
            duration: 0.8
          }}
          className={cn(
            'fixed z-50 pointer-events-none',
            'flex items-center space-x-3 px-5 py-3',
            'backdrop-blur-md border-2 rounded-2xl',
            'shadow-2xl',
            bgColorClass,
            borderColorClass,
            xpGain.isBonus && 'ring-2 ring-yellow-400/50',
            className
          )}
          style={{
            left: xpGain.position?.x ? `${xpGain.position.x}px` : '50%',
            top: xpGain.position?.y ? `${xpGain.position.y}px` : '50%',
            transform: !xpGain.position ? 'translate(-50%, -50%)' : undefined
          }}
        >
          {/* Icon */}
          <motion.div
            initial={{ rotate: -180, scale: 0 }}
            animate={{ rotate: 0, scale: 1 }}
            transition={{
              type: 'spring',
              stiffness: 300,
              damping: 20,
              delay: 0.1
            }}
            className={cn(
              'w-10 h-10 rounded-full flex items-center justify-center',
              'bg-gradient-to-br from-white/10 to-white/5',
              'border-2',
              borderColorClass
            )}
          >
            <SourceIcon className={cn('w-5 h-5', colorClass)} />
          </motion.div>

          {/* XP Amount and Multiplier */}
          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
            className="flex flex-col items-start"
          >
            <div className="flex items-center space-x-2">
              <span className={cn('text-2xl font-bold font-mono', colorClass)}>
                +{xpGain.amount}
              </span>
              <span className="text-sm font-medium text-white/80">XP</span>

              {/* Multiplier Badge */}
              {xpGain.multiplier && xpGain.multiplier > 1 && (
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.4, type: 'spring', stiffness: 400 }}
                  className="px-2 py-1 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full text-xs font-bold text-white"
                >
                  {xpGain.multiplier}x
                </motion.div>
              )}

              {/* Streak Indicator */}
              {xpGain.streak && xpGain.streak > 1 && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.5, type: 'spring' }}
                  className="flex items-center space-x-1 px-2 py-1 bg-orange-500/20 border border-orange-500/30 rounded-full"
                >
                  <Flame className="w-3 h-3 text-orange-400" />
                  <span className="text-xs font-bold text-orange-300">{xpGain.streak}</span>
                </motion.div>
              )}
            </div>

            {/* Description */}
            <motion.span
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="text-xs text-white/60 font-medium"
            >
              {xpGain.description}
            </motion.span>
          </motion.div>

          {/* Sparkle Effects */}
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              initial={{
                opacity: 0,
                scale: 0,
                x: 0,
                y: 0
              }}
              animate={{
                opacity: [0, 1, 0],
                scale: [0, 1.5, 0],
                x: (Math.random() - 0.5) * 100,
                y: (Math.random() - 0.5) * 100
              }}
              transition={{
                duration: 2,
                delay: 0.3 + i * 0.1,
                ease: 'easeOut'
              }}
              className={cn(
                'absolute w-2 h-2 rounded-full pointer-events-none',
                colorClass.replace('text-', 'bg-')
              )}
              style={{
                left: '50%',
                top: '50%'
              }}
            />
          ))}

          {/* Bonus Glow Effect */}
          {xpGain.isBonus && (
            <motion.div
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: [0, 0.8, 0], scale: [0, 2, 0] }}
              transition={{ duration: 1.5, repeat: 2 }}
              className={cn(
                'absolute inset-0 rounded-2xl blur-xl',
                bgColorClass.replace('/20', '/40')
              )}
            />
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// XP Notification Manager Component
interface XPNotificationManagerProps {
  className?: string;
  enableSound?: boolean;
  maxNotifications?: number;
}

export function XPNotificationManager({
  className,
  enableSound = true,
  maxNotifications = 5
}: XPNotificationManagerProps) {
  const [notifications, setNotifications] = useState<XPGain[]>([]);

  // Function to add new XP notification
  const addXPNotification = (xpGain: Omit<XPGain, 'id' | 'timestamp'>) => {
    const notification: XPGain = {
      ...xpGain,
      id: `xp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      soundEnabled: enableSound
    };

    setNotifications(prev => {
      const updated = [...prev, notification];
      // Keep only the most recent notifications
      return updated.slice(-maxNotifications);
    });
  };

  // Function to remove notification
  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // Expose addXPNotification globally for easy access
  useEffect(() => {
    (window as any).addXPNotification = addXPNotification;
    
    return () => {
      delete (window as any).addXPNotification;
    };
  }, []);

  return (
    <div className={cn('fixed inset-0 pointer-events-none z-50', className)}>
      {notifications.map(notification => (
        <XPNotification
          key={notification.id}
          xpGain={notification}
          onComplete={() => removeNotification(notification.id)}
        />
      ))}
    </div>
  );
}

// Hook for triggering XP notifications
export function useXPNotifications() {
  const triggerXPNotification = (xpGain: Omit<XPGain, 'id' | 'timestamp'>) => {
    const analytics = GamificationAnalytics.getInstance();

    // Measure notification trigger performance
    analytics.measureOperation('xp_notification_trigger', () => {
      if (typeof window !== 'undefined' && (window as any).addXPNotification) {
        (window as any).addXPNotification(xpGain);
      }
    });
  };

  const triggerXPGain = (
    amount: number,
    source: string,
    description: string,
    position?: { x: number; y: number },
    options?: {
      color?: string;
      icon?: React.ComponentType<any>;
      multiplier?: number;
      streak?: number;
      isBonus?: boolean;
      soundEnabled?: boolean;
    }
  ) => {
    triggerXPNotification({
      amount,
      source,
      description,
      position,
      ...options
    });
  };

  // Convenience methods for common XP gains
  const triggerLessonXP = (amount: number, lessonName: string, position?: { x: number; y: number }) => {
    triggerXPGain(amount, 'lesson', `Completed: ${lessonName}`, position, {
      icon: BookOpen,
      color: 'text-blue-400'
    });
  };

  const triggerQuizXP = (amount: number, score: number, position?: { x: number; y: number }) => {
    triggerXPGain(amount, 'quiz', `Quiz Score: ${score}%`, position, {
      icon: Target,
      color: 'text-green-400'
    });
  };

  const triggerStreakXP = (amount: number, streakDays: number, position?: { x: number; y: number }) => {
    triggerXPGain(amount, 'streak', `${streakDays} day streak!`, position, {
      icon: Flame,
      color: 'text-orange-400',
      streak: streakDays,
      isBonus: streakDays >= 7
    });
  };

  const triggerBonusXP = (amount: number, reason: string, multiplier: number, position?: { x: number; y: number }) => {
    triggerXPGain(amount, 'bonus', reason, position, {
      icon: Star,
      color: 'text-pink-400',
      multiplier,
      isBonus: true
    });
  };

  const triggerAchievementXP = (amount: number, achievementName: string, position?: { x: number; y: number }) => {
    triggerXPGain(amount, 'achievement', `Achievement: ${achievementName}`, position, {
      icon: Trophy,
      color: 'text-yellow-400',
      isBonus: true
    });
  };

  return {
    triggerXPNotification,
    triggerXPGain,
    triggerLessonXP,
    triggerQuizXP,
    triggerStreakXP,
    triggerBonusXP,
    triggerAchievementXP
  };
}
