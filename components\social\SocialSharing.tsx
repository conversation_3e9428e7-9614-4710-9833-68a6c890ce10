'use client';

import React from 'react';
import { SocialSharingSystem } from './SocialSharingSystem';

interface SocialSharingProps {
  onShare: (platform: string, content: any) => void;
  userProgress: any;
  className?: string;
}

export function SocialSharing({ onShare, userProgress, className }: SocialSharingProps) {
  return (
    <SocialSharingSystem
      className={className}
      progressData={userProgress}
      shareType="progress"
      onShare={onShare}
    />
  );
}

export default SocialSharing;
