# API Reference

## Overview

The Solidity Learning Platform provides a comprehensive REST API for managing users, courses, progress tracking, analytics, and more. All API endpoints follow RESTful conventions and return JSON responses.

## Base URL

- **Development**: `http://localhost:3000/api`
- **Production**: `https://your-domain.com/api`

## Authentication

The API uses JWT tokens for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Authentication Endpoints

#### POST /api/auth/signin

Authenticate a user and return a JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-123",
      "email": "<EMAIL>",
      "name": "<PERSON>",
      "role": "student"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### POST /api/auth/signup

Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "John Doe",
  "role": "student"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "student",
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

#### POST /api/auth/signout

Sign out the current user and invalidate the token.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully signed out"
}
```

#### GET /api/auth/session

Get the current user session information.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-123",
      "email": "<EMAIL>",
      "name": "John Doe",
      "role": "student",
      "profile": {
        "avatar": "https://example.com/avatar.jpg",
        "bio": "Learning Solidity",
        "experience": "beginner"
      }
    },
    "session": {
      "expiresAt": "2024-01-01T00:00:00.000Z",
      "lastActivity": "2024-01-01T00:00:00.000Z"
    }
  }
}
```

## User Management

#### GET /api/users/profile

Get the current user's profile information.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user-123",
    "email": "<EMAIL>",
    "name": "John Doe",
    "profile": {
      "avatar": "https://example.com/avatar.jpg",
      "bio": "Learning Solidity development",
      "experience": "beginner",
      "interests": ["blockchain", "smart-contracts"],
      "goals": ["learn-solidity", "build-dapp"]
    },
    "stats": {
      "coursesCompleted": 3,
      "lessonsCompleted": 25,
      "totalXP": 1250,
      "currentLevel": 5,
      "streak": 7
    }
  }
}
```

#### PUT /api/users/profile

Update the current user's profile.

**Headers:**
```
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "name": "John Smith",
  "profile": {
    "bio": "Blockchain developer",
    "experience": "intermediate",
    "interests": ["blockchain", "defi", "nfts"]
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "user-123",
    "name": "John Smith",
    "profile": {
      "bio": "Blockchain developer",
      "experience": "intermediate",
      "interests": ["blockchain", "defi", "nfts"]
    }
  }
}
```

## Course Management

#### GET /api/courses

Get a list of all available courses.

**Query Parameters:**
- `page` (number): Page number for pagination (default: 1)
- `limit` (number): Number of items per page (default: 10)
- `difficulty` (string): Filter by difficulty level
- `category` (string): Filter by category

**Response:**
```json
{
  "success": true,
  "data": {
    "courses": [
      {
        "id": "course-123",
        "title": "Solidity Fundamentals",
        "description": "Learn the basics of Solidity programming",
        "difficulty": "beginner",
        "category": "programming",
        "duration": 120,
        "lessonsCount": 12,
        "studentsCount": 1500,
        "rating": 4.8,
        "thumbnail": "https://example.com/course-thumb.jpg",
        "instructor": {
          "id": "instructor-123",
          "name": "Jane Doe",
          "avatar": "https://example.com/instructor.jpg"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

#### GET /api/courses/:id

Get detailed information about a specific course.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "course-123",
    "title": "Solidity Fundamentals",
    "description": "Learn the basics of Solidity programming",
    "difficulty": "beginner",
    "category": "programming",
    "duration": 120,
    "lessonsCount": 12,
    "studentsCount": 1500,
    "rating": 4.8,
    "thumbnail": "https://example.com/course-thumb.jpg",
    "syllabus": [
      {
        "id": "module-1",
        "title": "Introduction to Solidity",
        "lessons": [
          {
            "id": "lesson-1",
            "title": "What is Solidity?",
            "duration": 10,
            "type": "video"
          }
        ]
      }
    ],
    "instructor": {
      "id": "instructor-123",
      "name": "Jane Doe",
      "bio": "Blockchain expert with 5+ years experience",
      "avatar": "https://example.com/instructor.jpg"
    },
    "prerequisites": ["basic-programming"],
    "learningOutcomes": [
      "Understand Solidity syntax",
      "Write smart contracts",
      "Deploy to testnet"
    ]
  }
}
```

## Progress Tracking

#### GET /api/progress

Get the current user's learning progress.

**Headers:**
```
Authorization: Bearer <token>
```

**Query Parameters:**
- `courseId` (string): Filter by specific course

**Response:**
```json
{
  "success": true,
  "data": {
    "overall": {
      "coursesEnrolled": 5,
      "coursesCompleted": 2,
      "lessonsCompleted": 45,
      "totalXP": 2250,
      "currentLevel": 8,
      "streak": 12
    },
    "courses": [
      {
        "courseId": "course-123",
        "title": "Solidity Fundamentals",
        "progress": 75,
        "lessonsCompleted": 9,
        "totalLessons": 12,
        "lastAccessed": "2024-01-01T00:00:00.000Z",
        "timeSpent": 180,
        "xpEarned": 450
      }
    ]
  }
}
```

#### POST /api/progress/lesson

Update progress for a specific lesson.

**Headers:**
```
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "lessonId": "lesson-123",
  "courseId": "course-123",
  "completed": true,
  "score": 95,
  "timeSpent": 15,
  "codeSubmitted": "contract MyContract { ... }"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "lessonProgress": {
      "lessonId": "lesson-123",
      "completed": true,
      "score": 95,
      "timeSpent": 15,
      "completedAt": "2024-01-01T00:00:00.000Z"
    },
    "xpEarned": 50,
    "newAchievements": [
      {
        "id": "achievement-123",
        "name": "First Contract",
        "description": "Completed your first smart contract",
        "icon": "🎉",
        "xpReward": 100
      }
    ],
    "levelUp": {
      "newLevel": 9,
      "previousLevel": 8,
      "xpRequired": 2500,
      "xpCurrent": 2300
    }
  }
}
```

## Analytics

#### POST /api/analytics/event

Track a custom analytics event.

**Headers:**
```
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "event": "lesson_completed",
  "properties": {
    "lessonId": "lesson-123",
    "courseId": "course-123",
    "timeSpent": 15,
    "score": 95
  },
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Event tracked successfully"
}
```

#### GET /api/analytics/user

Get analytics data for the current user.

**Headers:**
```
Authorization: Bearer <token>
```

**Query Parameters:**
- `period` (string): Time period (7d, 30d, 90d, 1y)
- `metrics` (string[]): Specific metrics to include

**Response:**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalTimeSpent": 1200,
      "lessonsCompleted": 45,
      "averageScore": 87,
      "streakDays": 12,
      "xpEarned": 2250
    },
    "timeline": [
      {
        "date": "2024-01-01",
        "timeSpent": 60,
        "lessonsCompleted": 2,
        "xpEarned": 100
      }
    ],
    "performance": {
      "strongAreas": ["variables", "functions"],
      "improvementAreas": ["inheritance", "events"],
      "averageCompletionTime": 12.5
    }
  }
}
```

## Achievements & Gamification

#### GET /api/achievements

Get user achievements and available badges.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "earned": [
      {
        "id": "achievement-123",
        "name": "First Contract",
        "description": "Completed your first smart contract",
        "icon": "🎉",
        "category": "milestone",
        "xpReward": 100,
        "earnedAt": "2024-01-01T00:00:00.000Z"
      }
    ],
    "available": [
      {
        "id": "achievement-456",
        "name": "Speed Learner",
        "description": "Complete 5 lessons in one day",
        "icon": "⚡",
        "category": "performance",
        "xpReward": 200,
        "progress": 3,
        "target": 5
      }
    ],
    "stats": {
      "totalEarned": 15,
      "totalAvailable": 50,
      "totalXP": 2250,
      "currentLevel": 8
    }
  }
}
```

## Error Handling

All API endpoints return consistent error responses:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": [
      {
        "field": "email",
        "message": "Email is required"
      }
    ]
  }
}
```

### Common Error Codes

- `VALIDATION_ERROR`: Invalid input data
- `AUTHENTICATION_ERROR`: Invalid or missing authentication
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `INTERNAL_ERROR`: Server error

## Rate Limiting

API endpoints are rate limited to prevent abuse:

- **Authentication endpoints**: 5 requests per minute
- **General endpoints**: 100 requests per minute
- **Analytics endpoints**: 1000 requests per hour

Rate limit headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

## Webhooks

The platform supports webhooks for real-time notifications:

#### POST /api/webhooks/register

Register a webhook endpoint.

**Headers:**
```
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "url": "https://your-app.com/webhook",
  "events": ["lesson_completed", "achievement_earned"],
  "secret": "your-webhook-secret"
}
```

### Webhook Events

- `lesson_completed`: User completes a lesson
- `course_completed`: User completes a course
- `achievement_earned`: User earns an achievement
- `level_up`: User levels up
- `streak_milestone`: User reaches streak milestone

## SDK and Libraries

### JavaScript/TypeScript SDK

```bash
npm install @solidity-learning/sdk
```

```typescript
import { SolidityLearningAPI } from '@solidity-learning/sdk';

const api = new SolidityLearningAPI({
  baseURL: 'https://api.solidity-learning.com',
  apiKey: 'your-api-key'
});

// Get user progress
const progress = await api.progress.get();

// Track lesson completion
await api.progress.completeLesson({
  lessonId: 'lesson-123',
  score: 95
});
```

### Python SDK

```bash
pip install solidity-learning-sdk
```

```python
from solidity_learning import SolidityLearningAPI

api = SolidityLearningAPI(
    base_url='https://api.solidity-learning.com',
    api_key='your-api-key'
)

# Get user progress
progress = api.progress.get()

# Track lesson completion
api.progress.complete_lesson(
    lesson_id='lesson-123',
    score=95
)
```

## Testing

Use the test environment for development:

- **Base URL**: `https://api-test.solidity-learning.com`
- **Test API Key**: Available in developer dashboard
- **Rate Limits**: Relaxed for testing

## Support

- **Documentation**: [docs.solidity-learning.com](https://docs.solidity-learning.com)
- **API Status**: [status.solidity-learning.com](https://status.solidity-learning.com)
- **Support**: [<EMAIL>](mailto:<EMAIL>)
