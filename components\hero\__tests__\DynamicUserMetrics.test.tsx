/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { DynamicUserMetrics, CompactUserMetrics } from '../DynamicUserMetrics';

// Mock GSAP
jest.mock('gsap', () => ({
  gsap: {
    set: jest.fn(),
    to: jest.fn(() => ({ kill: jest.fn() })),
    registerPlugin: jest.fn(),
  },
}));

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
mockIntersectionObserver.mockReturnValue({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
});
window.IntersectionObserver = mockIntersectionObserver;

describe('DynamicUserMetrics', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all metric cards with correct labels', () => {
    render(<DynamicUserMetrics />);
    
    expect(screen.getByText('Active Learners')).toBeInTheDocument();
    expect(screen.getByText('Courses')).toBeInTheDocument();
    expect(screen.getByText('Projects Built')).toBeInTheDocument();
    expect(screen.getByText('Success Rate')).toBeInTheDocument();
  });

  it('displays metric values with proper formatting', async () => {
    render(<DynamicUserMetrics animated={false} />);
    
    await waitFor(() => {
      expect(screen.getByText('10,247')).toBeInTheDocument();
      expect(screen.getByText('52')).toBeInTheDocument();
      expect(screen.getByText('25,834')).toBeInTheDocument();
      expect(screen.getByText('94')).toBeInTheDocument();
    });
  });

  it('shows descriptions when showDescription is true', () => {
    render(<DynamicUserMetrics showDescription={true} />);
    
    expect(screen.getByText('Developers currently learning Solidity')).toBeInTheDocument();
    expect(screen.getByText('Comprehensive learning modules')).toBeInTheDocument();
    expect(screen.getByText('Smart contracts deployed')).toBeInTheDocument();
    expect(screen.getByText('Course completion rate')).toBeInTheDocument();
  });

  it('hides descriptions when showDescription is false', () => {
    render(<DynamicUserMetrics showDescription={false} />);
    
    expect(screen.queryByText('Developers currently learning Solidity')).not.toBeInTheDocument();
    expect(screen.queryByText('Comprehensive learning modules')).not.toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(<DynamicUserMetrics className="custom-class" />);
    
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('has proper accessibility attributes', () => {
    render(<DynamicUserMetrics />);
    
    const region = screen.getByRole('region', { name: 'Platform statistics' });
    expect(region).toBeInTheDocument();
    
    // Check for aria-live regions on counters
    const counters = screen.getAllByText(/\d+/);
    counters.forEach(counter => {
      expect(counter).toHaveAttribute('aria-live', 'polite');
    });
  });

  it('sets up intersection observer for animation trigger', () => {
    render(<DynamicUserMetrics />);
    
    expect(mockIntersectionObserver).toHaveBeenCalledWith(
      expect.any(Function),
      { threshold: 0.3 }
    );
  });

  it('skips animation when animated is false', () => {
    render(<DynamicUserMetrics animated={false} />);
    
    // Values should be set immediately without animation
    expect(screen.getByText('10,247')).toBeInTheDocument();
  });

  it('handles hover interactions correctly', async () => {
    const { container } = render(<DynamicUserMetrics />);
    
    const cards = container.querySelectorAll('.group');
    expect(cards).toHaveLength(4);
    
    // Each card should have hover effects
    cards.forEach(card => {
      expect(card).toHaveClass('group');
    });
  });
});

describe('CompactUserMetrics', () => {
  it('renders compact version with correct metrics', () => {
    render(<CompactUserMetrics />);
    
    expect(screen.getByText('10,000+')).toBeInTheDocument();
    expect(screen.getByText('learners')).toBeInTheDocument();
    expect(screen.getByText('94%')).toBeInTheDocument();
    expect(screen.getByText('success')).toBeInTheDocument();
    expect(screen.getByText('25k+')).toBeInTheDocument();
    expect(screen.getByText('projects')).toBeInTheDocument();
  });

  it('displays appropriate icons', () => {
    const { container } = render(<CompactUserMetrics />);
    
    // Check for icon elements (Lucide icons render as SVGs)
    const icons = container.querySelectorAll('svg');
    expect(icons).toHaveLength(3); // Users, Star, TrendingUp icons
  });

  it('applies custom className to compact version', () => {
    const { container } = render(<CompactUserMetrics className="compact-custom" />);
    
    expect(container.firstChild).toHaveClass('compact-custom');
  });

  it('has proper motion animation attributes', () => {
    const { container } = render(<CompactUserMetrics />);
    
    // Check that the component is wrapped in motion.div
    expect(container.firstChild).toHaveAttribute('style');
  });
});

describe('DynamicUserMetrics Accessibility', () => {
  it('meets WCAG accessibility standards', () => {
    render(<DynamicUserMetrics />);
    
    // Check for proper heading structure
    const region = screen.getByRole('region');
    expect(region).toHaveAttribute('aria-label', 'Platform statistics');
    
    // Check for proper color contrast (icons should have appropriate colors)
    const icons = screen.getAllByRole('img', { hidden: true });
    expect(icons.length).toBeGreaterThan(0);
  });

  it('supports keyboard navigation', () => {
    render(<DynamicUserMetrics />);
    
    // Cards should be focusable for keyboard users
    const cards = screen.getAllByRole('generic');
    cards.forEach(card => {
      // Motion components should maintain accessibility
      expect(card).toBeInTheDocument();
    });
  });

  it('provides screen reader announcements', () => {
    render(<DynamicUserMetrics />);
    
    // Check for aria-live regions
    const liveRegions = screen.getAllByText(/\d+/);
    liveRegions.forEach(region => {
      expect(region).toHaveAttribute('aria-live', 'polite');
    });
  });
});

describe('DynamicUserMetrics Performance', () => {
  it('cleans up intersection observer on unmount', () => {
    const mockDisconnect = jest.fn();
    mockIntersectionObserver.mockReturnValue({
      observe: jest.fn(),
      unobserve: jest.fn(),
      disconnect: mockDisconnect,
    });

    const { unmount } = render(<DynamicUserMetrics />);
    unmount();

    expect(mockDisconnect).toHaveBeenCalled();
  });

  it('handles real-time updates efficiently', async () => {
    jest.useFakeTimers();
    
    render(<DynamicUserMetrics />);
    
    // Fast-forward time to trigger updates
    jest.advanceTimersByTime(30000);
    
    // Component should handle updates without errors
    expect(screen.getByText('Active Learners')).toBeInTheDocument();
    
    jest.useRealTimers();
  });

  it('respects reduced motion preferences', () => {
    // Mock matchMedia for reduced motion
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: query === '(prefers-reduced-motion: reduce)',
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    render(<DynamicUserMetrics />);
    
    // Animation should be skipped for users who prefer reduced motion
    expect(screen.getByText('Active Learners')).toBeInTheDocument();
  });
});
