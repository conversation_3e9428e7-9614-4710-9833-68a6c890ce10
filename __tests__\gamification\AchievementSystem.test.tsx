import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { act } from 'react-dom/test-utils';
import '@testing-library/jest-dom';
import { AchievementCelebration, AchievementToast } from '@/components/gamification/AchievementCelebration';
import { MicroAchievementManager, useMicroAchievements } from '@/components/gamification/MicroAchievements';
import { AchievementNotificationManager } from '@/components/gamification/AchievementNotificationManager';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock achievement data
const mockAchievement = {
  id: 'test-achievement',
  title: 'Test Achievement',
  description: 'A test achievement',
  longDescription: 'This is a longer description of the test achievement',
  rarity: 'rare' as const,
  category: 'learning' as const,
  requirements: {
    type: 'lesson_completion' as const,
    target: 5,
    current: 5
  },
  rewards: {
    xp: 100,
    badge: 'Test Badge',
    title: 'Test Title',
    unlocks: ['Feature 1', 'Feature 2']
  },
  unlockedAt: new Date(),
  progress: 100
};

describe('AchievementCelebration', () => {
  const mockOnClose = jest.fn();
  const mockOnShare = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders achievement celebration with correct content', () => {
    render(
      <AchievementCelebration
        achievement={mockAchievement}
        onClose={mockOnClose}
        onShare={mockOnShare}
      />
    );

    expect(screen.getByText('Achievement Unlocked!')).toBeInTheDocument();
    expect(screen.getByText('Test Achievement')).toBeInTheDocument();
    expect(screen.getByText('A test achievement')).toBeInTheDocument();
    expect(screen.getByText('+100 XP')).toBeInTheDocument();
  });

  it('displays rarity correctly', () => {
    render(
      <AchievementCelebration
        achievement={mockAchievement}
        onClose={mockOnClose}
      />
    );

    expect(screen.getByText('rare')).toBeInTheDocument();
  });

  it('shows rewards section', () => {
    render(
      <AchievementCelebration
        achievement={mockAchievement}
        onClose={mockOnClose}
      />
    );

    expect(screen.getByText('Additional Rewards')).toBeInTheDocument();
    expect(screen.getByText('Badge: Test Badge')).toBeInTheDocument();
    expect(screen.getByText('Title: Test Title')).toBeInTheDocument();
  });

  it('handles close button click', () => {
    render(
      <AchievementCelebration
        achievement={mockAchievement}
        onClose={mockOnClose}
      />
    );

    const closeButton = screen.getByLabelText('Close celebration');
    fireEvent.click(closeButton);

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('handles share button clicks', () => {
    render(
      <AchievementCelebration
        achievement={mockAchievement}
        onClose={mockOnClose}
        onShare={mockOnShare}
      />
    );

    const twitterButton = screen.getByText('Twitter');
    fireEvent.click(twitterButton);

    expect(mockOnShare).toHaveBeenCalledWith('twitter');
  });

  it('auto-closes when enabled', async () => {
    jest.useFakeTimers();

    render(
      <AchievementCelebration
        achievement={mockAchievement}
        onClose={mockOnClose}
        autoClose={true}
        autoCloseDelay={1000}
      />
    );

    act(() => {
      jest.advanceTimersByTime(1000);
    });

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });

    jest.useRealTimers();
  });

  it('renders confetti when enabled', () => {
    const { container } = render(
      <AchievementCelebration
        achievement={mockAchievement}
        onClose={mockOnClose}
        showConfetti={true}
      />
    );

    // Check for confetti elements
    const confettiElements = container.querySelectorAll('[class*="absolute"]');
    expect(confettiElements.length).toBeGreaterThan(0);
  });
});

describe('AchievementToast', () => {
  const mockOnClose = jest.fn();
  const mockOnClick = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders toast notification', () => {
    render(
      <AchievementToast
        achievement={mockAchievement}
        onClose={mockOnClose}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText('Achievement Unlocked!')).toBeInTheDocument();
    expect(screen.getByText('Test Achievement')).toBeInTheDocument();
  });

  it('auto-closes after duration', async () => {
    jest.useFakeTimers();

    render(
      <AchievementToast
        achievement={mockAchievement}
        onClose={mockOnClose}
        duration={1000}
      />
    );

    act(() => {
      jest.advanceTimersByTime(1000);
    });

    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    });

    jest.useRealTimers();
  });

  it('handles click events', () => {
    render(
      <AchievementToast
        achievement={mockAchievement}
        onClose={mockOnClose}
        onClick={mockOnClick}
      />
    );

    const toast = screen.getByText('Test Achievement').closest('div');
    if (toast) {
      fireEvent.click(toast);
      expect(mockOnClick).toHaveBeenCalled();
    }
  });
});

describe('MicroAchievementManager', () => {
  const mockOnAchievementUnlocked = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    delete (window as any).checkMicroAchievement;
  });

  it('renders without crashing', () => {
    render(
      <MicroAchievementManager
        onAchievementUnlocked={mockOnAchievementUnlocked}
      />
    );
  });

  it('exposes checkMicroAchievement function globally', () => {
    render(
      <MicroAchievementManager
        onAchievementUnlocked={mockOnAchievementUnlocked}
      />
    );

    expect(typeof (window as any).checkMicroAchievement).toBe('function');
  });

  it('triggers achievement when condition is met', () => {
    render(
      <MicroAchievementManager
        onAchievementUnlocked={mockOnAchievementUnlocked}
      />
    );

    act(() => {
      (window as any).checkMicroAchievement('first-line');
    });

    expect(mockOnAchievementUnlocked).toHaveBeenCalled();
  });

  it('does not trigger same achievement twice', () => {
    render(
      <MicroAchievementManager
        onAchievementUnlocked={mockOnAchievementUnlocked}
      />
    );

    act(() => {
      (window as any).checkMicroAchievement('first-line');
      (window as any).checkMicroAchievement('first-line');
    });

    expect(mockOnAchievementUnlocked).toHaveBeenCalledTimes(1);
  });
});

describe('useMicroAchievements hook', () => {
  const TestComponent = () => {
    const { triggerMicroAchievement } = useMicroAchievements();

    return (
      <button onClick={() => triggerMicroAchievement('test-trigger')}>
        Trigger Achievement
      </button>
    );
  };

  beforeEach(() => {
    (window as any).checkMicroAchievement = jest.fn();
  });

  it('calls global function when triggered', () => {
    render(<TestComponent />);

    fireEvent.click(screen.getByText('Trigger Achievement'));

    expect((window as any).checkMicroAchievement).toHaveBeenCalledWith('test-trigger', undefined);
  });
});

describe('AchievementNotificationManager', () => {
  const mockOnShare = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    delete (window as any).addAchievementNotification;
  });

  it('renders without crashing', () => {
    render(
      <AchievementNotificationManager
        onShare={mockOnShare}
      />
    );
  });

  it('exposes addAchievementNotification function globally', () => {
    render(
      <AchievementNotificationManager
        onShare={mockOnShare}
      />
    );

    expect(typeof (window as any).addAchievementNotification).toBe('function');
  });

  it('handles major achievements with celebration', () => {
    const { container } = render(
      <AchievementNotificationManager
        onShare={mockOnShare}
      />
    );

    act(() => {
      (window as any).addAchievementNotification(
        { ...mockAchievement, rarity: 'legendary' },
        'major',
        10
      );
    });

    // Should show celebration modal for major achievements
    expect(container.querySelector('[class*="fixed"]')).toBeInTheDocument();
  });

  it('limits toast notifications', () => {
    const { container } = render(
      <AchievementNotificationManager
        maxToasts={2}
        onShare={mockOnShare}
      />
    );

    act(() => {
      // Add more toasts than the limit
      for (let i = 0; i < 5; i++) {
        (window as any).addAchievementNotification(
          { ...mockAchievement, id: `achievement-${i}` },
          'toast',
          1
        );
      }
    });

    // Should only show maximum number of toasts
    const toasts = container.querySelectorAll('[class*="top-4"]');
    expect(toasts.length).toBeLessThanOrEqual(2);
  });
});

describe('Performance Tests', () => {
  it('achievement celebration renders within performance threshold', () => {
    const startTime = performance.now();

    render(
      <AchievementCelebration
        achievement={mockAchievement}
        onClose={() => {}}
      />
    );

    const renderTime = performance.now() - startTime;

    // Should render within 100ms
    expect(renderTime).toBeLessThan(100);
  });

  it('handles multiple achievement notifications efficiently', () => {
    const startTime = performance.now();

    render(<AchievementNotificationManager />);

    act(() => {
      for (let i = 0; i < 10; i++) {
        (window as any).addAchievementNotification(
          { ...mockAchievement, id: `perf-test-${i}` },
          'toast',
          1
        );
      }
    });

    const processingTime = performance.now() - startTime;

    // Should process 10 notifications within 200ms
    expect(processingTime).toBeLessThan(200);
  });
});

describe('Accessibility Tests', () => {
  it('achievement celebration has proper focus management', () => {
    render(
      <AchievementCelebration
        achievement={mockAchievement}
        onClose={() => {}}
      />
    );

    // Should have focusable elements
    const closeButton = screen.getByLabelText('Close celebration');
    expect(closeButton).toBeInTheDocument();
    expect(closeButton.getAttribute('tabIndex')).not.toBe('-1');
  });

  it('provides screen reader accessible content', () => {
    render(
      <AchievementToast
        achievement={mockAchievement}
        onClose={() => {}}
      />
    );

    // Should have descriptive text for screen readers
    expect(screen.getByText('Achievement Unlocked!')).toBeInTheDocument();
    expect(screen.getByText('Test Achievement')).toBeInTheDocument();
    expect(screen.getByText('A test achievement')).toBeInTheDocument();
  });

  it('supports keyboard navigation', () => {
    render(
      <AchievementCelebration
        achievement={mockAchievement}
        onClose={() => {}}
      />
    );

    const shareButtons = screen.getAllByRole('button');
    shareButtons.forEach(button => {
      expect(button.getAttribute('tabIndex')).not.toBe('-1');
    });
  });

  it('has sufficient color contrast for rarity indicators', () => {
    const { container } = render(
      <AchievementToast
        achievement={mockAchievement}
        onClose={() => {}}
      />
    );

    const rarityElement = screen.getByText('rare');
    expect(rarityElement).toBeInTheDocument();
    
    // Should have appropriate styling for accessibility
    const computedStyle = window.getComputedStyle(rarityElement);
    expect(computedStyle.color).toBeTruthy();
  });
});
