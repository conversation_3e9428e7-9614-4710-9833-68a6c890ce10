'use client';

import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';

// Analytics integrations
import { useEnhancedTracking } from './AnalyticsIntegration';
import { useABTesting } from './ABTestingFramework';
import { useFeedbackWidget } from './ConversionFunnelSystem';

// Performance monitoring
import { usePerformanceMonitoring } from './PerformanceMonitoring';

// Types
interface AnalyticsConfig {
  googleAnalyticsId?: string;
  hotjarId?: string;
  sentryDsn?: string;
  enableHeatmaps: boolean;
  enableSessionRecordings: boolean;
  enableUserConsent: boolean;
  enableABTesting: boolean;
  enablePerformanceMonitoring: boolean;
  enableFeedbackWidgets: boolean;
  privacyCompliant: boolean;
  trackingLevel: 'minimal' | 'standard' | 'enhanced';
}

interface UserSegment {
  id: string;
  name: string;
  criteria: {
    experience?: 'beginner' | 'intermediate' | 'advanced';
    engagement?: 'low' | 'medium' | 'high';
    conversionProbability?: number;
    churnRisk?: 'low' | 'medium' | 'high';
  };
}

interface ConversionFunnel {
  step: string;
  name: string;
  users: number;
  conversions: number;
  conversionRate: number;
  dropOffRate: number;
  averageTime: number;
}

interface AnalyticsContextType {
  config: AnalyticsConfig;
  userSegment: UserSegment | null;
  conversionFunnel: ConversionFunnel[];
  isLoading: boolean;
  error: string | null;
  
  // Methods
  trackEvent: (event: string, properties?: any) => void;
  trackConversion: (step: string, value?: number) => void;
  trackUserJourney: (step: string, metadata?: any) => void;
  identifyUser: (userId: string, traits?: any) => void;
  updateUserSegment: (segment: UserSegment) => void;
  
  // A/B Testing
  getVariant: (testId: string) => string;
  trackABConversion: (testId: string, variantId: string) => void;
  
  // Performance
  trackPerformanceMetric: (metric: string, value: number) => void;
  reportError: (error: Error, context?: any) => void;
  
  // Feedback
  showFeedbackWidget: (trigger: string) => void;
  collectFeedback: (feedback: any) => void;
}

const AnalyticsContext = createContext<AnalyticsContextType | null>(null);

interface ComprehensiveAnalyticsSystemProps {
  children: React.ReactNode;
  config: AnalyticsConfig;
  userId?: string;
  userTraits?: any;
}

export function ComprehensiveAnalyticsSystem({
  children,
  config,
  userId,
  userTraits
}: ComprehensiveAnalyticsSystemProps) {
  const router = useRouter();
  const [userSegment, setUserSegment] = useState<UserSegment | null>(null);
  const [conversionFunnel, setConversionFunnel] = useState<ConversionFunnel[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize analytics integrations
  const { 
    trackEvent: baseTrackEvent, 
    trackConversionTouchpoint,
    trackUserEngagement,
    isAnalyticsReady 
  } = useEnhancedTracking({
    googleAnalyticsId: config.googleAnalyticsId,
    hotjarId: config.hotjarId,
    enableHeatmaps: config.enableHeatmaps,
    enableSessionRecordings: config.enableSessionRecordings,
    enableUserConsent: config.enableUserConsent,
    privacyCompliant: config.privacyCompliant,
    trackingLevel: config.trackingLevel
  });

  const { 
    assignVariant, 
    trackConversion: trackABTestConversion 
  } = useABTesting();

  const { 
    triggerWidget,
    submitFeedback 
  } = useFeedbackWidget();

  const {
    trackMetric,
    reportError: reportPerformanceError,
    getMetrics
  } = usePerformanceMonitoring({
    enableRealTimeMonitoring: config.enablePerformanceMonitoring,
    enableErrorTracking: true,
    sentryDsn: config.sentryDsn
  });

  // Initialize user segmentation
  useEffect(() => {
    if (userId && userTraits) {
      const segment = determineUserSegment(userTraits);
      setUserSegment(segment);
      
      // Track user identification
      baseTrackEvent({
        event: 'user_identified',
        category: 'user',
        action: 'identify',
        customParameters: {
          userId,
          segment: segment.id,
          traits: userTraits
        }
      });
    }
  }, [userId, userTraits, baseTrackEvent]);

  // Initialize conversion funnel tracking
  useEffect(() => {
    const initializeFunnel = async () => {
      try {
        setIsLoading(true);
        const funnelData = await fetchConversionFunnelData();
        setConversionFunnel(funnelData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize analytics');
      } finally {
        setIsLoading(false);
      }
    };

    if (isAnalyticsReady) {
      initializeFunnel();
    }
  }, [isAnalyticsReady]);

  // Enhanced event tracking
  const trackEvent = useCallback((event: string, properties: any = {}) => {
    baseTrackEvent({
      event,
      category: properties.category || 'engagement',
      action: properties.action || event,
      customParameters: {
        ...properties,
        userSegment: userSegment?.id,
        timestamp: Date.now(),
        page: window.location.pathname,
        referrer: document.referrer
      }
    });
  }, [baseTrackEvent, userSegment]);

  // Conversion tracking
  const trackConversion = useCallback((step: string, value: number = 0) => {
    trackConversionTouchpoint(step, 'comprehensive_analytics', value);
    
    // Update funnel data
    setConversionFunnel(prev => prev.map(funnelStep => 
      funnelStep.step === step 
        ? { ...funnelStep, conversions: funnelStep.conversions + 1 }
        : funnelStep
    ));

    trackEvent('conversion', {
      category: 'conversion',
      action: step,
      value,
      funnelStep: step
    });
  }, [trackConversionTouchpoint, trackEvent]);

  // User journey tracking
  const trackUserJourney = useCallback((step: string, metadata: any = {}) => {
    trackEvent('user_journey', {
      category: 'journey',
      action: step,
      ...metadata,
      journeyStep: step,
      sessionId: getSessionId(),
      userSegment: userSegment?.id
    });
  }, [trackEvent, userSegment]);

  // User identification
  const identifyUser = useCallback((newUserId: string, traits: any = {}) => {
    trackEvent('user_identify', {
      category: 'user',
      action: 'identify',
      userId: newUserId,
      traits
    });

    // Update user segment based on new traits
    const segment = determineUserSegment(traits);
    setUserSegment(segment);
  }, [trackEvent]);

  // A/B Testing integration
  const getVariant = useCallback((testId: string) => {
    const variant = assignVariant(testId);
    
    trackEvent('ab_test_assignment', {
      category: 'experiment',
      action: 'assign',
      testId,
      variant,
      userSegment: userSegment?.id
    });

    return variant;
  }, [assignVariant, trackEvent, userSegment]);

  const trackABConversion = useCallback((testId: string, variantId: string) => {
    trackABTestConversion(testId, variantId);
    
    trackEvent('ab_test_conversion', {
      category: 'experiment',
      action: 'convert',
      testId,
      variant: variantId,
      userSegment: userSegment?.id
    });
  }, [trackABTestConversion, trackEvent, userSegment]);

  // Performance monitoring
  const trackPerformanceMetric = useCallback((metric: string, value: number) => {
    trackMetric(metric, value);
    
    trackEvent('performance_metric', {
      category: 'performance',
      action: metric,
      value,
      metric
    });
  }, [trackMetric, trackEvent]);

  const reportError = useCallback((error: Error, context: any = {}) => {
    reportPerformanceError(error, {
      ...context,
      userSegment: userSegment?.id,
      userId,
      page: window.location.pathname
    });

    trackEvent('error_reported', {
      category: 'error',
      action: 'report',
      error: error.message,
      stack: error.stack,
      ...context
    });
  }, [reportPerformanceError, trackEvent, userSegment, userId]);

  // Feedback collection
  const showFeedbackWidget = useCallback((trigger: string) => {
    triggerWidget(trigger);
    
    trackEvent('feedback_widget_shown', {
      category: 'feedback',
      action: 'show',
      trigger,
      userSegment: userSegment?.id
    });
  }, [triggerWidget, trackEvent, userSegment]);

  const collectFeedback = useCallback((feedback: any) => {
    submitFeedback(feedback);
    
    trackEvent('feedback_submitted', {
      category: 'feedback',
      action: 'submit',
      feedbackType: feedback.type,
      rating: feedback.rating,
      userSegment: userSegment?.id
    });
  }, [submitFeedback, trackEvent, userSegment]);

  const updateUserSegment = useCallback((segment: UserSegment) => {
    setUserSegment(segment);
    
    trackEvent('user_segment_updated', {
      category: 'user',
      action: 'segment_update',
      previousSegment: userSegment?.id,
      newSegment: segment.id
    });
  }, [trackEvent, userSegment]);

  const contextValue: AnalyticsContextType = {
    config,
    userSegment,
    conversionFunnel,
    isLoading,
    error,
    trackEvent,
    trackConversion,
    trackUserJourney,
    identifyUser,
    updateUserSegment,
    getVariant,
    trackABConversion,
    trackPerformanceMetric,
    reportError,
    showFeedbackWidget,
    collectFeedback
  };

  return (
    <AnalyticsContext.Provider value={contextValue}>
      {children}
    </AnalyticsContext.Provider>
  );
}

// Hook to use analytics context
export function useComprehensiveAnalytics() {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useComprehensiveAnalytics must be used within ComprehensiveAnalyticsSystem');
  }
  return context;
}

// Helper functions
function determineUserSegment(userTraits: any): UserSegment {
  // Simple segmentation logic - can be enhanced with ML
  const experience = userTraits.experience || 'beginner';
  const engagementScore = userTraits.engagementScore || 0;
  const completionRate = userTraits.completionRate || 0;

  let engagement: 'low' | 'medium' | 'high' = 'low';
  if (engagementScore > 70) engagement = 'high';
  else if (engagementScore > 40) engagement = 'medium';

  let churnRisk: 'low' | 'medium' | 'high' = 'low';
  if (completionRate < 30) churnRisk = 'high';
  else if (completionRate < 60) churnRisk = 'medium';

  const conversionProbability = Math.min(
    (engagementScore * 0.4 + completionRate * 0.6) / 100,
    1
  );

  return {
    id: `${experience}_${engagement}_${churnRisk}`,
    name: `${experience.charAt(0).toUpperCase() + experience.slice(1)} ${engagement.charAt(0).toUpperCase() + engagement.slice(1)} Engagement`,
    criteria: {
      experience,
      engagement,
      conversionProbability,
      churnRisk
    }
  };
}

async function fetchConversionFunnelData(): Promise<ConversionFunnel[]> {
  // Mock funnel data - replace with actual API call
  return [
    {
      step: 'awareness',
      name: 'Landing Page Visit',
      users: 10000,
      conversions: 3000,
      conversionRate: 30,
      dropOffRate: 70,
      averageTime: 45
    },
    {
      step: 'interest',
      name: 'Course Preview',
      users: 3000,
      conversions: 1200,
      conversionRate: 40,
      dropOffRate: 60,
      averageTime: 120
    },
    {
      step: 'trial',
      name: 'Trial Signup',
      users: 1200,
      conversions: 600,
      conversionRate: 50,
      dropOffRate: 50,
      averageTime: 300
    },
    {
      step: 'activation',
      name: 'First Lesson Complete',
      users: 600,
      conversions: 480,
      conversionRate: 80,
      dropOffRate: 20,
      averageTime: 900
    },
    {
      step: 'retention',
      name: 'Week 1 Active',
      users: 480,
      conversions: 360,
      conversionRate: 75,
      dropOffRate: 25,
      averageTime: 1800
    },
    {
      step: 'revenue',
      name: 'Paid Conversion',
      users: 360,
      conversions: 108,
      conversionRate: 30,
      dropOffRate: 70,
      averageTime: 2400
    }
  ];
}

function getSessionId(): string {
  let sessionId = sessionStorage.getItem('analytics_session_id');
  if (!sessionId) {
    sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('analytics_session_id', sessionId);
  }
  return sessionId;
}

export default ComprehensiveAnalyticsSystem;
