'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Crown, 
  Star, 
  Lock, 
  Unlock,
  Zap, 
  Trophy,
  Target,
  Code,
  Shield,
  Rocket,
  Sparkles,
  Gift,
  ChevronRight,
  Eye,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { GlassCard } from '@/components/ui/Glassmorphism';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import { ProgressBar } from '@/components/xp/ProgressBar';

export interface LevelReward {
  type: 'feature' | 'content' | 'cosmetic' | 'xp_bonus';
  name: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  value?: string | number;
}

export interface LevelInfo {
  level: number;
  title: string;
  description: string;
  xpRequired: number;
  xpTotal: number;
  color: string;
  rewards: LevelReward[];
  unlocks: string[];
  isUnlocked: boolean;
  isCurrentLevel?: boolean;
}

interface LevelProgressionRoadmapProps {
  levels: LevelInfo[];
  currentLevel: number;
  currentXP: number;
  onLevelPreview?: (level: LevelInfo) => void;
  className?: string;
  compact?: boolean;
}

export function LevelProgressionRoadmap({
  levels,
  currentLevel,
  currentXP,
  onLevelPreview,
  className,
  compact = false
}: LevelProgressionRoadmapProps) {
  const [selectedLevel, setSelectedLevel] = useState<LevelInfo | null>(null);
  const [showPreview, setShowPreview] = useState(false);

  const currentLevelInfo = levels.find(l => l.level === currentLevel);
  const nextLevelInfo = levels.find(l => l.level === currentLevel + 1);

  const handleLevelClick = (level: LevelInfo) => {
    setSelectedLevel(level);
    setShowPreview(true);
    onLevelPreview?.(level);
  };

  const getProgressToNextLevel = () => {
    if (!currentLevelInfo || !nextLevelInfo) return 100;
    
    const currentLevelXP = currentLevelInfo.xpTotal;
    const nextLevelXP = nextLevelInfo.xpTotal;
    const progressXP = currentXP - currentLevelXP;
    const requiredXP = nextLevelXP - currentLevelXP;
    
    return Math.min((progressXP / requiredXP) * 100, 100);
  };

  const getXPToNextLevel = () => {
    if (!nextLevelInfo) return 0;
    return Math.max(nextLevelInfo.xpTotal - currentXP, 0);
  };

  if (compact) {
    return (
      <GlassCard className={cn('p-4', className)}>
        <div className="space-y-4">
          {/* Current Level Progress */}
          <div className="text-center">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <Crown className="w-5 h-5 text-yellow-400" />
              <span className="text-lg font-bold text-white">Level {currentLevel}</span>
            </div>
            
            {nextLevelInfo && (
              <>
                <ProgressBar
                  progress={getProgressToNextLevel()}
                  label={`${getXPToNextLevel()} XP to Level ${currentLevel + 1}`}
                  color="yellow"
                  size="sm"
                  animated={true}
                />
                
                <div className="mt-2 text-xs text-gray-400">
                  Next: {nextLevelInfo.title}
                </div>
              </>
            )}
          </div>

          {/* Next 3 Levels Preview */}
          <div className="space-y-2">
            {levels
              .filter(l => l.level > currentLevel && l.level <= currentLevel + 3)
              .map(level => (
                <motion.div
                  key={level.level}
                  whileHover={{ scale: 1.02 }}
                  onClick={() => handleLevelClick(level)}
                  className="flex items-center space-x-3 p-2 rounded-lg bg-white/5 border border-white/10 cursor-pointer hover:bg-white/10 transition-all"
                >
                  <div className={cn(
                    'w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold',
                    level.isUnlocked ? level.color : 'bg-gray-600 text-gray-400'
                  )}>
                    {level.level}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-white truncate">{level.title}</div>
                    <div className="text-xs text-gray-400">{level.rewards.length} rewards</div>
                  </div>
                  
                  <ChevronRight className="w-4 h-4 text-gray-400" />
                </motion.div>
              ))}
          </div>
        </div>
      </GlassCard>
    );
  }

  return (
    <div className={className}>
      <GlassCard className="p-6">
        <div className="space-y-6">
          {/* Header */}
          <div className="text-center">
            <h2 className="text-2xl font-bold text-white mb-2">Level Progression Roadmap</h2>
            <p className="text-gray-400">Unlock new features and rewards as you level up</p>
          </div>

          {/* Current Progress */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center space-x-4">
              <div className="text-center">
                <div className={cn(
                  'w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-2',
                  currentLevelInfo?.color || 'bg-gray-600'
                )}>
                  {currentLevel}
                </div>
                <div className="text-sm text-gray-400">Current</div>
              </div>
              
              {nextLevelInfo && (
                <>
                  <ChevronRight className="w-6 h-6 text-gray-400" />
                  <div className="text-center">
                    <div className={cn(
                      'w-16 h-16 rounded-full flex items-center justify-center text-2xl font-bold mb-2 border-2 border-dashed',
                      'bg-gray-800 border-gray-600 text-gray-400'
                    )}>
                      {nextLevelInfo.level}
                    </div>
                    <div className="text-sm text-gray-400">Next</div>
                  </div>
                </>
              )}
            </div>

            {nextLevelInfo && (
              <div className="space-y-2">
                <ProgressBar
                  progress={getProgressToNextLevel()}
                  label={`${currentXP.toLocaleString()} / ${nextLevelInfo.xpTotal.toLocaleString()} XP`}
                  color="yellow"
                  size="lg"
                  animated={true}
                  glowEffect={true}
                />
                
                <div className="text-sm text-gray-300">
                  <span className="font-semibold text-yellow-400">{getXPToNextLevel().toLocaleString()} XP</span> to unlock {nextLevelInfo.title}
                </div>
              </div>
            )}
          </div>

          {/* Level Roadmap */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold text-white">Upcoming Levels</h3>
            
            <div className="grid gap-3">
              {levels
                .filter(l => l.level <= currentLevel + 5)
                .map(level => (
                  <motion.div
                    key={level.level}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    whileHover={{ scale: 1.02 }}
                    onClick={() => handleLevelClick(level)}
                    className={cn(
                      'flex items-center space-x-4 p-4 rounded-xl border cursor-pointer transition-all',
                      level.level === currentLevel
                        ? 'bg-yellow-500/20 border-yellow-500/30 ring-2 ring-yellow-500/20'
                        : level.isUnlocked
                        ? 'bg-green-500/10 border-green-500/20 hover:bg-green-500/20'
                        : 'bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/70'
                    )}
                  >
                    {/* Level Icon */}
                    <div className="relative">
                      <div className={cn(
                        'w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold',
                        level.level === currentLevel
                          ? 'bg-gradient-to-br from-yellow-400 to-orange-500 text-white'
                          : level.isUnlocked
                          ? level.color + ' text-white'
                          : 'bg-gray-700 text-gray-400'
                      )}>
                        {level.level === currentLevel ? (
                          <Crown className="w-6 h-6" />
                        ) : level.isUnlocked ? (
                          <Unlock className="w-6 h-6" />
                        ) : (
                          <Lock className="w-6 h-6" />
                        )}
                      </div>
                      
                      {level.level === currentLevel && (
                        <motion.div
                          animate={{ scale: [1, 1.2, 1] }}
                          transition={{ duration: 2, repeat: Infinity }}
                          className="absolute -top-1 -right-1 w-4 h-4 bg-yellow-400 rounded-full"
                        />
                      )}
                    </div>

                    {/* Level Info */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className="font-semibold text-white">Level {level.level}</h4>
                        <span className="text-sm text-gray-400">•</span>
                        <span className="text-sm font-medium text-gray-300">{level.title}</span>
                      </div>
                      
                      <p className="text-sm text-gray-400 mt-1">{level.description}</p>
                      
                      <div className="flex items-center space-x-4 mt-2">
                        <div className="flex items-center space-x-1">
                          <Zap className="w-3 h-3 text-yellow-400" />
                          <span className="text-xs text-yellow-400 font-medium">
                            {level.xpTotal.toLocaleString()} XP
                          </span>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <Gift className="w-3 h-3 text-purple-400" />
                          <span className="text-xs text-purple-400 font-medium">
                            {level.rewards.length} rewards
                          </span>
                        </div>
                        
                        {level.unlocks.length > 0 && (
                          <div className="flex items-center space-x-1">
                            <Sparkles className="w-3 h-3 text-green-400" />
                            <span className="text-xs text-green-400 font-medium">
                              {level.unlocks.length} unlocks
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Preview Button */}
                    <div className="flex items-center space-x-2">
                      <EnhancedButton
                        variant="ghost"
                        size="sm"
                        className="text-gray-400 hover:text-white"
                      >
                        <Eye className="w-4 h-4" />
                      </EnhancedButton>
                      
                      <ChevronRight className="w-4 h-4 text-gray-400" />
                    </div>
                  </motion.div>
                ))}
            </div>
          </div>
        </div>
      </GlassCard>

      {/* Level Preview Modal */}
      <AnimatePresence>
        {showPreview && selectedLevel && (
          <LevelPreviewModal
            level={selectedLevel}
            onClose={() => setShowPreview(false)}
            currentLevel={currentLevel}
            currentXP={currentXP}
          />
        )}
      </AnimatePresence>
    </div>
  );
}

// Level Preview Modal Component
interface LevelPreviewModalProps {
  level: LevelInfo;
  onClose: () => void;
  currentLevel: number;
  currentXP: number;
}

function LevelPreviewModal({ level, onClose, currentLevel, currentXP }: LevelPreviewModalProps) {
  const isUnlocked = level.level <= currentLevel;
  const xpNeeded = Math.max(level.xpTotal - currentXP, 0);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        onClick={(e) => e.stopPropagation()}
        className="max-w-2xl w-full max-h-[90vh] overflow-y-auto"
      >
        <GlassCard className="p-8 border-2 border-yellow-500/30">
          {/* Close Button */}
          <EnhancedButton
            onClick={onClose}
            variant="ghost"
            size="sm"
            className="absolute top-4 right-4 w-8 h-8 p-0 text-gray-400 hover:text-white"
          >
            <X className="w-4 h-4" />
          </EnhancedButton>

          <div className="space-y-6">
            {/* Header */}
            <div className="text-center">
              <div className={cn(
                'w-24 h-24 rounded-full flex items-center justify-center text-3xl font-bold mx-auto mb-4',
                isUnlocked ? level.color : 'bg-gray-700 text-gray-400'
              )}>
                {isUnlocked ? (
                  level.level === currentLevel ? <Crown className="w-12 h-12" /> : level.level
                ) : (
                  <Lock className="w-12 h-12" />
                )}
              </div>
              
              <h2 className="text-3xl font-bold text-white mb-2">Level {level.level}</h2>
              <h3 className="text-xl text-gray-300 mb-2">{level.title}</h3>
              <p className="text-gray-400">{level.description}</p>
            </div>

            {/* XP Requirements */}
            <div className="text-center p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
              <div className="flex items-center justify-center space-x-2 mb-2">
                <Zap className="w-5 h-5 text-yellow-400" />
                <span className="text-lg font-semibold text-yellow-300">
                  {level.xpTotal.toLocaleString()} XP Required
                </span>
              </div>
              
              {!isUnlocked && (
                <div className="text-sm text-yellow-200">
                  {xpNeeded.toLocaleString()} XP needed to unlock
                </div>
              )}
            </div>

            {/* Rewards */}
            <div className="space-y-4">
              <h4 className="text-xl font-semibold text-white flex items-center">
                <Gift className="w-5 h-5 mr-2 text-purple-400" />
                Rewards & Unlocks
              </h4>
              
              <div className="grid gap-3">
                {level.rewards.map((reward, index) => {
                  const Icon = reward.icon;
                  return (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className={cn(
                        'flex items-center space-x-3 p-3 rounded-lg border',
                        isUnlocked ? 'bg-green-500/10 border-green-500/20' : 'bg-gray-800/50 border-gray-700/50'
                      )}
                    >
                      <div className={cn(
                        'w-10 h-10 rounded-full flex items-center justify-center',
                        isUnlocked ? 'bg-green-500/20 text-green-400' : 'bg-gray-700 text-gray-400'
                      )}>
                        <Icon className="w-5 h-5" />
                      </div>
                      
                      <div className="flex-1">
                        <h5 className={cn(
                          'font-medium',
                          isUnlocked ? 'text-white' : 'text-gray-400'
                        )}>
                          {reward.name}
                        </h5>
                        <p className={cn(
                          'text-sm',
                          isUnlocked ? 'text-gray-300' : 'text-gray-500'
                        )}>
                          {reward.description}
                        </p>
                      </div>
                      
                      {isUnlocked && (
                        <div className="text-green-400">
                          <Unlock className="w-4 h-4" />
                        </div>
                      )}
                    </motion.div>
                  );
                })}
              </div>
            </div>

            {/* Feature Unlocks */}
            {level.unlocks.length > 0 && (
              <div className="space-y-3">
                <h4 className="text-lg font-semibold text-white flex items-center">
                  <Sparkles className="w-5 h-5 mr-2 text-green-400" />
                  Feature Unlocks
                </h4>
                
                <div className="grid gap-2">
                  {level.unlocks.map((unlock, index) => (
                    <div
                      key={index}
                      className={cn(
                        'flex items-center space-x-2 p-2 rounded-lg',
                        isUnlocked ? 'bg-green-500/10 text-green-300' : 'bg-gray-800/50 text-gray-400'
                      )}
                    >
                      {isUnlocked ? (
                        <Unlock className="w-4 h-4" />
                      ) : (
                        <Lock className="w-4 h-4" />
                      )}
                      <span className="text-sm">{unlock}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </GlassCard>
      </motion.div>
    </motion.div>
  );
}
