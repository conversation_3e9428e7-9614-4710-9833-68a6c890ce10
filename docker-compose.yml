version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: solanalearn-postgres
    environment:
      POSTGRES_DB: solanalearn
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - solanalearn-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: solanalearn-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - solanalearn-network

  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: solanalearn-app
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/solanalearn
      - REDIS_URL=redis://redis:6379
      - NEXTAUTH_URL=http://localhost:3000
      - NEXTAUTH_SECRET=your-nextauth-secret-here
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GITHUB_CLIENT_ID=${GITHUB_CLIENT_ID}
      - GITHUB_CLIENT_SECRET=${GITHUB_CLIENT_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
    depends_on:
      - postgres
      - redis
    networks:
      - solanalearn-network
    volumes:
      - ./prisma:/app/prisma

  # Socket.io Server for real-time collaboration
  socket-server:
    build:
      context: ./socket-server
      dockerfile: Dockerfile
    container_name: solanalearn-socket
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=********************************************/solanalearn
    depends_on:
      - postgres
      - redis
    networks:
      - solanalearn-network

volumes:
  postgres_data:
  redis_data:

networks:
  solanalearn-network:
    driver: bridge
