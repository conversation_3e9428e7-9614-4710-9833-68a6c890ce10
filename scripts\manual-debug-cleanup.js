#!/usr/bin/env node

/**
 * Manual Debug Statement Cleanup
 * Identifies and removes debug statements from key files
 */

const fs = require('fs');
const path = require('path');

// Files with known debug statements
const filesToClean = [
  'app/dashboard/page.tsx',
  'app/learn/page.tsx',
  'app/code/page.tsx',
  'components/learning/LearningDashboard.tsx',
  'components/code/CodeLab.tsx'
];

function cleanDebugStatements(content) {
  let cleaned = content;
  let removedCount = 0;
  
  // Remove console.log statements (but preserve error logging)
  cleaned = cleaned.replace(/console\.log\([^)]*\);\s*\n?/g, () => {
    removedCount++;
    return '';
  });
  
  // Remove console.debug statements
  cleaned = cleaned.replace(/console\.debug\([^)]*\);\s*\n?/g, () => {
    removedCount++;
    return '';
  });
  
  // Remove console.info statements (but preserve important ones)
  cleaned = cleaned.replace(/console\.info\([^)]*\);\s*\n?/g, () => {
    removedCount++;
    return '';
  });
  
  // Remove TODO comments
  cleaned = cleaned.replace(/\/\/\s*TODO:.*$/gm, () => {
    removedCount++;
    return '';
  });
  
  // Remove FIXME comments
  cleaned = cleaned.replace(/\/\/\s*FIXME:.*$/gm, () => {
    removedCount++;
    return '';
  });
  
  // Clean up extra empty lines
  cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  return { content: cleaned, removedCount };
}

function processFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return { processed: false, removedCount: 0 };
    }
    
    const originalContent = fs.readFileSync(filePath, 'utf8');
    const result = cleanDebugStatements(originalContent);
    
    if (result.removedCount > 0) {
      // Create backup
      const backupDir = 'backups/debug-cleanup';
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }
      
      const backupPath = path.join(backupDir, path.basename(filePath));
      fs.writeFileSync(backupPath, originalContent);
      
      // Write cleaned content
      fs.writeFileSync(filePath, result.content);
      
      console.log(`✅ Cleaned ${filePath}: ${result.removedCount} statements removed`);
      return { processed: true, removedCount: result.removedCount };
    } else {
      console.log(`ℹ️  No debug statements found in ${filePath}`);
      return { processed: false, removedCount: 0 };
    }
  } catch (error) {
    console.log(`❌ Error processing ${filePath}: ${error.message}`);
    return { processed: false, removedCount: 0 };
  }
}

function main() {
  console.log('🧹 Manual Debug Statement Cleanup');
  console.log('='.repeat(50));
  
  let totalRemoved = 0;
  let filesProcessed = 0;
  
  filesToClean.forEach(filePath => {
    const result = processFile(filePath);
    if (result.processed) {
      filesProcessed++;
      totalRemoved += result.removedCount;
    }
  });
  
  console.log('='.repeat(50));
  console.log(`📊 Summary:`);
  console.log(`   Files processed: ${filesProcessed}`);
  console.log(`   Debug statements removed: ${totalRemoved}`);
  console.log(`   Estimated size reduction: ${Math.round(totalRemoved * 0.05)}KB`);
  
  if (filesProcessed > 0) {
    console.log(`✅ Debug cleanup completed successfully`);
    console.log(`💾 Backups saved to: backups/debug-cleanup/`);
  } else {
    console.log(`ℹ️  No debug statements found to remove`);
  }
}

if (require.main === module) {
  main();
}

module.exports = { cleanDebugStatements, processFile };
