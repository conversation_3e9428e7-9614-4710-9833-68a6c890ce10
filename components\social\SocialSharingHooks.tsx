'use client';

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Share2, Trophy, Star, Zap } from 'lucide-react';
import { useProgressTracking } from '@/components/progress/ProgressTrackingSystem';
import { SocialSharingSystem } from './SocialSharingSystem';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

// Hook for social sharing functionality
export function useSocialSharing() {
  const { userProgress, addXP } = useProgressTracking();
  const [shareHistory, setShareHistory] = useState<any[]>(() => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('share_history');
      return saved ? JSON.parse(saved) : [];
    }
    return [];
  });

  const trackShare = useCallback((platform: string, shareType: string, data: any) => {
    const shareRecord = {
      id: Date.now().toString(),
      platform,
      shareType,
      data,
      timestamp: new Date(),
      xpEarned: 10
    };

    const newHistory = [shareRecord, ...shareHistory.slice(0, 49)]; // Keep last 50 shares
    setShareHistory(newHistory);
    localStorage.setItem('share_history', JSON.stringify(newHistory));

    // Award XP
    addXP(10, `Shared ${shareType} on ${platform}`);

    // Update total share count
    const totalShares = parseInt(localStorage.getItem('social_shares') || '0') + 1;
    localStorage.setItem('social_shares', totalShares.toString());

    // Check for social influencer achievements
    if (totalShares === 5) {
      window.dispatchEvent(new CustomEvent('achievement_unlocked', {
        detail: { achievementId: 'social-newcomer' }
      }));
    } else if (totalShares === 25) {
      window.dispatchEvent(new CustomEvent('achievement_unlocked', {
        detail: { achievementId: 'social-influencer' }
      }));
    } else if (totalShares === 100) {
      window.dispatchEvent(new CustomEvent('achievement_unlocked', {
        detail: { achievementId: 'social-legend' }
      }));
    }
  }, [shareHistory, addXP]);

  const getShareableAchievement = useCallback((achievementId: string) => {
    // Mock achievement data - in real app, fetch from achievements system
    const achievements = {
      'first-compilation': {
        id: 'first-compilation',
        title: 'First Compilation',
        description: 'Successfully compiled your first Solidity smart contract',
        icon: Trophy,
        xpReward: 50,
        rarity: 'common' as const,
        unlockedAt: new Date()
      },
      'level-up': {
        id: 'level-up',
        title: 'Level Up!',
        description: 'Reached a new level in your learning journey',
        icon: Star,
        xpReward: 100,
        rarity: 'rare' as const,
        unlockedAt: new Date()
      },
      'streak-master': {
        id: 'streak-master',
        title: 'Streak Master',
        description: 'Maintained a 7-day learning streak',
        icon: Zap,
        xpReward: 150,
        rarity: 'epic' as const,
        unlockedAt: new Date()
      },
      'social-newcomer': {
        id: 'social-newcomer',
        title: 'Social Newcomer',
        description: 'Shared your first 5 achievements',
        icon: Share2,
        xpReward: 75,
        rarity: 'common' as const,
        unlockedAt: new Date()
      },
      'social-influencer': {
        id: 'social-influencer',
        title: 'Social Influencer',
        description: 'Shared 25 achievements and progress updates',
        icon: Share2,
        xpReward: 200,
        rarity: 'rare' as const,
        unlockedAt: new Date()
      },
      'social-legend': {
        id: 'social-legend',
        title: 'Social Legend',
        description: 'Shared 100 achievements - true community champion!',
        icon: Share2,
        xpReward: 500,
        rarity: 'legendary' as const,
        unlockedAt: new Date()
      }
    };

    return achievements[achievementId as keyof typeof achievements];
  }, []);

  const shareAchievement = useCallback((achievementId: string, platform?: string) => {
    const achievement = getShareableAchievement(achievementId);
    if (!achievement) return;

    const progressData = {
      level: userProgress.level,
      totalXP: userProgress.totalXP,
      completionPercentage: Math.round((userProgress.completedMilestones.length / 20) * 100), // Assuming 20 total milestones
      streak: userProgress.currentStreak
    };

    if (platform) {
      // Direct share to specific platform
      trackShare(platform, 'achievement', { achievement, progressData });
    }

    return { achievement, progressData };
  }, [getShareableAchievement, userProgress, trackShare]);

  const shareProgress = useCallback((pathName?: string) => {
    const progressData = {
      level: userProgress.level,
      totalXP: userProgress.totalXP,
      completionPercentage: Math.round((userProgress.completedMilestones.length / 20) * 100),
      streak: userProgress.currentStreak,
      pathName
    };

    return progressData;
  }, [userProgress]);

  const shareLevelUp = useCallback((newLevel: number) => {
    const progressData = {
      level: newLevel,
      totalXP: userProgress.totalXP,
      completionPercentage: Math.round((userProgress.completedMilestones.length / 20) * 100),
      streak: userProgress.currentStreak
    };

    return progressData;
  }, [userProgress]);

  const shareStreak = useCallback((streakDays: number) => {
    const progressData = {
      level: userProgress.level,
      totalXP: userProgress.totalXP,
      completionPercentage: Math.round((userProgress.completedMilestones.length / 20) * 100),
      streak: streakDays
    };

    return progressData;
  }, [userProgress]);

  return {
    shareHistory,
    trackShare,
    shareAchievement,
    shareProgress,
    shareLevelUp,
    shareStreak,
    getShareableAchievement,
    totalShares: parseInt(localStorage.getItem('social_shares') || '0')
  };
}

// Quick share button component
export function QuickShareButton({ 
  shareType, 
  data, 
  className,
  variant = 'button'
}: { 
  shareType: 'achievement' | 'level_up' | 'streak' | 'progress' | 'completion'; 
  data: any; 
  className?: string;
  variant?: 'button' | 'icon';
}) {
  const [showSharing, setShowSharing] = useState(false);

  return (
    <div className={`relative ${className}`}>
      {variant === 'icon' ? (
        <button
          onClick={() => setShowSharing(true)}
          className="p-2 text-gray-400 hover:text-white transition-colors rounded-lg hover:bg-white/10"
          title="Share"
        >
          <Share2 className="w-4 h-4" />
        </button>
      ) : (
        <EnhancedButton
          onClick={() => setShowSharing(true)}
          variant="ghost"
          size="sm"
          className="text-gray-400 hover:text-white"
        >
          <Share2 className="w-4 h-4 mr-1" />
          Share
        </EnhancedButton>
      )}

      <AnimatePresence>
        {showSharing && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 z-40 bg-black/20"
              onClick={() => setShowSharing(false)}
            />
            
            {/* Share Panel */}
            <motion.div
              className="absolute top-full left-0 mt-2 z-50 min-w-[300px]"
              initial={{ opacity: 0, scale: 0.8, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <div className="bg-gray-900 rounded-lg shadow-2xl border border-gray-700 p-4">
                <SocialSharingSystem
                  shareType={shareType}
                  {...data}
                  onShare={() => setShowSharing(false)}
                  showAnalytics={false}
                />
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}

// Social sharing integration for achievements
export function AchievementShareButton({ 
  achievementId, 
  className 
}: { 
  achievementId: string; 
  className?: string; 
}) {
  const { shareAchievement } = useSocialSharing();
  
  const handleShare = () => {
    const shareData = shareAchievement(achievementId);
    return shareData;
  };

  const shareData = handleShare();
  
  if (!shareData) return null;

  return (
    <QuickShareButton
      shareType="achievement"
      data={shareData}
      className={className}
      variant="icon"
    />
  );
}

// Progress sharing component
export function ProgressShareButton({ 
  pathName, 
  className 
}: { 
  pathName?: string; 
  className?: string; 
}) {
  const { shareProgress } = useSocialSharing();
  
  const progressData = shareProgress(pathName);

  return (
    <QuickShareButton
      shareType="progress"
      data={{ progressData }}
      className={className}
    />
  );
}

// Level up sharing component
export function LevelUpShareButton({ 
  newLevel, 
  className 
}: { 
  newLevel: number; 
  className?: string; 
}) {
  const { shareLevelUp } = useSocialSharing();
  
  const progressData = shareLevelUp(newLevel);

  return (
    <QuickShareButton
      shareType="level_up"
      data={{ progressData }}
      className={className}
    />
  );
}

// Streak sharing component
export function StreakShareButton({ 
  streakDays, 
  className 
}: { 
  streakDays: number; 
  className?: string; 
}) {
  const { shareStreak } = useSocialSharing();
  
  const progressData = shareStreak(streakDays);

  return (
    <QuickShareButton
      shareType="streak"
      data={{ progressData }}
      className={className}
    />
  );
}

// Social sharing analytics component
export function SocialSharingAnalytics({ className }: { className?: string }) {
  const { shareHistory, totalShares } = useSocialSharing();
  
  const platformStats = shareHistory.reduce((acc, share) => {
    acc[share.platform] = (acc[share.platform] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const totalXPFromSharing = totalShares * 10;

  return (
    <div className={`bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10 ${className}`}>
      <h3 className="text-lg font-semibold text-white mb-4">Social Impact</h3>
      
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-400">{totalShares}</div>
          <div className="text-sm text-gray-400">Total Shares</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-green-400">+{totalXPFromSharing}</div>
          <div className="text-sm text-gray-400">XP Earned</div>
        </div>
      </div>

      {Object.keys(platformStats).length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-white mb-2">Platform Breakdown</h4>
          <div className="space-y-2">
            {Object.entries(platformStats).map(([platform, count]) => (
              <div key={platform} className="flex items-center justify-between text-sm">
                <span className="text-gray-300 capitalize">{platform}</span>
                <span className="text-white">{count}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
