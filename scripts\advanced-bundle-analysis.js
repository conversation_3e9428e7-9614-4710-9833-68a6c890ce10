#!/usr/bin/env node

/**
 * Advanced Bundle Analysis & Tree Shaking Optimizer
 * Analyzes bundle composition and optimizes for better tree shaking
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class BundleAnalyzer {
  constructor() {
    this.outputDir = 'bundle-analysis';
    this.reportPath = path.join(this.outputDir, 'bundle-report.json');
    this.optimizationsPath = path.join(this.outputDir, 'optimizations.json');
  }

  log(message, level = 'info') {
    const timestamp = new Date().toISOString();
    console.log(`[${timestamp}] [${level.toUpperCase()}] ${message}`);
  }

  ensureOutputDir() {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  async runBundleAnalysis() {
    this.log('Running bundle analysis...');
    
    try {
      // Build the application with bundle analysis
      execSync('ANALYZE=true npm run build', { 
        stdio: 'inherit',
        env: { ...process.env, ANALYZE: 'true' }
      });
      
      this.log('Bundle analysis completed');
    } catch (error) {
      this.log(`Bundle analysis failed: ${error.message}`, 'error');
      throw error;
    }
  }

  analyzePackageJson() {
    this.log('Analyzing package.json dependencies...');
    
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = packageJson.dependencies || {};
    const devDependencies = packageJson.devDependencies || {};
    
    const analysis = {
      totalDependencies: Object.keys(dependencies).length,
      totalDevDependencies: Object.keys(devDependencies).length,
      heavyDependencies: [],
      unusedDependencies: [],
      treeshakingIssues: [],
      recommendations: []
    };

    // Identify potentially heavy dependencies
    const heavyPackages = [
      'lodash', 'moment', 'rxjs', 'three', 'monaco-editor',
      '@monaco-editor/react', 'chart.js', 'recharts', 'd3'
    ];

    Object.keys(dependencies).forEach(dep => {
      if (heavyPackages.some(heavy => dep.includes(heavy))) {
        analysis.heavyDependencies.push({
          name: dep,
          version: dependencies[dep],
          recommendation: this.getHeavyPackageRecommendation(dep)
        });
      }
    });

    // Check for tree-shaking issues
    const problematicPackages = [
      'lodash', 'rxjs', 'antd', 'material-ui', 'semantic-ui'
    ];

    Object.keys(dependencies).forEach(dep => {
      if (problematicPackages.some(prob => dep.includes(prob))) {
        analysis.treeshakingIssues.push({
          name: dep,
          issue: 'May not support tree-shaking properly',
          solution: this.getTreeshakingSolution(dep)
        });
      }
    });

    return analysis;
  }

  getHeavyPackageRecommendation(packageName) {
    const recommendations = {
      'lodash': 'Consider using lodash-es or individual lodash functions',
      'moment': 'Consider switching to date-fns or dayjs for smaller bundle size',
      'three': 'Use dynamic imports for Three.js components',
      'monaco-editor': 'Already configured for code splitting',
      'chart.js': 'Consider using recharts or lightweight alternatives',
      'd3': 'Import only needed D3 modules'
    };

    return recommendations[packageName] || 'Consider code splitting or alternatives';
  }

  getTreeshakingSolution(packageName) {
    const solutions = {
      'lodash': 'Use import { function } from "lodash/function" or lodash-es',
      'rxjs': 'Use import { operator } from "rxjs/operators"',
      'antd': 'Use babel-plugin-import for tree shaking',
      'material-ui': 'Import components individually'
    };

    return solutions[packageName] || 'Check documentation for tree-shaking support';
  }

  analyzeImportPatterns() {
    this.log('Analyzing import patterns...');
    
    const analysis = {
      totalFiles: 0,
      problematicImports: [],
      optimizationOpportunities: [],
      barrelExportIssues: []
    };

    const findFiles = (dir, extensions = ['.ts', '.tsx', '.js', '.jsx']) => {
      const files = [];
      
      if (!fs.existsSync(dir)) return files;
      
      const items = fs.readdirSync(dir);
      
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          files.push(...findFiles(fullPath, extensions));
        } else if (stat.isFile() && extensions.includes(path.extname(item))) {
          files.push(fullPath);
        }
      }
      
      return files;
    };

    const files = findFiles('.', ['.ts', '.tsx', '.js', '.jsx']);
    analysis.totalFiles = files.length;

    files.forEach(file => {
      try {
        const content = fs.readFileSync(file, 'utf8');
        
        // Check for problematic import patterns
        const lines = content.split('\n');
        lines.forEach((line, index) => {
          // Check for barrel export imports that might cause issues
          if (line.includes('import') && line.includes('from') && line.includes('./')) {
            const match = line.match(/import\s+.*\s+from\s+['"](.*)['"];?/);
            if (match && match[1].endsWith('/index')) {
              analysis.barrelExportIssues.push({
                file,
                line: index + 1,
                import: match[1],
                suggestion: 'Consider direct imports to avoid circular dependencies'
              });
            }
          }

          // Check for default imports from libraries that support named imports
          if (line.includes('import') && !line.includes('{')) {
            const match = line.match(/import\s+(\w+)\s+from\s+['"]([^'"]+)['"];?/);
            if (match) {
              const [, importName, packageName] = match;
              if (packageName === 'lodash' || packageName === 'rxjs') {
                analysis.problematicImports.push({
                  file,
                  line: index + 1,
                  import: line.trim(),
                  issue: 'Default import may prevent tree shaking',
                  suggestion: `Use named imports: import { ${importName} } from "${packageName}"`
                });
              }
            }
          }

          // Check for large library imports
          if (line.includes('import') && line.includes('*')) {
            const match = line.match(/import\s+\*\s+as\s+\w+\s+from\s+['"]([^'"]+)['"];?/);
            if (match) {
              analysis.optimizationOpportunities.push({
                file,
                line: index + 1,
                import: line.trim(),
                suggestion: 'Consider importing only needed functions'
              });
            }
          }
        });
      } catch (error) {
        this.log(`Error analyzing ${file}: ${error.message}`, 'warn');
      }
    });

    return analysis;
  }

  generateOptimizations() {
    this.log('Generating optimization recommendations...');
    
    const packageAnalysis = this.analyzePackageJson();
    const importAnalysis = this.analyzeImportPatterns();
    
    const optimizations = {
      timestamp: new Date().toISOString(),
      summary: {
        totalIssuesFound: packageAnalysis.heavyDependencies.length + 
                         packageAnalysis.treeshakingIssues.length +
                         importAnalysis.problematicImports.length,
        potentialSavings: this.estimatePotentialSavings(packageAnalysis, importAnalysis)
      },
      packageOptimizations: packageAnalysis,
      importOptimizations: importAnalysis,
      recommendations: this.generateRecommendations(packageAnalysis, importAnalysis),
      automatedFixes: this.generateAutomatedFixes(importAnalysis)
    };

    return optimizations;
  }

  estimatePotentialSavings(packageAnalysis, importAnalysis) {
    let estimatedSavings = 0;
    
    // Estimate savings from heavy dependencies
    packageAnalysis.heavyDependencies.forEach(dep => {
      if (dep.name.includes('lodash')) estimatedSavings += 50; // KB
      if (dep.name.includes('moment')) estimatedSavings += 67; // KB
      if (dep.name.includes('three')) estimatedSavings += 100; // KB
    });

    // Estimate savings from import optimizations
    estimatedSavings += importAnalysis.problematicImports.length * 5; // KB per import
    estimatedSavings += importAnalysis.optimizationOpportunities.length * 10; // KB per opportunity

    return {
      estimatedKB: estimatedSavings,
      estimatedPercentage: Math.min(estimatedSavings / 1000 * 100, 30) // Max 30% estimated
    };
  }

  generateRecommendations(packageAnalysis, importAnalysis) {
    const recommendations = [];

    // Package-level recommendations
    if (packageAnalysis.heavyDependencies.length > 0) {
      recommendations.push({
        category: 'Dependencies',
        priority: 'high',
        title: 'Optimize Heavy Dependencies',
        description: 'Replace or optimize heavy dependencies to reduce bundle size',
        actions: packageAnalysis.heavyDependencies.map(dep => dep.recommendation)
      });
    }

    // Import-level recommendations
    if (importAnalysis.problematicImports.length > 0) {
      recommendations.push({
        category: 'Imports',
        priority: 'medium',
        title: 'Fix Problematic Import Patterns',
        description: 'Update import statements to enable better tree shaking',
        actions: ['Use named imports instead of default imports', 'Import specific functions only']
      });
    }

    // Tree shaking recommendations
    recommendations.push({
      category: 'Tree Shaking',
      priority: 'medium',
      title: 'Enhance Tree Shaking',
      description: 'Improve tree shaking effectiveness',
      actions: [
        'Use ES modules instead of CommonJS',
        'Avoid importing entire libraries',
        'Use babel-plugin-transform-imports for automatic optimization',
        'Configure webpack to mark packages as side-effect free'
      ]
    });

    return recommendations;
  }

  generateAutomatedFixes(importAnalysis) {
    const fixes = [];

    importAnalysis.problematicImports.forEach(issue => {
      if (issue.import.includes('lodash')) {
        fixes.push({
          file: issue.file,
          line: issue.line,
          original: issue.import,
          fixed: issue.suggestion,
          type: 'import_optimization'
        });
      }
    });

    return fixes;
  }

  createWebpackOptimizations() {
    this.log('Creating webpack optimization configuration...');
    
    const webpackOptimizations = {
      // Enhanced tree shaking configuration
      optimization: {
        usedExports: true,
        sideEffects: false,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 244000,
          cacheGroups: {
            // Vendor libraries with good tree shaking
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10
            },
            // Common application code
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              priority: 5,
              reuseExistingChunk: true
            }
          }
        }
      },
      
      // Module resolution optimizations
      resolve: {
        alias: {
          // Optimize lodash imports
          'lodash': 'lodash-es',
          // Use production builds
          'react': 'react/index.js',
          'react-dom': 'react-dom/index.js'
        }
      },
      
      // Plugin configurations for better tree shaking
      plugins: [
        // Add webpack plugins for optimization
      ]
    };

    const configPath = path.join(this.outputDir, 'webpack-optimizations.js');
    fs.writeFileSync(configPath, `module.exports = ${JSON.stringify(webpackOptimizations, null, 2)};`);
    
    return webpackOptimizations;
  }

  generateReport() {
    this.log('Generating comprehensive bundle analysis report...');
    
    const optimizations = this.generateOptimizations();
    const webpackConfig = this.createWebpackOptimizations();
    
    const report = {
      ...optimizations,
      webpackOptimizations: webpackConfig,
      nextSteps: [
        'Review and implement package optimizations',
        'Update import statements based on recommendations',
        'Apply webpack configuration optimizations',
        'Run bundle analysis again to measure improvements',
        'Set up bundle size monitoring in CI/CD'
      ]
    };

    fs.writeFileSync(this.reportPath, JSON.stringify(report, null, 2));
    
    return report;
  }

  displaySummary(report) {
    console.log('\n=== BUNDLE ANALYSIS REPORT ===');
    console.log(`Total issues found: ${report.summary.totalIssuesFound}`);
    console.log(`Estimated potential savings: ${report.summary.potentialSavings.estimatedKB} KB (${report.summary.potentialSavings.estimatedPercentage.toFixed(1)}%)`);
    
    console.log('\nPackage Analysis:');
    console.log(`  Heavy dependencies: ${report.packageOptimizations.heavyDependencies.length}`);
    console.log(`  Tree-shaking issues: ${report.packageOptimizations.treeshakingIssues.length}`);
    
    console.log('\nImport Analysis:');
    console.log(`  Files analyzed: ${report.importOptimizations.totalFiles}`);
    console.log(`  Problematic imports: ${report.importOptimizations.problematicImports.length}`);
    console.log(`  Optimization opportunities: ${report.importOptimizations.optimizationOpportunities.length}`);
    
    console.log('\nTop Recommendations:');
    report.recommendations.slice(0, 3).forEach((rec, index) => {
      console.log(`  ${index + 1}. [${rec.priority.toUpperCase()}] ${rec.title}`);
      console.log(`     ${rec.description}`);
    });
    
    console.log(`\nDetailed report saved to: ${this.reportPath}`);
    console.log(`Webpack optimizations saved to: ${path.join(this.outputDir, 'webpack-optimizations.js')}`);
  }

  async run() {
    this.log('Starting advanced bundle analysis...');
    
    this.ensureOutputDir();
    
    try {
      // Run bundle analysis
      await this.runBundleAnalysis();
      
      // Generate comprehensive report
      const report = this.generateReport();
      
      // Display summary
      this.displaySummary(report);
      
      this.log('Bundle analysis completed successfully');
      
    } catch (error) {
      this.log(`Bundle analysis failed: ${error.message}`, 'error');
      throw error;
    }
  }
}

// Run the analyzer
if (require.main === module) {
  const analyzer = new BundleAnalyzer();
  analyzer.run().catch(error => {
    console.error('Bundle analysis failed:', error);
    process.exit(1);
  });
}

module.exports = BundleAnalyzer;
