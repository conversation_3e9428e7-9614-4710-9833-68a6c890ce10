# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains production-specific environment variables
# These values are optimized for production performance and security
# =============================================================================

# Application Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://soliditylearn.com
DEBUG=false
VERBOSE_LOGGING=false
ENABLE_QUERY_LOGGING=false
ENABLE_PERFORMANCE_MONITORING=true

# Database Configuration (Production Database with Read Replicas)
DATABASE_URL="postgresql://prod_user:<EMAIL>:5432/solidity_learning_prod?schema=public"
DATABASE_POOL_MIN=5
DATABASE_POOL_MAX=20
DATABASE_POOL_TIMEOUT=30000

# Redis Configuration (Production Redis Cluster)
REDIS_URL="redis://prod-redis-cluster.amazonaws.com:6379"
REDIS_PASSWORD="prod-redis-secure-password"
REDIS_DB=0

# Authentication (Production Security)
NEXTAUTH_URL=https://soliditylearn.com
SESSION_TIMEOUT=43200
SESSION_UPDATE_AGE=1800
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_SAME_SITE=strict

# Socket.io Configuration (Production Scale)
SOCKET_IO_PORT=3001
SOCKET_IO_CORS_ORIGINS="https://soliditylearn.com"
SOCKET_IO_MAX_CONNECTIONS=1000
SOCKET_IO_CONNECTION_TIMEOUT=60000

# Collaboration Limits (Production)
MAX_COLLABORATION_SESSIONS=500
MAX_PARTICIPANTS_PER_SESSION=10
SESSION_IDLE_TIMEOUT=1800000

# Rate Limiting (Strict Production Limits)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=false
API_RATE_LIMIT_PER_MINUTE=60
AUTH_RATE_LIMIT_PER_MINUTE=5
COLLABORATION_RATE_LIMIT_PER_MINUTE=30

# AI Services (Production Limits)
AI_REQUESTS_PER_MINUTE=60
AI_REQUESTS_PER_HOUR=1000
AI_REQUESTS_PER_DAY=10000
OPENAI_MODEL="gpt-4"
OPENAI_MAX_TOKENS=2048
OPENAI_TEMPERATURE=0.7
GOOGLE_AI_MODEL="gemini-pro"

# Email Configuration (Production SendGrid)
SENDGRID_API_KEY="SG.production-api-key"
SENDGRID_FROM_EMAIL="<EMAIL>"
SMTP_FROM_NAME="Solidity Learning Platform"

# Email Templates (Production)
EMAIL_VERIFICATION_TEMPLATE_ID="d-prod-verification-template"
PASSWORD_RESET_TEMPLATE_ID="d-prod-reset-template"
WELCOME_EMAIL_TEMPLATE_ID="d-prod-welcome-template"

# File Storage (Production S3 with CDN)
AWS_S3_BUCKET="solidity-learning-prod-uploads"
AWS_S3_BUCKET_URL="https://cdn.soliditylearn.com"
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,text/plain,application/json"

# Blockchain (Mainnet and Testnet)
ETHEREUM_RPC_URL="https://mainnet.infura.io/v3/prod-project-id"
ETHEREUM_TESTNET_RPC_URL="https://sepolia.infura.io/v3/prod-project-id"
ALCHEMY_API_KEY="prod-alchemy-api-key"
ALCHEMY_NETWORK="eth-mainnet"

# Feature Flags (Production Stable Features)
FEATURE_AI_TUTORING=true
FEATURE_COLLABORATION=true
FEATURE_CODE_COMPILATION=true
FEATURE_BLOCKCHAIN_INTEGRATION=true
FEATURE_GAMIFICATION=true
FEATURE_SOCIAL_FEATURES=true
FEATURE_ADVANCED_ANALYTICS=true

# Beta Features (Disabled in Production)
BETA_VOICE_CHAT=false
BETA_VIDEO_COLLABORATION=false
BETA_AI_CODE_REVIEW=false

# Monitoring & Analytics (Production)
SENTRY_DSN="https://<EMAIL>/prod-project-id"
SENTRY_ORG="solidity-learning"
SENTRY_PROJECT="production"
SENTRY_AUTH_TOKEN="prod-sentry-auth-token"
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-PRODUCTION-ID"
NEXT_PUBLIC_POSTHOG_KEY="phc_prod-posthog-key"
NEXT_PUBLIC_POSTHOG_HOST="https://app.posthog.com"
NEXT_PUBLIC_MIXPANEL_TOKEN="prod-mixpanel-token"

# Security (Maximum Security)
CONTENT_SECURITY_POLICY_REPORT_URI="https://soliditylearn.com/api/csp-report"
HSTS_MAX_AGE=31536000

# Third-party Integrations (Production Keys)
GITHUB_APP_ID="prod-github-app-id"
STRIPE_PUBLISHABLE_KEY="pk_live_prod_key"
STRIPE_SECRET_KEY="sk_live_prod_key"
STRIPE_WEBHOOK_SECRET="whsec_prod_webhook_secret"

# Discord Integration (Production Bot)
DISCORD_BOT_TOKEN="prod-discord-bot-token"
DISCORD_GUILD_ID="prod-discord-server-id"

# Health Checks (Production)
HEALTH_CHECK_ENDPOINT="/api/health"
HEALTH_CHECK_TIMEOUT=5000

# Backup Configuration (Production)
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET="solidity-learning-prod-backups"

# Build Configuration (Production Optimized)
BUILD_STANDALONE=true
OUTPUT_EXPORT=false
ANALYZE_BUNDLE=false

# Performance & Caching (Production Optimized)
CACHE_TTL=3600
LOG_LEVEL=warn

# Production Security Headers
SESSION_COOKIE_HTTP_ONLY=true
SESSION_COOKIE_MAX_AGE=43200
