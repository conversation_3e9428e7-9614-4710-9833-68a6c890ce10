'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Check, 
  X, 
  Star, 
  Filter, 
  ChevronDown, 
  ChevronUp,
  Award,
  Clock,
  DollarSign,
  Users,
  Code,
  Zap,
  Shield,
  TrendingUp,
  ExternalLink,
  Crown
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

interface CompetitorFeature {
  id: string;
  name: string;
  category: 'learning' | 'technical' | 'support' | 'certification' | 'pricing';
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  weight: number; // Importance weight for scoring
}

interface Competitor {
  id: string;
  name: string;
  logo: string;
  tagline: string;
  pricing: {
    free: string;
    premium: string;
  };
  rating: number;
  reviews: number;
  features: Record<string, boolean | string | number>;
  strengths: string[];
  weaknesses: string[];
  testimonial?: {
    text: string;
    author: string;
    role: string;
    rating: number;
  };
}

// Feature definitions
const features: CompetitorFeature[] = [
  {
    id: 'real-testnet',
    name: 'Real Testnet Deployment',
    category: 'technical',
    description: 'Deploy contracts to actual blockchain testnets',
    icon: Zap,
    weight: 9
  },
  {
    id: 'ai-assistance',
    name: 'AI-Powered Code Assistant',
    category: 'technical',
    description: 'Real-time AI help with code debugging and optimization',
    icon: Code,
    weight: 8
  },
  {
    id: 'gamification',
    name: 'Gamification System',
    category: 'learning',
    description: 'XP, achievements, and progress tracking',
    icon: Award,
    weight: 7
  },
  {
    id: 'job-placement',
    name: 'Job Placement Support',
    category: 'support',
    description: 'Direct connections to hiring partners',
    icon: Users,
    weight: 9
  },
  {
    id: 'live-mentoring',
    name: 'Live 1-on-1 Mentoring',
    category: 'support',
    description: 'Personal mentoring sessions with experts',
    icon: Users,
    weight: 8
  },
  {
    id: 'blockchain-certificate',
    name: 'Blockchain-Verified Certificates',
    category: 'certification',
    description: 'Certificates stored on blockchain for verification',
    icon: Shield,
    weight: 7
  },
  {
    id: 'hands-on-projects',
    name: 'Hands-on Projects',
    category: 'learning',
    description: 'Build real DApps and smart contracts',
    icon: Code,
    weight: 8
  },
  {
    id: 'community-support',
    name: 'Active Community',
    category: 'support',
    description: 'Discord community with 24/7 support',
    icon: Users,
    weight: 6
  },
  {
    id: 'mobile-app',
    name: 'Mobile Learning App',
    category: 'technical',
    description: 'Learn on-the-go with mobile app',
    icon: Zap,
    weight: 5
  },
  {
    id: 'lifetime-access',
    name: 'Lifetime Access',
    category: 'pricing',
    description: 'One-time payment for lifetime access',
    icon: Clock,
    weight: 6
  }
];

// Competitor data
const competitors: Competitor[] = [
  {
    id: 'our-platform',
    name: 'Solidity Learning Platform',
    logo: '/logos/our-platform.svg',
    tagline: 'The most comprehensive Solidity learning experience',
    pricing: {
      free: 'Free tier available',
      premium: '$49/month'
    },
    rating: 4.9,
    reviews: 2156,
    features: {
      'real-testnet': true,
      'ai-assistance': true,
      'gamification': true,
      'job-placement': true,
      'live-mentoring': true,
      'blockchain-certificate': true,
      'hands-on-projects': true,
      'community-support': true,
      'mobile-app': true,
      'lifetime-access': false
    },
    strengths: [
      'Real blockchain deployment experience',
      'AI-powered learning assistance',
      'Strong job placement record (87%)',
      'Comprehensive gamification system'
    ],
    weaknesses: [
      'Newer platform (less brand recognition)',
      'No lifetime access option'
    ],
    testimonial: {
      text: "The real testnet deployment feature was a game-changer. I felt confident deploying to mainnet after practicing here.",
      author: "Sarah Chen",
      role: "Blockchain Developer at ConsenSys",
      rating: 5
    }
  },
  {
    id: 'coursera',
    name: 'Coursera',
    logo: '/logos/coursera.svg',
    tagline: 'University-quality courses online',
    pricing: {
      free: 'Audit for free',
      premium: '$39-79/month'
    },
    rating: 4.2,
    reviews: 15420,
    features: {
      'real-testnet': false,
      'ai-assistance': false,
      'gamification': false,
      'job-placement': false,
      'live-mentoring': false,
      'blockchain-certificate': true,
      'hands-on-projects': true,
      'community-support': false,
      'mobile-app': true,
      'lifetime-access': false
    },
    strengths: [
      'University partnerships',
      'Established brand reputation',
      'Wide course variety',
      'Academic rigor'
    ],
    weaknesses: [
      'No real blockchain deployment',
      'Limited practical experience',
      'No personalized mentoring',
      'Generic learning approach'
    ]
  },
  {
    id: 'udemy',
    name: 'Udemy',
    logo: '/logos/udemy.svg',
    tagline: 'Learn anything, on your schedule',
    pricing: {
      free: 'Some free courses',
      premium: '$10-200 per course'
    },
    rating: 4.1,
    reviews: 8934,
    features: {
      'real-testnet': false,
      'ai-assistance': false,
      'gamification': false,
      'job-placement': false,
      'live-mentoring': false,
      'blockchain-certificate': false,
      'hands-on-projects': true,
      'community-support': false,
      'mobile-app': true,
      'lifetime-access': true
    },
    strengths: [
      'Affordable one-time pricing',
      'Lifetime access to courses',
      'Large instructor community',
      'Frequent sales and discounts'
    ],
    weaknesses: [
      'Inconsistent course quality',
      'No live support or mentoring',
      'No real blockchain interaction',
      'Limited career support'
    ]
  },
  {
    id: 'codecademy',
    name: 'Codecademy',
    logo: '/logos/codecademy.svg',
    tagline: 'Learn to code interactively',
    pricing: {
      free: 'Basic courses free',
      premium: '$15.99/month'
    },
    rating: 4.3,
    reviews: 12567,
    features: {
      'real-testnet': false,
      'ai-assistance': false,
      'gamification': true,
      'job-placement': false,
      'live-mentoring': false,
      'blockchain-certificate': false,
      'hands-on-projects': true,
      'community-support': true,
      'mobile-app': true,
      'lifetime-access': false
    },
    strengths: [
      'Interactive coding environment',
      'Good for programming basics',
      'Structured learning paths',
      'Active community forums'
    ],
    weaknesses: [
      'Limited blockchain content',
      'No real deployment experience',
      'No job placement assistance',
      'Generic certificates'
    ]
  }
];

const categories = {
  learning: { label: 'Learning Experience', icon: Award, color: 'text-green-400' },
  technical: { label: 'Technical Features', icon: Code, color: 'text-blue-400' },
  support: { label: 'Support & Mentoring', icon: Users, color: 'text-purple-400' },
  certification: { label: 'Certification', icon: Shield, color: 'text-yellow-400' },
  pricing: { label: 'Pricing & Access', icon: DollarSign, color: 'text-red-400' }
};

export function CompetitiveComparison({ className }: { className?: string }) {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);
  const [expandedCompetitor, setExpandedCompetitor] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState<'rating' | 'price' | 'features'>('rating');

  // Filter features by selected categories
  const filteredFeatures = useMemo(() => {
    if (selectedCategories.length === 0) return features;
    return features.filter(feature => selectedCategories.includes(feature.category));
  }, [selectedCategories]);

  // Calculate feature scores for each competitor
  const competitorScores = useMemo(() => {
    return competitors.map(competitor => {
      const totalWeight = filteredFeatures.reduce((sum, feature) => sum + feature.weight, 0);
      const score = filteredFeatures.reduce((sum, feature) => {
        const hasFeature = competitor.features[feature.id];
        return sum + (hasFeature ? feature.weight : 0);
      }, 0);
      
      return {
        ...competitor,
        score: totalWeight > 0 ? Math.round((score / totalWeight) * 100) : 0
      };
    });
  }, [filteredFeatures]);

  // Sort competitors
  const sortedCompetitors = useMemo(() => {
    return [...competitorScores].sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'price':
          const priceA = parseFloat(a.pricing.premium.replace(/[^0-9.]/g, ''));
          const priceB = parseFloat(b.pricing.premium.replace(/[^0-9.]/g, ''));
          return priceA - priceB;
        case 'features':
          return b.score - a.score;
        default:
          return 0;
      }
    });
  }, [competitorScores, sortBy]);

  const toggleCategory = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    );
  };

  const renderFeatureValue = (value: boolean | string | number) => {
    if (typeof value === 'boolean') {
      return value ? (
        <Check className="w-5 h-5 text-green-400 mx-auto" />
      ) : (
        <X className="w-5 h-5 text-red-400 mx-auto" />
      );
    }
    return <span className="text-gray-300 text-sm">{value}</span>;
  };

  return (
    <div className={cn('max-w-7xl mx-auto', className)}>
      {/* Header */}
      <div className="text-center mb-8">
        <motion.h2
          className="text-3xl md:text-4xl font-bold text-white mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Platform Comparison
        </motion.h2>
        <motion.p
          className="text-gray-300 text-lg max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          See how our comprehensive Solidity learning platform compares to other popular coding education platforms
        </motion.p>
      </div>

      {/* Filters and Controls */}
      <motion.div
        className="mb-8 space-y-4"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
      >
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-center space-x-4">
            <EnhancedButton
              onClick={() => setShowFilters(!showFilters)}
              variant="ghost"
              className="text-gray-400 hover:text-white border-gray-600"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
              {showFilters ? <ChevronUp className="w-4 h-4 ml-2" /> : <ChevronDown className="w-4 h-4 ml-2" />}
            </EnhancedButton>
            {selectedCategories.length > 0 && (
              <span className="text-sm text-gray-400">
                {selectedCategories.length} filter{selectedCategories.length !== 1 ? 's' : ''} active
              </span>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-400">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as any)}
              className="bg-gray-800 border border-gray-600 rounded-lg px-3 py-1 text-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="rating">Rating</option>
              <option value="price">Price</option>
              <option value="features">Feature Score</option>
            </select>
          </div>
        </div>

        {/* Category Filters */}
        <AnimatePresence>
          {showFilters && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="bg-white/5 backdrop-blur-sm rounded-lg p-4 border border-white/10">
                <h3 className="text-white font-medium mb-3">Filter by Category</h3>
                <div className="flex flex-wrap gap-3">
                  {Object.entries(categories).map(([key, category]) => {
                    const Icon = category.icon;
                    const isSelected = selectedCategories.includes(key);
                    
                    return (
                      <button
                        key={key}
                        onClick={() => toggleCategory(key)}
                        className={cn(
                          'flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 border',
                          isSelected
                            ? `${category.color} bg-current/20 border-current/30`
                            : 'text-gray-400 bg-gray-800/50 border-gray-600 hover:border-gray-500'
                        )}
                      >
                        <Icon className="w-4 h-4" />
                        <span>{category.label}</span>
                      </button>
                    );
                  })}
                </div>
                <div className="mt-3 flex justify-between items-center">
                  <button
                    onClick={() => setSelectedCategories([])}
                    className="text-sm text-gray-400 hover:text-white transition-colors"
                  >
                    Clear all filters
                  </button>
                  <button
                    onClick={() => setSelectedCategories(Object.keys(categories))}
                    className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    Select all
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Comparison Table */}
      <motion.div
        className="bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
      >
        {/* Mobile View - Cards */}
        <div className="block lg:hidden">
          <div className="space-y-4 p-4">
            {sortedCompetitors.map((competitor, index) => (
              <motion.div
                key={competitor.id}
                className={cn(
                  'bg-white/5 rounded-lg border p-4',
                  competitor.id === 'our-platform' 
                    ? 'border-blue-500 bg-blue-500/10' 
                    : 'border-white/10'
                )}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                {/* Competitor Header */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center">
                      <span className="text-lg font-bold text-white">
                        {competitor.name.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-white flex items-center space-x-2">
                        <span>{competitor.name}</span>
                        {competitor.id === 'our-platform' && (
                          <Crown className="w-4 h-4 text-yellow-400" />
                        )}
                      </h3>
                      <div className="flex items-center space-x-2 text-sm">
                        <div className="flex items-center space-x-1">
                          <Star className="w-3 h-3 text-yellow-400 fill-current" />
                          <span className="text-gray-300">{competitor.rating}</span>
                        </div>
                        <span className="text-gray-500">•</span>
                        <span className="text-gray-400">{competitor.reviews.toLocaleString()} reviews</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-white">{competitor.score}%</div>
                    <div className="text-xs text-gray-400">Feature Score</div>
                  </div>
                </div>

                {/* Pricing */}
                <div className="mb-4 p-3 bg-white/5 rounded-lg">
                  <div className="text-sm text-gray-400 mb-1">Pricing</div>
                  <div className="text-white font-medium">{competitor.pricing.premium}</div>
                  <div className="text-xs text-gray-400">{competitor.pricing.free}</div>
                </div>

                {/* Key Features */}
                <div className="grid grid-cols-2 gap-2 mb-4">
                  {filteredFeatures.slice(0, 6).map((feature) => (
                    <div key={feature.id} className="flex items-center space-x-2 text-sm">
                      {renderFeatureValue(competitor.features[feature.id])}
                      <span className="text-gray-300 truncate">{feature.name}</span>
                    </div>
                  ))}
                </div>

                {/* Expand Button */}
                <button
                  onClick={() => setExpandedCompetitor(
                    expandedCompetitor === competitor.id ? null : competitor.id
                  )}
                  className="w-full text-blue-400 hover:text-blue-300 text-sm transition-colors"
                >
                  {expandedCompetitor === competitor.id ? 'Show Less' : 'Show All Features'}
                </button>

                {/* Expanded Features */}
                <AnimatePresence>
                  {expandedCompetitor === competitor.id && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="mt-4 space-y-2 overflow-hidden"
                    >
                      {filteredFeatures.slice(6).map((feature) => (
                        <div key={feature.id} className="flex items-center space-x-2 text-sm">
                          {renderFeatureValue(competitor.features[feature.id])}
                          <span className="text-gray-300">{feature.name}</span>
                        </div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Desktop View - Table */}
        <div className="hidden lg:block overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-white/10">
                <th className="text-left p-4 text-white font-semibold">Platform</th>
                {sortedCompetitors.map((competitor) => (
                  <th key={competitor.id} className="text-center p-4 min-w-[200px]">
                    <div className={cn(
                      'space-y-2',
                      competitor.id === 'our-platform' && 'relative'
                    )}>
                      {competitor.id === 'our-platform' && (
                        <div className="absolute -top-2 -right-2">
                          <Crown className="w-5 h-5 text-yellow-400" />
                        </div>
                      )}
                      <div className="w-12 h-12 bg-gray-700 rounded-lg mx-auto flex items-center justify-center">
                        <span className="text-lg font-bold text-white">
                          {competitor.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <h3 className="font-semibold text-white">{competitor.name}</h3>
                        <div className="flex items-center justify-center space-x-1 text-sm">
                          <Star className="w-3 h-3 text-yellow-400 fill-current" />
                          <span className="text-gray-300">{competitor.rating}</span>
                          <span className="text-gray-500">({competitor.reviews.toLocaleString()})</span>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-white">{competitor.pricing.premium}</div>
                        <div className="text-xs text-gray-400">{competitor.pricing.free}</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-blue-400">{competitor.score}%</div>
                        <div className="text-xs text-gray-400">Feature Score</div>
                      </div>
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {filteredFeatures.map((feature, index) => {
                const categoryInfo = categories[feature.category];
                const Icon = feature.icon;
                
                return (
                  <motion.tr
                    key={feature.id}
                    className="border-b border-white/5 hover:bg-white/5 transition-colors"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                  >
                    <td className="p-4">
                      <div className="flex items-center space-x-3">
                        <Icon className="w-5 h-5 text-gray-400" />
                        <div>
                          <div className="font-medium text-white">{feature.name}</div>
                          <div className="text-sm text-gray-400">{feature.description}</div>
                          <div className={cn('text-xs font-medium mt-1', categoryInfo.color)}>
                            {categoryInfo.label}
                          </div>
                        </div>
                      </div>
                    </td>
                    {sortedCompetitors.map((competitor) => (
                      <td key={competitor.id} className="p-4 text-center">
                        {renderFeatureValue(competitor.features[feature.id])}
                      </td>
                    ))}
                  </motion.tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* Testimonials */}
      <motion.div
        className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.8 }}
      >
        {sortedCompetitors
          .filter(c => c.testimonial)
          .slice(0, 2)
          .map((competitor) => (
            <div
              key={competitor.id}
              className={cn(
                'bg-white/5 backdrop-blur-sm rounded-lg p-6 border',
                competitor.id === 'our-platform' 
                  ? 'border-blue-500/30 bg-blue-500/10' 
                  : 'border-white/10'
              )}
            >
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center">
                  <span className="text-sm font-bold text-white">
                    {competitor.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <div className="font-medium text-white">{competitor.name}</div>
                  <div className="flex items-center space-x-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={cn(
                          'w-3 h-3',
                          i < competitor.testimonial!.rating
                            ? 'text-yellow-400 fill-current'
                            : 'text-gray-600'
                        )}
                      />
                    ))}
                  </div>
                </div>
              </div>
              <blockquote className="text-gray-300 italic mb-4">
                "{competitor.testimonial!.text}"
              </blockquote>
              <div className="text-sm">
                <div className="font-medium text-white">{competitor.testimonial!.author}</div>
                <div className="text-gray-400">{competitor.testimonial!.role}</div>
              </div>
            </div>
          ))}
      </motion.div>

      {/* CTA */}
      <motion.div
        className="mt-12 text-center bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg p-8 border border-blue-500/30"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.0 }}
      >
        <h3 className="text-2xl font-bold text-white mb-4">Ready to Experience the Difference?</h3>
        <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
          Join thousands of developers who chose our platform for its comprehensive features, 
          real-world experience, and proven job placement success.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <EnhancedButton
            className="bg-blue-600 hover:bg-blue-700 text-white"
            touchTarget
          >
            Start Free Trial
          </EnhancedButton>
          <EnhancedButton
            variant="ghost"
            className="text-blue-400 hover:text-blue-300 border-blue-400/30"
            touchTarget
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            View Full Feature List
          </EnhancedButton>
        </div>
      </motion.div>
    </div>
  );
}
