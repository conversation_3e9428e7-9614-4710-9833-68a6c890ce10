'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Flame, 
  Crown, 
  Medal, 
  TrendingUp, 
  Users,
  Calendar,
  Award,
  Star,
  Zap,
  Trophy
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { GlassCard } from '@/components/ui/Glassmorphism';
import { EnhancedButton } from '@/components/ui/EnhancedButton';

export interface StreakLeaderboardEntry {
  id: string;
  username: string;
  avatar?: string;
  currentStreak: number;
  longestStreak: number;
  rank: number;
  isCurrentUser?: boolean;
  streakStartDate: Date;
  totalXP: number;
  badges: string[];
}

interface StreakLeaderboardProps {
  entries: StreakLeaderboardEntry[];
  currentUserRank?: number;
  timeframe: 'daily' | 'weekly' | 'monthly' | 'all-time';
  onTimeframeChange?: (timeframe: 'daily' | 'weekly' | 'monthly' | 'all-time') => void;
  className?: string;
  compact?: boolean;
}

export function StreakLeaderboard({
  entries,
  currentUserRank,
  timeframe,
  onTimeframeChange,
  className,
  compact = false
}: StreakLeaderboardProps) {
  const [selectedTab, setSelectedTab] = useState<'current' | 'longest'>('current');

  const timeframes = [
    { id: 'daily', name: 'Today', icon: Calendar },
    { id: 'weekly', name: 'This Week', icon: Calendar },
    { id: 'monthly', name: 'This Month', icon: Calendar },
    { id: 'all-time', name: 'All Time', icon: Trophy }
  ];

  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1:
        return <Crown className="w-5 h-5 text-yellow-400" />;
      case 2:
        return <Medal className="w-5 h-5 text-gray-300" />;
      case 3:
        return <Medal className="w-5 h-5 text-orange-400" />;
      default:
        return <span className="text-sm font-bold text-gray-400">#{rank}</span>;
    }
  };

  const getRankColor = (rank: number) => {
    switch (rank) {
      case 1:
        return 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-yellow-500/30';
      case 2:
        return 'bg-gradient-to-r from-gray-400/20 to-gray-500/20 border-gray-400/30';
      case 3:
        return 'bg-gradient-to-r from-orange-400/20 to-orange-500/20 border-orange-400/30';
      default:
        return 'bg-white/5 border-white/10';
    }
  };

  const getStreakIntensity = (streak: number) => {
    if (streak === 0) return 'text-gray-400';
    if (streak < 3) return 'text-orange-400';
    if (streak < 7) return 'text-orange-500';
    if (streak < 14) return 'text-red-500';
    if (streak < 30) return 'text-red-600';
    return 'text-purple-500';
  };

  const sortedEntries = [...entries].sort((a, b) => {
    const valueA = selectedTab === 'current' ? a.currentStreak : a.longestStreak;
    const valueB = selectedTab === 'current' ? b.currentStreak : b.longestStreak;
    return valueB - valueA;
  });

  if (compact) {
    return (
      <GlassCard className={cn('p-4', className)}>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-white flex items-center">
              <TrendingUp className="w-5 h-5 mr-2 text-orange-400" />
              Streak Leaders
            </h3>
            <span className="text-xs text-gray-400">{timeframe}</span>
          </div>
          
          <div className="space-y-2">
            {sortedEntries.slice(0, 5).map((entry, index) => (
              <motion.div
                key={entry.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={cn(
                  'flex items-center space-x-3 p-2 rounded-lg border',
                  entry.isCurrentUser ? 'bg-blue-500/20 border-blue-500/30' : 'bg-white/5 border-white/10'
                )}
              >
                <div className="flex items-center justify-center w-6">
                  {getRankIcon(index + 1)}
                </div>
                
                <div className="w-8 h-8 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-xs font-bold text-white">
                  {entry.username.charAt(0).toUpperCase()}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="text-sm font-medium text-white truncate">
                    {entry.username}
                  </div>
                </div>
                
                <div className="flex items-center space-x-1">
                  <Flame className={cn('w-4 h-4', getStreakIntensity(entry.currentStreak))} />
                  <span className="text-sm font-bold text-white">
                    {selectedTab === 'current' ? entry.currentStreak : entry.longestStreak}
                  </span>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </GlassCard>
    );
  }

  return (
    <GlassCard className={cn('p-6', className)}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Streak Leaderboard</h2>
              <p className="text-sm text-gray-400">Compete with other learners</p>
            </div>
          </div>
          
          {currentUserRank && (
            <div className="text-center">
              <div className="text-2xl font-bold text-white">#{currentUserRank}</div>
              <div className="text-xs text-gray-400">Your Rank</div>
            </div>
          )}
        </div>

        {/* Timeframe Selector */}
        <div className="flex space-x-2 overflow-x-auto">
          {timeframes.map(tf => {
            const Icon = tf.icon;
            return (
              <EnhancedButton
                key={tf.id}
                onClick={() => onTimeframeChange?.(tf.id as any)}
                variant={timeframe === tf.id ? 'default' : 'outline'}
                size="sm"
                className={cn(
                  'flex items-center space-x-2 whitespace-nowrap',
                  timeframe === tf.id && 'bg-orange-500 hover:bg-orange-600'
                )}
              >
                <Icon className="w-4 h-4" />
                <span>{tf.name}</span>
              </EnhancedButton>
            );
          })}
        </div>

        {/* Tab Selector */}
        <div className="flex space-x-2">
          <EnhancedButton
            onClick={() => setSelectedTab('current')}
            variant={selectedTab === 'current' ? 'default' : 'outline'}
            size="sm"
            className={cn(
              'flex items-center space-x-2',
              selectedTab === 'current' && 'bg-orange-500 hover:bg-orange-600'
            )}
          >
            <Flame className="w-4 h-4" />
            <span>Current Streaks</span>
          </EnhancedButton>
          
          <EnhancedButton
            onClick={() => setSelectedTab('longest')}
            variant={selectedTab === 'longest' ? 'default' : 'outline'}
            size="sm"
            className={cn(
              'flex items-center space-x-2',
              selectedTab === 'longest' && 'bg-orange-500 hover:bg-orange-600'
            )}
          >
            <Trophy className="w-4 h-4" />
            <span>Longest Streaks</span>
          </EnhancedButton>
        </div>

        {/* Leaderboard Entries */}
        <div className="space-y-3">
          <AnimatePresence mode="wait">
            {sortedEntries.map((entry, index) => {
              const streakValue = selectedTab === 'current' ? entry.currentStreak : entry.longestStreak;
              
              return (
                <motion.div
                  key={`${entry.id}-${selectedTab}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ delay: index * 0.05 }}
                  className={cn(
                    'flex items-center space-x-4 p-4 rounded-xl border transition-all',
                    entry.isCurrentUser 
                      ? 'bg-blue-500/20 border-blue-500/30 ring-2 ring-blue-500/20' 
                      : getRankColor(index + 1),
                    'hover:bg-white/10'
                  )}
                >
                  {/* Rank */}
                  <div className="flex items-center justify-center w-8">
                    {getRankIcon(index + 1)}
                  </div>

                  {/* Avatar */}
                  <div className="relative">
                    <div className="w-12 h-12 rounded-full bg-gradient-to-br from-gray-600 to-gray-700 flex items-center justify-center text-lg font-bold text-white">
                      {entry.avatar ? (
                        <img src={entry.avatar} alt={entry.username} className="w-full h-full rounded-full object-cover" />
                      ) : (
                        entry.username.charAt(0).toUpperCase()
                      )}
                    </div>
                    
                    {/* Streak indicator */}
                    {streakValue > 0 && (
                      <motion.div
                        animate={{ scale: [1, 1.2, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                        className="absolute -top-1 -right-1 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center"
                      >
                        <Flame className="w-3 h-3 text-white" />
                      </motion.div>
                    )}
                  </div>

                  {/* User Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h4 className="font-semibold text-white truncate">{entry.username}</h4>
                      {entry.isCurrentUser && (
                        <span className="text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">You</span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-4 mt-1">
                      <div className="flex items-center space-x-1">
                        <Calendar className="w-3 h-3 text-gray-400" />
                        <span className="text-xs text-gray-400">
                          Started {entry.streakStartDate.toLocaleDateString()}
                        </span>
                      </div>
                      
                      {entry.badges.length > 0 && (
                        <div className="flex items-center space-x-1">
                          <Award className="w-3 h-3 text-yellow-400" />
                          <span className="text-xs text-yellow-400">{entry.badges.length} badges</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Streak Value */}
                  <div className="text-right">
                    <div className="flex items-center space-x-2">
                      <Flame className={cn('w-6 h-6', getStreakIntensity(streakValue))} />
                      <div>
                        <div className="text-2xl font-bold text-white">{streakValue}</div>
                        <div className="text-xs text-gray-400">
                          {streakValue === 1 ? 'day' : 'days'}
                        </div>
                      </div>
                    </div>
                    
                    {/* XP Display */}
                    <div className="flex items-center justify-end space-x-1 mt-1">
                      <Zap className="w-3 h-3 text-yellow-400" />
                      <span className="text-xs text-yellow-400 font-medium">
                        {entry.totalXP.toLocaleString()} XP
                      </span>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </AnimatePresence>
        </div>

        {/* Empty State */}
        {sortedEntries.length === 0 && (
          <div className="text-center py-8">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-300 mb-2">No streaks yet</h3>
            <p className="text-gray-400">Be the first to start a learning streak!</p>
          </div>
        )}

        {/* Stats Summary */}
        {sortedEntries.length > 0 && (
          <div className="grid grid-cols-3 gap-4 pt-4 border-t border-white/10">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">
                {Math.max(...sortedEntries.map(e => selectedTab === 'current' ? e.currentStreak : e.longestStreak))}
              </div>
              <div className="text-xs text-gray-400">Top Streak</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-white">
                {Math.round(sortedEntries.reduce((sum, e) => sum + (selectedTab === 'current' ? e.currentStreak : e.longestStreak), 0) / sortedEntries.length)}
              </div>
              <div className="text-xs text-gray-400">Average</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{sortedEntries.length}</div>
              <div className="text-xs text-gray-400">Active Users</div>
            </div>
          </div>
        )}
      </div>
    </GlassCard>
  );
}
