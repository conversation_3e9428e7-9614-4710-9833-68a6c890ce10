'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { User, Trophy, BookOpen, Users, Star } from 'lucide-react';

interface NotificationData {
  id: string;
  type: 'signup' | 'completion' | 'achievement' | 'milestone';
  user: {
    name: string;
    avatar?: string;
    location?: string;
  };
  content: {
    title: string;
    description: string;
    icon: React.ReactNode;
    course?: string;
    achievement?: string;
  };
  timestamp: Date;
}

interface RealTimeNotificationSystemProps {
  className?: string;
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  maxVisible?: number;
  autoHideDuration?: number;
  enableSound?: boolean;
}

// Mock notification generator for demo purposes
const generateMockNotification = (): NotificationData => {
  const users = [
    { name: '<PERSON>', location: 'San Francisco' },
    { name: '<PERSON>', location: 'Madrid' },
    { name: '<PERSON>', location: 'Seoul' },
    { name: '<PERSON>', location: 'London' },
    { name: '<PERSON>', location: 'Cairo' },
    { name: '<PERSON>', location: 'Sydney' },
    { name: '<PERSON> Silva', location: 'São Paulo' },
    { name: 'Yuki Tanaka', location: 'Tokyo' }
  ];

  const courses = [
    'Solidity Fundamentals',
    'Smart Contract Security',
    'DeFi Development',
    'NFT Marketplace',
    'Web3 Integration',
    'Advanced Solidity'
  ];

  const achievements = [
    'First Contract Deployed',
    'Security Expert',
    'Gas Optimizer',
    'DeFi Pioneer',
    'NFT Creator',
    'Community Helper'
  ];

  const types: NotificationData['type'][] = ['signup', 'completion', 'achievement', 'milestone'];
  const type = types[Math.floor(Math.random() * types.length)];
  const user = users[Math.floor(Math.random() * users.length)];

  const notifications = {
    signup: {
      title: `${user.name} just joined!`,
      description: `New student from ${user.location}`,
      icon: <User className="w-4 h-4" />
    },
    completion: {
      title: `${user.name} completed a lesson`,
      description: `Just finished "${courses[Math.floor(Math.random() * courses.length)]}"`,
      icon: <BookOpen className="w-4 h-4" />,
      course: courses[Math.floor(Math.random() * courses.length)]
    },
    achievement: {
      title: `${user.name} earned an achievement!`,
      description: `Unlocked "${achievements[Math.floor(Math.random() * achievements.length)]}"`,
      icon: <Trophy className="w-4 h-4" />,
      achievement: achievements[Math.floor(Math.random() * achievements.length)]
    },
    milestone: {
      title: `${user.name} reached a milestone`,
      description: `Completed ${Math.floor(Math.random() * 50) + 10} lessons`,
      icon: <Star className="w-4 h-4" />
    }
  };

  return {
    id: `notification-${Date.now()}-${Math.random()}`,
    type,
    user: {
      ...user,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${user.name}`
    },
    content: notifications[type],
    timestamp: new Date()
  };
};

export function RealTimeNotificationSystem({
  className,
  position = 'bottom-right',
  maxVisible = 3,
  autoHideDuration = 5000,
  enableSound = false
}: RealTimeNotificationSystemProps) {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [isVisible, setIsVisible] = useState(true);

  // Add new notification
  const addNotification = useCallback((notification: NotificationData) => {
    setNotifications(prev => {
      const newNotifications = [notification, ...prev];
      return newNotifications.slice(0, maxVisible);
    });

    // Play sound if enabled
    if (enableSound && typeof window !== 'undefined') {
      try {
        const audio = new Audio('/sounds/notification.mp3');
        audio.volume = 0.3;
        audio.play().catch(() => {
          // Ignore audio play errors (user interaction required)
        });
      } catch (error) {
        // Ignore audio errors
      }
    }

    // Auto-hide notification
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== notification.id));
    }, autoHideDuration);
  }, [maxVisible, autoHideDuration, enableSound]);

  // Generate mock notifications for demo
  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.7) { // 30% chance every interval
        addNotification(generateMockNotification());
      }
    }, 8000 + Math.random() * 12000); // Random interval between 8-20 seconds

    return () => clearInterval(interval);
  }, [addNotification]);

  // Handle manual dismiss
  const dismissNotification = useCallback((id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  }, []);

  // Position classes
  const positionClasses = {
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4'
  };

  if (!isVisible) return null;

  return (
    <div
      className={cn(
        'fixed z-50 pointer-events-none',
        positionClasses[position],
        className
      )}
      role="region"
      aria-label="Live notifications"
      aria-live="polite"
    >
      <AnimatePresence mode="popLayout">
        {notifications.map((notification, index) => (
          <motion.div
            key={notification.id}
            initial={{ 
              opacity: 0, 
              scale: 0.8,
              x: position.includes('right') ? 100 : -100,
              y: position.includes('bottom') ? 20 : -20
            }}
            animate={{ 
              opacity: 1, 
              scale: 1,
              x: 0,
              y: 0
            }}
            exit={{ 
              opacity: 0, 
              scale: 0.8,
              x: position.includes('right') ? 100 : -100,
              transition: { duration: 0.2 }
            }}
            transition={{ 
              type: "spring", 
              stiffness: 300, 
              damping: 30,
              delay: index * 0.1
            }}
            layout
            className={cn(
              'mb-3 pointer-events-auto',
              index > 0 && 'opacity-80'
            )}
          >
            <NotificationCard
              notification={notification}
              onDismiss={() => dismissNotification(notification.id)}
            />
          </motion.div>
        ))}
      </AnimatePresence>

      {/* Toggle visibility button */}
      <motion.button
        onClick={() => setIsVisible(!isVisible)}
        className="mt-2 p-2 bg-white/10 backdrop-blur-sm rounded-full text-white/70 hover:text-white hover:bg-white/20 transition-colors pointer-events-auto"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        aria-label={isVisible ? 'Hide notifications' : 'Show notifications'}
      >
        <Users className="w-4 h-4" />
      </motion.button>
    </div>
  );
}

interface NotificationCardProps {
  notification: NotificationData;
  onDismiss: () => void;
}

function NotificationCard({ notification, onDismiss }: NotificationCardProps) {
  const [isHovered, setIsHovered] = useState(false);

  const getTypeColor = (type: NotificationData['type']) => {
    switch (type) {
      case 'signup':
        return 'border-blue-500/50 bg-blue-500/10';
      case 'completion':
        return 'border-green-500/50 bg-green-500/10';
      case 'achievement':
        return 'border-yellow-500/50 bg-yellow-500/10';
      case 'milestone':
        return 'border-purple-500/50 bg-purple-500/10';
      default:
        return 'border-gray-500/50 bg-gray-500/10';
    }
  };

  const getIconColor = (type: NotificationData['type']) => {
    switch (type) {
      case 'signup':
        return 'text-blue-400';
      case 'completion':
        return 'text-green-400';
      case 'achievement':
        return 'text-yellow-400';
      case 'milestone':
        return 'text-purple-400';
      default:
        return 'text-gray-400';
    }
  };

  return (
    <motion.div
      className={cn(
        'relative w-80 p-4 rounded-lg border backdrop-blur-sm shadow-lg',
        'bg-white/95 dark:bg-gray-900/95',
        getTypeColor(notification.type),
        'cursor-pointer transition-all duration-200',
        isHovered && 'scale-105 shadow-xl'
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onDismiss}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          onDismiss();
        }
      }}
      aria-label={`Notification: ${notification.content.title}. Click to dismiss.`}
    >
      {/* Close button */}
      <button
        onClick={(e) => {
          e.stopPropagation();
          onDismiss();
        }}
        className="absolute top-2 right-2 p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
        aria-label="Dismiss notification"
      >
        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>

      <div className="flex items-start space-x-3">
        {/* Avatar */}
        <div className="flex-shrink-0">
          {notification.user.avatar ? (
            <img
              src={notification.user.avatar}
              alt={`${notification.user.name}'s avatar`}
              className="w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
              <User className="w-5 h-5 text-white" />
            </div>
          )}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <div className={cn('flex-shrink-0', getIconColor(notification.type))}>
              {notification.content.icon}
            </div>
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white truncate">
              {notification.content.title}
            </h4>
          </div>
          
          <p className="text-xs text-gray-600 dark:text-gray-300 mb-2">
            {notification.content.description}
          </p>

          {/* Additional info */}
          {notification.content.course && (
            <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
              Course: {notification.content.course}
            </div>
          )}

          {notification.content.achievement && (
            <div className="text-xs text-yellow-600 dark:text-yellow-400 font-medium">
              🏆 {notification.content.achievement}
            </div>
          )}

          {/* Timestamp */}
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {notification.timestamp.toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </div>
        </div>
      </div>

      {/* Progress bar for auto-hide */}
      <motion.div
        className="absolute bottom-0 left-0 h-0.5 bg-current opacity-30"
        initial={{ width: '100%' }}
        animate={{ width: '0%' }}
        transition={{ duration: 5, ease: 'linear' }}
      />
    </motion.div>
  );
}

export default RealTimeNotificationSystem;
