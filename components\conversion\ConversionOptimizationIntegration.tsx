'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

// Import all conversion optimization components
import { ExitIntentSystem } from './ExitIntentSystem';
import { ScrollEngagementSystem } from './ScrollEngagementSystem';
import { UrgencyScarcitySystem } from './UrgencyScarcitySystem';
import { TrialOnboardingSystem } from './TrialOnboardingSystem';
import { ExitIntentProvider } from './ExitIntentAnalytics';
import { ScrollEngagementProvider } from './ScrollEngagementSystem';

interface ConversionOptimizationProps {
  className?: string;
  variant?: 'full' | 'landing' | 'trial' | 'content';
  enableExitIntent?: boolean;
  enableScrollEngagement?: boolean;
  enableUrgencyScarcity?: boolean;
  enableTrialOnboarding?: boolean;
  userSegment?: string;
  onConversion?: (system: string, action: string, data?: any) => void;
}

interface ConversionMetrics {
  exitIntentRecovery: number;
  scrollEngagementRate: number;
  urgencyConversions: number;
  trialSignups: number;
  overallConversionRate: number;
  bounceRateReduction: number;
  userSatisfactionScore: number;
}

// Main conversion optimization integration component
export function ConversionOptimizationIntegration({
  className,
  variant = 'full',
  enableExitIntent = true,
  enableScrollEngagement = true,
  enableUrgencyScarcity = true,
  enableTrialOnboarding = true,
  userSegment = 'general',
  onConversion
}: ConversionOptimizationProps) {
  const [conversionMetrics, setConversionMetrics] = useState<ConversionMetrics>({
    exitIntentRecovery: 0,
    scrollEngagementRate: 0,
    urgencyConversions: 0,
    trialSignups: 0,
    overallConversionRate: 0,
    bounceRateReduction: 0,
    userSatisfactionScore: 4.8
  });

  const [isOptimizationActive, setIsOptimizationActive] = useState(true);
  const [abTestVariant, setAbTestVariant] = useState<'A' | 'B'>('A');

  // Initialize A/B testing
  useEffect(() => {
    const variant = Math.random() < 0.5 ? 'A' : 'B';
    setAbTestVariant(variant);
    
    // Track A/B test assignment
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'ab_test_assignment', {
        variant,
        user_segment: userSegment,
        timestamp: new Date().toISOString()
      });
    }
  }, [userSegment]);

  // Handle conversions from all systems
  const handleConversion = (system: string, action: string, data?: any) => {
    // Update metrics based on system
    setConversionMetrics(prev => {
      const updated = { ...prev };
      
      switch (system) {
        case 'exit_intent':
          updated.exitIntentRecovery += 1;
          break;
        case 'scroll_engagement':
          updated.scrollEngagementRate += 1;
          break;
        case 'urgency_scarcity':
          updated.urgencyConversions += 1;
          break;
        case 'trial_onboarding':
          updated.trialSignups += 1;
          break;
      }
      
      // Calculate overall conversion rate
      const totalInteractions = updated.exitIntentRecovery + updated.scrollEngagementRate + 
                               updated.urgencyConversions + updated.trialSignups;
      updated.overallConversionRate = totalInteractions > 0 ? 
        ((updated.exitIntentRecovery + updated.urgencyConversions + updated.trialSignups) / totalInteractions) * 100 : 0;
      
      return updated;
    });

    // Forward to parent handler
    onConversion?.(system, action, data);

    // Track with analytics
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'conversion_optimization', {
        system,
        action,
        user_segment: userSegment,
        ab_variant: abTestVariant,
        timestamp: new Date().toISOString()
      });
    }
  };

  // Landing page variant - focus on trial conversion
  if (variant === 'landing') {
    return (
      <ExitIntentProvider>
        <ScrollEngagementProvider>
          <div className={cn('space-y-8', className)}>
            {/* Trial onboarding as primary focus */}
            {enableTrialOnboarding && (
              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
              >
                <TrialOnboardingSystem
                  onConversion={(action, data) => handleConversion('trial_onboarding', action, data)}
                />
              </motion.section>
            )}

            {/* Urgency and scarcity for conversion pressure */}
            {enableUrgencyScarcity && (
              <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
              >
                <UrgencyScarcitySystem
                  onConversion={(action, data) => handleConversion('urgency_scarcity', action, data)}
                />
              </motion.section>
            )}

            {/* Scroll engagement for content interaction */}
            {enableScrollEngagement && (
              <ScrollEngagementSystem
                onConversion={(action, data) => handleConversion('scroll_engagement', action, data)}
              />
            )}

            {/* Exit intent as last resort */}
            {enableExitIntent && (
              <ExitIntentSystem
                onConversion={(action) => handleConversion('exit_intent', action)}
              />
            )}
          </div>
        </ScrollEngagementProvider>
      </ExitIntentProvider>
    );
  }

  // Trial variant - focus on onboarding optimization
  if (variant === 'trial') {
    return (
      <div className={cn('space-y-6', className)}>
        {/* Progressive onboarding */}
        {enableTrialOnboarding && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <TrialOnboardingSystem
              onConversion={(action, data) => handleConversion('trial_onboarding', action, data)}
            />
          </motion.section>
        )}

        {/* Gentle urgency for trial extension */}
        {enableUrgencyScarcity && (
          <motion.section
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <UrgencyScarcitySystem
              onConversion={(action, data) => handleConversion('urgency_scarcity', action, data)}
            />
          </motion.section>
        )}
      </div>
    );
  }

  // Content variant - focus on engagement
  if (variant === 'content') {
    return (
      <ScrollEngagementProvider>
        <div className={cn('relative', className)}>
          {/* Scroll engagement for content interaction */}
          {enableScrollEngagement && (
            <ScrollEngagementSystem
              onConversion={(action, data) => handleConversion('scroll_engagement', action, data)}
            />
          )}

          {/* Exit intent for content abandonment */}
          {enableExitIntent && (
            <ExitIntentSystem
              onConversion={(action) => handleConversion('exit_intent', action)}
            />
          )}
        </div>
      </ScrollEngagementProvider>
    );
  }

  // Full variant - all systems active
  return (
    <ExitIntentProvider>
      <ScrollEngagementProvider>
        <div className={cn('space-y-12', className)}>
          {/* Trial onboarding system */}
          {enableTrialOnboarding && (
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <TrialOnboardingSystem
                onConversion={(action, data) => handleConversion('trial_onboarding', action, data)}
              />
            </motion.section>
          )}

          {/* Urgency and scarcity system */}
          {enableUrgencyScarcity && (
            <motion.section
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <UrgencyScarcitySystem
                onConversion={(action, data) => handleConversion('urgency_scarcity', action, data)}
              />
            </motion.section>
          )}

          {/* Scroll engagement system */}
          {enableScrollEngagement && (
            <ScrollEngagementSystem
              onConversion={(action, data) => handleConversion('scroll_engagement', action, data)}
            />
          )}

          {/* Exit intent system */}
          {enableExitIntent && (
            <ExitIntentSystem
              onConversion={(action) => handleConversion('exit_intent', action)}
            />
          )}

          {/* Conversion metrics dashboard (development mode) */}
          {process.env.NODE_ENV === 'development' && (
            <motion.div
              className="fixed bottom-4 left-4 bg-black/90 text-white p-4 rounded-lg text-xs max-w-xs"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1 }}
            >
              <h4 className="font-bold mb-2">Conversion Metrics</h4>
              <div className="space-y-1">
                <div>Exit Intent: {conversionMetrics.exitIntentRecovery}</div>
                <div>Scroll Engagement: {conversionMetrics.scrollEngagementRate}</div>
                <div>Urgency: {conversionMetrics.urgencyConversions}</div>
                <div>Trial Signups: {conversionMetrics.trialSignups}</div>
                <div>Overall Rate: {conversionMetrics.overallConversionRate.toFixed(1)}%</div>
                <div>A/B Variant: {abTestVariant}</div>
              </div>
            </motion.div>
          )}
        </div>
      </ScrollEngagementProvider>
    </ExitIntentProvider>
  );
}

// Hook for conversion optimization analytics
export function useConversionOptimizationAnalytics() {
  const [analytics, setAnalytics] = useState({
    totalConversions: 0,
    conversionsBySystem: {} as Record<string, number>,
    conversionsBySegment: {} as Record<string, number>,
    abTestResults: {} as Record<string, { conversions: number; views: number; rate: number }>,
    successMetrics: {
      bounceRateReduction: 0,
      scrollToConversionIncrease: 0,
      trialSignupBoost: 0,
      userSatisfactionMaintained: true
    }
  });

  const trackConversion = (system: string, userSegment: string, abVariant: string) => {
    setAnalytics(prev => ({
      ...prev,
      totalConversions: prev.totalConversions + 1,
      conversionsBySystem: {
        ...prev.conversionsBySystem,
        [system]: (prev.conversionsBySystem[system] || 0) + 1
      },
      conversionsBySegment: {
        ...prev.conversionsBySegment,
        [userSegment]: (prev.conversionsBySegment[userSegment] || 0) + 1
      },
      abTestResults: {
        ...prev.abTestResults,
        [abVariant]: {
          conversions: (prev.abTestResults[abVariant]?.conversions || 0) + 1,
          views: prev.abTestResults[abVariant]?.views || 0,
          rate: 0 // Will be calculated
        }
      }
    }));
  };

  const getOptimizationInsights = () => {
    const bestPerformingSystem = Object.entries(analytics.conversionsBySystem)
      .sort(([,a], [,b]) => b - a)[0];

    const bestPerformingSegment = Object.entries(analytics.conversionsBySegment)
      .sort(([,a], [,b]) => b - a)[0];

    // Calculate A/B test results
    const abResults = Object.entries(analytics.abTestResults).map(([variant, data]) => ({
      variant,
      conversions: data.conversions,
      views: data.views,
      rate: data.views > 0 ? (data.conversions / data.views) * 100 : 0
    }));

    return {
      totalConversions: analytics.totalConversions,
      bestPerformingSystem: bestPerformingSystem ? {
        system: bestPerformingSystem[0],
        conversions: bestPerformingSystem[1]
      } : null,
      bestPerformingSegment: bestPerformingSegment ? {
        segment: bestPerformingSegment[0],
        conversions: bestPerformingSegment[1]
      } : null,
      abTestResults: abResults,
      successMetrics: analytics.successMetrics
    };
  };

  return {
    analytics,
    trackConversion,
    getOptimizationInsights
  };
}

// Export all components for easy access
export {
  ExitIntentSystem,
  ScrollEngagementSystem,
  UrgencyScarcitySystem,
  TrialOnboardingSystem
};

// Default export
export default ConversionOptimizationIntegration;
