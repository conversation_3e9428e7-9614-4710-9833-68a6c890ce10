'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Users, Zap, Target, Fire, Trophy, Code, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * Represents a single notification with all its properties
 */
interface NotificationData {
  /** Unique identifier for the notification */
  id: string;
  /** Type of notification affecting styling and behavior */
  type: 'activity' | 'achievement' | 'milestone' | 'social';
  /** Icon component to display with the notification */
  icon: React.ComponentType<{ className?: string }>;
  /** Emoji to display alongside the icon */
  emoji: string;
  /** Main notification message text */
  message: string;
  /** When the notification was created */
  timestamp: Date;
  /** Priority level affecting display order and styling */
  priority: 'low' | 'medium' | 'high';
  /** Tailwind color class for the notification theme */
  color: string;
  /** Optional auto-dismiss duration in milliseconds */
  duration?: number;
  /** Optional location information for location-based notifications */
  location?: string;
  /** Optional time ago string for recent activity */
  timeAgo?: string;
  /** Optional user avatar URL */
  avatar?: string;
  /** Optional user name for personalized notifications */
  userName?: string;
}

/**
 * Props for the FloatingNotifications component
 */
interface FloatingNotificationsProps {
  /** Additional CSS classes to apply to the container */
  className?: string;
  /** Screen position for the notification container */
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left';
  /** Maximum number of notifications to show simultaneously */
  maxVisible?: number;
  /** Interval in milliseconds for auto-rotating notifications */
  autoRotateInterval?: number;
  /** Whether to play sound effects for new notifications */
  enableSound?: boolean;
  /** Whether to respect user's reduced motion preferences */
  respectReducedMotion?: boolean;
}

/**
 * Dynamic Floating Notifications System
 * 
 * Displays real-time social proof messages with auto-rotation and smooth animations.
 * Positioned in a fixed corner with accessibility support and reduced motion preferences.
 * 
 * @component
 * @example
 * ```tsx
 * <FloatingNotifications 
 *   position="top-right"
 *   maxVisible={3}
 *   autoRotateInterval={4500}
 *   respectReducedMotion={true}
 * />
 * ```
 */
export function FloatingNotifications({
  className = '',
  position = 'top-right',
  maxVisible = 3,
  autoRotateInterval = 4500,
  enableSound = false,
  respectReducedMotion = true
}: FloatingNotificationsProps) {
  const [notifications, setNotifications] = useState<NotificationData[]>([]);
  const [visibleNotifications, setVisibleNotifications] = useState<NotificationData[]>([]);
  const [isPaused, setIsPaused] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const soundRef = useRef<HTMLAudioElement | null>(null);

  // Enhanced notification data with location-based and real-time activity
  const getLocationBasedNotifications = () => {
    const cities = ['San Francisco', 'New York', 'London', 'Berlin', 'Tokyo', 'Singapore', 'Toronto', 'Sydney'];
    const randomCity = cities[Math.floor(Math.random() * cities.length)];
    const randomCount = Math.floor(Math.random() * 20) + 5;

    return [
      {
        type: 'activity' as const,
        icon: Fire,
        emoji: '🔥',
        message: `${randomCount} people from ${randomCity} started learning in the last hour`,
        priority: 'high' as const,
        color: 'text-orange-400',
        duration: 5000,
        location: randomCity
      },
      {
        type: 'social' as const,
        icon: Users,
        emoji: '🌍',
        message: `${Math.floor(Math.random() * 15) + 8} developers from your city are learning Solidity this week`,
        priority: 'medium' as const,
        color: 'text-green-400',
        duration: 4500,
        location: 'local'
      }
    ];
  };

  const getRealTimeActivityNotifications = () => {
    const users = [
      { name: 'Sarah', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face' },
      { name: 'Alex', avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face' },
      { name: 'Maya', avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face' },
      { name: 'David', avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face' },
      { name: 'Emma', avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=32&h=32&fit=crop&crop=face' },
      { name: 'James', avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=32&h=32&fit=crop&crop=face' },
      { name: 'Lisa', avatar: 'https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=32&h=32&fit=crop&crop=face' },
      { name: 'Ryan', avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=32&h=32&fit=crop&crop=face' },
      { name: 'Sophia', avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=32&h=32&fit=crop&crop=face' },
      { name: 'Michael', avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=32&h=32&fit=crop&crop=face' }
    ];

    const activities = [
      'deployed their first smart contract',
      'completed the DeFi fundamentals course',
      'earned "Gas Optimization Expert" badge',
      'finished building a DEX',
      'mastered Solidity security patterns',
      'completed 50 coding challenges',
      'built their first NFT marketplace',
      'earned "Smart Contract Auditor" certification'
    ];

    const randomUser = users[Math.floor(Math.random() * users.length)];
    const randomActivity = activities[Math.floor(Math.random() * activities.length)];
    const timeAgo = Math.floor(Math.random() * 30) + 1;

    return {
      type: 'achievement' as const,
      icon: Sparkles,
      emoji: '✨',
      message: `${randomUser.name} just ${randomActivity}`,
      priority: 'medium' as const,
      color: 'text-blue-400',
      duration: 4500,
      timeAgo: `${timeAgo} minutes ago`,
      avatar: randomUser.avatar,
      userName: randomUser.name
    };
  };

  const getStatisticsNotifications = () => {
    const stats = [
      {
        type: 'social' as const,
        icon: Target,
        emoji: '🎯',
        message: `${Math.floor(Math.random() * 50) + 100} developers are currently online`,
        priority: 'medium' as const,
        color: 'text-green-400',
        duration: 4000
      },
      {
        type: 'activity' as const,
        icon: Code,
        emoji: '💻',
        message: `${Math.floor(Math.random() * 20) + 10} smart contracts deployed in the last hour`,
        priority: 'medium' as const,
        color: 'text-purple-400',
        duration: 4000
      },
      {
        type: 'milestone' as const,
        icon: Trophy,
        emoji: '🏆',
        message: `${Math.floor(Math.random() * 100) + 500} lessons completed today`,
        priority: 'low' as const,
        color: 'text-yellow-400',
        duration: 4500
      }
    ];

    return stats[Math.floor(Math.random() * stats.length)];
  };

  const generateDynamicNotification = () => {
    const notificationTypes = [
      () => getLocationBasedNotifications()[Math.floor(Math.random() * 2)],
      getRealTimeActivityNotifications,
      getStatisticsNotifications
    ];

    const randomType = notificationTypes[Math.floor(Math.random() * notificationTypes.length)];
    return randomType();
  };

  // Check for reduced motion preference
  const prefersReducedMotion = typeof window !== 'undefined' 
    ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
    : false;

  const shouldAnimate = respectReducedMotion ? !prefersReducedMotion : true;

  // Initialize notifications with dynamic content
  useEffect(() => {
    const initialNotifications = Array.from({ length: 5 }, (_, index) => {
      const dynamicNotif = generateDynamicNotification();
      return {
        ...dynamicNotif,
        id: `notification-${index}-${Date.now()}`,
        timestamp: new Date(Date.now() - Math.random() * 3600000) // Random time within last hour
      };
    });

    setNotifications(initialNotifications);
    setVisibleNotifications(initialNotifications.slice(0, maxVisible));
  }, [maxVisible]);

  // Auto-rotation logic
  useEffect(() => {
    if (!shouldAnimate || isPaused || notifications.length === 0) return;

    intervalRef.current = setInterval(() => {
      setCurrentIndex(prev => {
        const nextIndex = (prev + 1) % notifications.length;
        const nextNotifications = notifications.slice(nextIndex, nextIndex + maxVisible);
        
        // Handle wrap-around
        if (nextNotifications.length < maxVisible) {
          const remaining = maxVisible - nextNotifications.length;
          nextNotifications.push(...notifications.slice(0, remaining));
        }
        
        setVisibleNotifications(nextNotifications);
        return nextIndex;
      });
    }, autoRotateInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [notifications, maxVisible, autoRotateInterval, shouldAnimate, isPaused]);

  // Sound effect for new notifications
  useEffect(() => {
    if (enableSound && typeof window !== 'undefined') {
      soundRef.current = new Audio('/sounds/notification.mp3');
      soundRef.current.volume = 0.3;
    }
  }, [enableSound]);

  // Add new notification with enhanced real-time updates
  useEffect(() => {
    const addRandomNotification = () => {
      const dynamicNotif = generateDynamicNotification();
      const newNotification: NotificationData = {
        ...dynamicNotif,
        id: `notification-${Date.now()}`,
        timestamp: new Date()
      };

      setNotifications(prev => [newNotification, ...prev.slice(0, 19)]); // Keep max 20 notifications

      if (enableSound && soundRef.current) {
        soundRef.current.play().catch(() => {
          // Ignore autoplay restrictions
        });
      }
    };

    // Add new notification every 8-15 seconds for more engagement
    const randomInterval = setInterval(addRandomNotification, Math.random() * 7000 + 8000);

    return () => clearInterval(randomInterval);
  }, [enableSound]);

  const getPositionClasses = () => {
    switch (position) {
      case 'top-left':
        return 'top-4 left-4';
      case 'bottom-right':
        return 'bottom-4 right-4';
      case 'bottom-left':
        return 'bottom-4 left-4';
      default:
        return 'top-4 right-4';
    }
  };

  const dismissNotification = (id: string) => {
    setVisibleNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const handleMouseEnter = () => {
    setIsPaused(true);
  };

  const handleMouseLeave = () => {
    setIsPaused(false);
  };

  if (!shouldAnimate && visibleNotifications.length === 0) {
    return null;
  }

  return (
    <div
      className={cn(
        'fixed z-50 flex flex-col space-y-2 max-w-sm',
        getPositionClasses(),
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      role="region"
      aria-label="Live notifications"
      aria-live="polite"
    >
      <AnimatePresence mode="popLayout">
        {visibleNotifications.map((notification, index) => (
          <NotificationCard
            key={notification.id}
            notification={notification}
            index={index}
            onDismiss={dismissNotification}
            shouldAnimate={shouldAnimate}
          />
        ))}
      </AnimatePresence>
    </div>
  );
}

/**
 * Individual Notification Card Component
 */
interface NotificationCardProps {
  notification: NotificationData;
  index: number;
  onDismiss: (id: string) => void;
  shouldAnimate: boolean;
}

function NotificationCard({ 
  notification, 
  index, 
  onDismiss, 
  shouldAnimate 
}: NotificationCardProps) {
  const [isVisible, setIsVisible] = useState(true);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const IconComponent = notification.icon;

  useEffect(() => {
    if (notification.duration) {
      timeoutRef.current = setTimeout(() => {
        setIsVisible(false);
        setTimeout(() => onDismiss(notification.id), 300);
      }, notification.duration);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [notification.duration, notification.id, onDismiss]);

  const variants = {
    initial: shouldAnimate ? {
      opacity: 0,
      x: 300,
      scale: 0.8
    } : {
      opacity: 1,
      x: 0,
      scale: 1
    },
    animate: {
      opacity: isVisible ? 1 : 0,
      x: isVisible ? 0 : 300,
      scale: isVisible ? 1 : 0.8,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30,
        delay: shouldAnimate ? index * 0.1 : 0
      }
    },
    exit: {
      opacity: 0,
      x: 300,
      scale: 0.8,
      transition: {
        duration: 0.3
      }
    }
  };

  const getPriorityStyles = () => {
    switch (notification.priority) {
      case 'high':
        return 'border-red-400/30 bg-red-500/10 shadow-red-500/20';
      case 'medium':
        return 'border-blue-400/30 bg-blue-500/10 shadow-blue-500/20';
      default:
        return 'border-gray-400/30 bg-gray-500/10 shadow-gray-500/20';
    }
  };

  return (
    <motion.div
      variants={variants}
      initial="initial"
      animate="animate"
      exit="exit"
      className={cn(
        'glass p-4 rounded-lg border backdrop-blur-md shadow-lg',
        'hover:shadow-xl transition-shadow duration-300 cursor-pointer',
        'group relative overflow-hidden',
        getPriorityStyles()
      )}
      onClick={() => onDismiss(notification.id)}
      role="alert"
      aria-live="assertive"
    >
      {/* Background gradient animation */}
      <motion.div
        className="absolute inset-0 opacity-0 group-hover:opacity-20 transition-opacity duration-300"
        animate={{
          background: shouldAnimate
            ? `linear-gradient(45deg, ${notification.color.replace('text-', '')}20, transparent, ${notification.color.replace('text-', '')}20)`
            : 'transparent'
        }}
      />

      {/* Content */}
      <div className="relative z-10 flex items-start space-x-3">
        {/* Avatar or Icon */}
        <div className="flex-shrink-0">
          {notification.avatar ? (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 300, delay: 0.1 }}
              className="relative"
            >
              <img
                src={notification.avatar}
                alt={notification.userName || 'User'}
                className="w-8 h-8 rounded-full border-2 border-white/20"
              />
              <motion.div
                className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border border-white/50"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </motion.div>
          ) : (
            <div className={cn(
              'w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0',
              'bg-gradient-to-br from-white/20 to-white/10 backdrop-blur-sm'
            )}>
              <span className="text-lg" role="img" aria-label={notification.type}>
                {notification.emoji}
              </span>
            </div>
          )}
        </div>

        <div className="flex-1 min-w-0">
          <p className="text-sm text-white font-medium leading-relaxed">
            {notification.message}
          </p>
          <div className="flex items-center space-x-2 mt-1">
            <p className="text-xs text-gray-400">
              {notification.timeAgo || notification.timestamp.toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
            {notification.location && notification.location !== 'local' && (
              <>
                <span className="text-xs text-gray-500">•</span>
                <p className="text-xs text-gray-400 flex items-center">
                  <span className="mr-1">📍</span>
                  {notification.location}
                </p>
              </>
            )}
          </div>
        </div>

        <button
          onClick={(e) => {
            e.stopPropagation();
            onDismiss(notification.id);
          }}
          className="text-gray-400 hover:text-white transition-colors duration-200 p-1"
          aria-label="Dismiss notification"
        >
          <X className="w-4 h-4" />
        </button>
      </div>

      {/* Priority indicator */}
      {notification.priority === 'high' && (
        <motion.div
          className="absolute top-2 left-2 w-2 h-2 bg-red-400 rounded-full"
          animate={{ scale: [1, 1.2, 1], opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 1.5, repeat: Infinity }}
        />
      )}
    </motion.div>
  );
}
