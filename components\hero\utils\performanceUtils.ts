/**
 * Performance optimization utilities for Hero Section components
 */

/**
 * Debounce function to limit the rate of function calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  immediate?: boolean
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null;
  
  return function executedFunction(...args: Parameters<T>) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    
    const callNow = immediate && !timeout;
    
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    
    if (callNow) func(...args);
  };
}

/**
 * Throttle function to limit function execution frequency
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return function executedFunction(...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Check if user prefers reduced motion
 */
export function prefersReducedMotion(): boolean {
  if (typeof window === 'undefined') return false;
  
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
}

/**
 * Optimize animation frame requests
 */
export function requestAnimationFrameOptimized(callback: () => void): number {
  if (typeof window === 'undefined') return 0;
  
  return window.requestAnimationFrame(callback);
}

/**
 * Cancel animation frame with fallback
 */
export function cancelAnimationFrameOptimized(id: number): void {
  if (typeof window === 'undefined') return;
  
  window.cancelAnimationFrame(id);
}

/**
 * Intersection Observer with performance optimizations
 */
export function createOptimizedIntersectionObserver(
  callback: IntersectionObserverCallback,
  options?: IntersectionObserverInit
): IntersectionObserver | null {
  if (typeof window === 'undefined' || !('IntersectionObserver' in window)) {
    return null;
  }
  
  const defaultOptions: IntersectionObserverInit = {
    threshold: 0.1,
    rootMargin: '50px',
    ...options
  };
  
  return new IntersectionObserver(callback, defaultOptions);
}

/**
 * Lazy load images with performance optimizations
 */
export function lazyLoadImage(
  src: string,
  placeholder?: string
): Promise<HTMLImageElement> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => resolve(img);
    img.onerror = reject;
    
    // Use placeholder while loading
    if (placeholder) {
      img.src = placeholder;
    }
    
    // Load actual image
    img.src = src;
  });
}

/**
 * Preload critical resources
 */
export function preloadResource(
  href: string,
  as: string,
  type?: string,
  crossOrigin?: string
): void {
  if (typeof document === 'undefined') return;
  
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  
  if (type) link.type = type;
  if (crossOrigin) link.crossOrigin = crossOrigin;
  
  document.head.appendChild(link);
}

/**
 * Measure performance metrics
 */
export interface PerformanceMetrics {
  fcp?: number;
  lcp?: number;
  cls?: number;
  fid?: number;
  ttfb?: number;
}

export function measurePerformance(): PerformanceMetrics {
  if (typeof window === 'undefined' || !('performance' in window)) {
    return {};
  }
  
  const metrics: PerformanceMetrics = {};
  
  try {
    // First Contentful Paint
    const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0];
    if (fcpEntry) {
      metrics.fcp = fcpEntry.startTime;
    }
    
    // Largest Contentful Paint
    const lcpEntries = performance.getEntriesByType('largest-contentful-paint');
    if (lcpEntries.length > 0) {
      metrics.lcp = lcpEntries[lcpEntries.length - 1].startTime;
    }
    
    // Time to First Byte
    const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    if (navigationEntry) {
      metrics.ttfb = navigationEntry.responseStart - navigationEntry.requestStart;
    }
  } catch (error) {
    console.warn('Performance measurement failed:', error);
  }
  
  return metrics;
}

/**
 * Optimize bundle size by code splitting
 */
export function dynamicImport<T>(
  importFunction: () => Promise<T>
): Promise<T> {
  return importFunction().catch(error => {
    console.error('Dynamic import failed:', error);
    throw error;
  });
}

/**
 * Memory usage monitoring
 */
export function getMemoryUsage(): MemoryInfo | null {
  if (typeof window === 'undefined' || !('performance' in window) || !(performance as any).memory) {
    return null;
  }
  
  return (performance as any).memory;
}

/**
 * Check if device has limited resources
 */
export function isLowEndDevice(): boolean {
  if (typeof navigator === 'undefined') return false;
  
  // Check for device memory
  if ('deviceMemory' in navigator) {
    return (navigator as any).deviceMemory <= 4; // 4GB or less
  }
  
  // Check for hardware concurrency
  if ('hardwareConcurrency' in navigator) {
    return navigator.hardwareConcurrency <= 2; // 2 cores or less
  }
  
  // Check for connection type
  if ('connection' in navigator) {
    const connection = (navigator as any).connection;
    return connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g';
  }
  
  return false;
}

/**
 * Optimize animations based on device capabilities
 */
export function getOptimalAnimationSettings(): {
  duration: number;
  easing: string;
  reducedMotion: boolean;
} {
  const reducedMotion = prefersReducedMotion();
  const lowEndDevice = isLowEndDevice();
  
  return {
    duration: reducedMotion ? 0 : lowEndDevice ? 150 : 300,
    easing: lowEndDevice ? 'linear' : 'ease-out',
    reducedMotion
  };
}

/**
 * Batch DOM updates for better performance
 */
export function batchDOMUpdates(updates: (() => void)[]): void {
  if (typeof window === 'undefined') return;
  
  requestAnimationFrameOptimized(() => {
    updates.forEach(update => update());
  });
}

/**
 * Virtual scrolling helper for large lists
 */
export interface VirtualScrollOptions {
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}

export function calculateVirtualScrollItems(
  scrollTop: number,
  totalItems: number,
  options: VirtualScrollOptions
): { startIndex: number; endIndex: number; offsetY: number } {
  const { itemHeight, containerHeight, overscan = 5 } = options;
  
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const visibleItems = Math.ceil(containerHeight / itemHeight);
  const endIndex = Math.min(totalItems - 1, startIndex + visibleItems + overscan * 2);
  const offsetY = startIndex * itemHeight;
  
  return { startIndex, endIndex, offsetY };
}

/**
 * Optimize event listeners
 */
export function addOptimizedEventListener(
  element: Element | Window,
  event: string,
  handler: EventListener,
  options?: AddEventListenerOptions
): () => void {
  const optimizedOptions = {
    passive: true,
    ...options
  };
  
  element.addEventListener(event, handler, optimizedOptions);
  
  return () => {
    element.removeEventListener(event, handler, optimizedOptions);
  };
}

/**
 * Web Worker helper for offloading heavy computations
 */
export function createWebWorker(workerFunction: Function): Worker | null {
  if (typeof Worker === 'undefined') return null;
  
  const blob = new Blob([`(${workerFunction.toString()})()`], {
    type: 'application/javascript'
  });
  
  return new Worker(URL.createObjectURL(blob));
}

/**
 * Service Worker registration helper
 */
export async function registerServiceWorker(scriptURL: string): Promise<ServiceWorkerRegistration | null> {
  if (typeof navigator === 'undefined' || !('serviceWorker' in navigator)) {
    return null;
  }
  
  try {
    const registration = await navigator.serviceWorker.register(scriptURL);
    console.log('Service Worker registered successfully:', registration);
    return registration;
  } catch (error) {
    console.error('Service Worker registration failed:', error);
    return null;
  }
}
