'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Settings, 
  Maximize, 
  RotateCcw,
  BookOpen,
  Clock,
  User,
  Star,
  Bookmark,
  Share2,
  ThumbsUp,
  MessageCircle,
  ChevronLeft,
  ChevronRight,
  Download,
  ExternalLink,
  Eye,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';

import { useContentSystem } from './ComprehensiveContentSystem';

interface InteractiveContentViewerProps {
  contentId: string;
  className?: string;
  autoPlay?: boolean;
  showControls?: boolean;
  showMetadata?: boolean;
  showEngagement?: boolean;
  enableComments?: boolean;
  enableNotes?: boolean;
}

export function InteractiveContentViewer({
  contentId,
  className,
  autoPlay = false,
  showControls = true,
  showMetadata = true,
  showEngagement = true,
  enableComments = true,
  enableNotes = true
}: InteractiveContentViewerProps) {
  const { 
    getContentById, 
    trackContentInteraction, 
    bookmarkContent, 
    rateContent, 
    shareContent 
  } = useContentSystem();

  const content = getContentById(contentId);
  const [isPlaying, setIsPlaying] = useState(autoPlay);
  const [isMuted, setIsMuted] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [userRating, setUserRating] = useState(0);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showNotes, setShowNotes] = useState(false);
  const [notes, setNotes] = useState('');
  const [readingProgress, setReadingProgress] = useState(0);

  const videoRef = useRef<HTMLVideoElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!content) return;

    // Track content view
    trackContentInteraction(contentId, 'view');

    // Check if bookmarked
    const bookmarks = JSON.parse(localStorage.getItem('bookmarked_content') || '[]');
    setIsBookmarked(bookmarks.includes(contentId));

    // Load user notes
    const savedNotes = localStorage.getItem(`notes_${contentId}`);
    if (savedNotes) setNotes(savedNotes);

    // Load user rating
    const savedRating = localStorage.getItem(`rating_${contentId}`);
    if (savedRating) setUserRating(parseInt(savedRating));
  }, [content, contentId, trackContentInteraction]);

  // Track reading progress for articles
  useEffect(() => {
    if (content?.type !== 'article' || !contentRef.current) return;

    const handleScroll = () => {
      const element = contentRef.current!;
      const scrollTop = element.scrollTop;
      const scrollHeight = element.scrollHeight - element.clientHeight;
      const progress = Math.min((scrollTop / scrollHeight) * 100, 100);
      setReadingProgress(progress);

      // Track reading milestones
      if (progress >= 25 && progress < 50) {
        trackContentInteraction(contentId, 'reading_milestone', { milestone: 25 });
      } else if (progress >= 50 && progress < 75) {
        trackContentInteraction(contentId, 'reading_milestone', { milestone: 50 });
      } else if (progress >= 75 && progress < 100) {
        trackContentInteraction(contentId, 'reading_milestone', { milestone: 75 });
      } else if (progress >= 100) {
        trackContentInteraction(contentId, 'complete');
      }
    };

    contentRef.current.addEventListener('scroll', handleScroll);
    return () => contentRef.current?.removeEventListener('scroll', handleScroll);
  }, [content, contentId, trackContentInteraction]);

  const handlePlayPause = () => {
    if (content?.type === 'video' && videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
      trackContentInteraction(contentId, isPlaying ? 'pause' : 'play');
    }
  };

  const handleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
      trackContentInteraction(contentId, isMuted ? 'unmute' : 'mute');
    }
  };

  const handleBookmark = () => {
    bookmarkContent(contentId);
    setIsBookmarked(!isBookmarked);
  };

  const handleRating = (rating: number) => {
    setUserRating(rating);
    rateContent(contentId, rating);
    localStorage.setItem(`rating_${contentId}`, rating.toString());
  };

  const handleShare = (platform: string) => {
    shareContent(contentId, platform);
  };

  const handleNoteSave = () => {
    localStorage.setItem(`notes_${contentId}`, notes);
    trackContentInteraction(contentId, 'note_save', { noteLength: notes.length });
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  if (!content) {
    return (
      <div className={cn('flex items-center justify-center h-64', className)}>
        <div className="text-center">
          <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
            Content Not Found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            The requested content could not be found.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden', className)}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-2 mb-2">
              <div className={cn(
                'px-2 py-1 rounded text-xs font-medium',
                content.type === 'video' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                content.type === 'article' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                content.type === 'tutorial' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
              )}>
                {content.type.toUpperCase()}
              </div>
              <div className={cn(
                'px-2 py-1 rounded text-xs font-medium',
                content.metadata.difficulty === 'beginner' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                content.metadata.difficulty === 'intermediate' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
              )}>
                {content.metadata.difficulty}
              </div>
            </div>
            
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {content.title}
            </h1>
            
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {content.description}
            </p>

            {showMetadata && (
              <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center space-x-1">
                  <User className="w-4 h-4" />
                  <span>{content.author.name}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Clock className="w-4 h-4" />
                  <span>{content.metadata.duration} min</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Eye className="w-4 h-4" />
                  <span>{content.engagement.views.toLocaleString()} views</span>
                </div>
                <div className="flex items-center space-x-1">
                  <Star className="w-4 h-4 fill-current text-yellow-400" />
                  <span>{content.engagement.rating.toFixed(1)}</span>
                </div>
              </div>
            )}
          </div>

          {showEngagement && (
            <div className="flex items-center space-x-2 ml-4">
              <button
                onClick={handleBookmark}
                className={cn(
                  'p-2 rounded-lg transition-colors',
                  isBookmarked 
                    ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700'
                )}
                aria-label={isBookmarked ? 'Remove bookmark' : 'Add bookmark'}
              >
                <Bookmark className={cn('w-5 h-5', isBookmarked && 'fill-current')} />
              </button>

              <div className="relative">
                <button
                  onClick={() => setShowSettings(!showSettings)}
                  className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700 transition-colors"
                  aria-label="Share content"
                >
                  <Share2 className="w-5 h-5" />
                </button>

                <AnimatePresence>
                  {showSettings && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.95, y: -10 }}
                      animate={{ opacity: 1, scale: 1, y: 0 }}
                      exit={{ opacity: 0, scale: 0.95, y: -10 }}
                      className="absolute right-0 top-full mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 z-10"
                    >
                      <div className="p-2">
                        <button
                          onClick={() => handleShare('twitter')}
                          className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                        >
                          Share on Twitter
                        </button>
                        <button
                          onClick={() => handleShare('linkedin')}
                          className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                        >
                          Share on LinkedIn
                        </button>
                        <button
                          onClick={() => handleShare('copy')}
                          className="w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
                        >
                          Copy Link
                        </button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </div>
          )}
        </div>

        {/* Reading Progress Bar */}
        {content.type === 'article' && (
          <div className="mt-4">
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
              <span>Reading Progress</span>
              <span>{Math.round(readingProgress)}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
              <motion.div
                className="bg-blue-500 h-1 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${readingProgress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Content Area */}
      <div className="relative">
        {content.type === 'video' && (
          <div className="relative bg-black">
            <video
              ref={videoRef}
              className="w-full aspect-video"
              poster="/video-placeholder.jpg"
              onTimeUpdate={(e) => {
                const video = e.target as HTMLVideoElement;
                setCurrentTime(video.currentTime);
                setProgress((video.currentTime / video.duration) * 100);
              }}
              onLoadedMetadata={(e) => {
                const video = e.target as HTMLVideoElement;
                setDuration(video.duration);
              }}
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
            >
              <source src="/sample-video.mp4" type="video/mp4" />
              Your browser does not support the video tag.
            </video>

            {/* Video Controls */}
            {showControls && (
              <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={handlePlayPause}
                    className="text-white hover:text-gray-300 transition-colors"
                    aria-label={isPlaying ? 'Pause' : 'Play'}
                  >
                    {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                  </button>

                  <button
                    onClick={handleMute}
                    className="text-white hover:text-gray-300 transition-colors"
                    aria-label={isMuted ? 'Unmute' : 'Mute'}
                  >
                    {isMuted ? <VolumeX className="w-5 h-5" /> : <Volume2 className="w-5 h-5" />}
                  </button>

                  <div className="flex-1 flex items-center space-x-2">
                    <span className="text-white text-sm">{formatTime(currentTime)}</span>
                    <div className="flex-1 bg-white/20 rounded-full h-1">
                      <div
                        className="bg-white rounded-full h-1 transition-all duration-300"
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                    <span className="text-white text-sm">{formatTime(duration)}</span>
                  </div>

                  <button
                    onClick={() => setIsFullscreen(!isFullscreen)}
                    className="text-white hover:text-gray-300 transition-colors"
                    aria-label="Toggle fullscreen"
                  >
                    <Maximize className="w-5 h-5" />
                  </button>
                </div>
              </div>
            )}
          </div>
        )}

        {(content.type === 'article' || content.type === 'tutorial' || content.type === 'documentation') && (
          <div
            ref={contentRef}
            className="p-6 prose prose-lg dark:prose-invert max-w-none overflow-y-auto max-h-96"
            dangerouslySetInnerHTML={{ __html: content.content }}
          />
        )}
      </div>

      {/* Rating Section */}
      {showEngagement && (
        <div className="p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                Rate this content
              </h3>
              <div className="flex items-center space-x-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <button
                    key={star}
                    onClick={() => handleRating(star)}
                    className={cn(
                      'w-6 h-6 transition-colors',
                      star <= userRating 
                        ? 'text-yellow-400 fill-current' 
                        : 'text-gray-300 hover:text-yellow-400'
                    )}
                    aria-label={`Rate ${star} stars`}
                  >
                    <Star className="w-full h-full" />
                  </button>
                ))}
                {userRating > 0 && (
                  <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">
                    You rated this {userRating} star{userRating !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
            </div>

            {enableNotes && (
              <button
                onClick={() => setShowNotes(!showNotes)}
                className={cn(
                  'px-4 py-2 rounded-lg transition-colors',
                  showNotes
                    ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-gray-700'
                )}
              >
                <BookOpen className="w-4 h-4 mr-2 inline" />
                {showNotes ? 'Hide Notes' : 'Take Notes'}
              </button>
            )}
          </div>

          {/* Notes Section */}
          <AnimatePresence>
            {showNotes && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="mt-4 overflow-hidden"
              >
                <textarea
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  placeholder="Take notes about this content..."
                  className="w-full h-32 p-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white"
                />
                <div className="flex justify-end mt-2">
                  <button
                    onClick={handleNoteSave}
                    className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    Save Notes
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}

      {/* Tags */}
      <div className="px-6 pb-6">
        <div className="flex flex-wrap gap-2">
          {content.metadata.tags.map((tag) => (
            <span
              key={tag}
              className="px-2 py-1 bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 text-xs rounded-full"
            >
              #{tag}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}

export default InteractiveContentViewer;
