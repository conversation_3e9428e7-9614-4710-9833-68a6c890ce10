'use client';

import React, { useEffect, useCallback, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { BarChart3, Users, TrendingUp, <PERSON>, MousePointer, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AnalyticsConfig {
  googleAnalyticsId?: string;
  hotjarId?: string;
  enableHeatmaps: boolean;
  enableSessionRecordings: boolean;
  enableUserConsent: boolean;
  privacyCompliant: boolean;
  trackingLevel: 'minimal' | 'standard' | 'enhanced';
}

interface UserConsentState {
  analytics: boolean;
  heatmaps: boolean;
  sessionRecordings: boolean;
  marketing: boolean;
  hasConsented: boolean;
}

interface AnalyticsEvent {
  event: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  customParameters?: Record<string, any>;
}

interface UserJourneyStep {
  step: string;
  timestamp: number;
  page: string;
  action: string;
  metadata?: Record<string, any>;
}

// Google Analytics 4 Integration
export function useGoogleAnalytics(config: AnalyticsConfig) {
  const [isInitialized, setIsInitialized] = useState(false);
  const { googleAnalyticsId, enableUserConsent } = config;

  useEffect(() => {
    if (!googleAnalyticsId) return;

    // Load Google Analytics script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${googleAnalyticsId}`;
    document.head.appendChild(script);

    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    function gtag(...args: any[]) {
      window.dataLayer.push(args);
    }
    window.gtag = gtag;

    gtag('js', new Date());

    // Configure with privacy settings
    const gtagConfig: any = {
      anonymize_ip: true,
      allow_google_signals: !enableUserConsent,
      allow_ad_personalization_signals: false
    };

    if (enableUserConsent) {
      // Wait for consent before initializing
      gtag('consent', 'default', {
        analytics_storage: 'denied',
        ad_storage: 'denied',
        functionality_storage: 'denied',
        personalization_storage: 'denied',
        security_storage: 'granted',
        wait_for_update: 500
      });
    }

    gtag('config', googleAnalyticsId, gtagConfig);
    setIsInitialized(true);

    return () => {
      // Cleanup script
      const scripts = document.querySelectorAll(`script[src*="${googleAnalyticsId}"]`);
      scripts.forEach(script => script.remove());
    };
  }, [googleAnalyticsId, enableUserConsent]);

  const trackEvent = useCallback((eventData: AnalyticsEvent) => {
    if (!isInitialized || !window.gtag) return;

    const { event, category, action, label, value, customParameters } = eventData;

    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
      custom_map: customParameters,
      ...customParameters
    });
  }, [isInitialized]);

  const trackPageView = useCallback((page: string, title?: string) => {
    if (!isInitialized || !window.gtag) return;

    window.gtag('config', googleAnalyticsId, {
      page_path: page,
      page_title: title
    });
  }, [isInitialized, googleAnalyticsId]);

  const trackConversion = useCallback((conversionId: string, value?: number, currency = 'USD') => {
    if (!isInitialized || !window.gtag) return;

    window.gtag('event', 'conversion', {
      send_to: conversionId,
      value: value,
      currency: currency
    });
  }, [isInitialized]);

  const setUserProperties = useCallback((properties: Record<string, any>) => {
    if (!isInitialized || !window.gtag) return;

    window.gtag('config', googleAnalyticsId, {
      custom_map: properties
    });
  }, [isInitialized, googleAnalyticsId]);

  return {
    isInitialized,
    trackEvent,
    trackPageView,
    trackConversion,
    setUserProperties
  };
}

// Hotjar Integration
export function useHotjar(config: AnalyticsConfig) {
  const [isInitialized, setIsInitialized] = useState(false);
  const { hotjarId, enableHeatmaps, enableSessionRecordings } = config;

  useEffect(() => {
    if (!hotjarId || (!enableHeatmaps && !enableSessionRecordings)) return;

    // Load Hotjar script
    (function(h: any, o: any, t: any, j: any, a?: any, r?: any) {
      h.hj = h.hj || function(...args: any[]) { (h.hj.q = h.hj.q || []).push(args); };
      h._hjSettings = { hjid: hotjarId, hjsv: 6 };
      a = o.getElementsByTagName('head')[0];
      r = o.createElement('script');
      r.async = 1;
      r.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv;
      a.appendChild(r);
    })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=');

    setIsInitialized(true);

    return () => {
      // Cleanup Hotjar
      if (window.hj) {
        window.hj('stateChange', '/');
      }
    };
  }, [hotjarId, enableHeatmaps, enableSessionRecordings]);

  const identifyUser = useCallback((userId: string, attributes?: Record<string, any>) => {
    if (!isInitialized || !window.hj) return;

    window.hj('identify', userId, attributes);
  }, [isInitialized]);

  const trackEvent = useCallback((eventName: string, attributes?: Record<string, any>) => {
    if (!isInitialized || !window.hj) return;

    window.hj('event', eventName, attributes);
  }, [isInitialized]);

  const startRecording = useCallback(() => {
    if (!isInitialized || !window.hj || !enableSessionRecordings) return;

    window.hj('trigger', 'start_recording');
  }, [isInitialized, enableSessionRecordings]);

  const stopRecording = useCallback(() => {
    if (!isInitialized || !window.hj) return;

    window.hj('trigger', 'stop_recording');
  }, [isInitialized]);

  return {
    isInitialized,
    identifyUser,
    trackEvent,
    startRecording,
    stopRecording
  };
}

// User Consent Management
export function useUserConsent() {
  const [consent, setConsent] = useState<UserConsentState>({
    analytics: false,
    heatmaps: false,
    sessionRecordings: false,
    marketing: false,
    hasConsented: false
  });

  const [showConsentBanner, setShowConsentBanner] = useState(false);

  useEffect(() => {
    // Check for existing consent
    const savedConsent = localStorage.getItem('user_consent');
    if (savedConsent) {
      try {
        const parsed = JSON.parse(savedConsent);
        setConsent(parsed);
      } catch (error) {
        console.error('Failed to parse saved consent:', error);
        setShowConsentBanner(true);
      }
    } else {
      setShowConsentBanner(true);
    }
  }, []);

  const updateConsent = useCallback((newConsent: Partial<UserConsentState>) => {
    const updatedConsent = { ...consent, ...newConsent, hasConsented: true };
    setConsent(updatedConsent);
    localStorage.setItem('user_consent', JSON.stringify(updatedConsent));
    setShowConsentBanner(false);

    // Update Google Analytics consent
    if (window.gtag) {
      window.gtag('consent', 'update', {
        analytics_storage: updatedConsent.analytics ? 'granted' : 'denied',
        functionality_storage: updatedConsent.heatmaps ? 'granted' : 'denied',
        personalization_storage: updatedConsent.marketing ? 'granted' : 'denied'
      });
    }

    // Update Hotjar consent
    if (window.hj) {
      if (!updatedConsent.heatmaps && !updatedConsent.sessionRecordings) {
        window.hj('consent', false);
      } else {
        window.hj('consent', true);
      }
    }
  }, [consent]);

  const acceptAll = useCallback(() => {
    updateConsent({
      analytics: true,
      heatmaps: true,
      sessionRecordings: true,
      marketing: true
    });
  }, [updateConsent]);

  const rejectAll = useCallback(() => {
    updateConsent({
      analytics: false,
      heatmaps: false,
      sessionRecordings: false,
      marketing: false
    });
  }, [updateConsent]);

  return {
    consent,
    showConsentBanner,
    updateConsent,
    acceptAll,
    rejectAll
  };
}

// User Journey Tracking
export function useUserJourney() {
  const [journey, setJourney] = useState<UserJourneyStep[]>([]);
  const sessionId = useRef(Date.now().toString());

  const trackStep = useCallback((step: string, action: string, metadata?: Record<string, any>) => {
    const journeyStep: UserJourneyStep = {
      step,
      timestamp: Date.now(),
      page: window.location.pathname,
      action,
      metadata
    };

    setJourney(prev => [...prev, journeyStep]);

    // Store in sessionStorage for persistence
    const sessionJourney = JSON.parse(sessionStorage.getItem('user_journey') || '[]');
    sessionJourney.push(journeyStep);
    sessionStorage.setItem('user_journey', JSON.stringify(sessionJourney));
  }, []);

  const getJourneyFunnel = useCallback(() => {
    const funnel = {
      landing: journey.filter(step => step.action === 'page_view' && step.step === 'landing').length,
      trial_signup: journey.filter(step => step.action === 'trial_signup').length,
      first_lesson: journey.filter(step => step.action === 'lesson_start').length,
      paid_conversion: journey.filter(step => step.action === 'paid_conversion').length
    };

    return funnel;
  }, [journey]);

  const getDropOffPoints = useCallback(() => {
    const dropOffs: Record<string, number> = {};
    
    journey.forEach((step, index) => {
      if (index === journey.length - 1) {
        // Last step in journey - potential drop-off
        dropOffs[step.step] = (dropOffs[step.step] || 0) + 1;
      }
    });

    return dropOffs;
  }, [journey]);

  return {
    journey,
    sessionId: sessionId.current,
    trackStep,
    getJourneyFunnel,
    getDropOffPoints
  };
}

// Enhanced Event Tracking Hook
export function useEnhancedTracking(config: AnalyticsConfig) {
  const ga = useGoogleAnalytics(config);
  const hotjar = useHotjar(config);
  const { consent } = useUserConsent();
  const { trackStep } = useUserJourney();

  const trackEvent = useCallback((eventData: AnalyticsEvent) => {
    // Track with Google Analytics if consented
    if (consent.analytics && ga.isInitialized) {
      ga.trackEvent(eventData);
    }

    // Track with Hotjar if consented
    if (consent.heatmaps && hotjar.isInitialized) {
      hotjar.trackEvent(eventData.action, {
        category: eventData.category,
        label: eventData.label,
        ...eventData.customParameters
      });
    }

    // Track in user journey
    trackStep(eventData.category, eventData.action, eventData.customParameters);

    // Log for debugging in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Analytics Event:', eventData);
    }
  }, [consent, ga, hotjar, trackStep]);

  const trackConversionTouchpoint = useCallback((
    touchpoint: string, 
    system: string, 
    value?: number
  ) => {
    trackEvent({
      event: 'conversion_touchpoint',
      category: 'conversion',
      action: touchpoint,
      label: system,
      value,
      customParameters: {
        conversion_system: system,
        touchpoint_type: touchpoint,
        timestamp: Date.now()
      }
    });
  }, [trackEvent]);

  const trackUserEngagement = useCallback((
    engagementType: string,
    duration?: number,
    metadata?: Record<string, any>
  ) => {
    trackEvent({
      event: 'user_engagement',
      category: 'engagement',
      action: engagementType,
      value: duration,
      customParameters: {
        engagement_duration: duration,
        ...metadata
      }
    });
  }, [trackEvent]);

  const trackLearningProgress = useCallback((
    lessonId: string,
    progress: number,
    timeSpent: number
  ) => {
    trackEvent({
      event: 'learning_progress',
      category: 'education',
      action: 'lesson_progress',
      label: lessonId,
      value: progress,
      customParameters: {
        lesson_id: lessonId,
        progress_percentage: progress,
        time_spent_seconds: timeSpent,
        completion_rate: progress / 100
      }
    });
  }, [trackEvent]);

  return {
    trackEvent,
    trackConversionTouchpoint,
    trackUserEngagement,
    trackLearningProgress,
    isAnalyticsReady: ga.isInitialized && consent.analytics,
    isHeatmapReady: hotjar.isInitialized && consent.heatmaps
  };
}

// Consent Banner Component
export function ConsentBanner() {
  const { showConsentBanner, acceptAll, rejectAll, updateConsent } = useUserConsent();
  const [showDetails, setShowDetails] = useState(false);

  if (!showConsentBanner) return null;

  return (
    <motion.div
      className="fixed bottom-0 left-0 right-0 z-50 bg-gray-900 border-t border-gray-700 p-4 shadow-lg"
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="max-w-6xl mx-auto">
        <div className="flex flex-col lg:flex-row items-start lg:items-center justify-between space-y-4 lg:space-y-0">
          <div className="flex-1">
            <h3 className="text-white font-medium mb-2">Cookie Preferences</h3>
            <p className="text-gray-300 text-sm">
              We use cookies and similar technologies to improve your experience, analyze usage, 
              and provide personalized content. You can manage your preferences below.
            </p>
          </div>
          
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <button
              onClick={() => setShowDetails(!showDetails)}
              className="text-blue-400 hover:text-blue-300 text-sm underline"
            >
              {showDetails ? 'Hide' : 'Show'} Details
            </button>
            <button
              onClick={rejectAll}
              className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg text-sm transition-colors"
            >
              Reject All
            </button>
            <button
              onClick={acceptAll}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
            >
              Accept All
            </button>
          </div>
        </div>

        {showDetails && (
          <motion.div
            className="mt-4 pt-4 border-t border-gray-700"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            transition={{ duration: 0.3 }}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {[
                { key: 'analytics', label: 'Analytics', description: 'Help us understand how you use our platform' },
                { key: 'heatmaps', label: 'Heatmaps', description: 'Visual analysis of user interactions' },
                { key: 'sessionRecordings', label: 'Session Recordings', description: 'Record user sessions for UX improvements' },
                { key: 'marketing', label: 'Marketing', description: 'Personalized content and advertisements' }
              ].map(({ key, label, description }) => (
                <div key={key} className="bg-gray-800 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-white text-sm font-medium">{label}</span>
                    <input
                      type="checkbox"
                      className="rounded border-gray-600 bg-gray-700 text-blue-600 focus:ring-blue-500"
                      onChange={(e) => updateConsent({ [key]: e.target.checked })}
                    />
                  </div>
                  <p className="text-gray-400 text-xs">{description}</p>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
}
