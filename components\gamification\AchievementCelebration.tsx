'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Trophy, 
  Star, 
  Sparkles, 
  Gift, 
  Crown,
  Award,
  Zap,
  X,
  Share2,
  Download,
  ExternalLink
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { GlassCard } from '@/components/ui/Glassmorphism';
import { EnhancedButton } from '@/components/ui/EnhancedButton';
import { Achievement } from '@/lib/achievements/types';

interface AchievementCelebrationProps {
  achievement: Achievement;
  onClose: () => void;
  onShare?: (platform: string) => void;
  autoClose?: boolean;
  autoCloseDelay?: number;
  showConfetti?: boolean;
}

export function AchievementCelebration({
  achievement,
  onClose,
  onShare,
  autoClose = false,
  autoCloseDelay = 8000,
  showConfetti = true
}: AchievementCelebrationProps) {
  const [celebrationPhase, setCelebrationPhase] = useState<'entrance' | 'celebration' | 'rewards'>('entrance');

  useEffect(() => {
    // Celebration sequence
    const timer1 = setTimeout(() => setCelebrationPhase('celebration'), 500);
    const timer2 = setTimeout(() => setCelebrationPhase('rewards'), 2000);

    // Auto close
    let autoCloseTimer: NodeJS.Timeout;
    if (autoClose) {
      autoCloseTimer = setTimeout(onClose, autoCloseDelay);
    }

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      if (autoCloseTimer) clearTimeout(autoCloseTimer);
    };
  }, [autoClose, autoCloseDelay, onClose]);

  const getRarityColor = () => {
    switch (achievement.rarity) {
      case 'common':
        return 'from-gray-400 to-gray-600';
      case 'uncommon':
        return 'from-green-400 to-green-600';
      case 'rare':
        return 'from-blue-400 to-blue-600';
      case 'epic':
        return 'from-purple-400 to-purple-600';
      case 'legendary':
        return 'from-yellow-400 to-orange-500';
      default:
        return 'from-gray-400 to-gray-600';
    }
  };

  const getRarityGlow = () => {
    switch (achievement.rarity) {
      case 'common':
        return 'shadow-gray-500/50';
      case 'uncommon':
        return 'shadow-green-500/50';
      case 'rare':
        return 'shadow-blue-500/50';
      case 'epic':
        return 'shadow-purple-500/50';
      case 'legendary':
        return 'shadow-yellow-500/50';
      default:
        return 'shadow-gray-500/50';
    }
  };

  const handleShare = (platform: string) => {
    onShare?.(platform);
  };

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      >
        {/* Confetti Background */}
        {showConfetti && (
          <div className="absolute inset-0 pointer-events-none overflow-hidden">
            {[...Array(60)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ 
                  opacity: 0,
                  y: -100,
                  x: Math.random() * window.innerWidth,
                  rotate: 0,
                  scale: 0
                }}
                animate={{ 
                  opacity: [0, 1, 1, 0],
                  y: window.innerHeight + 100,
                  rotate: Math.random() * 720,
                  scale: [0, 1, 1, 0]
                }}
                transition={{
                  duration: 4 + Math.random() * 2,
                  delay: Math.random() * 3,
                  ease: 'easeOut'
                }}
                className={cn(
                  'absolute w-3 h-3 rounded-full',
                  [
                    'bg-yellow-400',
                    'bg-blue-400', 
                    'bg-green-400',
                    'bg-purple-400',
                    'bg-red-400',
                    'bg-orange-400',
                    'bg-pink-400'
                  ][Math.floor(Math.random() * 7)]
                )}
              />
            ))}
          </div>
        )}

        {/* Main Achievement Modal */}
        <motion.div
          initial={{ scale: 0.3, opacity: 0, y: 100 }}
          animate={{ scale: 1, opacity: 1, y: 0 }}
          exit={{ scale: 0.3, opacity: 0, y: 100 }}
          transition={{ 
            type: 'spring', 
            stiffness: 300, 
            damping: 25,
            delay: 0.2
          }}
          className="relative max-w-lg w-full max-h-[90vh] overflow-y-auto"
        >
          <GlassCard className="p-8 border-2 border-yellow-500/30 relative overflow-hidden">
            {/* Close Button */}
            <EnhancedButton
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="absolute top-4 right-4 w-8 h-8 p-0 text-gray-400 hover:text-white z-10"
              aria-label="Close celebration"
            >
              <X className="w-4 h-4" />
            </EnhancedButton>

            {/* Achievement Header */}
            <div className="text-center space-y-6">
              {/* Achievement Unlocked Text */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
              >
                <h1 className="text-3xl font-bold text-white mb-2">Achievement Unlocked!</h1>
                <div className="flex items-center justify-center space-x-2">
                  <Sparkles className="w-5 h-5 text-yellow-400" />
                  <span className={cn(
                    'text-sm font-medium uppercase tracking-wider',
                    achievement.rarity === 'legendary' ? 'text-yellow-400' :
                    achievement.rarity === 'epic' ? 'text-purple-400' :
                    achievement.rarity === 'rare' ? 'text-blue-400' :
                    achievement.rarity === 'uncommon' ? 'text-green-400' :
                    'text-gray-400'
                  )}>
                    {achievement.rarity}
                  </span>
                  <Sparkles className="w-5 h-5 text-yellow-400" />
                </div>
              </motion.div>

              {/* Achievement Icon */}
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ 
                  type: 'spring', 
                  stiffness: 200, 
                  damping: 15,
                  delay: 0.7
                }}
                className="relative"
              >
                <div className={cn(
                  'w-32 h-32 mx-auto rounded-full flex items-center justify-center shadow-2xl',
                  'bg-gradient-to-br',
                  getRarityColor(),
                  getRarityGlow()
                )}>
                  <Trophy className="w-16 h-16 text-white" />
                </div>
                
                {/* Rotating glow effect */}
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: 'linear' }}
                  className={cn(
                    'absolute inset-0 rounded-full opacity-30',
                    'bg-gradient-to-r',
                    getRarityColor()
                  )}
                  style={{
                    background: `conic-gradient(from 0deg, transparent, ${achievement.rarity === 'legendary' ? '#fbbf24' : '#6366f1'}, transparent)`
                  }}
                />
              </motion.div>

              {/* Achievement Details */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1 }}
                className="space-y-3"
              >
                <h2 className="text-2xl font-bold text-white">{achievement.title}</h2>
                <p className="text-gray-300 text-lg">{achievement.description}</p>
                {achievement.longDescription && (
                  <p className="text-gray-400 text-sm">{achievement.longDescription}</p>
                )}
              </motion.div>

              {/* XP Reward */}
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.2 }}
                className="flex items-center justify-center space-x-3 p-4 bg-yellow-500/20 border border-yellow-500/30 rounded-xl"
              >
                <Zap className="w-6 h-6 text-yellow-400" />
                <div>
                  <div className="text-2xl font-bold text-yellow-300">+{achievement.rewards.xp} XP</div>
                  <div className="text-sm text-yellow-200/80">Experience Points</div>
                </div>
              </motion.div>

              {/* Additional Rewards */}
              {(achievement.rewards.badge || achievement.rewards.title || achievement.rewards.unlocks) && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.4 }}
                  className="space-y-3"
                >
                  <h3 className="text-lg font-semibold text-white flex items-center justify-center">
                    <Gift className="w-5 h-5 mr-2 text-green-400" />
                    Additional Rewards
                  </h3>
                  
                  <div className="grid gap-3">
                    {achievement.rewards.badge && (
                      <div className="flex items-center justify-center space-x-2 p-3 bg-purple-500/20 border border-purple-500/30 rounded-lg">
                        <Award className="w-5 h-5 text-purple-400" />
                        <span className="text-purple-300 font-medium">Badge: {achievement.rewards.badge}</span>
                      </div>
                    )}
                    
                    {achievement.rewards.title && (
                      <div className="flex items-center justify-center space-x-2 p-3 bg-blue-500/20 border border-blue-500/30 rounded-lg">
                        <Crown className="w-5 h-5 text-blue-400" />
                        <span className="text-blue-300 font-medium">Title: {achievement.rewards.title}</span>
                      </div>
                    )}
                    
                    {achievement.rewards.unlocks && achievement.rewards.unlocks.length > 0 && (
                      <div className="p-3 bg-green-500/20 border border-green-500/30 rounded-lg">
                        <div className="flex items-center justify-center space-x-2 mb-2">
                          <Sparkles className="w-5 h-5 text-green-400" />
                          <span className="text-green-300 font-medium">Content Unlocked</span>
                        </div>
                        <div className="text-sm text-green-200">
                          {achievement.rewards.unlocks.join(', ')}
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}

              {/* Share Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.6 }}
                className="space-y-4"
              >
                <h3 className="text-lg font-semibold text-white">Share Your Achievement</h3>
                <div className="flex justify-center space-x-3">
                  <EnhancedButton
                    onClick={() => handleShare('twitter')}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2"
                    size="sm"
                  >
                    <Share2 className="w-4 h-4 mr-2" />
                    Twitter
                  </EnhancedButton>
                  
                  <EnhancedButton
                    onClick={() => handleShare('linkedin')}
                    className="bg-blue-700 hover:bg-blue-800 text-white px-4 py-2"
                    size="sm"
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    LinkedIn
                  </EnhancedButton>
                  
                  <EnhancedButton
                    onClick={() => handleShare('download')}
                    variant="outline"
                    size="sm"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Save Image
                  </EnhancedButton>
                </div>
              </motion.div>

              {/* Continue Button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.8 }}
              >
                <EnhancedButton
                  onClick={onClose}
                  className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white px-8 py-3 text-lg font-semibold"
                  touchTarget
                >
                  <Trophy className="w-5 h-5 mr-2" />
                  Continue Learning
                </EnhancedButton>
              </motion.div>
            </div>

            {/* Sparkle Effects */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {[...Array(25)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ 
                    opacity: 0,
                    scale: 0,
                    x: Math.random() * 100 + '%',
                    y: Math.random() * 100 + '%'
                  }}
                  animate={{ 
                    opacity: [0, 1, 0],
                    scale: [0, 1.5, 0],
                    rotate: 360
                  }}
                  transition={{
                    duration: 3,
                    delay: Math.random() * 4,
                    repeat: Infinity,
                    repeatDelay: Math.random() * 6
                  }}
                  className="absolute w-2 h-2 bg-yellow-400 rounded-full"
                />
              ))}
            </div>
          </GlassCard>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}

// Achievement Toast Notification Component
interface AchievementToastProps {
  achievement: Achievement;
  onClose: () => void;
  onClick?: () => void;
  duration?: number;
}

export function AchievementToast({
  achievement,
  onClose,
  onClick,
  duration = 5000
}: AchievementToastProps) {
  useEffect(() => {
    const timer = setTimeout(onClose, duration);
    return () => clearTimeout(timer);
  }, [duration, onClose]);

  return (
    <motion.div
      initial={{ opacity: 0, x: 300, scale: 0.8 }}
      animate={{ opacity: 1, x: 0, scale: 1 }}
      exit={{ opacity: 0, x: 300, scale: 0.8 }}
      transition={{ type: 'spring', stiffness: 300, damping: 25 }}
      className="fixed top-4 right-4 z-50 max-w-sm w-full cursor-pointer"
      onClick={onClick}
    >
      <GlassCard className="p-4 border-l-4 border-yellow-500 hover:border-yellow-400 transition-colors">
        <div className="flex items-start space-x-3">
          <div className="w-10 h-10 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
            <Trophy className="w-5 h-5 text-white" />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="font-semibold text-white text-sm">Achievement Unlocked!</h4>
              <span className={cn(
                'text-xs px-2 py-0.5 rounded-full font-medium',
                achievement.rarity === 'legendary' ? 'bg-yellow-500/20 text-yellow-400' :
                achievement.rarity === 'epic' ? 'bg-purple-500/20 text-purple-400' :
                achievement.rarity === 'rare' ? 'bg-blue-500/20 text-blue-400' :
                'bg-gray-500/20 text-gray-400'
              )}>
                {achievement.rarity}
              </span>
            </div>
            <p className="text-white font-medium text-sm">{achievement.title}</p>
            <p className="text-gray-400 text-xs mt-1">{achievement.description}</p>
            <div className="flex items-center space-x-2 mt-2">
              <Zap className="w-3 h-3 text-yellow-400" />
              <span className="text-yellow-400 text-xs font-medium">+{achievement.rewards.xp} XP</span>
            </div>
          </div>
          
          <EnhancedButton
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            variant="ghost"
            size="sm"
            className="w-6 h-6 p-0 text-gray-400 hover:text-white flex-shrink-0"
          >
            <X className="w-3 h-3" />
          </EnhancedButton>
        </div>
      </GlassCard>
    </motion.div>
  );
}
