'use client';

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import { 
  MessageCircle, 
  X, 
  Send, 
  Bot, 
  User, 
  Minimize2, 
  Maximize2,
  HelpCircle,
  BookOpen,
  Code,
  Lightbulb
} from 'lucide-react';

interface Message {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

interface AIAssistantWidgetProps {
  className?: string;
  position?: 'bottom-right' | 'bottom-left';
  enableContextualHelp?: boolean;
  currentPage?: string;
  userProgress?: {
    currentLesson?: string;
    completedLessons?: number;
    currentCourse?: string;
  };
}

const contextualSuggestions = {
  '/': [
    'How do I get started with Solidity?',
    'What makes this platform different?',
    'Can I try a lesson for free?',
    'How long does it take to learn Solidity?'
  ],
  '/learn': [
    'Explain this concept in simpler terms',
    'Show me a practical example',
    'What are the prerequisites for this lesson?',
    'How does this relate to real-world development?'
  ],
  '/code': [
    'Help me debug this code',
    'Explain this error message',
    'What are best practices for this pattern?',
    'How can I optimize this contract?'
  ],
  '/dashboard': [
    'How can I improve my learning progress?',
    'What should I focus on next?',
    'Explain my performance metrics',
    'How do I unlock more achievements?'
  ]
};

const quickActions = [
  {
    icon: <HelpCircle className="w-4 h-4" />,
    label: 'Get Help',
    message: 'I need help with something'
  },
  {
    icon: <BookOpen className="w-4 h-4" />,
    label: 'Explain Concept',
    message: 'Can you explain this concept to me?'
  },
  {
    icon: <Code className="w-4 h-4" />,
    label: 'Code Review',
    message: 'Can you review my code?'
  },
  {
    icon: <Lightbulb className="w-4 h-4" />,
    label: 'Learning Tips',
    message: 'Give me some learning tips'
  }
];

export function AIAssistantWidget({
  className,
  position = 'bottom-right',
  enableContextualHelp = true,
  currentPage = '/',
  userProgress
}: AIAssistantWidgetProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'assistant',
      content: "Hi! I'm your AI learning assistant. I'm here to help you with Solidity concepts, code reviews, and any questions you have. How can I assist you today?",
      timestamp: new Date(),
      suggestions: enableContextualHelp ? contextualSuggestions[currentPage as keyof typeof contextualSuggestions] || contextualSuggestions['/'] : undefined
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (isOpen && !isMinimized) {
      inputRef.current?.focus();
    }
  }, [isOpen, isMinimized]);

  const generateAIResponse = async (userMessage: string): Promise<string> => {
    // Simulate AI thinking time
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // Simple response generation based on keywords
    const message = userMessage.toLowerCase();
    
    if (message.includes('solidity') || message.includes('smart contract')) {
      return "Solidity is a programming language for writing smart contracts on Ethereum. It's statically typed and supports inheritance, libraries, and complex user-defined types. Would you like me to explain any specific Solidity concepts?";
    }
    
    if (message.includes('help') || message.includes('stuck')) {
      return "I'm here to help! Can you tell me more about what you're working on? Are you having trouble with a specific concept, debugging code, or understanding an error message?";
    }
    
    if (message.includes('error') || message.includes('debug')) {
      return "I'd be happy to help you debug! Please share your code and the error message you're seeing. Common issues include syntax errors, type mismatches, and gas optimization problems.";
    }
    
    if (message.includes('best practice') || message.includes('optimize')) {
      return "Great question about best practices! Some key areas to focus on: gas optimization, security patterns (like checks-effects-interactions), proper error handling, and code readability. What specific area would you like to explore?";
    }
    
    if (message.includes('learn') || message.includes('start')) {
      return "Excellent! I recommend starting with the fundamentals: variables, functions, and basic contract structure. Then move on to more advanced topics like inheritance, events, and security patterns. Are you completely new to programming or just new to Solidity?";
    }

    // Default response
    return "That's an interesting question! While I can help with Solidity concepts, code reviews, and learning guidance, I might need more context to give you the best answer. Could you provide more details about what you're trying to accomplish?";
  };

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: content.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);

    try {
      const response = await generateAIResponse(content);
      
      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response,
        timestamp: new Date(),
        suggestions: enableContextualHelp ? contextualSuggestions[currentPage as keyof typeof contextualSuggestions]?.slice(0, 2) : undefined
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: "I'm sorry, I'm having trouble responding right now. Please try again in a moment.",
        timestamp: new Date()
      };

      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleQuickAction = (message: string) => {
    handleSendMessage(message);
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  const positionClasses = {
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4'
  };

  return (
    <div
      className={cn(
        'fixed z-50',
        positionClasses[position],
        className
      )}
    >
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ 
              opacity: 1, 
              scale: 1, 
              y: 0,
              height: isMinimized ? 60 : 500
            }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ type: 'spring', stiffness: 300, damping: 30 }}
            className={cn(
              'bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700',
              'w-80 overflow-hidden',
              isMinimized ? 'h-15' : 'h-[500px]'
            )}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-blue-500 to-purple-600 text-white">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                  <Bot className="w-4 h-4" />
                </div>
                <div>
                  <h3 className="font-semibold text-sm">AI Assistant</h3>
                  <p className="text-xs opacity-90">Always here to help</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="p-1 hover:bg-white/20 rounded transition-colors"
                  aria-label={isMinimized ? 'Maximize chat' : 'Minimize chat'}
                >
                  {isMinimized ? <Maximize2 className="w-4 h-4" /> : <Minimize2 className="w-4 h-4" />}
                </button>
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-white/20 rounded transition-colors"
                  aria-label="Close chat"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            </div>

            {!isMinimized && (
              <>
                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4 h-80">
                  {messages.map((message) => (
                    <MessageBubble
                      key={message.id}
                      message={message}
                      onSuggestionClick={handleSuggestionClick}
                    />
                  ))}
                  
                  {isTyping && (
                    <div className="flex items-center space-x-2">
                      <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                        <Bot className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>

                {/* Quick Actions */}
                {messages.length === 1 && (
                  <div className="px-4 pb-2">
                    <div className="grid grid-cols-2 gap-2">
                      {quickActions.map((action) => (
                        <button
                          key={action.label}
                          onClick={() => handleQuickAction(action.message)}
                          className="flex items-center space-x-2 p-2 text-xs bg-gray-50 dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
                        >
                          {action.icon}
                          <span>{action.label}</span>
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Input */}
                <div className="p-4 border-t border-gray-200 dark:border-gray-700">
                  <div className="flex space-x-2">
                    <input
                      ref={inputRef}
                      type="text"
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          handleSendMessage(inputValue);
                        }
                      }}
                      placeholder="Ask me anything about Solidity..."
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-800 dark:text-white text-sm"
                      disabled={isTyping}
                    />
                    <button
                      onClick={() => handleSendMessage(inputValue)}
                      disabled={!inputValue.trim() || isTyping}
                      className="px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      aria-label="Send message"
                    >
                      <Send className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </>
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Chat Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'w-14 h-14 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full shadow-lg',
          'flex items-center justify-center',
          'hover:shadow-xl transition-all duration-300',
          'focus:outline-none focus:ring-4 focus:ring-blue-500/25',
          isOpen && 'scale-0'
        )}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        animate={{ scale: isOpen ? 0 : 1 }}
        aria-label="Open AI assistant chat"
      >
        <MessageCircle className="w-6 h-6" />
      </motion.button>
    </div>
  );
}

interface MessageBubbleProps {
  message: Message;
  onSuggestionClick: (suggestion: string) => void;
}

function MessageBubble({ message, onSuggestionClick }: MessageBubbleProps) {
  const isUser = message.type === 'user';

  return (
    <div className={cn('flex', isUser ? 'justify-end' : 'justify-start')}>
      <div className={cn('flex space-x-2 max-w-[80%]', isUser && 'flex-row-reverse space-x-reverse')}>
        {/* Avatar */}
        <div className={cn(
          'w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0',
          isUser 
            ? 'bg-blue-500 text-white' 
            : 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-400'
        )}>
          {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
        </div>

        {/* Message */}
        <div className="space-y-2">
          <div className={cn(
            'rounded-lg p-3 text-sm',
            isUser
              ? 'bg-blue-500 text-white'
              : 'bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-white'
          )}>
            {message.content}
          </div>

          {/* Suggestions */}
          {message.suggestions && message.suggestions.length > 0 && (
            <div className="space-y-1">
              {message.suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  onClick={() => onSuggestionClick(suggestion)}
                  className="block w-full text-left text-xs p-2 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded border border-blue-200 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors"
                >
                  {suggestion}
                </button>
              ))}
            </div>
          )}

          {/* Timestamp */}
          <div className={cn(
            'text-xs opacity-70',
            isUser ? 'text-right' : 'text-left'
          )}>
            {message.timestamp.toLocaleTimeString([], { 
              hour: '2-digit', 
              minute: '2-digit' 
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

export default AIAssistantWidget;
