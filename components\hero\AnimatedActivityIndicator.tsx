'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Users, Code, Zap, Globe, TrendingUp } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ActivityData {
  id: string;
  type: 'user_joined' | 'code_compiled' | 'contract_deployed' | 'lesson_completed';
  count: number;
  location?: string;
  timestamp: Date;
}

interface AnimatedActivityIndicatorProps {
  className?: string;
  showLocation?: boolean;
  updateInterval?: number;
  maxIndicators?: number;
}

export function AnimatedActivityIndicator({
  className,
  showLocation = true,
  updateInterval = 3000,
  maxIndicators = 5
}: AnimatedActivityIndicatorProps) {
  const [activities, setActivities] = useState<ActivityData[]>([]);
  const [onlineCount, setOnlineCount] = useState(127);

  // Generate random activity data
  const generateActivity = (): ActivityData => {
    const types: ActivityData['type'][] = ['user_joined', 'code_compiled', 'contract_deployed', 'lesson_completed'];
    const locations = ['San Francisco', 'New York', 'London', 'Berlin', 'Tokyo', 'Singapore', 'Toronto', 'Sydney'];
    
    const type = types[Math.floor(Math.random() * types.length)];
    const location = showLocation ? locations[Math.floor(Math.random() * locations.length)] : undefined;
    
    return {
      id: `activity-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      count: Math.floor(Math.random() * 5) + 1,
      location,
      timestamp: new Date()
    };
  };

  // Update activities periodically
  useEffect(() => {
    const interval = setInterval(() => {
      const newActivity = generateActivity();
      setActivities(prev => [newActivity, ...prev.slice(0, maxIndicators - 1)]);
      
      // Randomly update online count
      setOnlineCount(prev => {
        const change = Math.floor(Math.random() * 10) - 5; // -5 to +5
        return Math.max(50, prev + change);
      });
    }, updateInterval);

    return () => clearInterval(interval);
  }, [updateInterval, maxIndicators, showLocation]);

  // Initialize with some activities
  useEffect(() => {
    const initialActivities = Array.from({ length: 3 }, () => generateActivity());
    setActivities(initialActivities);
  }, []);

  const getActivityIcon = (type: ActivityData['type']) => {
    switch (type) {
      case 'user_joined':
        return Users;
      case 'code_compiled':
        return Code;
      case 'contract_deployed':
        return Zap;
      case 'lesson_completed':
        return TrendingUp;
      default:
        return Users;
    }
  };

  const getActivityMessage = (activity: ActivityData) => {
    const { type, count, location } = activity;
    const locationText = location ? ` from ${location}` : '';
    
    switch (type) {
      case 'user_joined':
        return `${count} developer${count > 1 ? 's' : ''} joined${locationText}`;
      case 'code_compiled':
        return `${count} smart contract${count > 1 ? 's' : ''} compiled${locationText}`;
      case 'contract_deployed':
        return `${count} contract${count > 1 ? 's' : ''} deployed${locationText}`;
      case 'lesson_completed':
        return `${count} lesson${count > 1 ? 's' : ''} completed${locationText}`;
      default:
        return `${count} activity${count > 1 ? 'ies' : ''} recorded${locationText}`;
    }
  };

  const getActivityColor = (type: ActivityData['type']) => {
    switch (type) {
      case 'user_joined':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'code_compiled':
        return 'text-blue-400 bg-blue-500/20 border-blue-500/30';
      case 'contract_deployed':
        return 'text-purple-400 bg-purple-500/20 border-purple-500/30';
      case 'lesson_completed':
        return 'text-orange-400 bg-orange-500/20 border-orange-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Online Users Counter */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-center space-x-2 p-3 rounded-lg bg-white/5 border border-white/10 backdrop-blur-sm"
      >
        <div className="relative">
          <Globe className="w-5 h-5 text-green-400" />
          <motion.div
            className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full"
            animate={{ scale: [1, 1.3, 1], opacity: [0.7, 1, 0.7] }}
            transition={{ duration: 2, repeat: Infinity }}
          />
        </div>
        <span className="text-white font-medium">
          <motion.span
            key={onlineCount}
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {onlineCount}
          </motion.span>
        </span>
        <span className="text-gray-400 text-sm">developers online</span>
      </motion.div>

      {/* Activity Feed */}
      <div className="space-y-2">
        <AnimatePresence mode="popLayout">
          {activities.map((activity, index) => {
            const Icon = getActivityIcon(activity.type);
            const colorClasses = getActivityColor(activity.type);
            
            return (
              <motion.div
                key={activity.id}
                initial={{ opacity: 0, x: -50, scale: 0.9 }}
                animate={{ 
                  opacity: 1, 
                  x: 0, 
                  scale: 1,
                  transition: {
                    type: "spring",
                    stiffness: 300,
                    damping: 30,
                    delay: index * 0.1
                  }
                }}
                exit={{ 
                  opacity: 0, 
                  x: 50, 
                  scale: 0.9,
                  transition: { duration: 0.2 }
                }}
                layout
                className={cn(
                  'flex items-center space-x-3 p-3 rounded-lg border backdrop-blur-sm',
                  'transition-all duration-300 hover:scale-105',
                  colorClasses
                )}
              >
                {/* Activity Icon */}
                <motion.div
                  className="w-8 h-8 rounded-full bg-white/10 flex items-center justify-center flex-shrink-0"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Icon className="w-4 h-4" />
                </motion.div>

                {/* Activity Message */}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white">
                    {getActivityMessage(activity)}
                  </p>
                  <p className="text-xs text-gray-400">
                    {activity.timestamp.toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>

                {/* Activity Count Badge */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 300 }}
                  className="w-6 h-6 rounded-full bg-white/20 flex items-center justify-center flex-shrink-0"
                >
                  <span className="text-xs font-bold text-white">
                    {activity.count}
                  </span>
                </motion.div>
              </motion.div>
            );
          })}
        </AnimatePresence>
      </div>

      {/* Activity Pulse Indicator */}
      <motion.div
        className="flex items-center justify-center space-x-2 p-2"
        animate={{ opacity: [0.5, 1, 0.5] }}
        transition={{ duration: 3, repeat: Infinity }}
      >
        <div className="flex space-x-1">
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-green-400 rounded-full"
              animate={{ scale: [1, 1.5, 1] }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2
              }}
            />
          ))}
        </div>
        <span className="text-xs text-gray-400">Live activity</span>
      </motion.div>
    </div>
  );
}

// Hook for activity data
export function useActivityData() {
  const [activityStats, setActivityStats] = useState({
    totalUsers: 1247,
    activeToday: 127,
    contractsDeployed: 3456,
    lessonsCompleted: 8923
  });

  useEffect(() => {
    // Simulate real-time updates
    const interval = setInterval(() => {
      setActivityStats(prev => ({
        totalUsers: prev.totalUsers + Math.floor(Math.random() * 3),
        activeToday: Math.max(50, prev.activeToday + Math.floor(Math.random() * 10) - 5),
        contractsDeployed: prev.contractsDeployed + Math.floor(Math.random() * 2),
        lessonsCompleted: prev.lessonsCompleted + Math.floor(Math.random() * 5)
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return activityStats;
}

// Activity indicator for smaller spaces
export function CompactActivityIndicator({ className }: { className?: string }) {
  const [pulseCount, setPulseCount] = useState(0);
  const activityStats = useActivityData();

  useEffect(() => {
    const interval = setInterval(() => {
      setPulseCount(prev => prev + 1);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  return (
    <motion.div
      className={cn(
        'flex items-center space-x-2 px-3 py-2 rounded-full',
        'bg-green-500/20 border border-green-500/30 backdrop-blur-sm',
        className
      )}
      whileHover={{ scale: 1.05 }}
    >
      <motion.div
        key={pulseCount}
        className="w-2 h-2 bg-green-400 rounded-full"
        animate={{ scale: [1, 1.5, 1], opacity: [0.7, 1, 0.7] }}
        transition={{ duration: 1.5 }}
      />
      <span className="text-sm font-medium text-green-300">
        {activityStats.activeToday} online
      </span>
    </motion.div>
  );
}
