'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  AlertCircle, 
  AlertTriangle, 
  Info, 
  X, 
  ChevronDown, 
  ChevronRight,
  Lightbulb,
  ExternalLink,
  Copy,
  CheckCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface CompilationError {
  line: number;
  column: number;
  message: string;
  severity: 'error' | 'warning' | 'info';
  code?: string;
  suggestion?: string;
  helpUrl?: string;
}

interface CompilationErrorDisplayProps {
  errors: CompilationError[];
  sourceCode: string;
  className?: string;
  maxHeight?: string;
  showLineNumbers?: boolean;
  enableQuickFix?: boolean;
  onErrorClick?: (error: CompilationError) => void;
  onQuickFix?: (error: CompilationError, fix: string) => void;
}

export function CompilationErrorDisplay({
  errors,
  sourceCode,
  className,
  maxHeight = '300px',
  showLineNumbers = true,
  enableQuickFix = true,
  onErrorClick,
  onQuickFix
}: CompilationErrorDisplayProps) {
  const [expandedErrors, setExpandedErrors] = useState<Set<number>>(new Set());
  const [copiedSuggestion, setCopiedSuggestion] = useState<number | null>(null);

  const toggleErrorExpansion = (index: number) => {
    const newExpanded = new Set(expandedErrors);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedErrors(newExpanded);
  };

  const getErrorIcon = (severity: CompilationError['severity']) => {
    switch (severity) {
      case 'error':
        return AlertCircle;
      case 'warning':
        return AlertTriangle;
      case 'info':
        return Info;
      default:
        return AlertCircle;
    }
  };

  const getErrorColor = (severity: CompilationError['severity']) => {
    switch (severity) {
      case 'error':
        return 'text-red-400 border-red-500/30 bg-red-500/10';
      case 'warning':
        return 'text-yellow-400 border-yellow-500/30 bg-yellow-500/10';
      case 'info':
        return 'text-blue-400 border-blue-500/30 bg-blue-500/10';
      default:
        return 'text-red-400 border-red-500/30 bg-red-500/10';
    }
  };

  const getCommonSolutions = (error: CompilationError) => {
    const message = error.message.toLowerCase();
    const solutions: string[] = [];

    if (message.includes('undeclared identifier')) {
      solutions.push('Check if the variable is declared in the correct scope');
      solutions.push('Verify the spelling of the identifier');
      solutions.push('Import the required contract or library');
    } else if (message.includes('type mismatch')) {
      solutions.push('Check the data types of your variables');
      solutions.push('Use explicit type casting if needed');
      solutions.push('Verify function parameter types');
    } else if (message.includes('gas')) {
      solutions.push('Optimize your code to reduce gas consumption');
      solutions.push('Consider using more efficient data structures');
      solutions.push('Break down complex operations into smaller functions');
    } else if (message.includes('visibility')) {
      solutions.push('Add explicit visibility specifier (public, private, internal, external)');
      solutions.push('Check if the function should be accessible from outside');
    }

    return solutions;
  };

  const copyToClipboard = async (text: string, index: number) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedSuggestion(index);
      setTimeout(() => setCopiedSuggestion(null), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  const getCodeSnippetWithHighlight = (error: CompilationError) => {
    const lines = sourceCode.split('\n');
    const errorLine = error.line - 1;
    const startLine = Math.max(0, errorLine - 2);
    const endLine = Math.min(lines.length - 1, errorLine + 2);
    
    return lines.slice(startLine, endLine + 1).map((line, index) => {
      const lineNumber = startLine + index + 1;
      const isErrorLine = lineNumber === error.line;
      
      return {
        number: lineNumber,
        content: line,
        isError: isErrorLine,
        column: isErrorLine ? error.column : null
      };
    });
  };

  if (errors.length === 0) {
    return null;
  }

  return (
    <div className={cn('space-y-3', className)} style={{ maxHeight, overflowY: 'auto' }}>
      <AnimatePresence>
        {errors.map((error, index) => {
          const Icon = getErrorIcon(error.severity);
          const isExpanded = expandedErrors.has(index);
          const codeSnippet = getCodeSnippetWithHighlight(error);
          const solutions = getCommonSolutions(error);

          return (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ delay: index * 0.1 }}
              className={cn(
                'border rounded-lg overflow-hidden',
                getErrorColor(error.severity)
              )}
            >
              {/* Error Header */}
              <div
                className="flex items-start space-x-3 p-4 cursor-pointer hover:bg-white/5 transition-colors"
                onClick={() => {
                  toggleErrorExpansion(index);
                  if (onErrorClick) {
                    onErrorClick(error);
                  }
                }}
              >
                <Icon className="w-5 h-5 flex-shrink-0 mt-0.5" />
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-sm capitalize">{error.severity}</span>
                    {showLineNumbers && (
                      <span className="text-xs opacity-75">
                        Line {error.line}, Column {error.column}
                      </span>
                    )}
                    {error.code && (
                      <span className="text-xs bg-white/10 px-2 py-0.5 rounded">
                        {error.code}
                      </span>
                    )}
                  </div>
                  <p className="text-sm leading-relaxed">{error.message}</p>
                </div>
                <motion.div
                  animate={{ rotate: isExpanded ? 90 : 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <ChevronRight className="w-4 h-4" />
                </motion.div>
              </div>

              {/* Expanded Content */}
              <AnimatePresence>
                {isExpanded && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="border-t border-current/20"
                  >
                    {/* Code Snippet */}
                    <div className="p-4 bg-black/20">
                      <h4 className="text-sm font-medium mb-2 flex items-center space-x-2">
                        <span>Code Context</span>
                      </h4>
                      <div className="bg-gray-900 rounded-lg p-3 font-mono text-sm overflow-x-auto">
                        {codeSnippet.map((line, lineIndex) => (
                          <div
                            key={lineIndex}
                            className={cn(
                              'flex items-start space-x-3',
                              line.isError && 'bg-red-500/20 -mx-3 px-3 py-1'
                            )}
                          >
                            <span className="text-gray-500 text-xs w-8 text-right flex-shrink-0">
                              {line.number}
                            </span>
                            <span className="text-gray-300 flex-1">
                              {line.content || ' '}
                              {line.isError && line.column && (
                                <div className="relative">
                                  <div
                                    className="absolute top-0 w-1 h-4 bg-red-400"
                                    style={{ left: `${line.column * 0.6}ch` }}
                                  />
                                </div>
                              )}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Solutions */}
                    {solutions.length > 0 && (
                      <div className="p-4 border-t border-current/20">
                        <h4 className="text-sm font-medium mb-2 flex items-center space-x-2">
                          <Lightbulb className="w-4 h-4" />
                          <span>Suggested Solutions</span>
                        </h4>
                        <ul className="space-y-2">
                          {solutions.map((solution, solutionIndex) => (
                            <li key={solutionIndex} className="flex items-start space-x-2 text-sm">
                              <span className="text-current/60 mt-1">•</span>
                              <span className="flex-1">{solution}</span>
                              {enableQuickFix && (
                                <button
                                  onClick={() => copyToClipboard(solution, solutionIndex)}
                                  className="text-current/60 hover:text-current transition-colors p-1"
                                  title="Copy suggestion"
                                >
                                  {copiedSuggestion === solutionIndex ? (
                                    <CheckCircle className="w-3 h-3" />
                                  ) : (
                                    <Copy className="w-3 h-3" />
                                  )}
                                </button>
                              )}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Custom Suggestion */}
                    {error.suggestion && (
                      <div className="p-4 border-t border-current/20">
                        <h4 className="text-sm font-medium mb-2">Quick Fix</h4>
                        <div className="bg-white/5 rounded-lg p-3">
                          <p className="text-sm mb-2">{error.suggestion}</p>
                          {enableQuickFix && onQuickFix && (
                            <button
                              onClick={() => onQuickFix(error, error.suggestion!)}
                              className="text-xs bg-current/20 hover:bg-current/30 px-3 py-1 rounded transition-colors"
                            >
                              Apply Fix
                            </button>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Help Link */}
                    {error.helpUrl && (
                      <div className="p-4 border-t border-current/20">
                        <a
                          href={error.helpUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="inline-flex items-center space-x-2 text-sm hover:underline"
                        >
                          <ExternalLink className="w-4 h-4" />
                          <span>Learn more about this error</span>
                        </a>
                      </div>
                    )}
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          );
        })}
      </AnimatePresence>
    </div>
  );
}

// Error summary component
export function ErrorSummary({ 
  errors, 
  className 
}: { 
  errors: CompilationError[]; 
  className?: string; 
}) {
  const errorCount = errors.filter(e => e.severity === 'error').length;
  const warningCount = errors.filter(e => e.severity === 'warning').length;
  const infoCount = errors.filter(e => e.severity === 'info').length;

  if (errors.length === 0) {
    return (
      <div className={cn('flex items-center space-x-2 text-green-400', className)}>
        <CheckCircle className="w-4 h-4" />
        <span className="text-sm">No compilation errors</span>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center space-x-4 text-sm', className)}>
      {errorCount > 0 && (
        <div className="flex items-center space-x-1 text-red-400">
          <AlertCircle className="w-4 h-4" />
          <span>{errorCount} error{errorCount !== 1 ? 's' : ''}</span>
        </div>
      )}
      {warningCount > 0 && (
        <div className="flex items-center space-x-1 text-yellow-400">
          <AlertTriangle className="w-4 h-4" />
          <span>{warningCount} warning{warningCount !== 1 ? 's' : ''}</span>
        </div>
      )}
      {infoCount > 0 && (
        <div className="flex items-center space-x-1 text-blue-400">
          <Info className="w-4 h-4" />
          <span>{infoCount} info</span>
        </div>
      )}
    </div>
  );
}
