# =============================================================================
# STAGING ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains staging-specific environment variables
# These values mirror production but with staging-specific resources
# =============================================================================

# Application Configuration
NODE_ENV=staging
NEXT_PUBLIC_APP_URL=https://staging.soliditylearn.com
DEBUG=false
VERBOSE_LOGGING=true
ENABLE_QUERY_LOGGING=false
ENABLE_PERFORMANCE_MONITORING=true

# Database Configuration (Staging Database)
DATABASE_URL="postgresql://staging_user:<EMAIL>:5432/solidity_learning_staging?schema=public"
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=8
DATABASE_POOL_TIMEOUT=20000

# Redis Configuration (Staging Redis)
REDIS_URL="redis://staging-redis.amazonaws.com:6379"
REDIS_PASSWORD="staging-redis-password"
REDIS_DB=0

# Authentication
NEXTAUTH_URL=https://staging.soliditylearn.com
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_SAME_SITE=strict

# Socket.io Configuration
SOCKET_IO_PORT=3001
SOCKET_IO_CORS_ORIGINS="https://staging.soliditylearn.com"
SOCKET_IO_MAX_CONNECTIONS=200

# Rate Limiting (Production-like)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
API_RATE_LIMIT_PER_MINUTE=60
AUTH_RATE_LIMIT_PER_MINUTE=5
COLLABORATION_RATE_LIMIT_PER_MINUTE=30

# AI Services (Moderate Limits)
AI_REQUESTS_PER_MINUTE=40
AI_REQUESTS_PER_HOUR=800
AI_REQUESTS_PER_DAY=5000

# Email Configuration (Staging SendGrid)
SENDGRID_API_KEY="SG.staging-api-key"
SENDGRID_FROM_EMAIL="<EMAIL>"

# File Storage (Staging S3 Bucket)
AWS_S3_BUCKET="solidity-learning-staging-uploads"
AWS_S3_BUCKET_URL="https://solidity-learning-staging-uploads.s3.amazonaws.com"
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,text/plain,application/json"

# Blockchain (Testnet)
ETHEREUM_RPC_URL="https://sepolia.infura.io/v3/staging-project-id"
ETHEREUM_TESTNET_RPC_URL="https://sepolia.infura.io/v3/staging-project-id"

# Feature Flags (Production Features Only)
FEATURE_AI_TUTORING=true
FEATURE_COLLABORATION=true
FEATURE_CODE_COMPILATION=true
FEATURE_BLOCKCHAIN_INTEGRATION=true
FEATURE_GAMIFICATION=true
FEATURE_SOCIAL_FEATURES=true
FEATURE_ADVANCED_ANALYTICS=true

# Beta Features (Limited Testing)
BETA_VOICE_CHAT=false
BETA_VIDEO_COLLABORATION=true
BETA_AI_CODE_REVIEW=false

# Monitoring (Staging Sentry Project)
SENTRY_DSN="https://<EMAIL>/staging-project-id"
SENTRY_ORG="solidity-learning"
SENTRY_PROJECT="staging"
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-STAGING-ID"

# Security (Production-like)
CONTENT_SECURITY_POLICY_REPORT_URI="https://staging.soliditylearn.com/api/csp-report"
HSTS_MAX_AGE=31536000

# Third-party Integrations (Staging Keys)
STRIPE_PUBLISHABLE_KEY="pk_test_staging_key"
STRIPE_SECRET_KEY="sk_test_staging_key"

# Health Checks
HEALTH_CHECK_ENDPOINT="/api/health"
HEALTH_CHECK_TIMEOUT=5000

# Backup Configuration (Staging)
BACKUP_SCHEDULE="0 3 * * *"
BACKUP_RETENTION_DAYS=7
BACKUP_S3_BUCKET="solidity-learning-staging-backups"

# Caching
CACHE_TTL=1800
LOG_LEVEL=info

# Testing Configuration
TEST_DATABASE_URL="postgresql://test_user:<EMAIL>:5432/solidity_learning_test"
TEST_REDIS_URL="redis://staging-redis.amazonaws.com:6379/1"
