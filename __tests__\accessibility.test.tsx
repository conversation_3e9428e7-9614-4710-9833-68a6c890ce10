/**
 * Accessibility Test Suite for Solidity Learning Platform
 * 
 * Comprehensive WCAG 2.1 AA compliance testing for all components
 * including keyboard navigation, screen reader support, and color contrast.
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axe, toHaveNoViolations } from 'jest-axe';
import '@testing-library/jest-dom';

// Component imports
import { ComprehensiveEngagementSystem } from '@/components/engagement/ComprehensiveEngagementSystem';
import { RealTimeNotificationSystem } from '@/components/engagement/RealTimeNotificationSystem';
import { AnimatedStatsCounter } from '@/components/engagement/AnimatedStatsCounter';
import { AIAssistantWidget } from '@/components/engagement/AIAssistantWidget';
import { InteractiveFAQ } from '@/components/engagement/InteractiveFAQ';
import { SocialProofDisplay } from '@/components/conversion/SocialProofDisplay';
import { InteractiveContentViewer } from '@/components/content/InteractiveContentViewer';
import { ComprehensiveContentSystem } from '@/components/content/ComprehensiveContentSystem';

// Test utilities
import { 
  createMockUserContext, 
  createMockContentItems, 
  createAccessibilityTestConfig,
  renderWithProviders 
} from './test-utils';

// Extend Jest matchers
expect.extend(toHaveNoViolations);

// Mock framer-motion for accessibility tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
  useInView: () => true,
  useMotionValue: () => ({ set: jest.fn(), on: jest.fn() }),
  useSpring: () => ({ set: jest.fn(), on: jest.fn() })
}));

describe('WCAG 2.1 AA Compliance Tests', () => {
  const axeConfig = createAccessibilityTestConfig();

  describe('Real-time Notification System Accessibility', () => {
    test('has proper ARIA live region', () => {
      render(<RealTimeNotificationSystem />);
      
      const liveRegion = screen.getByRole('region', { name: /live notifications/i });
      expect(liveRegion).toHaveAttribute('aria-live', 'polite');
      expect(liveRegion).toHaveAttribute('aria-label', 'Live notifications');
    });

    test('notification cards have proper accessibility attributes', async () => {
      render(<RealTimeNotificationSystem />);
      
      // Wait for notification to appear
      await screen.findByRole('button', { name: /notification:/i }, { timeout: 10000 });
      
      const notificationButton = screen.getByRole('button', { name: /notification:/i });
      expect(notificationButton).toHaveAttribute('aria-label');
      expect(notificationButton).toHaveAttribute('tabIndex', '0');
    });

    test('dismiss buttons are keyboard accessible', async () => {
      const user = userEvent.setup();
      render(<RealTimeNotificationSystem />);
      
      const dismissButton = await screen.findByLabelText(/dismiss notification/i, {}, { timeout: 10000 });
      
      // Test keyboard interaction
      dismissButton.focus();
      expect(dismissButton).toHaveFocus();
      
      await user.keyboard('{Enter}');
      // Notification should be dismissed
    });

    test('meets WCAG 2.1 AA standards', async () => {
      const { container } = render(<RealTimeNotificationSystem />);
      const results = await axe(container, axeConfig);
      expect(results).toHaveNoViolations();
    });

    test('respects prefers-reduced-motion', () => {
      // Mock reduced motion preference
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(prefers-reduced-motion: reduce)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      render(<RealTimeNotificationSystem />);
      
      // Component should render without animations
      const region = screen.getByRole('region', { name: /live notifications/i });
      expect(region).toBeInTheDocument();
    });
  });

  describe('Animated Stats Counter Accessibility', () => {
    test('has proper semantic structure', () => {
      render(<AnimatedStatsCounter />);
      
      const statsRegion = screen.getByRole('region', { name: /platform statistics/i });
      expect(statsRegion).toBeInTheDocument();
      expect(statsRegion).toHaveAttribute('aria-label', 'Platform statistics');
    });

    test('stat cards have proper ARIA labels', () => {
      render(<AnimatedStatsCounter />);
      
      const statCards = screen.getAllByRole('article');
      statCards.forEach(card => {
        expect(card).toHaveAttribute('aria-label');
      });
    });

    test('counter values are announced to screen readers', async () => {
      render(<AnimatedStatsCounter />);
      
      // Check that counters have aria-live for dynamic updates
      const counters = screen.getAllByText(/\d+/);
      counters.forEach(counter => {
        const parent = counter.closest('[aria-live]');
        expect(parent).toHaveAttribute('aria-live', 'polite');
      });
    });

    test('meets WCAG 2.1 AA standards', async () => {
      const { container } = render(<AnimatedStatsCounter />);
      const results = await axe(container, axeConfig);
      expect(results).toHaveNoViolations();
    });
  });

  describe('AI Assistant Widget Accessibility', () => {
    test('chat button has proper accessibility attributes', () => {
      render(<AIAssistantWidget />);
      
      const chatButton = screen.getByLabelText(/open ai assistant chat/i);
      expect(chatButton).toHaveAttribute('aria-label', 'Open AI assistant chat');
      expect(chatButton).toHaveAttribute('type', 'button');
    });

    test('chat interface is keyboard navigable', async () => {
      const user = userEvent.setup();
      render(<AIAssistantWidget />);
      
      const chatButton = screen.getByLabelText(/open ai assistant chat/i);
      
      // Open chat with keyboard
      chatButton.focus();
      await user.keyboard('{Enter}');
      
      // Check that input is focusable
      const input = screen.getByPlaceholderText(/ask me anything/i);
      expect(input).toBeInTheDocument();
      
      input.focus();
      expect(input).toHaveFocus();
    });

    test('chat messages have proper structure', async () => {
      const user = userEvent.setup();
      render(<AIAssistantWidget />);
      
      // Open chat
      const chatButton = screen.getByLabelText(/open ai assistant chat/i);
      await user.click(chatButton);
      
      // Send a message
      const input = screen.getByPlaceholderText(/ask me anything/i);
      await user.type(input, 'Test message');
      
      const sendButton = screen.getByLabelText(/send message/i);
      await user.click(sendButton);
      
      // Check message structure
      const message = screen.getByText(/test message/i);
      expect(message).toBeInTheDocument();
    });

    test('meets WCAG 2.1 AA standards', async () => {
      const { container } = render(<AIAssistantWidget />);
      const results = await axe(container, axeConfig);
      expect(results).toHaveNoViolations();
    });

    test('supports screen reader navigation', async () => {
      const user = userEvent.setup();
      render(<AIAssistantWidget />);
      
      const chatButton = screen.getByLabelText(/open ai assistant chat/i);
      await user.click(chatButton);
      
      // Check for proper heading structure
      const heading = screen.getByText(/ai assistant/i);
      expect(heading).toBeInTheDocument();
      
      // Check for proper form labels
      const input = screen.getByPlaceholderText(/ask me anything/i);
      expect(input).toHaveAttribute('placeholder');
    });
  });

  describe('Interactive FAQ Accessibility', () => {
    test('has proper heading structure', () => {
      render(<InteractiveFAQ />);
      
      const mainHeading = screen.getByRole('heading', { level: 2 });
      expect(mainHeading).toHaveTextContent(/frequently asked questions/i);
    });

    test('search input has proper labeling', () => {
      render(<InteractiveFAQ />);
      
      const searchInput = screen.getByPlaceholderText(/search questions/i);
      expect(searchInput).toHaveAttribute('type', 'text');
      expect(searchInput).toHaveAttribute('placeholder');
    });

    test('FAQ items are properly structured as disclosure widgets', async () => {
      const user = userEvent.setup();
      render(<InteractiveFAQ />);
      
      const faqButtons = screen.getAllByRole('button', { expanded: false });
      expect(faqButtons.length).toBeGreaterThan(0);
      
      // Test first FAQ item
      const firstButton = faqButtons[0];
      expect(firstButton).toHaveAttribute('aria-expanded', 'false');
      
      await user.click(firstButton);
      expect(firstButton).toHaveAttribute('aria-expanded', 'true');
    });

    test('category filters are keyboard accessible', async () => {
      const user = userEvent.setup();
      render(<InteractiveFAQ />);
      
      const categoryButtons = screen.getAllByRole('button');
      const categoryButton = categoryButtons.find(button => 
        button.textContent?.includes('Getting Started')
      );
      
      if (categoryButton) {
        categoryButton.focus();
        expect(categoryButton).toHaveFocus();
        
        await user.keyboard('{Enter}');
        // Category should be selected
      }
    });

    test('helpful voting buttons have proper labels', async () => {
      const user = userEvent.setup();
      render(<InteractiveFAQ />);
      
      // Expand first FAQ
      const firstButton = screen.getAllByRole('button', { expanded: false })[0];
      await user.click(firstButton);
      
      // Check voting buttons
      const helpfulButton = screen.getByText(/👍 yes/i);
      const notHelpfulButton = screen.getByText(/👎 no/i);
      
      expect(helpfulButton).toBeInTheDocument();
      expect(notHelpfulButton).toBeInTheDocument();
    });

    test('meets WCAG 2.1 AA standards', async () => {
      const { container } = render(<InteractiveFAQ />);
      const results = await axe(container, axeConfig);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Social Proof Display Accessibility', () => {
    test('social proof cards have proper accessibility attributes', async () => {
      render(
        <SocialProofDisplay
          types={['numbers', 'testimonials', 'activity']}
          frequency={1000} // Faster for testing
        />
      );
      
      // Wait for social proof to appear
      const socialProofCard = await screen.findByRole('button', { 
        name: /social proof:/i 
      }, { timeout: 5000 });
      
      expect(socialProofCard).toHaveAttribute('aria-label');
      expect(socialProofCard).toHaveAttribute('tabIndex', '0');
    });

    test('dismiss buttons are accessible', async () => {
      const user = userEvent.setup();
      render(
        <SocialProofDisplay
          types={['numbers', 'testimonials']}
          frequency={1000}
        />
      );
      
      const dismissButton = await screen.findByLabelText(/dismiss notification/i, {}, { timeout: 5000 });
      
      dismissButton.focus();
      expect(dismissButton).toHaveFocus();
      
      await user.keyboard('{Enter}');
    });

    test('meets WCAG 2.1 AA standards', async () => {
      const { container } = render(
        <SocialProofDisplay
          types={['numbers']}
          frequency={1000}
        />
      );
      
      const results = await axe(container, axeConfig);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Content Viewer Accessibility', () => {
    test('content viewer has proper structure', () => {
      const mockContent = createMockContentItems(1)[0];
      
      render(
        <ComprehensiveContentSystem initialContent={[mockContent]}>
          <InteractiveContentViewer contentId={mockContent.id} />
        </ComprehensiveContentSystem>
      );
      
      // Check for proper heading structure
      const contentTitle = screen.getByRole('heading', { level: 1 });
      expect(contentTitle).toBeInTheDocument();
    });

    test('video controls are keyboard accessible', () => {
      const mockVideoContent = {
        ...createMockContentItems(1)[0],
        type: 'video' as const
      };
      
      render(
        <ComprehensiveContentSystem initialContent={[mockVideoContent]}>
          <InteractiveContentViewer contentId={mockVideoContent.id} />
        </ComprehensiveContentSystem>
      );
      
      // Video controls should be present and accessible
      const playButton = screen.queryByLabelText(/play|pause/i);
      if (playButton) {
        expect(playButton).toHaveAttribute('aria-label');
      }
    });

    test('rating system is accessible', async () => {
      const user = userEvent.setup();
      const mockContent = createMockContentItems(1)[0];
      
      render(
        <ComprehensiveContentSystem initialContent={[mockContent]}>
          <InteractiveContentViewer contentId={mockContent.id} />
        </ComprehensiveContentSystem>
      );
      
      // Check rating stars
      const ratingButtons = screen.getAllByLabelText(/rate \d+ stars/i);
      expect(ratingButtons.length).toBe(5);
      
      // Test keyboard interaction
      ratingButtons[0].focus();
      expect(ratingButtons[0]).toHaveFocus();
      
      await user.keyboard('{Enter}');
    });

    test('meets WCAG 2.1 AA standards', async () => {
      const mockContent = createMockContentItems(1)[0];
      
      const { container } = render(
        <ComprehensiveContentSystem initialContent={[mockContent]}>
          <InteractiveContentViewer contentId={mockContent.id} />
        </ComprehensiveContentSystem>
      );
      
      const results = await axe(container, axeConfig);
      expect(results).toHaveNoViolations();
    });
  });

  describe('Comprehensive System Integration Accessibility', () => {
    test('integrated systems maintain accessibility', async () => {
      const mockConfig = {
        enableNotifications: true,
        enableStatsCounter: true,
        enableAIAssistant: true,
        enableFAQ: true,
        enableExitIntent: false, // Disable for testing
        enableScrollCTAs: false,
        enableUrgencyTimers: false,
        enableSocialSharing: true,
        enableAchievements: true,
        enableAnalytics: true,
        trackEngagement: true,
        theme: 'auto' as const,
        position: 'bottom-right' as const,
        animationLevel: 'minimal' as const
      };

      const { container } = render(
        <ComprehensiveEngagementSystem
          config={mockConfig}
          userContext={createMockUserContext()}
        >
          <main>
            <h1>Test Page</h1>
            <p>This is a test page for accessibility testing.</p>
          </main>
        </ComprehensiveEngagementSystem>
      );
      
      // Check that main content is still accessible
      const mainContent = screen.getByRole('main');
      expect(mainContent).toBeInTheDocument();
      
      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toHaveTextContent('Test Page');
      
      // Run accessibility audit
      const results = await axe(container, axeConfig);
      expect(results).toHaveNoViolations();
    });

    test('focus management works correctly', async () => {
      const user = userEvent.setup();
      const mockConfig = {
        enableNotifications: false,
        enableStatsCounter: false,
        enableAIAssistant: true,
        enableFAQ: false,
        enableExitIntent: false,
        enableScrollCTAs: false,
        enableUrgencyTimers: false,
        enableSocialSharing: false,
        enableAchievements: false,
        enableAnalytics: false,
        trackEngagement: false,
        theme: 'auto' as const,
        position: 'bottom-right' as const,
        animationLevel: 'minimal' as const
      };

      render(
        <ComprehensiveEngagementSystem
          config={mockConfig}
          userContext={createMockUserContext()}
        >
          <button>Main Content Button</button>
        </ComprehensiveEngagementSystem>
      );
      
      // Test tab navigation
      await user.tab();
      const mainButton = screen.getByText('Main Content Button');
      expect(mainButton).toHaveFocus();
      
      await user.tab();
      const aiButton = screen.getByLabelText(/open ai assistant chat/i);
      expect(aiButton).toHaveFocus();
    });

    test('screen reader announcements work correctly', async () => {
      const mockConfig = {
        enableNotifications: true,
        enableStatsCounter: false,
        enableAIAssistant: false,
        enableFAQ: false,
        enableExitIntent: false,
        enableScrollCTAs: false,
        enableUrgencyTimers: false,
        enableSocialSharing: false,
        enableAchievements: false,
        enableAnalytics: false,
        trackEngagement: false,
        theme: 'auto' as const,
        position: 'bottom-right' as const,
        animationLevel: 'minimal' as const
      };

      render(
        <ComprehensiveEngagementSystem
          config={mockConfig}
          userContext={createMockUserContext()}
        >
          <div>Test Content</div>
        </ComprehensiveEngagementSystem>
      );
      
      // Check for live regions
      const liveRegion = screen.getByRole('region', { name: /live notifications/i });
      expect(liveRegion).toHaveAttribute('aria-live', 'polite');
    });
  });
});
