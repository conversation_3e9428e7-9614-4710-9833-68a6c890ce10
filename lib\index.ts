// Lib Barrel Export File
// Auto-generated for cleaner imports

// Utilities
export * from './utils';

// Hooks
export * from './hooks/useAuth';
export * from './hooks/useSettings';
export * from './hooks/useAchievements';
export * from './hooks/useApiData';
export * from './hooks/useAsyncButton';
export * from './hooks/useErrorRecovery';
export * from './hooks/useFeatureFlags';
export * from './hooks/useKeyboardNavigation';
export * from './hooks/useLazyLoading';
export * from './hooks/useRealTimeXP';
export * from './hooks/useSessionStatus';
export * from './hooks/useSolidityAnalyzer';
export * from './hooks/useSolidityDebugger';

// API
export * from './api/auth';
export * from './api/cache';
export * from './api/errors';
export * from './api/types';
export * from './api/utils';
export * from './api/validation';

// Auth
export * from './auth/config';
export * from './auth/sessionManager';
export * from './auth/password';

// Achievements
export * from './achievements/manager';
export * from './achievements/types';
export * from './achievements/data';

// Admin
export * from './admin/auth';
export * from './admin/types';

// AI
export * from './ai/LearningAssistant';

// Blockchain
export * from './blockchain/Web3Provider';

// Collaboration
export * from './collaboration/CollaborationClient';
export * from './collaboration/ConnectionManager';

// Community (core types only)
export * from './community/types';

// Note: Advanced community features not exported by default to reduce bundle size
// Import directly if needed:
// import { ... } from '@/lib/community/leaderboard';
// import { ... } from '@/lib/community/statistics';
// import { ... } from '@/lib/community/websocket';

// Compiler (available but not exported by default)
// import { ... } from '@/lib/compiler/SolidityCompiler';

// Config
export * from './config/environment';

// Curriculum
export * from './curriculum/manager';
export * from './curriculum/types';
export * from './curriculum/data';

// Editor
export * from './editor/MonacoSoliditySetup';
export * from './editor/SolidityLanguageDefinition';
export * from './editor/SolidityIntelliSense';

// Errors
export * from './errors/types';
export * from './errors/recovery';

// Features
export * from './features/feature-flags';

// Forms
export * from './forms/form-handler';

// Monitoring (core only)
export * from './monitoring/error-tracking';

// Note: Advanced monitoring features not exported by default
// Import directly if needed:
// import { ... } from '@/lib/monitoring/analytics';
// import { ... } from '@/lib/monitoring/logger';

// Performance (available but not exported by default)
// import { ... } from '@/lib/performance/PerformanceMonitor';

// Security
export * from './security/config';
export * from './security/validation';
export * from './security/session';

// Services
export * from './services/SettingsService';

// Socket
export * from './socket/client';
export * from './socket/SocketProvider';

// Storage
export * from './storage/CodePersistence';

// Theme
export * from './theme/ThemeProvider';

// VCS
export * from './vcs/SolidityVersionControl';
