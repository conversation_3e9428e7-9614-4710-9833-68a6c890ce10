
import React, { useState } from 'react';

interface LandingPageProps {
  onLogin: (formData: { email?: string; password?: string }) => void;
  onSignup: (formData: { email?: string; password?: string; confirmPassword?: string }) => void;
  onDirectAccess: () => void; // New prop
}

const LandingPage: React.FC<LandingPageProps> = ({ onLogin, onSignup, onDirectAccess }) => {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>('login');
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [signupEmail, setSignupEmail] = useState('');
  const [signupPassword, setSignupPassword] = useState('');
  const [signupConfirmPassword, setSignupConfirmPassword] = useState('');

  const handleLoginSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onLogin({ email: loginEmail, password: loginPassword });
  };

  const handleSignupSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (signupPassword !== signupConfirmPassword) {
      alert("Passwords don't match!"); 
      return;
    }
    onSignup({ email: signupEmail, password: signupPassword });
  };

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 text-brand-text-primary font-sans">
      <header className="text-center mb-10 md:mb-12">
        <h1 className="text-5xl md:text-6xl font-extrabold text-brand-accent mb-4 landing-title-animate">
          Solidity & Blockchain DevPath
        </h1>
        <p className="text-lg md:text-xl text-brand-text-secondary max-w-2xl mx-auto landing-title-animate" style={{ animationDelay: '0.2s' }}>
          Master smart contract development with our interactive learning platform, powered by Gemini AI. Track your progress and prepare for the future of Web3.
        </p>
      </header>

      <main className="w-full max-w-md p-8 md:p-10 glass-card">
        <div className="mb-6 flex border-b border-white/10">
          <button
            onClick={() => setActiveTab('login')}
            className={`landing-tab-button flex-1 py-3 text-center font-medium ${activeTab === 'login' ? 'text-brand-accent active-tab' : 'text-brand-text-muted hover:text-brand-text-primary'}`}
          >
            Login
          </button>
          <button
            onClick={() => setActiveTab('signup')}
            className={`landing-tab-button flex-1 py-3 text-center font-medium ${activeTab === 'signup' ? 'text-brand-accent active-tab' : 'text-brand-text-muted hover:text-brand-text-primary'}`}
          >
            Sign Up
          </button>
        </div>

        {activeTab === 'login' && (
          <form onSubmit={handleLoginSubmit} className="space-y-6">
            <div className="relative">
              <input 
                type="email" 
                id="login-email" 
                value={loginEmail} 
                onChange={(e) => setLoginEmail(e.target.value)} 
                className="landing-input peer w-full px-4 py-3 rounded-lg" 
                placeholder=" " 
                autoComplete="email" 
              />
              <label 
                htmlFor="login-email" 
                className="landing-label absolute text-sm left-4 top-3.5 origin-top-left"
              >
                Email Address (mock)
              </label>
            </div>
            <div className="relative">
              <input 
                type="password" 
                id="login-password" 
                value={loginPassword} 
                onChange={(e) => setLoginPassword(e.target.value)} 
                className="landing-input peer w-full px-4 py-3 rounded-lg" 
                placeholder=" " 
                autoComplete="current-password" 
              />
              <label 
                htmlFor="login-password" 
                className="landing-label absolute text-sm left-4 top-3.5 origin-top-left"
              >
                Password (mock)
              </label>
            </div>
            <button type="submit" className="landing-button w-full">
              Login
            </button>
          </form>
        )}

        {activeTab === 'signup' && (
          <form onSubmit={handleSignupSubmit} className="space-y-6">
            <div className="relative">
              <input 
                type="email" 
                id="signup-email" 
                value={signupEmail} 
                onChange={(e) => setSignupEmail(e.target.value)} 
                className="landing-input peer w-full px-4 py-3 rounded-lg" 
                placeholder=" " 
                autoComplete="email" 
              />
              <label 
                htmlFor="signup-email" 
                className="landing-label absolute text-sm left-4 top-3.5 origin-top-left"
              >
                Email Address (mock)
              </label>
            </div>
            <div className="relative">
              <input 
                type="password" 
                id="signup-password" 
                value={signupPassword} 
                onChange={(e) => setSignupPassword(e.target.value)} 
                className="landing-input peer w-full px-4 py-3 rounded-lg" 
                placeholder=" " 
                autoComplete="new-password"
              />
              <label 
                htmlFor="signup-password" 
                className="landing-label absolute text-sm left-4 top-3.5 origin-top-left"
              >
                Password (mock)
              </label>
            </div>
            <div className="relative">
              <input 
                type="password" 
                id="signup-confirm-password" 
                value={signupConfirmPassword} 
                onChange={(e) => setSignupConfirmPassword(e.target.value)} 
                className="landing-input peer w-full px-4 py-3 rounded-lg" 
                placeholder=" " 
                autoComplete="new-password"
              />
               <label 
                htmlFor="signup-confirm-password" 
                className="landing-label absolute text-sm left-4 top-3.5 origin-top-left"
              >
                Confirm Password (mock)
              </label>
            </div>
            <button type="submit" className="landing-button w-full">
              Create Account
            </button>
          </form>
        )}
        
        <div className="mt-8 text-center">
           <button 
            onClick={onDirectAccess}
            className="landing-button w-full bg-gradient-to-r from-emerald-500 via-green-500 to-emerald-500 hover:from-emerald-600 hover:via-green-600 hover:to-emerald-600"
            style={{
                 backgroundImage: 'linear-gradient(to right, var(--brand-colors-aurora-3, #10B981) 0%, #22C55E 50%, var(--brand-colors-aurora-3, #10B981) 100%)'
            }}
            >
            Access Course Directly
          </button>
        </div>

         <p className="text-xs text-brand-text-muted/70 mt-6 text-center">
          Login and Sign Up are simulated. No data is persistently stored beyond your browser's local storage for progress.
        </p>
      </main>
      
      <footer className="mt-10 md:mt-12 text-center space-y-4">
        <div className="flex justify-center items-center space-x-6 landing-title-animate" style={{ animationDelay: '0.4s' }}>
          <a 
            href="https://github.com/ezekaj" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-brand-text-muted hover:text-brand-accent transition-colors duration-200 flex items-center space-x-2"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            <span>GitHub</span>
          </a>
          <a 
            href="https://www.linkedin.com/in/elvi-zekaj-240b10243/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-brand-text-muted hover:text-brand-accent transition-colors duration-200 flex items-center space-x-2"
          >
            <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
            </svg>
            <span>LinkedIn</span>
          </a>
          <a 
            href="mailto:<EMAIL>" 
            className="text-brand-text-muted hover:text-brand-accent transition-colors duration-200 flex items-center space-x-2"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span>Email</span>
          </a>
        </div>
        <p className="text-sm text-brand-text-muted/80 landing-title-animate" style={{ animationDelay: '0.6s' }}>
          &copy; {new Date().getFullYear()} Solidity DevPath. Learn, Build, Innovate.
        </p>
      </footer>
    </div>
  );
};

export default LandingPage;
