import GamificationAnalytics from '@/lib/analytics/GamificationAnalytics';
import OfflineProgressStorage, { useOfflineProgress } from '@/lib/storage/OfflineProgressStorage';
import { renderHook, act } from '@testing-library/react';

// Mock performance API
const mockPerformance = {
  now: jest.fn(() => 1000),
  mark: jest.fn(),
  measure: jest.fn(),
  getEntriesByType: jest.fn(() => []),
  getEntriesByName: jest.fn(() => []),
  clearMarks: jest.fn(),
  clearMeasures: jest.fn(),
  memory: {
    usedJSHeapSize: 50 * 1024 * 1024, // 50MB
    totalJSHeapSize: 100 * 1024 * 1024, // 100MB
    jsHeapSizeLimit: 2 * 1024 * 1024 * 1024 // 2GB
  }
};

// Mock PerformanceObserver
const mockPerformanceObserver = jest.fn().mockImplementation((callback) => ({
  observe: jest.fn(),
  disconnect: jest.fn(),
  takeRecords: jest.fn(() => [])
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};

// Mock navigator
const mockNavigator = {
  onLine: true
};

describe('GamificationAnalytics', () => {
  let analytics: GamificationAnalytics;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock global objects
    global.performance = mockPerformance as any;
    global.PerformanceObserver = mockPerformanceObserver as any;
    global.localStorage = mockLocalStorage as any;
    global.navigator = mockNavigator as any;

    // Get fresh instance
    analytics = GamificationAnalytics.getInstance();
    analytics.setUserId('test-user');
  });

  describe('Performance Tracking', () => {
    it('tracks XP gained events with performance metrics', () => {
      const trackSpy = jest.spyOn(analytics, 'trackXPGained');
      
      analytics.trackXPGained(100, 'lesson', { lessonId: 'test-lesson' });
      
      expect(trackSpy).toHaveBeenCalledWith(100, 'lesson', { lessonId: 'test-lesson' });
    });

    it('measures operation performance', () => {
      const testOperation = jest.fn(() => 'result');
      
      const result = analytics.measureOperation('test-operation', testOperation);
      
      expect(result).toBe('result');
      expect(testOperation).toHaveBeenCalled();
    });

    it('measures async operation performance', async () => {
      const testAsyncOperation = jest.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return 'async-result';
      });
      
      const result = await analytics.measureAsyncOperation('test-async-operation', testAsyncOperation);
      
      expect(result).toBe('async-result');
      expect(testAsyncOperation).toHaveBeenCalled();
    });

    it('records performance metrics with correct status', () => {
      const recordSpy = jest.spyOn(analytics, 'recordPerformanceMetric');
      
      analytics.recordPerformanceMetric({
        name: 'xp-notification',
        value: 150, // Below 200ms threshold
        category: 'response_time',
        threshold: 200
      });
      
      expect(recordSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'xp-notification',
          value: 150,
          category: 'response_time',
          threshold: 200
        })
      );
    });

    it('identifies critical performance issues', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      analytics.recordPerformanceMetric({
        name: 'slow-operation',
        value: 500, // Above threshold
        category: 'response_time',
        threshold: 200
      });
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Critical performance issue detected')
      );
      
      consoleSpy.mockRestore();
    });

    it('generates performance report', () => {
      // Add some test metrics
      analytics.recordPerformanceMetric({
        name: 'xp-update-1',
        value: 100,
        category: 'response_time',
        threshold: 200
      });
      
      analytics.recordPerformanceMetric({
        name: 'xp-update-2',
        value: 300,
        category: 'response_time',
        threshold: 200
      });
      
      const report = analytics.getPerformanceReport();
      
      expect(report).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            category: 'response_time',
            average: expect.any(Number),
            maximum: expect.any(Number),
            minimum: expect.any(Number),
            criticalIssues: expect.any(Number),
            warnings: expect.any(Number),
            totalSamples: expect.any(Number),
            status: expect.stringMatching(/good|warning|critical/)
          })
        ])
      );
    });
  });

  describe('Event Tracking', () => {
    it('tracks achievement unlocked events', () => {
      const trackSpy = jest.spyOn(analytics, 'trackAchievementUnlocked');
      
      analytics.trackAchievementUnlocked('first-lesson', 'common', 50);
      
      expect(trackSpy).toHaveBeenCalledWith('first-lesson', 'common', 50);
    });

    it('tracks level up events', () => {
      const trackSpy = jest.spyOn(analytics, 'trackLevelUp');
      
      analytics.trackLevelUp(5, 4, 5000);
      
      expect(trackSpy).toHaveBeenCalledWith(5, 4, 5000);
    });

    it('tracks streak extended events', () => {
      const trackSpy = jest.spyOn(analytics, 'trackStreakExtended');
      
      analytics.trackStreakExtended(7, 6);
      
      expect(trackSpy).toHaveBeenCalledWith(7, 6);
    });

    it('tracks social sharing events', () => {
      const trackSpy = jest.spyOn(analytics, 'trackSocialShare');
      
      analytics.trackSocialShare('twitter', 'achievement', 'first-lesson');
      
      expect(trackSpy).toHaveBeenCalledWith('twitter', 'achievement', 'first-lesson');
    });

    it('generates session metrics', () => {
      // Add some test events
      analytics.trackXPGained(100, 'lesson');
      analytics.trackXPGained(50, 'quiz');
      analytics.trackAchievementUnlocked('test-achievement', 'rare', 200);
      
      const metrics = analytics.getSessionMetrics();
      
      expect(metrics).toEqual(
        expect.objectContaining({
          sessionDuration: expect.any(Number),
          actionsPerMinute: expect.any(Number),
          xpPerSession: expect.any(Number),
          achievementsUnlocked: expect.any(Number),
          featuresUsed: expect.any(Array),
          retentionScore: expect.any(Number),
          engagementLevel: expect.stringMatching(/low|medium|high|very_high/)
        })
      );
    });
  });

  describe('Performance Thresholds', () => {
    it('meets XP notification performance target (<200ms)', () => {
      const startTime = performance.now();
      
      // Simulate XP notification trigger
      analytics.trackXPGained(100, 'lesson');
      
      const duration = performance.now() - startTime;
      
      // Should complete within 200ms threshold
      expect(duration).toBeLessThan(200);
    });

    it('meets achievement notification performance target (<300ms)', () => {
      const startTime = performance.now();
      
      // Simulate achievement unlock
      analytics.trackAchievementUnlocked('test-achievement', 'rare', 100);
      
      const duration = performance.now() - startTime;
      
      // Should complete within 300ms threshold
      expect(duration).toBeLessThan(300);
    });

    it('maintains 60fps during animations', () => {
      // Mock requestAnimationFrame
      let frameCallback: FrameRequestCallback;
      global.requestAnimationFrame = jest.fn((callback) => {
        frameCallback = callback;
        return 1;
      });

      // Simulate frame measurement
      const frames: number[] = [];
      for (let i = 0; i < 60; i++) {
        const frameStart = performance.now();
        if (frameCallback) {
          frameCallback(frameStart);
        }
        frames.push(performance.now() - frameStart);
      }

      // Each frame should complete within 16.67ms (60fps)
      const avgFrameTime = frames.reduce((sum, time) => sum + time, 0) / frames.length;
      expect(avgFrameTime).toBeLessThan(16.67);
    });
  });
});

describe('OfflineProgressStorage', () => {
  let storage: OfflineProgressStorage;

  beforeEach(() => {
    jest.clearAllMocks();
    global.localStorage = mockLocalStorage as any;
    global.navigator = mockNavigator as any;
    
    storage = OfflineProgressStorage.getInstance();
  });

  describe('Data Persistence', () => {
    it('saves progress data to localStorage', async () => {
      const progressData = {
        totalXP: 1000,
        currentLevel: 5,
        achievements: ['first-lesson', 'week-warrior']
      };

      await storage.saveProgress(progressData);

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'solidity_learning_progress',
        expect.stringContaining('"totalXP":1000')
      );
    });

    it('loads progress data from localStorage', () => {
      const mockData = {
        userId: 'test-user',
        totalXP: 1000,
        currentLevel: 5,
        achievements: ['first-lesson'],
        version: 1
      };

      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockData));

      const loadedData = storage.loadProgress();

      expect(loadedData.totalXP).toBe(1000);
      expect(loadedData.currentLevel).toBe(5);
      expect(loadedData.achievements).toContain('first-lesson');
    });

    it('handles corrupted localStorage data gracefully', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-json');

      const loadedData = storage.loadProgress();

      // Should return default data when localStorage is corrupted
      expect(loadedData.totalXP).toBe(0);
      expect(loadedData.currentLevel).toBe(1);
    });

    it('migrates data between versions', () => {
      const oldVersionData = {
        userId: 'test-user',
        totalXP: 1000,
        // Missing new fields
      };

      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(oldVersionData));

      const loadedData = storage.loadProgress();

      // Should have migrated to current version with default values
      expect(loadedData.version).toBe(1);
      expect(loadedData.preferences).toBeDefined();
    });
  });

  describe('XP and Achievement Tracking', () => {
    it('adds XP correctly', async () => {
      const initialData = {
        totalXP: 500,
        sessionData: { sessionXP: 100, actionsCount: 5 }
      };
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(initialData));

      await storage.addXP(200, 'lesson');

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'solidity_learning_progress',
        expect.stringContaining('"totalXP":700')
      );
    });

    it('unlocks achievements without duplicates', async () => {
      const initialData = {
        achievements: ['first-lesson']
      };
      
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(initialData));

      await storage.unlockAchievement('week-warrior');
      await storage.unlockAchievement('first-lesson'); // Duplicate

      const calls = mockLocalStorage.setItem.mock.calls;
      const lastCall = calls[calls.length - 1];
      const savedData = JSON.parse(lastCall[1]);
      
      expect(savedData.achievements).toEqual(['first-lesson', 'week-warrior']);
    });

    it('updates streak data correctly', async () => {
      await storage.updateStreak(7);

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'solidity_learning_progress',
        expect.stringContaining('"currentStreak":7')
      );
    });
  });

  describe('Sync Management', () => {
    it('tracks online/offline status', () => {
      const syncStatus = storage.getSyncStatus();
      
      expect(syncStatus.isOnline).toBe(true);
    });

    it('queues changes for sync when offline', async () => {
      // Simulate offline
      Object.defineProperty(navigator, 'onLine', { value: false });

      await storage.addXP(100, 'lesson');

      // Should add to sync queue
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        'solidity_learning_sync_queue',
        expect.any(String)
      );
    });

    it('processes sync queue when online', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      await storage.forcSync();

      // Should attempt to process sync queue
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Synced item:'),
        expect.any(Object)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('Performance Tests', () => {
    it('saves data within performance threshold', async () => {
      const startTime = performance.now();

      await storage.saveProgress({ totalXP: 1000 });

      const duration = performance.now() - startTime;

      // Should save within 50ms
      expect(duration).toBeLessThan(50);
    });

    it('loads data within performance threshold', () => {
      mockLocalStorage.getItem.mockReturnValue(JSON.stringify({
        totalXP: 1000,
        achievements: Array.from({ length: 100 }, (_, i) => `achievement-${i}`)
      }));

      const startTime = performance.now();

      storage.loadProgress();

      const duration = performance.now() - startTime;

      // Should load within 20ms even with large datasets
      expect(duration).toBeLessThan(20);
    });

    it('handles large sync queues efficiently', async () => {
      // Create large sync queue
      const largeQueue = Array.from({ length: 1000 }, (_, i) => ({
        id: `sync-${i}`,
        action: 'update_progress',
        data: { totalXP: i * 100 },
        timestamp: new Date().toISOString()
      }));

      mockLocalStorage.getItem.mockReturnValue(JSON.stringify(largeQueue));

      const startTime = performance.now();

      await storage.forcSync();

      const duration = performance.now() - startTime;

      // Should process large queue within 5 seconds
      expect(duration).toBeLessThan(5000);
    });
  });
});

describe('useOfflineProgress hook', () => {
  beforeEach(() => {
    global.localStorage = mockLocalStorage as any;
    global.navigator = mockNavigator as any;
  });

  it('provides progress data and sync status', () => {
    const { result } = renderHook(() => useOfflineProgress());

    expect(result.current.progress).toBeDefined();
    expect(result.current.syncStatus).toBeDefined();
    expect(typeof result.current.addXP).toBe('function');
    expect(typeof result.current.unlockAchievement).toBe('function');
  });

  it('updates progress data reactively', async () => {
    const { result } = renderHook(() => useOfflineProgress());

    await act(async () => {
      await result.current.addXP(100, 'lesson');
    });

    expect(result.current.progress?.sessionData.sessionXP).toBeGreaterThan(0);
  });

  it('handles sync status changes', async () => {
    const { result } = renderHook(() => useOfflineProgress());

    // Initial sync status
    expect(result.current.syncStatus?.isOnline).toBe(true);

    // Simulate going offline
    await act(async () => {
      // Trigger offline event
      const offlineEvent = new Event('offline');
      window.dispatchEvent(offlineEvent);
    });

    // Should update sync status
    expect(result.current.syncStatus?.isOnline).toBe(false);
  });
});
