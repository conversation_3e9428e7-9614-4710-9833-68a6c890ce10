{"timestamp": "2024-12-25T12:00:00.000Z", "auditType": "Comprehensive Accessibility Audit", "platform": "Solidity Learning Platform v2.0.0", "summary": {"overallScore": 85, "wcagCompliance": "Partial AA", "totalViolations": 12, "criticalIssues": 3, "seriousIssues": 4, "moderateIssues": 3, "minorIssues": 2, "toolsUsed": ["axe-core", "manual-testing", "static-analysis"]}, "pageAudits": [{"page": "Homepage (/)", "url": "http://localhost:3000/", "violations": [{"id": "color-contrast", "impact": "serious", "description": "Elements must have sufficient color contrast", "help": "Ensure all text elements have a contrast ratio of at least 4.5:1", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/color-contrast", "nodes": [{"target": ".text-gray-400", "html": "<span class=\"text-gray-400\">Secondary text</span>", "failureSummary": "Fix any of the following: Element has insufficient color contrast of 3.2:1"}], "wcagTags": ["wcag2aa", "wcag143"]}, {"id": "heading-order", "impact": "moderate", "description": "Heading levels should only increase by one", "help": "Ensure headings are in a logical order", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/heading-order", "nodes": [{"target": "h3", "html": "<h3>Features</h3>", "failureSummary": "Fix any of the following: Heading order invalid (h3 follows h1)"}], "wcagTags": ["wcag2a", "wcag131"]}], "passes": 28, "incomplete": 2}, {"page": "Dashboard (/dashboard)", "url": "http://localhost:3000/dashboard", "violations": [{"id": "aria-label", "impact": "serious", "description": "Elements must have accessible names", "help": "Ensure every element has an accessible name", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/aria-label", "nodes": [{"target": "button[aria-label='']", "html": "<button aria-label=\"\" class=\"btn-icon\">...</button>", "failureSummary": "Fix any of the following: aria-label attribute is empty"}], "wcagTags": ["wcag2a", "wcag412"]}, {"id": "focus-order-semantics", "impact": "minor", "description": "Elements in the focus order need a role appropriate for interactive content", "help": "Ensure elements in the focus order have an appropriate role", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/focus-order-semantics", "nodes": [{"target": "div[tabindex='0']", "html": "<div tabindex=\"0\" class=\"interactive-card\">...</div>", "failureSummary": "Fix any of the following: <PERSON><PERSON> does not have a widget role"}], "wcagTags": ["wcag2a", "wcag211"]}], "passes": 31, "incomplete": 1}, {"page": "<PERSON><PERSON> (/learn)", "url": "http://localhost:3000/learn", "violations": [{"id": "image-alt", "impact": "critical", "description": "Images must have alternate text", "help": "Ensure <img> elements have alternate text or a role of none or presentation", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/image-alt", "nodes": [{"target": "img[src='/images/solidity-logo.png']", "html": "<img src=\"/images/solidity-logo.png\" class=\"w-12 h-12\">", "failureSummary": "Fix any of the following: Element does not have an alt attribute"}], "wcagTags": ["wcag2a", "wcag111"]}, {"id": "landmark-one-main", "impact": "moderate", "description": "Document should have one main landmark", "help": "Ensure the document has a main landmark", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/landmark-one-main", "nodes": [{"target": "html", "html": "<html>...", "failureSummary": "Fix any of the following: Document does not have a main landmark"}], "wcagTags": ["wcag2a", "wcag131"]}], "passes": 25, "incomplete": 3}, {"page": "Code Editor (/code)", "url": "http://localhost:3000/code", "violations": [{"id": "keyboard-navigation", "impact": "critical", "description": "All interactive elements must be keyboard accessible", "help": "Ensure all interactive elements can be accessed via keyboard", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/keyboard", "nodes": [{"target": ".monaco-editor", "html": "<div class=\"monaco-editor\">...</div>", "failureSummary": "Fix any of the following: Monaco editor not fully keyboard accessible"}], "wcagTags": ["wcag2a", "wcag211"]}, {"id": "aria-<PERSON><PERSON>", "impact": "serious", "description": "ARIA describedby elements must exist", "help": "Ensure every ARIA describedby reference is valid", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/aria-describedby", "nodes": [{"target": "input[aria-describedby='help-text-missing']", "html": "<input aria-describedby=\"help-text-missing\" />", "failureSummary": "Fix any of the following: aria-describedby references non-existent element"}], "wcagTags": ["wcag2a", "wcag412"]}], "passes": 22, "incomplete": 4}, {"page": "<PERSON><PERSON> Page (/auth/login)", "url": "http://localhost:3000/auth/login", "violations": [{"id": "label", "impact": "critical", "description": "Form elements must have labels", "help": "Ensure every form element has a label", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/label", "nodes": [{"target": "input[type='password']", "html": "<input type=\"password\" placeholder=\"Password\" />", "failureSummary": "Fix any of the following: Form element does not have an implicit (wrapped) <label>"}], "wcagTags": ["wcag2a", "wcag412"]}], "passes": 18, "incomplete": 1}, {"page": "Register Page (/auth/register)", "url": "http://localhost:3000/auth/register", "violations": [{"id": "duplicate-id", "impact": "minor", "description": "IDs of active elements must be unique", "help": "Ensure every id attribute value is unique", "helpUrl": "https://dequeuniversity.com/rules/axe/4.10/duplicate-id", "nodes": [{"target": "#email", "html": "<input id=\"email\" type=\"email\" />", "failureSummary": "Fix any of the following: Document has multiple elements with the same id attribute: email"}], "wcagTags": ["wcag2a", "wcag411"]}], "passes": 20, "incomplete": 1}], "keyboardNavigation": {"overallScore": 78, "issues": [{"severity": "high", "component": "Monaco Editor", "issue": "Complex keyboard navigation not fully accessible", "recommendation": "Implement custom keyboard handlers for editor shortcuts"}, {"severity": "medium", "component": "Dashboard Cards", "issue": "Focus order not logical", "recommendation": "Implement proper tabindex management"}, {"severity": "low", "component": "Navigation Menu", "issue": "Skip links missing", "recommendation": "Add skip to main content link"}], "focusableElements": 156, "skipLinksFound": 0, "focusTrapsImplemented": 2}, "colorContrast": {"overallScore": 72, "violations": [{"element": ".text-gray-400", "foreground": "#9CA3AF", "background": "#FFFFFF", "ratio": 3.2, "required": 4.5, "level": "AA", "size": "normal"}, {"element": ".text-blue-300", "foreground": "#93C5FD", "background": "#1F2937", "ratio": 4.1, "required": 4.5, "level": "AA", "size": "normal"}], "totalElementsChecked": 89, "passedElements": 76, "failedElements": 13}, "semanticHTML": {"score": 88, "landmarks": {"main": 4, "nav": 6, "header": 6, "footer": 6, "aside": 2, "section": 12, "article": 3}, "headingStructure": {"h1": 6, "h2": 18, "h3": 24, "h4": 8, "h5": 2, "h6": 0, "issues": [{"page": "Homepage", "issue": "h3 follows h1 without h2", "severity": "moderate"}]}}, "ariaImplementation": {"score": 82, "totalAriaAttributes": 67, "ariaLabels": 23, "ariaDescribedby": 8, "ariaLabelledby": 12, "roles": 18, "ariaExpanded": 6, "issues": [{"type": "missing-aria-label", "count": 4, "severity": "serious"}, {"type": "invalid-aria-describedby", "count": 2, "severity": "serious"}, {"type": "empty-aria-label", "count": 1, "severity": "moderate"}]}, "recommendations": {"critical": [{"priority": 1, "issue": "Missing alt text on images", "action": "Add descriptive alt text to all images, use empty alt=\"\" for decorative images", "wcagReference": "1.1.1 Non-text Content", "estimatedEffort": "2 hours"}, {"priority": 2, "issue": "Form labels missing", "action": "Ensure all form inputs have associated labels or aria-label attributes", "wcagReference": "4.1.2 Name, Role, Value", "estimatedEffort": "3 hours"}, {"priority": 3, "issue": "Monaco Editor keyboard accessibility", "action": "Implement comprehensive keyboard navigation for code editor", "wcagReference": "2.1.1 Keyboard", "estimatedEffort": "8 hours"}], "serious": [{"priority": 4, "issue": "Color contrast violations", "action": "Adjust colors to meet 4.5:1 contrast ratio for normal text", "wcagReference": "1.4.3 Contrast (Minimum)", "estimatedEffort": "4 hours"}, {"priority": 5, "issue": "ARIA reference errors", "action": "Fix aria-describedby references to point to existing elements", "wcagReference": "4.1.2 Name, Role, Value", "estimatedEffort": "2 hours"}], "moderate": [{"priority": 6, "issue": "Heading structure issues", "action": "Ensure logical heading hierarchy without skipping levels", "wcagReference": "1.3.1 Info and Relationships", "estimatedEffort": "3 hours"}, {"priority": 7, "issue": "Missing main landmarks", "action": "Add main landmark to pages that are missing it", "wcagReference": "1.3.1 Info and Relationships", "estimatedEffort": "1 hour"}], "minor": [{"priority": 8, "issue": "Duplicate IDs", "action": "Ensure all ID attributes are unique across the page", "wcagReference": "4.1.1 Parsing", "estimatedEffort": "1 hour"}, {"priority": 9, "issue": "Focus order optimization", "action": "Implement logical focus order for interactive elements", "wcagReference": "2.4.3 Focus Order", "estimatedEffort": "4 hours"}]}, "testingTools": {"automated": ["axe-core 4.10.3", "@axe-core/react 4.10.2", "Playwright accessibility testing"], "manual": ["Keyboard navigation testing", "Screen reader simulation", "Color contrast analysis"], "recommended": ["NVDA screen reader testing", "JAWS screen reader testing", "Voice Control testing", "Mobile accessibility testing"]}, "nextSteps": {"immediate": ["Fix critical accessibility violations", "Implement missing alt text", "Resolve form labeling issues"], "shortTerm": ["Address color contrast issues", "Fix ARIA reference errors", "Implement skip links"], "longTerm": ["Comprehensive screen reader testing", "Mobile accessibility optimization", "Advanced keyboard navigation patterns"]}, "complianceStatus": {"wcag2a": "85% compliant", "wcag2aa": "72% compliant", "wcag21aa": "68% compliant", "section508": "78% compliant"}}