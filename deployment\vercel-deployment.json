{"name": "solidity-learning-platform", "version": 2, "builds": [{"src": "package.json", "use": "@vercel/next", "config": {"maxLambdaSize": "50mb", "includeFiles": "public/**"}}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/sw.js", "dest": "/sw.js", "headers": {"Cache-Control": "public, max-age=0, must-revalidate", "Service-Worker-Allowed": "/"}}, {"src": "/manifest.json", "dest": "/manifest.json", "headers": {"Cache-Control": "public, max-age=31536000, immutable"}}, {"src": "/(.*)", "dest": "/$1"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}, {"source": "/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}, "functions": {"app/api/**/*.js": {"maxDuration": 30}}, "regions": ["iad1"], "github": {"enabled": true, "autoAlias": true, "autoJobCancelation": true}, "buildCommand": "npm run build", "devCommand": "npm run dev", "installCommand": "npm install", "outputDirectory": ".next"}